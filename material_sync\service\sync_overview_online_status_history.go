// service 同步历史上线数据
package service

import (
	"context"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
)

// SyncOverviewOnlineStatusHistory 同步素材历史上线数据
func SyncOverviewOnlineStatusHistory(ctx context.Context, req *material_sync.SyncOverviewOnlineStatusHistoryReq, rsp *material_sync.SyncOverviewOnlineStatusHistoryRsp) error {
	start_time, err := time.Parse("2006-01-02 15:04:05", req.GetStartTime())
	if err != nil {
		log.ErrorContextf(ctx, "parse start time failed: %s", err)
		return err
	}

	end_time, err := time.Parse("2006-01-02 15:04:05", req.GetEndTime())
	if err != nil {
		log.ErrorContextf(ctx, "parse end time failed: %s", err)
		return err
	}

	go SyncOverviewOnlineStatusHistoryGameCode(req.GetGameCode(), start_time, end_time)

	return nil
}

// SyncOverviewOnlineStatusHistoryGameCode 同步某一个game code的历史上线数据
func SyncOverviewOnlineStatusHistoryGameCode(game_code string, start_time, end_time time.Time) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncOverviewOnlineStatusHistoryGameCode start, game code: %s, start time: %v, end time: %v", game_code, start_time, end_time)

	st := time.Now()

	key2asset_id, err := GetChannelAssetKeyToAssetID(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getChannelAssetKeyToAssetID failed: %s", err)
		return
	}

	for start_time.Before(end_time) {
		err = updateOverviewOnlineStatusForGameCode(ctx, game_code, key2asset_id, start_time)
		if err != nil {
			log.ErrorContextf(ctx, "updateOverviewOnlineStatusForGameCode failed: %s", err)
		}
		start_time = start_time.AddDate(0, 0, 1)
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "SyncOverviewOnlineStatusGameCode end, game code: %s, cost: %v", game_code, cost)
}
