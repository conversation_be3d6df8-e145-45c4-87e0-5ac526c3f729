package service

import (
	"context"

	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/data"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise"
)

// GetCampaigns 拉取某个账号下的campaign列表
func GetCampaigns(ctx context.Context, req *pb.GetCampaignsReq, rsp *pb.GetCampaignsRsp) error {
	if req.GetGameCode() == "" || req.GetAccountId() == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code or account_id is empty")
	}

	account, err := data.GetOneMediaAccounts(ctx, req.GetGameCode(), req.GetAccountId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.GetOneMediaAccounts err:%v", err)
	}

	client := newApplovinAPIClient(account)
	camapaigns, err := client.GetCampaigns(ctx)
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "client.GetCampaigns err:%v", err)
	}
	for _, c := range camapaigns {
		t := &pb.Campaign{
			CampaignId:   c.CampaignID,
			CampaignName: c.Name,
			Status:       c.Status,
		}
		rsp.Campaigns = append(rsp.Campaigns, t)
	}
	return nil
}
