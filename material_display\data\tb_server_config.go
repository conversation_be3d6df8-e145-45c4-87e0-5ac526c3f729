package data

import (
	"context"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// GetGameUploadSetting 读取game_code上传配置
func GetGameUploadSetting(ctx context.Context, gameCode string) ([]*model.ServerConfig, error) {
	var rows []*model.ServerConfig
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(&model.ServerConfig{})
	query.Where("server = ?", "asset_upload_server_v2")
	query.Where("second_key = ?", gameCode)
	err := query.Select(&rows)
	return rows, err
}
