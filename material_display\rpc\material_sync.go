package rpc

import (
	"context"
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
)

// NotifyApplyLabelRuleToAix 通知将标签规则应用到aix library
func NotifyApplyLabelRuleToAix(ctx context.Context, gameCode string, ruleID int64) error {
	req := &pb.NotifyApplyLabelRuleToAixReq{
		GameCode: gameCode,
		RuleId:   ruleID,
	}
	rsp := &pb.NotifyApplyLabelRuleToAixRsp{}

	url := fmt.Sprintf("%v/api/v1/material_sync/notify_apply_label_rule_to_aix", conf.GetBizConf().MaterialSync)
	resp, err := newRequest(ctx).SetBody(req).SetResult(rsp).Post(url)
	if err != nil {
		return err
	}

	if resp.StatusCode() != 200 {
		return fmt.Errorf("apply_label_rule_to_aix failed, code: %v, msg: %v", resp.StatusCode(), resp.String())
	}

	return nil
}
