package data

import (
	"context"
	dbModel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/parser"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"fmt"
	"github.com/thoas/go-funk"
	"strings"
)

const (
	videoFormat = `('mp4', 'wmv', 'avi', 'mov', 'flv', 'mkv')`
	imageFormat = `('jpg', 'jpeg', 'png', 'bmp', 'gif')`
)

// LoadAssetList 加载所有素材id
func LoadAssetList(ctx context.Context, gameCode string, startAssetID uint64, pageSize uint32) []dbModel.CreativeOverview {
	objList := []dbModel.CreativeOverview{}
	db := postgresql.Pgdb
	sql := fmt.Sprintf("SELECT * FROM %s.tb_creative_overview_%s WHERE asset_id>? LIMIT ?", "arthub_sync", gameCode)
	_, err := db.Query(&objList, sql, startAssetID, pageSize)
	if err != nil {
		log.ErrorContextf(ctx, "db.Query error, err: %v, gameCode: %v, startAssetID: %v", err, gameCode, startAssetID)
	}
	return objList
}

// LoadPushAsset 加载需要推送的素材
func LoadPushAsset(ctx context.Context, gameCode string, pageSize uint32) []dbModel.CreativeAssetDetails {
	objList := []dbModel.CreativeAssetDetails{}
	// format := `('mp4', 'wmv', 'avi', 'mov', 'flv', 'mkv', 'jpg', 'jpeg', 'png', 'bmp', 'gif')`
	// FIXME: 打标服务处理能力有限，暂时只处理视频素材，且只支持mp4
	format := `('mp4')`
	retryHour := 1.0             // 重试时间，单位小时
	lastRetryHour := 24.0        // 最后一次重试时间，单位小时
	maxSize := 500 * 1024 * 1024 // 素材打标处理上限500MB
	db := postgresql.Pgdb

	sql := fmt.Sprintf("SELECT tb_creative_details_%s.* FROM arthub_sync.tb_creative_details_%s INNER JOIN arthub_sync.tb_creative_overview_%s ON tb_creative_details_%s.asset_id=tb_creative_overview_%s.asset_id", gameCode, gameCode, gameCode, gameCode, gameCode)
	sql += " WHERE "
	conditionList := []string{}
	conditionList = append(conditionList, fmt.Sprintf("size < %d", maxSize))                                                        // 素材大小限制
	conditionList = append(conditionList, fmt.Sprintf("format IN %s", format))                                                      // 素材格式限制
	conditionList = append(conditionList, "(robot_second_label is null or robot_second_label = '' or cover is null or cover = '')") // 未打标或者未生成封面
	// 一个小时内重试过的不再重试，最多三次，24小时后重试最后一次
	conditionList = append(conditionList, fmt.Sprintf("(((robot_label_try_cnts<3 or robot_label_try_cnts is null) AND (update_time < (SELECT to_char(now()-INTERVAL   '%f hour', 'yyyy-MM-dd HH24:mi:ss')) OR update_time IS NULL OR update_time='')) OR (robot_label_try_cnts=3 AND (update_time < (SELECT to_char(now()-INTERVAL '%f hour', 'yyyy-MM-dd HH24:mi:ss')))))", retryHour, lastRetryHour))
	conditionList = append(conditionList, "asset_status=1") // 素材状态为1: normal - 正常

	conditionSql := strings.Join(conditionList, " AND ")
	sql += conditionSql
	sql += " order by robot_label_try_cnts NULLS FIRST LIMIT 50"

	log.DebugContextf(ctx, "LoadPushAsset sql:%v, pageSize:%v", sql, pageSize)
	_, err := db.Query(&objList, sql, pageSize)
	if err != nil {
		log.ErrorContextf(ctx, "LoadPushAsset db.Query error, err: %v, gameCode: %v", err, gameCode)
	}
	return objList
}

// SetMaterialLabel 设置素材机器标签及封面信息
func SetMaterialLabel(ctx context.Context, gameCode, cover, cosUrl, robotFirstLabel, robotSecondLabel, assetId string) error {
	db := postgresql.Pgdb
	// 修改机器标签和封面
	args := []interface{}{}
	sql := fmt.Sprintf("UPDATE %s.tb_creative_details_%s SET ", "arthub_sync", gameCode)
	sqlList := []string{}
	if cover != "" {
		sqlList = append(sqlList, "cover=COALESCE(CASE cover WHEN '' THEN NULL ELSE cover END, ?)")
		args = append(args, cover)
	}
	if cosUrl != "" {
		sqlList = append(sqlList, "cos_url=COALESCE(CASE cos_url WHEN '' THEN NULL ELSE cos_url END, ?)")
		args = append(args, cosUrl)
	}
	if robotFirstLabel != "" {
		sqlList = append(sqlList, "robot_first_label=COALESCE(CASE robot_first_label WHEN '' THEN NULL ELSE robot_first_label END, ?)")
		args = append(args, strings.Join(funk.UniqString(strings.Split(robotFirstLabel, ",")), ","))
	}
	if robotSecondLabel != "" {
		sqlList = append(sqlList, "robot_second_label=COALESCE(CASE robot_second_label WHEN '' THEN NULL ELSE robot_second_label END, ?)")
		args = append(args, strings.Join(funk.UniqString(strings.Split(robotSecondLabel, ",")), ","))
	}
	if len(sqlList) > 0 {
		sql += strings.Join(sqlList, ",") + " WHERE asset_id=?"
		args = append(args, assetId)
		_, err := db.Exec(sql, args...)
		if err != nil {
			log.ErrorContextf(ctx, "db.Query error, err: %v, sql: %v, args: %v", err, sql, args)
			return err
		}
	}

	return nil
}

// SetAssetLabel 设置素材机器标签 asset_label_{game_code}表
func SetAssetLabel(ctx context.Context, gameCode, assetId, robotSecondLabel string) error {
	labelName := "AiX"
	db := postgresql.Pgdb
	timeNow := utils.GetCSTNowStr()

	secondLabelList := strings.Split(robotSecondLabel, ",")
	newAssetLabelList := make([]dbModel.AssetLabel, 0, len(secondLabelList))
	for _, secondLabel := range secondLabelList {
		firstLabel := ""
		secondLabel = parser.UpdateSecondLabel(secondLabel)
		if gameCode == "hok_prod" {
			firstLabel = parser.GetHokFirstLabel(secondLabel)
		} else {
			firstLabel = parser.GetFirstLabel(secondLabel)
		}
		newAssetLabelList = append(newAssetLabelList, dbModel.AssetLabel{
			AssetId:     assetId,
			LabelName:   labelName,
			FirstLabel:  firstLabel,
			SecondLabel: secondLabel,
			CreateTime:  timeNow,
			UpdateTime:  timeNow,
		})
	}
	_, err := db.WithContext(ctx).Model(&newAssetLabelList).TableExpr(fmt.Sprintf("%s.asset_label_%s", "arthub_sync", gameCode)).OnConflict(`("asset_id", "label_name", "first_label", "second_label") DO NOTHING`).Returning("NULL").Insert()
	return err
}

// SetMaterialLabelRetry 申请素材打标次数
func SetMaterialLabelRetry(ctx context.Context, gameCode string, assetId string) error {
	db := postgresql.Pgdb

	// 修改机器标签和封面
	sql := fmt.Sprintf("UPDATE %s.tb_creative_details_%s SET robot_label_try_cnts = COALESCE(robot_label_try_cnts+1, 1),update_time=to_char(now(), 'yyyy-MM-dd HH24:mi:ss') WHERE asset_id = ?", "arthub_sync", gameCode)
	log.DebugContextf(ctx, "SetMaterialLabelRetry sql:%v, assetId:%v", sql, assetId)
	_, err := db.Exec(sql, assetId)
	if err != nil {
		log.ErrorContextf(ctx, "SetMaterialLabelRetry db.Query error, err: %v, sql: %v", err, sql)
		return err
	}

	return nil
}

// BatchSetMaterialLabelRetry 批量申请素材打标次数
// map[gamecode][]assetID
func BatchSetMaterialLabelRetry(ctx context.Context, m map[string][]string) error {
	db := postgresql.Pgdb
	//// 修改机器标签和封面
	for gameCode, assetIds := range m {
		if len(assetIds) == 0 {
			continue
		}
		tableName := fmt.Sprintf("%s.tb_creative_details_%s", "arthub_sync", gameCode)
		_, err := db.Model((*dbModel.CreativeAssetDetails)(nil)).
			Table(tableName).
			WhereIn("asset_id IN(?)", assetIds).
			Set("robot_label_try_cnts = COALESCE(robot_label_try_cnts+1, 1)").
			Set("update_time = to_char(now(), 'yyyy-MM-dd HH24:mi:ss')").
			Update()
		if err != nil {
			log.ErrorContextf(ctx, "SetMaterialLabelRetry db.Query error, err: %v, game_code: %v, assetIds: %v",
				err, gameCode, assetIds)
			return err
		}
	}

	return nil
}

// Depot arthub资源库
type Depot struct {
	DepotID     string `pg:"depot_id"`
	DepotName   string `pg:"depot_name"`
	PublicToken string `pg:"public_token"`
	GameCode    string `pg:"game_code"`
	ArthubCode  string `pg:"arthub_code"`
	Type        int    `pg:"type"`
}

// GetDepotCfg 获取
func GetDepotCfg() (map[string]Depot, error) {
	ctx := log.NewSessionIDContext()
	gameCode2DepotName := make(map[string]Depot)
	var depots []Depot
	db := postgresql.Pgdb
	_, err := db.Query(&depots,
		fmt.Sprintf(`SELECT depot_id,depot_name,public_token,game_code,arthub_code,type FROM %s.tb_arthub_depot`, "arthub_sync"))
	if err != nil {
		log.ErrorContextf(ctx, "[error] get depot info error %v", err)
		return gameCode2DepotName, err
	}
	for _, item := range depots {
		gameCode2DepotName[item.GameCode] = item
	}
	return gameCode2DepotName, nil
}

type StatCount struct {
	Count uint32 `pg:"count"`
}

// StatTotalVideo 统计视频数量
func StatTotalVideo(ctx context.Context, gameCode string) uint32 {
	db := postgresql.Pgdb
	sql := fmt.Sprintf("SELECT count(1) as count FROM %s.tb_creative_details_%s WHERE format in %s", "arthub_sync", gameCode, videoFormat)
	var total StatCount
	_, err := db.Query(&total, sql)
	if err != nil {
		log.ErrorContextf(ctx, "StatTotalVideo db.Query error, err: %v, gameCode: %v", err, gameCode)
	}
	return total.Count
}

// StatTotalImage 统计图片数量
func StatTotalImage(ctx context.Context, gameCode string) uint32 {
	db := postgresql.Pgdb
	sql := fmt.Sprintf("SELECT count(1) as count FROM %s.tb_creative_details_%s WHERE format in %s", "arthub_sync", gameCode, imageFormat)
	var total StatCount
	_, err := db.Query(&total, sql)
	if err != nil {
		log.ErrorContextf(ctx, "StatTotalImage db.Query error, err: %v, gameCode: %v", err, gameCode)
	}
	return total.Count
}

// StatMissCoverVideo 统计缺少封面的视频数量
func StatMissCoverVideo(ctx context.Context, gameCode string) uint32 {
	db := postgresql.Pgdb
	sql := fmt.Sprintf("SELECT count(1) as count FROM %s.tb_creative_details_%s WHERE (cover is null or cover='') AND format in %s", "arthub_sync", gameCode, videoFormat)
	var total StatCount
	_, err := db.Query(&total, sql)
	if err != nil {
		log.ErrorContextf(ctx, "StatMissCoverVideo db.Query error, err: %v, gameCode: %v", err, gameCode)
	}
	return total.Count
}

// StatMissMetaVideo 统计缺少meta的视频数量
func StatMissMetaVideo(ctx context.Context, gameCode string) uint32 {
	db := postgresql.Pgdb
	sql := fmt.Sprintf("SELECT count(1) as count FROM %s.tb_creative_details_%s WHERE width=0 AND format in %s", "arthub_sync", gameCode, videoFormat)
	var total StatCount
	_, err := db.Query(&total, sql)
	if err != nil {
		log.ErrorContextf(ctx, "StatMissMetaVideo db.Query error, err: %v, gameCode: %v", err, gameCode)
	}
	return total.Count
}

// StatMissMetaImage 统计缺少meta的图片数量
func StatMissMetaImage(ctx context.Context, gameCode string) uint32 {
	db := postgresql.Pgdb
	sql := fmt.Sprintf("SELECT count(1) as count FROM %s.tb_creative_details_%s WHERE width=0 AND format in %s", "arthub_sync", gameCode, imageFormat)
	var total StatCount
	_, err := db.Query(&total, sql)
	if err != nil {
		log.ErrorContextf(ctx, "StatMissMetaImage db.Query error, err: %v, gameCode: %v", err, gameCode)
	}
	return total.Count
}
