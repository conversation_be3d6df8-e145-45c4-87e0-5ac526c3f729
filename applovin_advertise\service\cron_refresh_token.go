package service

import (
	"context"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/data"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
)

// RefreshToken 刷新账号token
func RefreshToken(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/RefreshToken")

	// 这里加锁，避免同时去处理
	key := "cron/RefreshToken"
	timeout := time.Minute * 10
	locked, _ := redis.GetApplovinAdvertiseLock(ctx, key, timeout)
	if !locked {
		log.WarningContextf(ctx, "RefreshToken GetLock false")
		return
	}
	// 释放锁
	defer redis.DelApplovinAdvertiseLock(ctx, key)

	accounts, err := data.GetAllMediaAccounts(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "RefreshToken data.GetAllMediaAccounts err:%v", err)
		return
	}

	for _, a := range accounts {
		refreshAccountToken(ctx, a)
	}
}

// 刷新账号的first party token
func refreshAccountToken(ctx context.Context, a *model.MediaAccount) error {
	// 判断是否快失效了，提前10分钟 （first party token有效期为3600秒）
	config := a.ApplovinAccountConfig
	expireAt := config.Authorization.ExpiresAtAIX
	now := time.Now()
	log.DebugContextf(ctx, "refreshAccountToken game_code:%v, account_id:%v, access_token expire_at:%v, expire_at_time:%v",
		a.GameCode, a.AccountId, expireAt, time.Unix(expireAt, 0))
	if now.Add(10*time.Minute).Unix() < expireAt {
		return nil
	}

	client := newApplovinTokenClient(a)
	token, err := client.GetFirstPartyToken(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "refreshAccountToken game_code:%v, account_id:%v, client.RefreshToken err:%v",
			a.GameCode, a.AccountId, err)
		return err
	}

	tmp := &model.ApplovinAccountConfig{
		ClientID:     config.ClientID,
		ClientSecret: config.ClientSecret,
		Authorization: model.Oauth2Token{
			AccessToken:  token.AccessToken,
			TokenType:    token.TokenType,
			ExpiresIn:    token.ExpiresIn,
			ExpiresAtAIX: int64(token.ExpiresIn) + time.Now().Unix(),
		},
	}

	a.AccountConfig, err = utils.MarshalToString(tmp)
	if err != nil {
		log.ErrorContextf(ctx, "RefreshToken game_code:%v, account_id:%v, AccountConfig json.MarshalToString err:%v",
			a.GameCode, a.AccountId, err)
		return err
	}

	// 保存
	err = data.UpdateMediaAccountByField(ctx, a, []string{"account_config"})
	if err != nil {
		log.ErrorContextf(ctx, "RefreshToken game_code:%v, account_id:%v, data.UpdateMediaAccountByField err:%v",
			a.GameCode, a.AccountId, err)
		return err
	}

	return nil
}
