package service

import (
	"strings"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

// SearchMaterialsByName 素材名字搜索
func SearchMaterialsByName(ctx *gin.Context, req *pb.SearchMaterialsByNameReq, rsp *pb.SearchMaterialsByNameRsp) error {
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	assetName := req.GetAssetName()
	searchType := req.GetSearchType()
	if gameCode == "" || assetName == "" || searchType == "" {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "param error")
	}

	depot, err := data.GetDepotWithContext(ctx, gameCode)
	if err != nil {
		return err
	}
	// 获取素材库的标签规则类型和分割符
	ruleType, _ := repo.GetDepotLabelRuleType(ctx, depot)
	splitReg, err := repo.GetDepotSerialSplitReg(ctx, depot)
	if err != nil {
		log.ErrorContextf(ctx, "SearchMaterialsByName repo.GetDepotSerialSplitReg err:%v", err)
		return err
	}

	searchTypes := strings.Split(searchType, "|")
	var overviews []*model.CreativeOverview
	// by_full:名字全匹配
	if funk.ContainsString(searchTypes, "by_full") {
		overviews, err = data.QueryCreativeOverviewByName(ctx, gameCode, []string{assetName})
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.QueryCreativeOverviewByName err:%v", err)
		}
	}
	// 名字全匹配没有找到，才进行 by_prefix:匹配编号前缀
	if len(overviews) == 0 && funk.ContainsString(searchTypes, "by_prefix") {
		matchName := getRuleRegMatchName(gameCode, ruleType, splitReg, assetName)
		if matchName != "" {
			// matchName已规则归一化处理

			rule := &model.TbAssetLabelRule{
				GameCode: gameCode,
				Type:     int32(ruleType),
				Rule:     matchName,
			}
			// 取一条即可
			overviews, _, err = repo.GetCreativeOverviewsByRule(ctx, rule, 0, 1)
			if err != nil {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetCreativeOverviewsByRule err:%v", err)
			}
		}
	}

	if len(overviews) > 0 {
		rsp.Materials = fillMeterialMetas(ctx,
			"", arthubOverViews2MaterialMetas(ctx, overviews), depot.Type, depot.GameCode, depot.ArthubCode)
	}
	rsp.Total = cast.ToUint32(len(rsp.Materials))
	return nil
}
