package service

import (
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
)

//
//type MediaMaterialUpdate struct {
//	AssetId     uint64 `json:"asset_id"`     // 素材id
//	DirectoryId uint64 `json:"directory_id"` // 素材所在媒体目录树目录id
//	//DirectoryName string            `json:"directory_name"` // 素材所在媒体目录树目录名称
//	UplineDate string            `json:"upline_date"` // 素材上传时间
//	Meta       MediaMaterialMeta `json:"meta"`        // 素材元数据信息
//}
//
//type MediaMaterialMeta struct {
//	Size    uint64 `json:"size"`    // 素材大小
//	Format  string `json:"format"`  // 素材格式
//	Creator string `json:"creator"` // 素材创建者
//	Updater string `json:"updater"` // 素材更新者
//
//	Width             uint32 `json:"width"`              // 宽
//	High              uint32 `json:"high"`               // 高
//	Duration          string `json:"duration"`           // 时长（视频类）
//	FrameRate         string `json:"frame_rate"`         // 帧率（视频类）
//	AspectRatio       string `json:"aspect_ratio"`       // 比例（视频类）
//	BitRate           string `json:"bit_rate"`           // 视频比特率
//	CompressionFormat string `json:"compression_format"` // 视频压缩格式
//	Cover             string `json:"cover"`              // 封面
//}

func ListMediaMaterialDirectoryInfo(ctx *gin.Context, req *material_display.MediaDirectoryListReq) (*material_display.MediaDirectoryListRsp, error) {
	var resp material_display.MediaDirectoryListRsp
	gamecode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if err := checkListMediaMaterialDirectoryInfoParam(req, gamecode); err != nil {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	parent_id := req.GetParentId()
	if len(parent_id) == 0 {
		parent_id = gamecode
	}
	tableName := fmt.Sprintf("%s.tb_creative_media_directory_%s", "arthub_sync", gamecode)
	db := postgresql.GetDBWithContext(ctx)
	result, err := db.Exec(`SELECT COUNT(*) FROM  pg_class WHERE relname = ?`, tableName)
	if err != nil {
		log.ErrorContextf(ctx, "fail to check gamecode in postgresql, gamecode: %v, err: %v", gamecode, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	if result.RowsReturned() < 1 {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "resource db not exist")
	}

	models := []*model.CreativeMediaDirectory{}
	sql := fmt.Sprintf(`SELECT * FROM %s.tb_creative_media_directory_%s WHERE parent_id = ?
		ORDER BY update_date desc OFFSET ? LIMIT ?`, "arthub_sync", gamecode)
	_, err = db.Query(&models, sql, parent_id, req.Offset, req.Limit)
	if err != nil {
		log.ErrorContextf(ctx, "error service.ListMediaMaterialDirectoryInfo, err: %v", err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	//debug
	log.DebugContextf(ctx, "ListMediaMaterialDirectoryInfo model length: %v\n", len(models))

	total, err := db.Model((*model.CreativeMediaDirectory)(nil)).
		Table(tableName).
		Where("parent_id = ?", parent_id).
		Count()
	if err != nil {
		log.ErrorContextf(ctx, "fail to count media directory in ListMediaMaterialDirectoryInfo,"+
			" parent_id: %d, err: %v", parent_id, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}

	resp.Total = uint64(total)
	resp.Dirs = mediaMaterialInfoToProto(models)
	return &resp, nil
}

func checkListMediaMaterialDirectoryInfoParam(req *material_display.MediaDirectoryListReq, gamecode string) error {
	if req.GetLimit() <= 0 {
		req.Limit = 10
	}
	if err := checkGameCodeKey(gamecode); err != nil {
		return err
	}
	return nil
}

func mediaMaterialInfoToProto(directorys []*model.CreativeMediaDirectory) []*material_display.MediaDirectory {
	dirs := []*material_display.MediaDirectory{}
	if len(directorys) == 0 {
		return dirs
	}
	for idx := range directorys {
		if directorys[idx] != nil {
			dir := singleMediaMaterialInfoToProto(directorys[idx])
			dirs = append(dirs, dir)
		}
	}
	return dirs
}

func singleMediaMaterialInfoToProto(directory *model.CreativeMediaDirectory) *material_display.MediaDirectory {
	if directory == nil {
		return nil
	}
	dir := material_display.MediaDirectory{
		Id:                   directory.ID,
		Name:                 directory.Name,
		ParentId:             directory.ParentID,
		ParentName:           directory.ParentName,
		CreateDate:           directory.CreateDate,
		UpdateDate:           directory.UpdateDate,
		DirectChildCount:     uint32(directory.DirectChildCount),
		TotalLeafCount:       uint32(directory.TotalLeafCount),
		DirectDirectoryCount: uint32(directory.DirectDirectoryCount),
		FullPathName:         directory.FullPathName,
		FullPathId:           directory.FullPathID,
	}
	return &dir
}

func singleDepotInfoToProto(depot *model.ArthubDepot) *material_display.DepotInfo {
	d := &material_display.DepotInfo{
		DepotId:              depot.DepotId,
		DepotName:            depot.DepotName,
		PublicToken:          depot.PublicToken,
		GameCode:             depot.GameCode,
		GameName:             depot.GameName,
		ArthubCode:           depot.ArthubCode,
		Type:                 int64(depot.Type),
		GoogleServiceAccount: depot.GoogleServiceAccount,
		CloudDriveStatus:     depot.DriveStatus,
	}
	if depot.ExtraInfo.GoogleDrive != nil {
		d.Extra = &material_display.DepotExtra{
			GoogleDrive: &material_display.DepotGoogleDrive{
				IsOauth2Grant: depot.ExtraInfo.GoogleDrive.IsOauth2Grant,
			},
		}
	}

	return d
}
