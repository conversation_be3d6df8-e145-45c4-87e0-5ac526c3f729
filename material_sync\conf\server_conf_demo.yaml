server: # 服务配置
  http_port: 8080 # http服务端口
  read_timeout: 60 # 读超时
  write_timeout: 60 # 写超时
  run_mode: "debug" # 运行模式, debug或release
log: # 日志配置
  level: "debug" # 日志级别
  file_name: "logs/serverlog.log" # 日志名称
  max_size: 100 # 日志文件大小, 单位MB
  max_backup: 10 # 日志文件数量
  max_age: 30 # 最长保留时间, 单位天
  line_length_limit: 10240 # 单条日志最大长度
  stdout: true # 是否需要标准输出
  json_format: false # 打印日志格式, 是否需要使用json

# 是否使用bigquery
use_bigquery: false

database: # 数据库相关配置
  postgresql: # pg配置
    url: "postgres://postgres:123456@127.0.0.1:5432/local?sslmode=disable" # URL
  redis: # redis配置
    addr: "127.0.0.1:6379"
    password: ""
  clickhouse: # ch配置
    url: "tcp://@localhost:9000/ieg_ads_bi_test?dial_timeout=1s&compress=true" # URL
  bigquery: # 默认的bigquery
    project_id: "tencent-xxxxx"
    dataset_id: "xxxxxx"
    service_account: "xxxxx"

ext_database: # 额外数据库相关配置
  bigquery:
    - name: "demo,aix_test" #game_code单独的bigquery, 可以支持多个游戏  逗号隔开
      project_id: "tencent-xxxxx"
      dataset_id: "xxxxxx"
      service_account: "xxxxx"

# cos配置
cos:
  bucket_url: "https://xxxxx.cos.ap-singapore.myqcloud.com"
  secret_id: "xxxxxx"
  secret_key: "xxxxx"

# 定时任务是否禁用(全局标志)
cron_task_disable: false
# 定时任务
cron_task:
  - name: CalculateKeywordFrequency # 关键词频率
    cron: "0 */2 * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: NotifyUnusedMaterial # 通知未使用的素材
    cron: "*/5 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncCreativeAnalysisPivot # 定时同步 analysis pivot
    cron: "0 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: PushMaterial # 定时推送素材
    cron: "* * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: MaterialMetrics # 定时统计素材指标
    cron: "@every 30m" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncCreativeRecommendCandidate # 每天凌晨生成一次
    cron: "0 1 * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SaveAllChannelAssets # 定时同步每天的渠道素材到总表
    cron: "* * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncChannelAssetLabelToCk # 每天凌晨生成同步渠道素材标签到ck
    cron: "0 4 * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncMediaContentMap # 设置素材内容同步
    cron: "5 0 * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncAssetMapTask # 设置aix_asset_id到channel_asset_id的映射
    cron: "30 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  ######  新标签规则体系增加的 20231206
  - name: AutoGenLableRule # 自动生成标签规则
    cron: "0 2 * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: CronLabelRuleTask # 标签规则打标任务处理
    cron: "* * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  ### 定时任务迁移到配置文件控制 20231221
  - name: SyncOnlineInfoTotal # 自动生成标签规则
    cron: "10 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncAssetInfoTotal # 标签规则打标任务处理
    cron: "20 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncImpressionDate # 自动生成标签规则
    cron: "30 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
  - name: SyncRuleLabels # 将标签规则结构化到ck外表(arthub_sync.tb_asset_rule_labels)
    cron: "0 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
