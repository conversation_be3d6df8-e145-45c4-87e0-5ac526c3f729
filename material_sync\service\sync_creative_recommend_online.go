package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	ckmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"github.com/go-pg/pg/v10/orm"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// insertUpdateCreativeRecommendOnlineForGameCode ...
func insertUpdateCreativeRecommendOnlineForGameCode(ctx context.Context, game_code string, onlines []pgmodel.CreativeRecommendOnline) error {
	log.DebugContextf(ctx, "get valid onlines number: %d", len(onlines))
	table_name := pgmodel.GetCreativeRecommendOnlineTableName(game_code)
	if len(onlines) > 0 {
		author := "material_sync.sync_creative_recommend_online_update"
		model := postgresql.GetDBWithContext(ctx).Model(&onlines)
		model.Table(table_name)
		model.OnConflict("(asset_id,channel_type,country_code,campaign_type) DO Update")
		model.Set("update_by=?", author)
		model.Set("update_time=excluded.update_time")
		model.Set("campaign_type=excluded.campaign_type")
		model.Returning("asset_id")
		_, err := model.Insert()
		if err != nil {
			return fmt.Errorf("insert recommend online failed: %s", err)
		}
	}

	expired_time := time.Now().AddDate(0, 0, -7).Format("2006-01-02 15:04:05") // 清理过期一周的数据
	pg_query := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendOnline{}).Table(table_name)
	pg_query.Where("update_time<?", expired_time)
	_, err := pg_query.Delete()
	if err != nil {
		return fmt.Errorf("delete expired online failed: %s", err)
	}

	return nil
}

// campaignNameStruct ...
type campaignNameStruct struct {
	ADCompany      string // 渠道方
	Region         string // 地区
	OS             string // 操作系统
	DateTime       string // 广告投放日期
	CampaignTarget string // campaign目标
	Custom         string // 自定义字段
	SpendType      string // 话费判定
	ProjectRegion  string // 立项区域
}

// parseCampaignName ...
func parseCampaignName(name string) *campaignNameStruct {
	cns := &campaignNameStruct{}

	strs := strings.Split(name, "-")
	if len(strs) < 7 {
		return cns
	}

	cns.ADCompany = strs[0]
	cns.Region = strs[1]
	cns.OS = strs[2]
	cns.DateTime = strs[3]
	cns.CampaignTarget = strs[4]

	if len(strs) <= 7 {
		cns.SpendType = strs[5]
		cns.ProjectRegion = strs[6]
		return cns
	}

	cns.Custom = strs[5]
	cns.SpendType = strs[6]
	cns.ProjectRegion = strs[7]
	return cns
}

// getLatestOnlineAdAsset 获取在线素材
func getLatestOnlineAdAsset(ctx context.Context, game_code string) ([]latestAdAsset, error) {
	pg_query := postgresql.GetDBWithContext(ctx).Model(&pgmodel.FacebookRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(game_code))
	today := time.Now().AddDate(0, 0, -1).Format("********") // 延时一天避免时差
	pg_query.Where("dtstatdate=?", today)
	select_columns := []string{"asset_name", "account_id", "asset_id", "main_country", "network", "country_list", "ad_group_id", "ad_group_name", "campaign_name"}
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)
	var fb_records []pgmodel.FacebookRealtimeAssetInfo
	err := pg_query.Select(&fb_records)
	if err != nil {
		log.ErrorContextf(ctx, "get facebook online number failed: %s", err)
		fb_records = nil
	}
	log.DebugContextf(ctx, "get facebook online data number: %d", len(fb_records))

	pg_query = postgresql.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.Where("dtstatdate=?", today)
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)
	var gg_records []pgmodel.GoogleRealtimeAssetInfo
	err = pg_query.Select(&gg_records)
	if err != nil {
		log.ErrorContextf(ctx, "get google online numbers failed: %s", err)
	}
	log.DebugContextf(ctx, "get google online data number: %d", len(gg_records))

	var ad_assets []latestAdAsset
	for _, record := range gg_records {
		if len(record.AssetId) == 0 {
			continue
		}

		ad_asset := latestAdAsset{}
		ad_asset.AssetName = record.AssetName
		ad_asset.AccountId = record.AccountId
		ad_asset.AssetId = record.AssetId
		ad_asset.MainCountry = record.MainCountry
		ad_asset.Network = record.Network
		ad_asset.AdgroupId = record.AdGroupId
		ad_asset.AdgroupName = record.AdGroupName
		ad_asset.CountryList = record.CountryList
		ad_asset.ChannelType = 1 // google
		ad_asset.CampaignName = record.CampaignName

		ad_assets = append(ad_assets, ad_asset)
	}

	for _, record := range fb_records {
		if len(record.AssetId) == 0 {
			continue
		}

		ad_asset := latestAdAsset{}
		ad_asset.AssetName = record.AssetName
		ad_asset.AccountId = record.AccountId
		ad_asset.AssetId = record.AssetId
		ad_asset.MainCountry = record.MainCountry
		ad_asset.Network = record.Network
		ad_asset.AdgroupId = record.AdGroupId
		ad_asset.AdgroupName = record.AdGroupName
		ad_asset.CountryList = record.CountryList
		ad_asset.ChannelType = 2 // facebook
		ad_asset.CampaignName = record.CampaignName

		ad_assets = append(ad_assets, ad_asset)
	}

	return ad_assets, nil
}

// getLatestOnlinePivots 获取最新一个周期的在线pivot数据
//lint:ignore U1000 Ignore unused function temporarily for debugging
func getLatestOnlinePivots(ctx context.Context, game_code string) ([]ckmodel.CreativeAnalysisPivotNew, error) {
	var ck_db *gorm.DB
	if game_code == "pubgm" {
		ck_db = clickhouse.GetGORMDBWithGameCode(ctx, game_code)
	} else {
		ck_db = clickhouse.GetGORMDB(ctx)
	}

	ck_query := ck_db.Model(&ckmodel.CreativeAnalysisPivotNew{})
	table_name := conf.GetBizConf().CreativeAnalysisPivotNew
	ck_query.Table(table_name)
	ck_query.Select("asset_name", "account_id", "asset_id", "main_country", "network", "country_list", "adgroup_id", "adgroup_name")

	ck_query.Where("game_code=?", game_code)
	ck_sub_query := ck_db.Table(table_name).Select("max(dtstatdate)")
	ck_sub_query.Where("game_code=?", game_code)
	ck_query.Where("dtstatdate=(?)", ck_sub_query)
	ck_query.Where("empty(asset_name)=0")
	ck_query.Where("empty(account_id)=0")
	ck_query.Where("empty(asset_id)=0")
	ck_query.Where("empty(main_country)=0")
	ck_query.Where("empty(network)=0")
	ck_query.Group("asset_name")
	ck_query.Group("account_id")
	ck_query.Group("asset_id")
	ck_query.Group("main_country")
	ck_query.Group("network")
	ck_query.Group("country_list")
	ck_query.Group("adgroup_id")
	ck_query.Group("adgroup_name")

	var pivots []ckmodel.CreativeAnalysisPivotNew
	result := ck_query.Find(&pivots)
	if result.Error != nil {
		return nil, fmt.Errorf("select creative analysis pivot new failed: %s", result.Error)
	}
	log.DebugContextf(ctx, "get pivots number: %d", len(pivots))

	return pivots, nil
}

// syncCreativeRecommendOnline 同步素材推荐需要用到的上线数据
func syncCreativeRecommendOnline(game_code string, key2asset_id map[string]string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "syncCreativeRecommendOnline start, game code: %s", game_code)

	st := time.Now()

	err := createCreativeRecommendOnlineForGameCode(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "createCreativeRecommendOnlineForGameCode failed: %s", err)
		return
	}

	ad_assets, err := getLatestOnlineAdAsset(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getLatestOnlineAdAsset failed: %s", err)
		return
	}

	if len(ad_assets) == 0 {
		return
	}

	onlines, err := genCreativeRecommendOnlines(ctx, game_code, ad_assets, key2asset_id)
	if err != nil {
		log.ErrorContextf(ctx, "genCreativeRecommendOnlines failed: %s", err)
	}

	log.DebugContextf(ctx, "game %s get onlines number: %d", game_code, len(onlines))

	err = insertUpdateCreativeRecommendOnlineForGameCode(ctx, game_code, onlines)
	if err != nil {
		log.ErrorContextf(ctx, "insertUpdateCreativeRecommendOnlineForGameCode failed: %s", err)
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "syncCreativeRecommendOnline end, game code: %s, cost: %v", game_code, cost)
}

// createCreativeRecommendOnlineForGameCode ...
func createCreativeRecommendOnlineForGameCode(ctx context.Context, game_code string) error {
	model := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendOnline{})
	table_name := pgmodel.GetCreativeRecommendOnlineTableName(game_code)
	model.Table(table_name)

	err := model.CreateTable(&orm.CreateTableOptions{IfNotExists: true})
	if err != nil {
		return err
	}

	return nil
}

// genCreativeRecommendOnlines ...
func genCreativeRecommendOnlines(ctx context.Context, game_code string, ad_assets []latestAdAsset, key2asset_id map[string]string) ([]pgmodel.CreativeRecommendOnline, error) {
	onlines_set := make(map[string]bool)
	var onlines []pgmodel.CreativeRecommendOnline
	duplicate_num := 0
	for _, ad_asset := range ad_assets {
		key := genChannelAssetKey(ad_asset.ChannelType, ad_asset.AccountId, ad_asset.AssetId)
		asset_id, ok := key2asset_id[key]
		if !ok {
			continue
		}

		tmp_onlines, err := genCreativeRecommendOnlineRecord(ctx, asset_id, ad_asset)
		if err != nil {
			log.ErrorContextf(ctx, "genCreativeRecommendOnlineRecord failed: %s", err)
			continue
		}

		for _, online := range tmp_onlines {
			pk := pgmodel.GenCreativeRecommendOnlinePK(&online)
			if onlines_set[pk] {
				duplicate_num++
				continue
			}

			onlines_set[pk] = true
			onlines = append(onlines, online)
		}
	}

	log.DebugContextf(ctx, "game %s get duplicate onlines number: %d", game_code, duplicate_num)

	return onlines, nil
}

// 从campaign_name中获取campaign_type, 需要注意的是该字段使用的是自定义字段.
func getCampaignType(name string) string {
	campaign_struct := parseCampaignName(name)
	campaign_type := campaign_struct.Custom
	campaign_type = strings.Split(campaign_type, "_")[0]

	switch {
	case campaign_type == "high":
		return "high_act"
	case funk.InStrings([]string{"purchase", "install"}, campaign_type):
		return campaign_type
	case strings.Contains(strings.ToLower(campaign_type), "login"):
		return "login"
	}

	return "other_types"
}

// genCreativeRecommendOnlineRecord ...
func genCreativeRecommendOnlineRecord(ctx context.Context, asset_id string, asset latestAdAsset) ([]pgmodel.CreativeRecommendOnline, error) {
	online := pgmodel.CreativeRecommendOnline{}
	online.AssetId = asset_id
	online.ChannelType = asset.ChannelType
	online.ChannelAccountID = asset.AccountId
	online.ChannelAssetId = asset.AssetId
	online.CountryCode = strings.ToLower(asset.MainCountry)
	online.AdgroupId = asset.AdgroupId
	online.AdgroupName = asset.AdgroupName
	author := "material_sync.sync_creative_recommend_online"
	online.CreateBy = author
	time_now := time.Now().Format("2006-01-02 15:04:05")
	online.CreateTime = time_now
	online.UpdateBy = author
	online.UpdateTime = time_now
	online.CampaignType = getCampaignType(asset.CampaignName)

	var onlines []pgmodel.CreativeRecommendOnline
	country_list := asset.CountryList
	country_list = strings.TrimLeft(country_list, "[")
	country_list = strings.TrimRight(country_list, "]")
	countries := strings.Split(country_list, ",")
	for _, country := range countries {
		country = strings.TrimSpace(country)
		if len(country) == 0 {
			continue
		}

		tmp_online := online
		tmp_online.CountryCode = strings.ToLower(country)
		onlines = append(onlines, tmp_online)
	}
	if len(onlines) == 0 {
		onlines = append(onlines, online)
	}

	return onlines, nil
}
