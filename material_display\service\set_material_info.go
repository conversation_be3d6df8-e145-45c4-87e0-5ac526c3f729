package service

import (
	"errors"
	"fmt"
	"strings"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pg "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
)

// SetMaterialInfo 编辑素材信息
func SetMaterialInfo(ctx *gin.Context, req *pb.SetMaterialInfoReq, rsp *pb.SetMaterialInfoRsp) error {
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if gameCode == "" {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	depot, ok := cache.DepotTbArthubDepotCache[gameCode]
	if !ok {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), fmt.Sprintf("no depot inf find for game code: %s", gameCode))
	}

	materialSetList := req.GetMaterialSetList()
	for _, materialSet := range materialSetList {
		res := uint32(1)
		err := setMaterialInfo(ctx, gameCode, &depot, materialSet)
		resMsg := ""
		if err != nil {
			log.WarningContextf(ctx, "error data.SetMaterialInfo, materialSet: %v, err: %v", materialSet, err)
			res = 0
			resMsg = err.Error()
		}
		rsp.SetResList = append(rsp.SetResList, &pb.SetRes{
			AssetId: materialSet.GetAssetId(),
			SetRes:  res,
			ResMsg:  resMsg,
		})
	}
	return nil
}

// SetMaterialInfo 设置素材信息，按需设置
func setMaterialInfo(ctx *gin.Context, gameCode string, depot *model.ArthubDepot, materialInfo *pb.MaterialSetInfo) error {
	db := pg.GetDBWithContext(ctx)

	// 获取素材信息
	tablename := fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", gameCode)
	asset := model.CreativeOverview{}
	assetId := materialInfo.GetAssetId()
	err := db.Model(&asset).Table(tablename).Where("asset_id = ?", assetId).Select()
	if err != nil {
		log.ErrorContextf(ctx, "fail to get asset information,  asset_id: %v,err: %v", materialInfo.GetAssetId(), err)
		return err
	}

	// 修改详情表
	args := []interface{}{}
	sql := fmt.Sprintf("UPDATE %s.tb_creative_details_%s SET ", "arthub_sync", gameCode)
	sqlList := []string{}

	sqlList = append(sqlList, "cover=?")
	args = append(args, materialInfo.GetCover())

	sqlList = append(sqlList, "manual_first_label=?")
	args = append(args, strings.Join(funk.UniqString(materialInfo.GetLabel().GetManualFirstLabel()), ","))

	sqlList = append(sqlList, "manual_second_label=?")
	args = append(args, strings.Join(funk.UniqString(materialInfo.GetLabel().GetManualSecondLabel()), ","))

	sqlList = append(sqlList, "robot_first_label=?")
	args = append(args, strings.Join(funk.UniqString(materialInfo.GetLabel().GetRobotFirstLabel()), ","))

	sqlList = append(sqlList, "robot_second_label=?")
	args = append(args, strings.Join(funk.UniqString(materialInfo.GetLabel().GetRobotSecondLabel()), ","))

	if len(sqlList) > 0 {
		sql += strings.Join(sqlList, ",") + " WHERE asset_id=?"
		args = append(args, materialInfo.GetAssetId())
		_, err = db.Exec(sql, args...)
		if err != nil {
			log.ErrorContextf(ctx, "sql: %v, args: %v", sql, args)
			return err
		}
	}

	if materialInfo.GetName() != "" {
		// 校验素材名称
		existed, err := db.Model((*model.CreativeOverview)(nil)).
			Table(tablename).
			Where("node_id = ? and asset_name = ? and asset_id != ? and asset_status=1", asset.NodeID, materialInfo.GetName(), asset.AssetID).Exists()
		if err != nil {
			log.ErrorContextf(ctx, "fail to check asset name reqpeated, nodeId: %v, assetName: %v,err: %v",
				asset.NodeID, materialInfo.GetName(), err)
			return err
		}
		if existed {
			log.ErrorContextf(ctx, "asset name repeated in directory '%v', asset_id: %v,"+
				" param asset_name: %v", asset.NodeID, materialInfo.GetAssetId(), materialInfo.GetName())
			return errors.New("asset name repeated")
		}

		// 修改arthub素材名称
		switch depot.Type {
		case utils.GAME_DEPOT_TYPE_ARTHUB:
			err = arthub.UpdateAssetNameByID(ctx, depot.PublicToken, depot.ArthubCode, materialInfo.GetAssetId(), materialInfo.GetName())
		case utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER:
			srv, _ := cache.GoogleServiceCache{}.Get(ctx, depot.GameCode)
			if srv != nil {
				err = srv.UpdateAssetNameByID(materialInfo.GetAssetId(), materialInfo.GetName()+"."+asset.FileFormat)
			}
		case utils.GAME_DEPOT_TYPE_DROPBOX:
			return fmt.Errorf("not support dropbox")
		default:
			return fmt.Errorf("not support depot type: %v", depot.Type)
		}
		if err != nil {
			return fmt.Errorf("arthub.UpdateAssetNameByID failed: %s", err)
		}

		// 修改概览表
		args = []interface{}{}
		sql = fmt.Sprintf("UPDATE %s.tb_creative_overview_%s SET ", "arthub_sync", gameCode)
		sqlList = []string{}

		sqlList = append(sqlList, "asset_name=?")
		args = append(args, materialInfo.GetName())

		if len(sqlList) > 0 {
			sql += strings.Join(sqlList, ",") + " WHERE asset_id=?"
			args = append(args, materialInfo.GetAssetId())
			_, err := db.Exec(sql, args...)
			if err != nil {
				log.ErrorContextf(ctx, "sql: %v, args: %v", sql, args)
				return err
			}
		}
	}

	return nil
}
