package data

import (
	"context"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"github.com/go-pg/pg/v10"
	"github.com/thoas/go-funk"
)

// DeleteRuleLabelsBeforTime 删除标签规则结构化ck外表历史数据
func DeleteRuleLabelsBeforTime(ctx context.Context, gameCode string, beforeTime time.Time) error {
	query := postgresql.GetDBWithContext(ctx).Model(&model.TbAssetRuleLabels{})
	query.Where("game_code = ?", gameCode)
	query.Where("update_time < ?", utils.FormatDefault(beforeTime))
	_, err := query.Delete()

	return err
}

// InsertRuleLabelsTransaction 插入标签规则结构化ck外表，会先删除旧标签，事务中处理
// deleteRows 先删除那些符合条件的行 (game_code, rule, type)
// labels 待插入的数据
func InsertRuleLabelsTransaction(ctx context.Context,
	deleteRows []*model.TbAssetRuleLabels, lables []*model.TbAssetRuleLabels) error {
	if len(deleteRows) == 0 || len(lables) == 0 {
		return nil
	}

	var multi [][]interface{}
	for _, row := range deleteRows {
		one := []interface{}{row.GameCode, row.Rule, row.Type}
		multi = append(multi, one)
	}

	err := postgresql.Transaction(ctx, func(tx *pg.Tx) error {
		// 先删除之前的标签
		deleteQuery := tx.Model(&model.TbAssetRuleLabels{})
		deleteQuery.WhereInMulti("(game_code, rule, type) in ?", multi)
		_, err := deleteQuery.Delete()
		if err != nil {
			return err
		}

		// 分批插入新的标签
		chunks := funk.Chunk(lables, 1000).([][]*model.TbAssetRuleLabels)
		for _, chunk := range chunks {
			insertQuery := tx.Model(&chunk)
			_, err = insertQuery.Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})

	return err
}

// GetLabelRule 拉取具体某个标签规则 无数据返回nil, nil
func GetLabelRule(ctx context.Context, gameCode string, rule string, ruleType int32) (*model.TbAssetLabelRule, error) {

	db := postgresql.GetDBWithContext(ctx)
	row := &model.TbAssetLabelRule{}
	query := db.Model(row)
	query.Where("game_code = ?", gameCode)
	query.Where("rule = ?", rule)
	query.Where("type = ?", ruleType)
	err := query.First()
	if err != nil {
		if err == pg.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return row, nil
}
