package service

import (
	"context"
	"fmt"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// UploadVirtualAssets 上传非实体素材列表
func UploadVirtualAssets(ctx *gin.Context, req *material_sync.UploadVirtualAssetsReq, rsp *material_sync.UploadVirtualAssetsRsp) error {
	game_code := ctx.Request.Header.Get("game")
	pg_assets, err := toPgVirtualAssets(ctx, req.GetUserName(), req.GetAssets())
	if err != nil {
		return fmt.Errorf("toPgVirtualAssets failed: %s", err)
	}

	update_time, err := upsertVirtualAssets(ctx, game_code, pg_assets)
	if err != nil {
		return fmt.Errorf("upsertVirtualAssets failed: %s", err)
	}

	switch req.GetMapHistoryType() {
	case 1: // 仅映射新增虚拟素材
		go mapHistoryChannelAssetsForVirtualWithUpdateTime(game_code, *update_time)
	case 2: // 映射所有上传虚拟素材
		go mapHistoryChannelAssetsForVirtual(game_code, pg_assets)
	default: // 默认方式
		go mapHistoryChannelAssetsForVirtualWithUpdateTime(game_code, *update_time)
	}

	return nil
}

// toPgVirtualAssets 转换为PG表的非实体素材结构体
func toPgVirtualAssets(ctx context.Context, user_name string, pb_assets []*material_sync.VirtualAsset) ([]pgmodel.VirtualAsset, error) {
	pg_assets := make([]pgmodel.VirtualAsset, 0, len(pb_assets))

	for _, pb_asset := range pb_assets {
		pg_asset := pgmodel.VirtualAsset{}
		pg_asset.AssetId = pb_asset.GetAssetId()
		pg_asset.AssetType = pb_asset.GetAssetType()
		pg_asset.AssetName = pb_asset.GetAssetName()
		pg_asset.AssetTheme = pb_asset.GetAssetTheme()
		pg_asset.AssetVersion = pb_asset.GetAssetVersion()
		pg_asset.CreateBy = user_name
		pg_asset.UpdateBy = user_name
		pg_asset.LabelName = pb_asset.GetLabelName()
		pg_asset.FirstLabel = pb_asset.GetFirstLabel()
		pg_asset.SecondLabel = pb_asset.GetSecondLabel()

		pg_assets = append(pg_assets, pg_asset)
	}

	return pg_assets, nil
}

// mapHistoryChannelAssetsForVirtualWithUpdateTime 根据虚拟素材映射广告标签, 只同步update_time之后创建的素材, 避免重复处理历史数据
func mapHistoryChannelAssetsForVirtualWithUpdateTime(game_code string, update_time time.Time) {
	ctx := log.NewSessionIDContext()
	var virtual_assets []pgmodel.VirtualAsset
	table_name := pgmodel.GetVirtualAssetTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&virtual_assets).Table(table_name)
	pg_query.Column("asset_id", "asset_type", "label_name", "first_label", "second_label")
	pg_query.Where("create_time>=?", update_time.Format("2006-01-02 15:04:05"))
	err := pg_query.Select()
	if err != nil {
		log.ErrorContextf(ctx, "select virtual assets failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get virtual assets number: %d", len(virtual_assets))

	mapHistoryChannelAssetsForVirtual(game_code, virtual_assets)
}

// mapHistoryChannelAssetsForVirtual 根据虚拟标签映射历史数据
func mapHistoryChannelAssetsForVirtual(game_code string, virtual_assets []pgmodel.VirtualAsset) {
	if len(virtual_assets) == 0 {
		return
	}

	youtube2assets, prefix2assets := genVirtualAssetMap(virtual_assets)

	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "mapHistoryChannelAssetsForVirtual start, game code: %s", game_code)

	start_time := time.Now()

	min_date, err := getMinDate(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getMinDate failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get min date: %v", *min_date)

	time_now := time.Now()
	time_current := *min_date
	channel_asset_label_set := make(map[string]bool)

	for time_now.After(time_current) {
		err := mapHistoryChannelAssetsForVirtualDaily(ctx, game_code, time_current, channel_asset_label_set, youtube2assets, prefix2assets)
		if err != nil {
			log.ErrorContextf(ctx, "mapHistoryChannelAssetsForVirtualDaily failed: %s", err)
		}

		time_current = time_current.AddDate(0, 0, 1)
	}

	cost := time.Since(start_time)
	log.InfoContextf(ctx, "mapHistoryChannelAssetsForVirtual end, game code: %s, cost: %v", game_code, cost)
}

// genVirtualAssetMap 生成虚拟素材映射
func genVirtualAssetMap(virtual_assets []pgmodel.VirtualAsset) (map[string][]pgmodel.VirtualAsset, map[string][]pgmodel.VirtualAsset) {
	youtube2assets := make(map[string][]pgmodel.VirtualAsset)
	prefix2assets := make(map[string][]pgmodel.VirtualAsset)

	for _, virtual_asset := range virtual_assets {
		switch virtual_asset.AssetType {
		case 1: // youtube id
			youtube2assets[virtual_asset.AssetId] = append(youtube2assets[virtual_asset.AssetId], virtual_asset)
		case 2: // 编号, 即素材名称前缀
			prefix2assets[virtual_asset.AssetId] = append(prefix2assets[virtual_asset.AssetId], virtual_asset)
		}
	}

	return youtube2assets, prefix2assets
}

// mapHistoryChannelAssetsForVirtualDaily 匹配每天的虚拟素材并更新pg
func mapHistoryChannelAssetsForVirtualDaily(ctx context.Context, game_code string, time_current time.Time, channel_asset_label_set map[string]bool, youtube2assets, prefix2assets map[string][]pgmodel.VirtualAsset) error {
	channel_assets, err := getChannelAssetsForVirtual(ctx, game_code, time_current)
	if err != nil {
		return fmt.Errorf("getChannelAssetsForVirtual failed: %s", err)
	}

	log.DebugContextf(ctx, "get channel assets number: %d", len(channel_assets))

	var channel_asset_labels []pgmodel.ChannelAssetLabel
	for _, channel_asset := range channel_assets {
		var inner_channel_asset_labels []pgmodel.ChannelAssetLabel
		if len(channel_asset.YoutubeId) > 0 {
			virtual_assets, ok := youtube2assets[channel_asset.YoutubeId]
			if ok {
				inner_channel_asset_labels = genChannelAssetLabelsFromVirtualAssets("material_sync.virtual_assets_youtube_id", channel_asset, virtual_assets)
			}
		}
		if len(channel_asset.AssetName) > 7 {
			prefix := channel_asset.AssetName[0:7]
			virtual_assets, ok := prefix2assets[prefix]
			if ok {
				inner_channel_asset_labels = append(inner_channel_asset_labels, genChannelAssetLabelsFromVirtualAssets("material_sync.virtual_assets_prefix", channel_asset, virtual_assets)...)
			}
		}

		for _, channel_asset_label := range inner_channel_asset_labels {
			key := genChannelAssetLabelKey(channel_asset_label)
			if !channel_asset_label_set[key] && len(channel_asset_label.SecondLabel) > 0 && channel_asset_label.SecondLabel != "-" {
				channel_asset_labels = append(channel_asset_labels, channel_asset_label)
				channel_asset_label_set[key] = true
			}
		}
	}

	log.DebugContextf(ctx, "get channel asset labels number: %d", len(channel_asset_labels))

	err = upsertChannelAssetLabels(ctx, game_code, channel_asset_labels)
	if err != nil {
		log.ErrorContextf(ctx, "upsertChannelAssetLabelsForNamePrefixLabel failed: %s", err)
	}

	return nil
}

// upsertChannelAssetLabels 插入/更新广告素材标签
func upsertChannelAssetLabels(ctx context.Context, game_code string, channel_asset_labels []pgmodel.ChannelAssetLabel) error {
	if len(channel_asset_labels) == 0 {
		return nil
	}

	time_now := time.Now().Format("2006-01-02 15:04:05")
	new_channel_asset_labels := make([]pgmodel.ChannelAssetLabel, 0, len(channel_asset_labels))
	for _, label := range channel_asset_labels {
		label.CreateTime = time_now
		label.UpdateTime = time_now

		new_channel_asset_labels = append(new_channel_asset_labels, label)
	}

	table_name := pgmodel.GetChannelAssetLabelTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&new_channel_asset_labels).Table(table_name)
	pg_query.OnConflict("(channel_type, channel_account_id, channel_asset_id, label_name, first_label, second_label, asset_name) do update")
	pg_query.Set("label_name=excluded.label_name")
	pg_query.Set("first_label=excluded.first_label")
	pg_query.Set("second_label=excluded.second_label")
	pg_query.Set("update_by=excluded.update_by")
	pg_query.Set("update_time=excluded.update_time")

	_, err := pg_query.Insert()
	if err != nil {
		return fmt.Errorf("insert channel asset label failed: %s", err)
	}

	return nil
}

// genChannelAssetLabelKey 生成渠道端素材标签key
func genChannelAssetLabelKey(label pgmodel.ChannelAssetLabel) string {
	return fmt.Sprintf("%d:%s:%s:%s:%s:%s", label.ChannelType, label.ChannelAccountID, label.ChannelAssetId, label.LabelName, label.FirstLabel, label.SecondLabel)
}

// genChannelAssetLabelsFromVirtualAssets 从虚拟素材中生成广告素材标签
func genChannelAssetLabelsFromVirtualAssets(author string, channel_asset channelAsset, virtual_assets []pgmodel.VirtualAsset) []pgmodel.ChannelAssetLabel {
	var channel_asset_labels []pgmodel.ChannelAssetLabel
	var channel_asset_label pgmodel.ChannelAssetLabel
	channel_asset_label.ChannelType = channel_asset.ChannelType
	channel_asset_label.ChannelAssetId = channel_asset.AssetId
	channel_asset_label.ChannelAccountID = channel_asset.AccountId
	channel_asset_label.AssetName = channel_asset.AssetName
	for _, virtual_asset := range virtual_assets {
		if len(virtual_asset.SecondLabel) == 0 || virtual_asset.SecondLabel == "-" {
			continue
		}

		channel_asset_label.LabelName = virtual_asset.LabelName
		channel_asset_label.FirstLabel = virtual_asset.FirstLabel
		channel_asset_label.SecondLabel = virtual_asset.SecondLabel
		channel_asset_label.CreateBy = author
		channel_asset_label.UpdateBy = author

		channel_asset_labels = append(channel_asset_labels, channel_asset_label)
	}

	return channel_asset_labels
}

// upsertVirtualAssets 插入更新非实体素材
func upsertVirtualAssets(ctx context.Context, game_code string, assets []pgmodel.VirtualAsset) (*time.Time, error) {
	time_now := time.Now()
	time_now_str := time_now.Format("2006-01-02 15:04:05")
	var new_assets []pgmodel.VirtualAsset
	for _, asset := range assets {
		asset.CreateTime = time_now_str
		asset.UpdateTime = time_now_str

		new_assets = append(new_assets, asset)
	}

	pg_query := pgdb.GetDBWithContext(ctx).Model(&new_assets).Returning("asset_id").Table(pgmodel.GetVirtualAssetTableName(game_code))
	pg_query.OnConflict("(asset_id, asset_type, label_name, first_label, second_label) do update")
	pg_query.Set("update_by=excluded.update_by")
	pg_query.Set("asset_name=excluded.asset_name")
	pg_query.Set("update_time=excluded.update_time")
	if _, err := pg_query.Insert(); err != nil {
		return nil, fmt.Errorf("insert virtual assets failed: %s", err)
	}

	return &time_now, nil
}

// upsertImpressions ...
func upsertImpressions(ctx context.Context, game_code string, impressions []*pgmodel.CreativeRecommendImpression) error {
	time_now := time.Now().Format("2006-01-02 15:04:05")
	for _, impression := range impressions {
		impression.CreateTime = time_now
		impression.UpdateTime = time_now
	}

	pg_query := pgdb.GetDBWithContext(ctx).Model(&impressions).Table(pgmodel.GetCreativeRecommendImpressionTableName(game_code))
	pg_query.OnConflict("(channel_type, channel_account_id, channel_asset_id) do update")
	pg_query.Set("asset_theme=CASE excluded.asset_theme WHEN '' THEN creative_recommend_impression.asset_theme ELSE excluded.asset_theme END")
	pg_query.Set("asset_version=CASE excluded.asset_version WHEN '' THEN creative_recommend_impression.asset_version ELSE excluded.asset_version END")
	pg_query.Set("update_by=CASE excluded.update_by WHEN '' THEN creative_recommend_impression.update_by ELSE excluded.update_by END")
	pg_query.Set("update_time=excluded.update_time")
	_, err := pg_query.Insert()
	if err != nil {
		return fmt.Errorf("insert impressions failed: %s", err)
	}

	return nil
}

// digAssetVersion 挖掘素材版本
//lint:ignore U1000 Ignore unused function temporarily for debugging
func digAssetVersion(game_code string, assets []pgmodel.VirtualAsset) {
	ctx := log.NewSessionIDContext()
	log.InfoContextf(ctx, "start digAssetVersion, game code: %s", game_code)

	min_date, err := getMinDate(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getMinDate failed: %s", err)
		return
	}

	asset_set := make(map[string]pgmodel.VirtualAsset)
	for _, asset := range assets {
		asset_set[asset.AssetId] = asset
	}

	cur_date := *min_date
	impression_set := make(map[string]bool)
	var impressions []*pgmodel.CreativeRecommendImpression
	time_now := time.Now()
	for time_now.After(cur_date) {
		daily_impressions, err := digAssetVersionDaily(ctx, game_code, &cur_date, asset_set)
		if err != nil {
			log.ErrorContextf(ctx, "digAssetVersionDaily failed: %s", err)
			return
		}

		for _, impression := range daily_impressions {
			pk := pgmodel.GenCreativeRecommendImpressionPK(impression)
			if impression_set[pk] {
				continue
			}

			impression_set[pk] = true
			impressions = append(impressions, impression)
		}

		cur_date = cur_date.AddDate(0, 0, 1)
	}
	log.DebugContextf(ctx, "get impressions len: %d", len(impressions))

	var dig_assets []pgmodel.VirtualAsset
	for _, asset := range assets {
		if len(asset.AssetVersion) == 0 {
			continue
		}

		dig_assets = append(dig_assets, asset)
	}
	log.DebugContextf(ctx, "get dig_asset len: %d", len(dig_assets))

	_, err = upsertVirtualAssets(ctx, game_code, dig_assets)
	if err != nil {
		log.ErrorContextf(ctx, "upsertVirtualAssets failed: %s", err)
		return
	}

	err = upsertImpressions(ctx, game_code, impressions)
	if err != nil {
		log.ErrorContextf(ctx, "upsertImpressions failed: %s", err)
	}
}

// getChannelAssetsForVirtual 获取虚拟素材使用的渠道端素材
func getChannelAssetsForVirtual(ctx context.Context, game_code string, date_time time.Time) ([]channelAsset, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.FacebookRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(game_code))

	date := date_time.Format("********")
	pg_query.Where("dtstatdate=?", date)
	select_columns := []string{"asset_name", "account_id", "asset_id"}
	pg_query.Column(select_columns...)
	pg_query.Where("asset_id is not null and asset_id != ''")
	pg_query.Group(select_columns...)

	var fb_records []pgmodel.FacebookRealtimeAssetInfo
	err := pg_query.Select(&fb_records)
	if err != nil {
		log.ErrorContextf(ctx, "select facebook asset info failed: %s", err)
	}

	log.DebugContextf(ctx, "game %s get facebook ad assets number: %d", game_code, len(fb_records))

	pg_query = pgdb.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.Where("dtstatdate=?", date)
	select_columns = []string{"asset_name", "account_id", "asset_id", "youtube_id"}
	pg_query.Column(select_columns...)
	pg_query.Where("asset_id is not null and asset_id != ''")
	pg_query.Group(select_columns...)

	var gg_records []pgmodel.GoogleRealtimeAssetInfo
	err = pg_query.Select(&gg_records)
	if err != nil {
		log.ErrorContextf(ctx, "get google asset info failed: %s", err)
	}

	log.DebugContextf(ctx, "game %s get google ad assets number: %d", game_code, len(gg_records))

	var channel_assets []channelAsset
	for _, record := range fb_records {
		channel_asset := channelAsset{}
		channel_asset.AccountId = record.AccountId
		channel_asset.AssetId = record.AssetId
		channel_asset.AssetName = record.AssetName
		channel_asset.ChannelType = 2 // facebook

		channel_assets = append(channel_assets, channel_asset)
	}

	for _, record := range gg_records {
		channel_asset := channelAsset{}
		channel_asset.AccountId = record.AccountId
		channel_asset.AssetId = record.AssetId
		channel_asset.AssetName = record.AssetName
		channel_asset.ChannelType = 1 // google
		channel_asset.YoutubeId = record.YoutubeId

		channel_assets = append(channel_assets, channel_asset)
	}

	return channel_assets, nil
}

// getMinDate ...
func getMinDate(ctx context.Context, game_code string) (*time.Time, error) {
	var gg_record pgmodel.GoogleRealtimeAssetInfo
	pg_query := pgdb.GetDBWithContext(ctx).Model(&gg_record).Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.ColumnExpr("min(dtstatdate) as dtstatdate")
	pg_query.Limit(1)
	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select google min dtstatdate failed: %s", err)
	}

	min_date_str := gg_record.Dtstatdate

	var fb_record pgmodel.FacebookRealtimeAssetInfo
	pg_query = pgdb.GetDBWithContext(ctx).Model(&fb_record).Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(game_code))
	pg_query.ColumnExpr("min(dtstatdate) as dtstatdate")
	pg_query.Limit(1)
	err = pg_query.Select()
	if err != nil {
		log.ErrorContextf(ctx, "select facebook min dtstatdate failed: %s", err)
	} else {
		if min_date_str > fb_record.Dtstatdate {
			min_date_str = fb_record.Dtstatdate
		}
	}

	min_date, err := time.Parse("********", min_date_str)
	if err != nil {
		return nil, err
	}

	return &min_date, nil
}

// digAssetVersionDaily 挖掘广告及素材版本信息, 注意asset_set中的version值会被修改
// 第一个返回值表示的是找到上线数据的广告
func digAssetVersionDaily(ctx context.Context, game_code string, date *time.Time, asset_set map[string]pgmodel.VirtualAsset) ([]*pgmodel.CreativeRecommendImpression, error) {
	var records []*pgmodel.GoogleRealtimeAssetInfo
	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.Column("customer_id", "asset_id", "youtube_id", "account_id")
	pg_query.Group("customer_id", "asset_id", "youtube_id", "account_id")
	pg_query.Where("youtube_id is not null and youtube_id != ''")
	pg_query.Where("impressions>0")
	date_str := date.Format("********")
	pg_query.Where("dtstatdate=?", date_str)
	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select online assets failed: %s", err)
	}

	var impressions []*pgmodel.CreativeRecommendImpression
	date_time := date.Format("2006-01-02 15:04:05")
	daily_asset_online := 0
	for _, record := range records {
		asset, ok := asset_set[record.YoutubeId]
		if !ok {
			continue
		}

		if len(asset.AssetVersion) == 0 {
			version, err := getPubgmAssetVersion(date_str)
			if err != nil {
				log.ErrorContextf(ctx, "getPubgmAssetVersion failed: %s", err)
				continue
			}

			asset.AssetVersion = version
			asset.UpdateBy = "material_sync.dig_asset_version"
			daily_asset_online++
		}

		var impression pgmodel.CreativeRecommendImpression
		impression.AssetTheme = asset.AssetTheme
		impression.AssetVersion = asset.AssetVersion
		impression.ChannelAccountID = record.AccountId
		impression.ChannelAssetId = record.AssetId
		impression.ChannelType = 1 // google
		impression.FirstImpressionDate = date_time
		author := "material_sync.dig_asset_version"
		impression.CreateBy = author
		impression.UpdateBy = author
		impressions = append(impressions, &impression)
	}
	log.DebugContextf(ctx, "get %d new assets on date %s", daily_asset_online, date_str)

	return impressions, nil
}

// getPubgmAssetVersion 根据上线时间获取pubgm素材版本, 时间格式为********
func getPubgmAssetVersion(date string) (string, error) {
	var version string
	switch {
	case date >= "********":
		version = "2.1.0"
	case date >= "********":
		version = "2.0.0"
	case date >= "********":
		version = "1.9.0"
	case date >= "********":
		version = "1.8.0"
	case date >= "********":
		version = "1.7.0"
	default:
		return "", fmt.Errorf("no version map for date: %s", date)
	}

	return version, nil
}
