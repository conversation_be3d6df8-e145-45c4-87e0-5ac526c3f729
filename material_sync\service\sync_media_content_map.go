package service

import (
	"context"
	"fmt"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/rpc/creative_insights"
	"github.com/google/uuid"
	"github.com/thoas/go-funk"
)

// SyncMediaContentMap 设置素材内容同步
func SyncMediaContentMap(ctx context.Context) {
	syncCreativeRecommendMediaContentMapLoop()
}

// syncCreativeRecommendMediaContentMapLoop ...
func syncCreativeRecommendMediaContentMapLoop() {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "syncCreativeRecommendMediaContentLoop start")

	st := time.Now()
	game_codes, err := getGameCodes(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getGameCodes failed: %s", err)
		return
	}

	for _, game_code := range game_codes {
		if game_code != "hok_prod" { // TODO: 将限制条件改为配置文件中添加
			continue
		}

		SyncCreativeRecommendMediaContentMapForGameCode(game_code)
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "syncCreativeRecommendMediaContentLoop end, cost: %s", cost)
}

// NeedMediaContentMapSync 是否需要做内容匹配
func NeedMediaContentMapSync(game_code string) bool {
	game_codes := conf.GetBizConf().MediaContentMapGames
	for _, g := range game_codes {
		if game_code == g {
			return true
		}
	}

	return false
}

// SyncCreativeRecommendMediaContentMapForGameCode ...
func SyncCreativeRecommendMediaContentMapForGameCode(game_code string) {
	ctx := log.NewSessionIDContext()
	log.InfoContextf(ctx, "syncCreativeRecommendMediaContentMapForGameCode start, game code: %s", game_code)

	st := time.Now()

	youtube_ids, err := LatestPublishYoutubeIDs(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "latestPublishYoutubeIDs failed: %s", err.Error())
		return
	}

	youtube_ids, err = FilterMappedYoutubeID(ctx, game_code, youtube_ids)
	if err != nil {
		log.ErrorContextf(ctx, "filterMappedYoutubeID failed: %s", err)
		return
	}

	batch_size := 50
	youtube_id_chunks := funk.ChunkStrings(youtube_ids, batch_size)
	for _, chunk := range youtube_id_chunks {
		err := CallAddMappings(ctx, game_code, chunk)
		if err != nil {
			log.ErrorContextf(ctx, "setAddMappings failed: %s", err)
			return
		}
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "syncCreativeRecommendMediaContentMapForGameCode end, game code: %s, cost: %s", game_code, cost)
}

// LatestPublishYoutubeIDs ...
func LatestPublishYoutubeIDs(ctx context.Context, game_code string) ([]string, error) {
	var records []*pgmodel.GoogleRealtimeAssetInfo
	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.Group("youtube_id")
	pg_query.Column("youtube_id")
	today := time.Now().AddDate(0, 0, -1).Format("20060102") // 延时一天避免时差
	pg_query.Where("dtstatdate=?", today)

	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select google real time asset info failed: %s", err.Error())
	}

	var youtube_ids []string
	for _, record := range records {
		if len(record.YoutubeId) == 0 {
			continue
		}

		youtube_ids = append(youtube_ids, record.YoutubeId)
	}

	return youtube_ids, nil
}

// CallAddMappings 设置素材映射
func CallAddMappings(ctx context.Context, game_code string, youtube_ids []string) error {
	if len(youtube_ids) == 0 {
		return nil
	}

	var req creative_insights.AddMappingReq
	req.ReqID = uuid.NewString()
	for _, youtube_id := range youtube_ids {
		var video creative_insights.Video
		video.GameCode = game_code
		video.StorageType = creative_insights.STORAGE_TYPE_YOUTUBE
		video.StorageURL = fmt.Sprintf("https://www.youtube.com/watch?v=%s", youtube_id)
		video.VideoID = youtube_id

		req.VideoList = append(req.VideoList, video)
	}

	target := conf.GetBizConf().CreativeInsightsTarget
	rsp, err := creative_insights.AddMapping(ctx, target, &req)
	if err != nil {
		return fmt.Errorf("creative_insights.AddMapping failed: %s", err)
	}

	if rsp.RetCode != "0" {
		return fmt.Errorf("add mapping error, ret code: %s, message: %s", rsp.RetCode, rsp.Message)
	}

	return nil
}

// FilterMappedYoutubeID 过滤已经映射过的youtube id
func FilterMappedYoutubeID(ctx context.Context, game_code string, youtube_ids []string) ([]string, error) {
	var records []pgmodel.CreativeRecommendMediaContentMap
	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(pgmodel.GetCreativeRecommendMediaContentMapTableName(game_code))
	pg_query.Column("media_id")
	pg_query.Group("media_id")

	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select creative recommend media content failed: %s", err)
	}

	mapped_id_set := make(map[string]bool)
	for _, record := range records {
		mapped_id_set[record.MediaID] = true
	}

	var filtered_ids []string
	for _, youtube_id := range youtube_ids {
		if mapped_id_set[youtube_id] {
			continue
		}

		filtered_ids = append(filtered_ids, youtube_id)
	}

	return filtered_ids, nil
}
