package cron

import (
	"context"

	"e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	pgModel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pg "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"github.com/thoas/go-funk"
)

// LoadAllDepotToken 加载所有depot token缓存
func LoadAllDepotToken(ctx context.Context) {
	var depotList []pgModel.ArthubDepot

	query := pg.GetDBWithContext(ctx).Model(&pgModel.ArthubDepot{})
	err := query.Select(&depotList)
	if err != nil {
		log.ErrorContextf(ctx, "LoadAllDepotToken err: %v", err)
		return
	}

	cache.DepotTbArthubDepotCache = funk.Map(depotList, func(d postgresql.ArthubDepot) (string, pgModel.ArthubDepot) { return d.GameCode, d }).(map[string]pgModel.ArthubDepot)
}
