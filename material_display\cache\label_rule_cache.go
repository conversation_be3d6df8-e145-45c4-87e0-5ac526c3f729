package cache

import (
	"context"
	"fmt"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/localcache"
)

// labelRuleMatchAssetCache 标签规则匹配的素材信息缓存
var labelRuleMatchAssetCache *localcache.LocalCache

func init() {
	var err error
	labelRuleMatchAssetCache, err = localcache.NewLocalCache(context.Background(), localcache.Config{
		Expire:          24 * time.Hour,
		MaxCacheStorage: 1024,
	})
	if err != nil {
		panic(fmt.Errorf("localcache.NewLocalCache labelRuleMatchAssetCache error:%v", err))
	}
}

// SetLabelRuleMatchAsset 设置缓存签规则匹配的素材信息
func SetLabelRuleMatchAsset(gameCode string, ruleType int, ruleName string, view *model.CreativeOverview) error {
	key := genLabelRuleMatchAssetKey(gameCode, ruleType, ruleName)
	err := labelRuleMatchAssetCache.Set(key, view)
	return err
}

// GetLabelRuleMatchAsset 从缓存获取标签规则匹配的素材信息
func GetLabelRuleMatchAsset(gameCode string, ruleType int, ruleName string) (*model.CreativeOverview, error) {
	key := genLabelRuleMatchAssetKey(gameCode, ruleType, ruleName)
	view := &model.CreativeOverview{}
	err := labelRuleMatchAssetCache.Get(key, view)
	return view, err
}

func genLabelRuleMatchAssetKey(gameCode string, ruleType int, ruleName string) string {
	return fmt.Sprintf("label_rule_match_asset:%s:%d:%s", gameCode, ruleType, ruleName)
}
