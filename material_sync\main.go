package main

import (
	"fmt"
	"net/http"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/cos"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/cron"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data/bigquery"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/metrics"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/router"

	"github.com/gin-gonic/gin"
)

// Init 初始化流程
func Init() {
	setting.Setup()
	log.Setup()

	conf.LoadBizConf()
	metrics.Setup()
	postgresql.Setup()
	redis.Init()
	// 海外gcp环境没有clickhouse，需要禁用
	if !conf.GetBizConf().DisabelClickhouse {
		clickhouse.SetupOrm()
	}
	bigquery.Setup()
	cos.SetupCos()
	cron.InitCron()
}

func main() {
	Init()

	defer func() {
		postgresql.Close()
	}()

	gin.SetMode(setting.GetConf().Server.RunMode)

	routersInit := router.InitRouter()
	readTimeout := time.Duration(setting.GetConf().Server.ReadTimeout * int(time.Second))
	writeTimeout := time.Duration(setting.GetConf().Server.WriteTimeout * int(time.Second))
	endPoint := fmt.Sprintf(":%d", setting.GetConf().Server.HTTPPort)
	maxHeaderBytes := 1 << 20

	server := &http.Server{
		Addr:           endPoint,
		Handler:        routersInit,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}

	log.Debugf("start http server listening %s", endPoint)

	err := server.ListenAndServe()
	if err != nil {
		log.Fatalf("server.ListenAndServe err:%v", err)
	}
}
