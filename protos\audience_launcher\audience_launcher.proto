syntax = "proto3";

package audience_launcher;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/audience_launcher";

import "aix/aix_common_message.proto";

// 获取卡片列表, POST, /api/v1/audience_launcher/get_card_info_list
message GetCardInfoListReq {
}

// 卡片基本信息
message CardBaseInfo {
    string launcher_id    = 1;  // 卡片id
    string launcher_name  = 2;  // 卡片名称
    string launcher_intro = 3;  // 卡片简介
    uint32 label_type     = 4;  // 标签类型 LabelType: 0-标准 1-智能
}

// 卡片附加信息
message CardExtInfo {
    uint32 live              = 1;   // live数据
    uint32 optimize_goal     = 2;   // 优化目标，位操作  0-空  1-活跃向优化  2-付费向优化
    uint32 optimize_retarget = 3;   // 1-newinstall retention% 2-newinstall purchase 3-reattribution reattribution
    uint32 media_type        = 4;   // 1-GG
    uint32 history_account   = 5;   // 0-不能拉历史account 1-能拉到历史account
    uint32 created_by        = 6;   // 创建者 1-model  2-rule-based
    uint32 high_type         = 7;   // 1-high act 2-high model 3-high attribution
    float high_percent       = 8;   // 百分比 不超过1
    uint32 audience_type     = 9;   // audience的类型 1-event
    uint32 frequency         = 10;  // 频率 1-daily
    repeated string country  = 11;  // 国家 "all"代表所有
    string os                = 12;  // 系统 "all"代表所有
    int32 install_range      = 13;  // <0负数代表前x天
    int32 install_register   = 14;  // <0负数代表前x天
    int32 install_active     = 15;  // <0负数代表前x天
    int32 install_ex_active  = 16;  // <0负数代表前x天
}

// 卡片详情
message CardInfo {
    uint32 card_type       = 1;  // 卡片类型  1-标准 2-profile
    CardBaseInfo base_info = 2;  // 卡片基础信息
    CardExtInfo ext_info   = 3;  // 卡片附加信息
}

// 卡片组
message CardGroup {
    string group_name                = 1;  // 卡片分组名称
    repeated CardInfo card_info_list = 2;  // 卡片详情列表
}

message GetCardInfoListRsp {
    aix.Result result                  = 1;  // 返回结果
    repeated CardGroup card_group_list = 2;  // 卡片详情列表
}

// 批量获取卡片详情, POST, /api/v1/audience_launcher/bt_get_card_info
message BtGetCardInfoReq {
    repeated string launcher_id_list = 1;  // 卡片id
}

message BtGetCardInfoRsp {
    repeated CardInfo card_info = 1;  // 卡片详情
}
