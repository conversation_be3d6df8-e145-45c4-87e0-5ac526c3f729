package service

import (
	"context"
	"fmt"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// SyncVirtualChannelAssetLabelsForGameCode 同步某个游戏的虚拟素材信息, 主要用于生成素材标签
func SyncVirtualChannelAssetLabelsForGameCode(game_code string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncVirtualAssetLabelsForGameCode for %s start", game_code)

	st := time.Now()

	var virtual_assets []pgmodel.VirtualAsset
	table_name := pgmodel.GetVirtualAssetTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&virtual_assets).Table(table_name)
	pg_query.Column("asset_id", "asset_type", "label_name", "first_label", "second_label")
	err := pg_query.Select()
	if err != nil {
		log.ErrorContextf(ctx, "select virtual assets failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get virtual assets number: %d", len(virtual_assets))

	mapChannelAssetsForVirtual(game_code, virtual_assets)

	cost := time.Since(st)
	log.InfoContextf(ctx, "SyncVirtualAssetLabelsForGameCode for %s end, cost: %v", game_code, cost)
}

// mapChannelAssetsForVirtual 根据虚拟标签映射历史数据
func mapChannelAssetsForVirtual(game_code string, virtual_assets []pgmodel.VirtualAsset) {
	if len(virtual_assets) == 0 {
		return
	}

	youtube2assets, prefix2assets := genVirtualAssetMap(virtual_assets)

	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "mapHistoryChannelAssetsForVirtual start, game code: %s", game_code)

	start_time := time.Now()

	time_now := time.Now().AddDate(0, 0, -1)
	channel_asset_label_set := make(map[string]bool)

	err := mapHistoryChannelAssetsForVirtualDaily(ctx, game_code, time_now, channel_asset_label_set, youtube2assets, prefix2assets)
	if err != nil {
		log.ErrorContextf(ctx, "mapHistoryChannelAssetsForVirtualDaily failed: %s", err)
	}

	cost := time.Since(start_time)
	log.InfoContextf(ctx, "mapHistoryChannelAssetsForVirtual end, game code: %s, cost: %v", game_code, cost)
}

// latestChannelAssetsWithYoutubeId 获取最近的渠道端素材及Youtube id
// 因为youtube id只有google使用, 故只需拉取google数据即可
//lint:ignore U1000 Ignore unused function temporarily for debugging
func latestChannelAssetsWithYoutubeId(ctx context.Context, game_code string) ([]channelAsset, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	today := time.Now().AddDate(0, 0, -1).Format("********") // 延迟一天以消除时差
	pg_query.Where("dtstatdate=?", today)
	select_columns := []string{"account_id", "asset_id", "youtube_id"}
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)

	var gg_records []pgmodel.GoogleRealtimeAssetInfo
	err := pg_query.Select(&gg_records)
	if err != nil {
		log.ErrorContextf(ctx, "get google online numbers failed: %s", err)
	}

	log.DebugContextf(ctx, "get google channel assets number: %d", len(gg_records))

	var channel_assets []channelAsset
	for _, record := range gg_records {
		if len(record.AssetId) == 0 {
			continue
		}

		ad_asset := channelAsset{}
		ad_asset.AccountId = record.AccountId
		ad_asset.AssetId = record.AssetId
		ad_asset.ChannelType = 1 // google
		ad_asset.YoutubeId = record.YoutubeId

		channel_assets = append(channel_assets, ad_asset)
	}

	return channel_assets, nil
}

// youtubeIDLabel youtube id标签
type youtubeIDLabel struct {
	LabelName   string // 标签名
	FirstLabel  string // 一级标签
	SecondLabel string // 二级标签
}

// getYoutubeIdToLabels 获取youtube id到标签的映射
//lint:ignore U1000 Ignore unused function temporarily for debugging
func getYoutubeIdToLabels(ctx context.Context, game_code string) (map[string][]youtubeIDLabel, error) {
	var records []pgmodel.VirtualAsset
	table_name := pgmodel.GetVirtualAssetTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(table_name)
	pg_query.Column("asset_id", "label_name", "first_label", "second_label")
	pg_query.Where("label_name != '' and label_name is not null")
	pg_query.Where("first_label != '' and first_label is not null")
	pg_query.Where("second_label != '' and second_label is not null")

	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select youtube id labels failed: %s", err)
	}

	log.InfoContextf(ctx, "get youtube id labels number: %d", len(records))

	id2labels := make(map[string][]youtubeIDLabel)
	for _, asset := range records {
		var label youtubeIDLabel
		label.LabelName = asset.LabelName
		label.FirstLabel = asset.FirstLabel
		label.SecondLabel = asset.SecondLabel

		id2labels[asset.AssetId] = append(id2labels[asset.AssetId], label)
	}

	return id2labels, nil
}
