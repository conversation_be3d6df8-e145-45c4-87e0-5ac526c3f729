syntax = "proto3";

package central;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/central";
import "aix/aix_common_message.proto";


// 获取翻译信息, POST, /api/v1/central/get_translation
message GetTranslationReq {
    string   game_code                     = 1;  // 必填 游戏标识 game_code
    string   media                         = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id                    = 3;  // 渠道账号id 不填表示账号无关
    repeated TranslationParam translations = 4;  // 待翻译源内容列表
}

// 翻译 参数
message TranslationParam {
    string translation_type = 1;  // 翻译类型. AIX_CAMPAIGN_TYPE
    string source_content   = 2;  // 翻译源内容. APP_INSTALL. 为空则表示查询 translation_type 下所有配置翻译项列表
}

// 翻译项
message TranslationItem {
    string source_content  = 1;  // 翻译源内容. 例如: APP_INSTALL.
    string target_content  = 2;  // 翻译目标内容. 例如: App Installs
    bool   is_visible      = 3;  // 翻译【源内容】是否处于可见状态. (注：翻译源内容和目标内容仍然可用) true:可见  false:不可见，需要翻译内容，又不允许 source_content 可见
    string source_category = 4;  // source_content 所属类别. 空串表示无类别, 不为空则source_category与source_content其中一项一致
}

// 翻译类型 & 翻译item列表
message TranslationTypeItem {
    string   translation_type      = 1;  // 翻译类型. 例如: AIX_CAMPAIGN_TYPE
    repeated TranslationItem items = 2;  // 翻译项列表
}

message GetTranslationRsp {
    aix.Result result                                  = 1;  // 返回结果
    string     trace_id                                = 2;  // 请求trace_id
    repeated   TranslationTypeItem translation_results = 3;  // 翻译结果列表
}


// 批量创建或更新翻译信息, POST, /api/v1/central/save_translations
message SaveTranslationsReq {
    string   game_code                                  = 1;  // 游戏标识 game_code. 默认为 AIX_ALL
    string   media                                      = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id                                 = 3;  // 渠道账号id. 默认为 AIX_ALL
    string   save_type                                  = 4;  // 保存类型. 默认为 DEFAULT. 支持: "DELETE_OLD_TRANSLATION": 根据type删除旧translation列表，再进行覆盖
    repeated TranslationTypeItem translation_type_items = 5;  // 翻译类型 & 翻译item列表
}

message SaveTranslationsRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
}

// 获取国家地区分类及其列表信息, POST, /api/v1/central/get_location
message GetLocationReq {
    string game_code  = 1;  // 必填 游戏标识 game_code
    string media      = 2;  // 必填 渠道标识 eg: TikTok
    string account_id = 3;  // 渠道账号id 不填表示账号无关
}

message RegionCategory {
    string   category_id    = 1;  // 分类唯一ID
    string   category_name  = 2;  // 分类名称
    repeated Region regions = 3;  // 分类下国家/地区列表
}

message Region {
    string region_id   = 1;  // 国家/地区 唯一ID
    string region_code = 2;  // 国家/地区 二字码(大写)
    string region_name = 3;  // 国家/地区 名称
}

message GetLocationRsp {
    aix.Result result                          = 1;  // 返回结果
    string     trace_id                        = 2;  // 请求trace_id
    repeated   RegionCategory region_categorys = 3;  // 国家地区分类列表
}

// 批量创建或更新国家地区信息, POST, /api/v1/central/save_locations
message SaveLocationsReq {
    string   game_code                       = 1;  // 游戏标识 game_code. 默认为 AIX_ALL
    string   media                           = 2;  // 必填 渠道标识 eg: TikTok . 默认为 AIX_ALL
    string   account_id                      = 3;  // 渠道账号id. 默认为 AIX_ALL
    string   save_type                       = 4;  // 保存类型. 默认为 DEFAULT. 支持: "DELETE_OLD_REGION": 删除旧region列表，再进行覆盖
    repeated RegionCategory region_categorys = 5;  // 国家地区分类列表
}

message SaveLocationsRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
}

// 查询模板列表, POST, /api/v1/central/get_templates
message GetTemplatesReq {
    string game_code     = 1;  // 必填 游戏标识game_code
    string media         = 2;  // 必填 渠道标识 eg: TikTok
    string template_type = 3;  // 必填 模板类型. 支持 TARGETING
}

message Template {
    string template_id      = 1;  // 模板唯一ID
    string template_type    = 2;  // 模板类型
    string template_name    = 3;  // 模板名称
    string template_content = 4;  // 模板内容. 模板类型为TARGETING: 校验是否为json格式
    string create_user      = 5;  // 模板创建者
    string update_user      = 6;  // 模板更新者
    string create_time      = 7;  // 模板创建时间
    string update_time      = 8;  // 模板更新时间
    string template_status  = 9;  // 模板状态. AVAILABLE: 可用状态; UNAVAILABLE: 不可用状态
}

message GetTemplatesRsp {
    aix.Result result             = 1;  // 返回结果
    string     trace_id           = 2;  // 请求trace_id
    repeated   Template templates = 3;  // 模板列表
}

// 新增或更新 模板, POST, /api/v1/central/save_template
message SaveTemplateReq {
    string   game_code     = 1;  // 必填 游戏标识game_code
    string   media         = 2;  // 必填 渠道标识 eg: TikTok
    string   template_type = 3;  // 必填 模板类型. 支持 TARGETING
    Template template      = 4;  // 模板数据
}

message SaveTemplateRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
    Template   template = 3;  // 模板数据. 新增时会填充 template_id
}

// 批量删除模板, POST, /api/v1/central/delete_templates
message DeleteTemplatesReq {
    string   game_code           = 1;  // 必填 游戏标识game_code
    string   media               = 2;  // 必填 渠道标识 eg: TikTok
    string   template_type       = 3;  // 必填 模板类型. 支持 TARGETING
    repeated string template_ids = 4;  // 模板ID列表
}

message DeleteTemplatesRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
}

// 上报监控数据, POST, /api/v1/central/report_monitor_data
message ReportMonitorDataReq {
    string monitor_type = 1;  // 监控类型. 
                              // support: [ACCOUNT_STATUS, CAMPAIGN_DELIVERY_STATUS, 
                              // ADGROUP_DELIVERY_STATUS, AD_DELIVERY_STATUS]
    oneof monitor_data {
        MonitorAccountStatusList          account_status_data           = 2;
        MonitorCampaignDeliveryStatusList campaign_delivery_status_data = 3;
        MonitorAdgroupDeliveryStatusList  adgroup_delivery_status_data  = 4;
        MonitorAdDeliveryStatusList       ad_delivery_status_data       = 5;
    }
}

message ReportMonitorDataRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
}

message MonitorAccountStatusList {
    repeated MonitorAccountStatus account_status_list = 1;
}

message MonitorAccountStatus {
    string game_code               = 1;  // 游戏标识 game_code
    string media                   = 2;  // 渠道标识 eg: TikTok
    string account_id              = 3;  // 渠道账号id
    string account_name            = 4;  // 账号名称
    string previous_account_status = 5;  // account 上一个 status
    string current_account_status  = 6;  // account 当前 status
}

message MonitorCampaignDeliveryStatusList {
    repeated MonitorCampaignDeliveryStatus campaign_delivery_list = 1;
}

message MonitorCampaignDeliveryStatus {
    string game_code                = 1;  // 游戏标识 game_code
    string media                    = 2;  // 渠道标识 eg: TikTok
    string account_id               = 3;  // 渠道账号id
    string account_name             = 4;  // 账号名称
    string campaign_id              = 5;  // campaign id
    string campaign_name            = 6;  // campaign 名称
    string previous_delivery_status = 7;  // campaign 上一个 delivery status
    string current_delivery_status  = 8;  // campaign 当前 delivery status
}

message MonitorAdgroupDeliveryStatusList {
    repeated MonitorAdgroupDeliveryStatus adgroup_delivery_list = 1;
}

message MonitorAdgroupDeliveryStatus {
    string game_code                = 1;  // 游戏标识 game_code
    string media                    = 2;  // 渠道标识 eg: TikTok
    string account_id               = 3;  // 渠道账号id
    string account_name             = 4;  // 渠道账号名称
    string adgroup_id               = 5;  // adgroup id
    string adgroup_name             = 6;  // adgroup 名称
    string previous_delivery_status = 7;  // adgroup 上一个 delivery status
    string current_delivery_status  = 8;  // adgroup 当前 delivery status
}

message MonitorAdDeliveryStatusList {
    repeated MonitorAdDeliveryStatus ad_delivery_list = 1;
}

message MonitorAdDeliveryStatus {
    string game_code                = 1;  // 游戏标识 game_code
    string media                    = 2;  // 渠道标识 eg: TikTok
    string account_id               = 3;  // 渠道账号id
    string account_name             = 4;  // 渠道账号名称
    string ad_id                    = 5;  // ad id
    string ad_name                  = 6;  // ad 名称
    string previous_delivery_status = 7;  // ad 上一个 delivery status
    string current_delivery_status  = 8;  // ad 当前 delivery status
}

// 发起monitor检测任务, POST, /api/v1/central/launch_monitor_task
message LaunchMonitorTaskReq {
    repeated string target_game_codes   = 1;  // 待检测目标 game_code 列表，不允许为空列表
    repeated string target_medias       = 2;  // 待检测目标 media 渠道列表，不允许为空列表
    repeated string target_status_types = 3;  // 待检测目标 status_type 渠道列表，不允许为空列表
}

message LaunchMonitorTaskRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
}
