package data

import (
	"context"

	chModel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
)

// LoadPivotDateData 拉取某个同步时间周期的所有数据 dateStr: 20220320 周日开始日期
func LoadPivotDateData(ctx context.Context, gameCode string, dateStr string) []chModel.CreativeAnalysisPivot {
	// 这部分逻辑分期
	// var dataList []chModel.CreativeAnalysisPivot
	// conn := ch.Conn
	// pageSize := 1000

	// for i := 0; i < 5000; i++ {
	// 	var tmpDataList []chModel.CreativeAnalysisPivot
	// 	sql := fmt.Sprintf("select * from %s.creative_analysis_pivot where game_code='%s' and dtstatdate=%s order by asset_name limit %d, %d", conf.GetBizConf().BiDbName, gameCode, dateStr, pageSize*i, pageSize)
	// 	err := conn.Select(ctx, &tmpDataList, sql)
	// 	if err != nil {
	// 		log.ErrorContextf(ctx, "error conn.Select, err: %v", err)
	// 	}
	// 	if len(tmpDataList) <= 0 {
	// 		break
	// 	}
	// 	dataList = append(dataList, tmpDataList...)
	// }
	// return dataList

	return nil
}
