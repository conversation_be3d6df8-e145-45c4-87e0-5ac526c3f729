package cache

import (
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/google"

	"github.com/gin-gonic/gin"
)

var GoogleServiceCacheMap map[string]*google.GoogleService

func InitGoogleServiceCache() {
	GoogleServiceCacheMap = make(map[string]*google.GoogleService)
}

type GoogleServiceCache struct{}

func (g GoogleServiceCache) Get(ctx *gin.Context, gameCode string) (*google.GoogleService, error) {
	return nil, fmt.Errorf("GoogleServiceCache deprecated")
}
