// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.1
// source: audience_launcher/audience_launcher.proto

package audience_launcher

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取卡片列表, POST, /api/v1/audience_launcher/get_card_info_list
type GetCardInfoListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCardInfoListReq) Reset() {
	*x = GetCardInfoListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardInfoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardInfoListReq) ProtoMessage() {}

func (x *GetCardInfoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardInfoListReq.ProtoReflect.Descriptor instead.
func (*GetCardInfoListReq) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{0}
}

// 卡片基本信息
type CardBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LauncherId    string `protobuf:"bytes,1,opt,name=launcher_id,json=launcherId,proto3" json:"launcher_id,omitempty"`          // 卡片id
	LauncherName  string `protobuf:"bytes,2,opt,name=launcher_name,json=launcherName,proto3" json:"launcher_name,omitempty"`    // 卡片名称
	LauncherIntro string `protobuf:"bytes,3,opt,name=launcher_intro,json=launcherIntro,proto3" json:"launcher_intro,omitempty"` // 卡片简介
	LabelType     uint32 `protobuf:"varint,4,opt,name=label_type,json=labelType,proto3" json:"label_type,omitempty"`            // 标签类型 LabelType: 0-标准 1-智能
}

func (x *CardBaseInfo) Reset() {
	*x = CardBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardBaseInfo) ProtoMessage() {}

func (x *CardBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardBaseInfo.ProtoReflect.Descriptor instead.
func (*CardBaseInfo) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{1}
}

func (x *CardBaseInfo) GetLauncherId() string {
	if x != nil {
		return x.LauncherId
	}
	return ""
}

func (x *CardBaseInfo) GetLauncherName() string {
	if x != nil {
		return x.LauncherName
	}
	return ""
}

func (x *CardBaseInfo) GetLauncherIntro() string {
	if x != nil {
		return x.LauncherIntro
	}
	return ""
}

func (x *CardBaseInfo) GetLabelType() uint32 {
	if x != nil {
		return x.LabelType
	}
	return 0
}

// 卡片附加信息
type CardExtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Live             uint32   `protobuf:"varint,1,opt,name=live,proto3" json:"live,omitempty"`                                                 // live数据
	OptimizeGoal     uint32   `protobuf:"varint,2,opt,name=optimize_goal,json=optimizeGoal,proto3" json:"optimize_goal,omitempty"`             // 优化目标，位操作  0-空  1-活跃向优化  2-付费向优化
	OptimizeRetarget uint32   `protobuf:"varint,3,opt,name=optimize_retarget,json=optimizeRetarget,proto3" json:"optimize_retarget,omitempty"` // 1-newinstall retention% 2-newinstall purchase 3-reattribution reattribution
	MediaType        uint32   `protobuf:"varint,4,opt,name=media_type,json=mediaType,proto3" json:"media_type,omitempty"`                      // 1-GG
	HistoryAccount   uint32   `protobuf:"varint,5,opt,name=history_account,json=historyAccount,proto3" json:"history_account,omitempty"`       // 0-不能拉历史account 1-能拉到历史account
	CreatedBy        uint32   `protobuf:"varint,6,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                      // 创建者 1-model  2-rule-based
	HighType         uint32   `protobuf:"varint,7,opt,name=high_type,json=highType,proto3" json:"high_type,omitempty"`                         // 1-high act 2-high model 3-high attribution
	HighPercent      float32  `protobuf:"fixed32,8,opt,name=high_percent,json=highPercent,proto3" json:"high_percent,omitempty"`               // 百分比 不超过1
	AudienceType     uint32   `protobuf:"varint,9,opt,name=audience_type,json=audienceType,proto3" json:"audience_type,omitempty"`             // audience的类型 1-event
	Frequency        uint32   `protobuf:"varint,10,opt,name=frequency,proto3" json:"frequency,omitempty"`                                      // 频率 1-daily
	Country          []string `protobuf:"bytes,11,rep,name=country,proto3" json:"country,omitempty"`                                           // 国家 "all"代表所有
	Os               string   `protobuf:"bytes,12,opt,name=os,proto3" json:"os,omitempty"`                                                     // 系统 "all"代表所有
	InstallRange     int32    `protobuf:"varint,13,opt,name=install_range,json=installRange,proto3" json:"install_range,omitempty"`            // <0负数代表前x天
	InstallRegister  int32    `protobuf:"varint,14,opt,name=install_register,json=installRegister,proto3" json:"install_register,omitempty"`   // <0负数代表前x天
	InstallActive    int32    `protobuf:"varint,15,opt,name=install_active,json=installActive,proto3" json:"install_active,omitempty"`         // <0负数代表前x天
	InstallExActive  int32    `protobuf:"varint,16,opt,name=install_ex_active,json=installExActive,proto3" json:"install_ex_active,omitempty"` // <0负数代表前x天
}

func (x *CardExtInfo) Reset() {
	*x = CardExtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardExtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardExtInfo) ProtoMessage() {}

func (x *CardExtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardExtInfo.ProtoReflect.Descriptor instead.
func (*CardExtInfo) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{2}
}

func (x *CardExtInfo) GetLive() uint32 {
	if x != nil {
		return x.Live
	}
	return 0
}

func (x *CardExtInfo) GetOptimizeGoal() uint32 {
	if x != nil {
		return x.OptimizeGoal
	}
	return 0
}

func (x *CardExtInfo) GetOptimizeRetarget() uint32 {
	if x != nil {
		return x.OptimizeRetarget
	}
	return 0
}

func (x *CardExtInfo) GetMediaType() uint32 {
	if x != nil {
		return x.MediaType
	}
	return 0
}

func (x *CardExtInfo) GetHistoryAccount() uint32 {
	if x != nil {
		return x.HistoryAccount
	}
	return 0
}

func (x *CardExtInfo) GetCreatedBy() uint32 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *CardExtInfo) GetHighType() uint32 {
	if x != nil {
		return x.HighType
	}
	return 0
}

func (x *CardExtInfo) GetHighPercent() float32 {
	if x != nil {
		return x.HighPercent
	}
	return 0
}

func (x *CardExtInfo) GetAudienceType() uint32 {
	if x != nil {
		return x.AudienceType
	}
	return 0
}

func (x *CardExtInfo) GetFrequency() uint32 {
	if x != nil {
		return x.Frequency
	}
	return 0
}

func (x *CardExtInfo) GetCountry() []string {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *CardExtInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *CardExtInfo) GetInstallRange() int32 {
	if x != nil {
		return x.InstallRange
	}
	return 0
}

func (x *CardExtInfo) GetInstallRegister() int32 {
	if x != nil {
		return x.InstallRegister
	}
	return 0
}

func (x *CardExtInfo) GetInstallActive() int32 {
	if x != nil {
		return x.InstallActive
	}
	return 0
}

func (x *CardExtInfo) GetInstallExActive() int32 {
	if x != nil {
		return x.InstallExActive
	}
	return 0
}

// 卡片详情
type CardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardType uint32        `protobuf:"varint,1,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"` // 卡片类型  1-标准 2-profile
	BaseInfo *CardBaseInfo `protobuf:"bytes,2,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`  // 卡片基础信息
	ExtInfo  *CardExtInfo  `protobuf:"bytes,3,opt,name=ext_info,json=extInfo,proto3" json:"ext_info,omitempty"`     // 卡片附加信息
}

func (x *CardInfo) Reset() {
	*x = CardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardInfo) ProtoMessage() {}

func (x *CardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardInfo.ProtoReflect.Descriptor instead.
func (*CardInfo) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{3}
}

func (x *CardInfo) GetCardType() uint32 {
	if x != nil {
		return x.CardType
	}
	return 0
}

func (x *CardInfo) GetBaseInfo() *CardBaseInfo {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *CardInfo) GetExtInfo() *CardExtInfo {
	if x != nil {
		return x.ExtInfo
	}
	return nil
}

// 卡片组
type CardGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupName    string      `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`            // 卡片分组名称
	CardInfoList []*CardInfo `protobuf:"bytes,2,rep,name=card_info_list,json=cardInfoList,proto3" json:"card_info_list,omitempty"` // 卡片详情列表
}

func (x *CardGroup) Reset() {
	*x = CardGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardGroup) ProtoMessage() {}

func (x *CardGroup) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardGroup.ProtoReflect.Descriptor instead.
func (*CardGroup) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{4}
}

func (x *CardGroup) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *CardGroup) GetCardInfoList() []*CardInfo {
	if x != nil {
		return x.CardInfoList
	}
	return nil
}

type GetCardInfoListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result        *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                      // 返回结果
	CardGroupList []*CardGroup `protobuf:"bytes,2,rep,name=card_group_list,json=cardGroupList,proto3" json:"card_group_list,omitempty"` // 卡片详情列表
}

func (x *GetCardInfoListRsp) Reset() {
	*x = GetCardInfoListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCardInfoListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardInfoListRsp) ProtoMessage() {}

func (x *GetCardInfoListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardInfoListRsp.ProtoReflect.Descriptor instead.
func (*GetCardInfoListRsp) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{5}
}

func (x *GetCardInfoListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetCardInfoListRsp) GetCardGroupList() []*CardGroup {
	if x != nil {
		return x.CardGroupList
	}
	return nil
}

// 批量获取卡片详情, POST, /api/v1/audience_launcher/bt_get_card_info
type BtGetCardInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LauncherIdList []string `protobuf:"bytes,1,rep,name=launcher_id_list,json=launcherIdList,proto3" json:"launcher_id_list,omitempty"` // 卡片id
}

func (x *BtGetCardInfoReq) Reset() {
	*x = BtGetCardInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BtGetCardInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BtGetCardInfoReq) ProtoMessage() {}

func (x *BtGetCardInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BtGetCardInfoReq.ProtoReflect.Descriptor instead.
func (*BtGetCardInfoReq) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{6}
}

func (x *BtGetCardInfoReq) GetLauncherIdList() []string {
	if x != nil {
		return x.LauncherIdList
	}
	return nil
}

type BtGetCardInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CardInfo []*CardInfo `protobuf:"bytes,1,rep,name=card_info,json=cardInfo,proto3" json:"card_info,omitempty"` // 卡片详情
}

func (x *BtGetCardInfoRsp) Reset() {
	*x = BtGetCardInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_launcher_audience_launcher_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BtGetCardInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BtGetCardInfoRsp) ProtoMessage() {}

func (x *BtGetCardInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_launcher_audience_launcher_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BtGetCardInfoRsp.ProtoReflect.Descriptor instead.
func (*BtGetCardInfoRsp) Descriptor() ([]byte, []int) {
	return file_audience_launcher_audience_launcher_proto_rawDescGZIP(), []int{7}
}

func (x *BtGetCardInfoRsp) GetCardInfo() []*CardInfo {
	if x != nil {
		return x.CardInfo
	}
	return nil
}

var File_audience_launcher_audience_launcher_proto protoreflect.FileDescriptor

var file_audience_launcher_audience_launcher_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x75, 0x6e, 0x63,
	0x68, 0x65, 0x72, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x75,
	0x6e, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x1c,
	0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x14, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x22, 0x9a, 0x01, 0x0a, 0x0c, 0x43, 0x61, 0x72, 0x64, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x75,
	0x6e, 0x63, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x61, 0x75,
	0x6e, 0x63, 0x68, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x72, 0x6f,
	0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xaa, 0x04, 0x0a, 0x0b, 0x43, 0x61, 0x72, 0x64, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x69, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6c,
	0x69, 0x76, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x5f,
	0x67, 0x6f, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69,
	0x6d, 0x69, 0x7a, 0x65, 0x47, 0x6f, 0x61, 0x6c, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69,
	0x6d, 0x69, 0x7a, 0x65, 0x5f, 0x72, 0x65, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09,
	0x68, 0x69, 0x67, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x68, 0x69, 0x67, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x69, 0x67,
	0x68, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0b, 0x68, 0x69, 0x67, 0x68, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x29,
	0x0a, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x65, 0x78, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x45, 0x78, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0xa0, 0x01, 0x0a,
	0x08, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x45,
	0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x6d, 0x0a, 0x09, 0x43, 0x61, 0x72, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c,
	0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x7f,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x44, 0x0a, 0x0f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61,
	0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x3c, 0x0a, 0x10, 0x42, 0x74, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6c,
	0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x4c, 0x0a,
	0x10, 0x42, 0x74, 0x47, 0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x40, 0x5a, 0x3e, 0x65,
	0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x65, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_audience_launcher_audience_launcher_proto_rawDescOnce sync.Once
	file_audience_launcher_audience_launcher_proto_rawDescData = file_audience_launcher_audience_launcher_proto_rawDesc
)

func file_audience_launcher_audience_launcher_proto_rawDescGZIP() []byte {
	file_audience_launcher_audience_launcher_proto_rawDescOnce.Do(func() {
		file_audience_launcher_audience_launcher_proto_rawDescData = protoimpl.X.CompressGZIP(file_audience_launcher_audience_launcher_proto_rawDescData)
	})
	return file_audience_launcher_audience_launcher_proto_rawDescData
}

var file_audience_launcher_audience_launcher_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_audience_launcher_audience_launcher_proto_goTypes = []interface{}{
	(*GetCardInfoListReq)(nil), // 0: audience_launcher.GetCardInfoListReq
	(*CardBaseInfo)(nil),       // 1: audience_launcher.CardBaseInfo
	(*CardExtInfo)(nil),        // 2: audience_launcher.CardExtInfo
	(*CardInfo)(nil),           // 3: audience_launcher.CardInfo
	(*CardGroup)(nil),          // 4: audience_launcher.CardGroup
	(*GetCardInfoListRsp)(nil), // 5: audience_launcher.GetCardInfoListRsp
	(*BtGetCardInfoReq)(nil),   // 6: audience_launcher.BtGetCardInfoReq
	(*BtGetCardInfoRsp)(nil),   // 7: audience_launcher.BtGetCardInfoRsp
	(*aix.Result)(nil),         // 8: aix.Result
}
var file_audience_launcher_audience_launcher_proto_depIdxs = []int32{
	1, // 0: audience_launcher.CardInfo.base_info:type_name -> audience_launcher.CardBaseInfo
	2, // 1: audience_launcher.CardInfo.ext_info:type_name -> audience_launcher.CardExtInfo
	3, // 2: audience_launcher.CardGroup.card_info_list:type_name -> audience_launcher.CardInfo
	8, // 3: audience_launcher.GetCardInfoListRsp.result:type_name -> aix.Result
	4, // 4: audience_launcher.GetCardInfoListRsp.card_group_list:type_name -> audience_launcher.CardGroup
	3, // 5: audience_launcher.BtGetCardInfoRsp.card_info:type_name -> audience_launcher.CardInfo
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_audience_launcher_audience_launcher_proto_init() }
func file_audience_launcher_audience_launcher_proto_init() {
	if File_audience_launcher_audience_launcher_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_audience_launcher_audience_launcher_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardInfoListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_launcher_audience_launcher_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_launcher_audience_launcher_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardExtInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_launcher_audience_launcher_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_launcher_audience_launcher_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_launcher_audience_launcher_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCardInfoListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_launcher_audience_launcher_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BtGetCardInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_launcher_audience_launcher_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BtGetCardInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_audience_launcher_audience_launcher_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_audience_launcher_audience_launcher_proto_goTypes,
		DependencyIndexes: file_audience_launcher_audience_launcher_proto_depIdxs,
		MessageInfos:      file_audience_launcher_audience_launcher_proto_msgTypes,
	}.Build()
	File_audience_launcher_audience_launcher_proto = out.File
	file_audience_launcher_audience_launcher_proto_rawDesc = nil
	file_audience_launcher_audience_launcher_proto_goTypes = nil
	file_audience_launcher_audience_launcher_proto_depIdxs = nil
}
