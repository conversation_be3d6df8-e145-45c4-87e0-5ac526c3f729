# 打包依赖阶段使用golang作为基础镜像
FROM golang:1.19.1 AS builder

# 编译docker依赖参数
ARG GOMOD_GIT_USER
ARG GOMOD_GIT_PWD

WORKDIR /data/app/

COPY . .
RUN git config --global url."https://${GOMOD_GIT_USER}:${GOMOD_GIT_PWD}@e.coding.intlgame.com".insteadOf "https://e.coding.intlgame.com"
RUN go env -w GOPRIVATE=e.coding.intlgame.com && \
    go get && \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -tags=jsoniter -o applovin_advertise

# 运行阶段指定基础镜像
# FROM alpine:latest
FROM centos:centos8

WORKDIR /data/app/

# 将上一个阶段publish文件夹下的所有文件复制进来
COPY --from=builder /data/app/applovin_advertise .

# 修复centos原始基础镜像无法使用yum工具问题
RUN sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-*
RUN sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-*

# 安装常用工具(telnet vim lrzsz) & 修复vim中文乱码
RUN yum install -y telnet vim lrzsz
RUN echo "set encoding=utf-8" >> ~/.vimrc

# 指定运行时环境变量
ENV GIN_MODE=debug

# 设置时区
ENV TZ=Asia/Shanghai

EXPOSE 8080

CMD ["./applovin_advertise"]