package service

import (
	"context"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"

	"github.com/gin-gonic/gin"
)

// CronTrigger 触发定时任务
func CronTrigger(ctx *gin.Context, req *pb.CronTriggerReq, rsp *pb.CronTriggerRsp) error {
	if req.GetCronName() == "SaveAllChannelAssets" {
		newCtx := log.NewSessionIDContext()
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			SaveAllChannelAssets(ctx)
		})
	}

	if req.GetCronName() == "SyncOnlineInfoTotal" {
		newCtx := log.NewSessionIDContext()
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			SyncOnlineInfoTotal(ctx)
		})
	}

	if req.GetCronName() == "SyncChannelAssetLabelToCkDailyRemoveDup" {
		newCtx := log.NewSessionIDContext()
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			SyncChannelAssetLabelToCkDailyRemoveDup("pubgm")
		})
	}

	if req.GetCronName() == "SyncChannelAssetLabelToCk" {
		newCtx := log.NewSessionIDContext()
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			SyncChannelAssetLabelToCk(ctx)
		})
	}

	// 触发某个游戏从某天同步渠道素材到总表
	if req.GetCronName() == "saveGameAllChannelAssetsByDate" {
		newCtx := log.NewSessionIDContext()
		nowDate := time.Now().Format(utils.NumericTimeFormatDate)
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			saveGameAllChannelAssetsByDate(ctx, req.GetGameCode(), req.GetDate(), req.GetDate() == nowDate)
		})
	}

	// 触发某个游戏自动生成标签规则
	if req.GetCronName() == "autoGenGameLableRule" {
		// 拉取所有的素材库
		depots, err := repo.GetAllDepots(ctx)
		if err != nil {
			return err
		}

		for _, depot := range depots {
			if depot.GameCode == req.GetGameCode() {
				newCtx := log.NewSessionIDContext()
				utils.GoNoTimeout(newCtx, func(ctx context.Context) {
					autoGenGameLableRule(newCtx, depot)
				})
				return nil
			}
		}
	}

	if req.CronName == "SyncRuleLabels" {
		newCtx := log.NewSessionIDContext()
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			SyncRuleLabels(ctx)
		})
	}

	if req.CronName == "CronLabelRuleTask" {
		newCtx := log.NewSessionIDContext()
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			CronLabelRuleTask(ctx)
		})
	}

	if req.CronName == "parseNameToOldLabelRules" {
		newCtx := log.NewSessionIDContext()
		utils.GoNoTimeout(newCtx, func(ctx context.Context) {
			parseNameToOldLabelRules(ctx, req.GetGameCode())
		})
	}
	return nil
}
