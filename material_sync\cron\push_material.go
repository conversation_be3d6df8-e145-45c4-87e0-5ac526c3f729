package cron

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/metrics/api"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"

	"github.com/thoas/go-funk"
)

type AdhocInferResult struct {
	Status      string `json:"game_code"`
	StatusCod   int32  `json:"status_cod"`
	Time        string `json:"time"`
	TaskId      string `json:"task_id"`
	QueueLength int32  `json:"queue_length"`
}

// AdhocInferRsp ...
type AdhocInferRsp struct {
	GameCode string `json:"game_code"`
	Result   string `json:"result"`
	Message  string `json:"message"`
	RetCode  int32  `json:"ret_code"`
}

// AssetItem ...
type AssetItem struct {
	AssetId     string `json:"asset_id"`
	GameCode    string `json:"game_code"`
	StorageType int    `json:"storage_type"` // 必填，存储类型，int类型，取值1-cos，2-arthub，3-google_driver，4-web_http
	StorageUrl  string `json:"storage_url"`  // 必填
	Md5         string `json:"md5"`          // 必填,arthub可以填写目录id
	gameCode    string // 实际的game_code,非传参
}

// AdhocInferReq ...
type AdhocInferReq struct {
	Caller    string      `json:"caller"`    // 必填，调用方,如:aix_creative_hub/aix_intellegency, 默认 unknown，后续用作统计
	TaskType  int         `json:"task_type"` // 必填，任务类型，如:1-cover封面服务,2-seg分幕服务,3-tag标签服务(加上分幕关键帧结果）
	AssetList []AssetItem `json:"asset_list"`
}

// PushSize 起始请求大小
var PushSize int = 50

const (
	// MaxQueueSize 打标服务队列最大值，超过该值每分钟只请求1个
	MaxWaitingSize          = 500
	MaxQueueSize            = 500
	MaxPushAssetNum         = 100 // 每次push素材到打标服务的最大数量
	StorageTypeCos          = 1
	StorageTypeArthub       = 2
	StorageTypeGoogleDriver = 3
	StorageTypeWebHttp      = 4
)

// PushResStruct 返回 res
type PushResStruct struct {
	RetCode string            `json:"ret_code"` // 响应码, 0-全部新增任务成功,999-部分成功，有些资产存在已有的任务, 1001-参数有误, 1002-服务内部报错
	Message string            `json:"message"`
	Data    PushResStructData `json:"data"`
}

type PushResStructData struct {
	Success []PushResStructDataSuccessItem `json:"success"` // 新增任务成功的资产id列表
	Existed []PushResStructDataExistedItem `json:"existed"` // 已经存在相应任务的资产id列表
	Failed  []PushResStructDataFailedItem  `json:"failed"`  // 新建失败的资产列表
}
type PushResStructDataSuccessItem struct {
	AssetID string `json:"asset_id"`
	TaskID  string `json:"task_id"`
}

type PushResStructDataExistedItem struct {
	AssetID    string                                 `json:"asset_id"`
	TaskID     string                                 `json:"task_id"`
	TaskStatus int                                    `json:"task_status"` // 任务状态 1-waiting,2-running,3-success,4-failed
	TaskResult PushResStructDataExistedItemTaskResult `json:"task_result"`
}

type PushResStructDataExistedItemTaskResult struct {
	CosURL           string        `json:"cos_url"`
	CoverURL         string        `json:"cover_url"`
	SceneInSeconds   []interface{} `json:"scene_in_seconds"`
	KeyFramesByScene []string      `json:"key_frames_by_scene"`
	LabelResult      []string      `json:"label_result"`
}

type PushResStructDataFailedItem struct {
	AssetID string `json:"asset_id"`
	TaskID  string `json:"task_id"`
	ErrMsg  string `json:"err_msg"`
}

// RspObj 通用 res
type RspObj struct {
	Data struct {
		Result string `json:"result"`
	} `json:"data"`
}

// QueueRes 队列长度结果
type QueueRes struct {
	RetCode string `json:"ret_code"`
	Message string `json:"message"`
	Data    struct {
		TaskType    int `json:"task_type"`
		QueueingNum int `json:"queueing_num"`
		WaitingNum  int `json:"waiting_num"`
	} `json:"data"`
}

func genReqMapKey(gameCode string, assetID string) string {
	return fmt.Sprintf("%v/%v", gameCode, assetID)
}

func getInfoFromReqKey(reqKey string) (string, string, error) {
	l := strings.Split(reqKey, "/")
	if len(l) != 2 {
		return "", "", fmt.Errorf("reqKey error, reqKey: %v", reqKey)
	}
	return l[0], l[1], nil
}

// 定时推送素材
func pushMaterial(ctx context.Context) {
	log.DebugContextf(ctx, "[info] pushMaterial start")
	if conf.GetBizConf().AdHocServerOn <= 0 {
		log.DebugContextf(ctx, "pushMaterial quit deal to switch off")
		return
	}
	gameCode2Depot, err := data.GetDepotCfg()
	if err != nil {
		return
	}
	// 先拉取打标服务队列长度
	queueLen, waitingLen, err := getQueueLen(ctx, 3)
	if err != nil {
		log.ErrorContextf(ctx, "error getQueueLen, err: %v", err)
		return
	} else {
		if queueLen > MaxQueueSize {
			log.InfoContextf(ctx, "queueLen: %v > MaxQueueSize: %v, now stop req", queueLen, MaxQueueSize)
			return
		}
		if waitingLen > MaxWaitingSize {
			log.InfoContextf(ctx, "waitingLen: %v > MaxWaitingSize: %v, now stop req", waitingLen, MaxWaitingSize)
			return
		}
	}

	// 请求未打标的素材
	reqMap := make(map[string]*AssetItem)
	// 暂定每个gamecode分配50条数据，临时调整
	for gameCode, depot := range gameCode2Depot {
		log.DebugContextf(ctx, "start LoadPushAsset gameCode:%v", gameCode)
		if !funk.InStrings(conf.GetBizConf().PushGameCodeList, gameCode) {
			continue
		}

		assetList := data.LoadPushAsset(ctx, gameCode, 50)

		storageType := StorageTypeArthub
		if depot.Type == 2 {
			storageType = StorageTypeGoogleDriver
		}

		for _, v := range assetList {
			item := AssetItem{
				AssetId:     v.AssetID,
				GameCode:    depot.ArthubCode, // 打标时使用arthub_code
				StorageType: storageType,
				StorageUrl:  v.AssetID,
				Md5:         "",
				gameCode:    gameCode,
			}
			reqMap[genReqMapKey(gameCode, v.AssetID)] = &item
		}
	}
	// 发起请求
	// url := fmt.Sprintf("http://%s/api/v1/cls-dispatcher_server/new_adhoc_infer_request", conf.GetBizConf().AdHocServer)
	url := fmt.Sprintf("http://%s/api/v1/creative_insights/new_infer", conf.GetBizConf().AdHocServer)
	req := AdhocInferReq{
		Caller:    "aix_creative_hub",
		TaskType:  3,
		AssetList: make([]AssetItem, 0),
	}
	idx := 0
	gameCodeMap := make(map[string]struct{})
	for reqKey, item := range reqMap {
		gameCode, assetID, err := getInfoFromReqKey(reqKey)
		if err != nil {
			continue
		}
		log.DebugContextf(ctx, "gameCode:%v, asset_id:%v send http req:%v", gameCode, assetID, *item)
		req.AssetList = append(req.AssetList, *item)
		gameCodeMap[gameCode] = struct{}{}
		idx++
		if idx >= MaxPushAssetNum {
			break
		}
	}
	log.DebugContextf(ctx, "pushMaterial new_infer req: %v", req.AssetList)
	if len(req.AssetList) == 0 {
		log.InfoContextf(ctx, "pushMaterial new_infer asset list is %d", 0)
		return
	}
	body, err := json.Marshal(&req)
	if err != nil {
		log.ErrorContextf(ctx, "[error] req body marshal fail, err: %v", err)
		return
	}
	res, err, cost := HTTPDo(ctx, "POST", url, body)
	if err != nil {
		log.ErrorContextf(ctx, "[error] send http req error %v,res: %v", err, res)
		batchMetricWithResult(cost, "adhoc", url, gameCodeMap, false)
		return
	}

	// 生成gamecode
	assetIDMap := generateGamecode2AssetIDMap(req.AssetList)
	log.DebugContextf(ctx, "pushMaterial db assetIDMap: %+v", assetIDMap)
	err = data.BatchSetMaterialLabelRetry(ctx, assetIDMap)
	if err != nil {
		log.ErrorContextf(ctx, "[error] SetMaterialLabelRetry error: %v, assetIDMap: %+v", err, assetIDMap)
		return
	}

	resObj := PushResStruct{}
	err = json.Unmarshal([]byte(res), &resObj)
	log.DebugContextf(ctx, "pushMaterial new_infer resp: %v", res)
	if err != nil {
		log.ErrorContextf(ctx, "[error] res unmarshal fail, err: %v", err)
		batchMetricWithResult(cost, "adhoc", url, gameCodeMap, false)
		return
	}
	err = dealInferResp(ctx, &resObj)
	if err != nil {
		log.ErrorContextf(ctx, "[error] push material fail, err: %v, res: %v", err, res)
		batchMetricWithResult(cost, "adhoc", url, gameCodeMap, false)
		return
	}
	_ = updateExistedData(ctx, &resObj, req.AssetList) // 更新响应中已经成功的数据
	batchMetricWithResult(cost, "adhoc", url, gameCodeMap, true)
}

func HTTPDo(ctx context.Context, method string, url string, body []byte) (string, error, float64) {
	start := time.Now()
	client := &http.Client{}
	reader := bytes.NewReader(body)
	req, err := http.NewRequest(method, url, reader)
	if err != nil {
		log.ErrorContextf(ctx, "[error] get http req error %v", err)
		return "", err, 0
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	cost := time.Since(start).Seconds()
	if err != nil {
		log.ErrorContextf(ctx, "[error] send http req error %v", err)
		return "", err, cost
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "[error] get http rsp error %v", err)
		return "", err, cost
	}
	return string(res), nil, cost
}

// 获取打标服务队列长度
// 返回 队列中的任务 、等待入队的任务数量、error
func getQueueLen(ctx context.Context, taskType int) (int, int, error) {
	//url := fmt.Sprintf("http://%s/api/v1/cls-dispatcher_server/query_q_len/crt_lbl_queue", conf.GetBizConf().AdHocServer)
	url := fmt.Sprintf("http://%s/api/v1/creative_insights/get_quene_number?task_type=%d", conf.GetBizConf().AdHocServer, taskType)
	labels := map[string]string{"service": "adhoc", "url": url}
	res, err, cost := HTTPDo(ctx, "GET", url, []byte{})
	if err != nil {
		log.ErrorContextf(ctx, "[error] send http req error %v,res: %v", err, string(res))
		api.MetricWithResult(cost, labels, false)
		return 0, 0, err
	}
	log.DebugContextf(ctx, "GetQueueLen res: %v", res)
	//rspObj := &RspObj{}
	//err = json.Unmarshal([]byte(res), rspObj)
	//if err != nil {
	//	log.ErrorContextf(ctx, "error json.Unmarshal rsp, err: %v", err)
	//	api.MetricWithResult(cost, labels, false)
	//	return 0, err
	//}
	queueRes := &QueueRes{}
	err = json.Unmarshal([]byte(res), queueRes)
	if err != nil {
		log.ErrorContextf(ctx, "error json.Unmarshal result, err: %v", err)
		api.MetricWithResult(cost, labels, false)
		return 0, 0, err
	}
	api.MetricWithResult(cost, labels, true)

	return queueRes.Data.QueueingNum, queueRes.Data.WaitingNum, nil
}

// 批量写入多个game_code
func batchMetricWithResult(cost float64, service, url string, gameCodeMap map[string]struct{}, success bool) {
	labels := map[string]string{"service": service, "url": url, "game": ""}
	for gameCode := range gameCodeMap {
		labels["game"] = gameCode
		api.MetricWithResult(cost, labels, success)
	}
}

func generateGamecode2AssetIDMap(assets []AssetItem) map[string][]string {
	if len(assets) == 0 {
		return nil
	}
	m := make(map[string][]string)
	for _, asset := range assets {
		assetList, ok := m[asset.gameCode]
		if !ok {
			m[asset.gameCode] = []string{asset.AssetId}
		} else {
			assetList = append(assetList, asset.AssetId)
			m[asset.gameCode] = assetList
		}
	}
	return m
}

// 响应码, 0-全部新增任务成功,999-部分成功，有些资产存在已有的任务, 1001-参数有误, 1002-服务内部报错
func dealInferResp(ctx context.Context, resp *PushResStruct) error {
	code, err := strconv.ParseInt(resp.RetCode, 10, 64)
	if err != nil {
		return err
	}
	if code == 0 {
		return nil
	}
	if code == 999 {
		log.InfoContextf(ctx, "push material part of the success, code: %v, success: %v, existed: %v",
			code, resp.Data.Success, resp.Data.Existed)
		return nil
	}
	err = fmt.Errorf("push material failed, ret_code: %v, failed: %v", code, resp.Data.Failed)
	return err
}

// updateExistedData 更新成功的数据，resp接口返回，reqAssetList 请求接口的数据
func updateExistedData(ctx context.Context, resp *PushResStruct, reqAssetList []AssetItem) (err error) {
	if resp == nil || resp.Data.Existed == nil || len(resp.Data.Existed) == 0 {
		return
	}
	for _, asset := range resp.Data.Existed {
		if asset.TaskStatus == 3 { // 任务状态 1-waiting,2-running,3-success,4-failed
			for _, reqAsset := range reqAssetList {
				if asset.AssetID == reqAsset.AssetId { // 获取gameCode
					gameCode := reqAsset.gameCode
					cover := asset.TaskResult.CoverURL
					cosUrl := asset.TaskResult.CosURL
					robotSecondLabel := strings.Join(asset.TaskResult.LabelResult, ",")
					err := data.SetMaterialLabel(ctx, gameCode, cover, cosUrl, "", robotSecondLabel, asset.AssetID)
					if err != nil {
						log.ErrorContextf(ctx, "updateExistedData.SetMaterialLabel error: %v", err)
					}
					err = data.SetAssetLabel(ctx, gameCode, asset.AssetID, robotSecondLabel)
					if err != nil {
						log.ErrorContextf(ctx, "updateExistedData.SetAssetLabel error: %v", err)
					}
				}
			}
		}
	}
	return nil
}
