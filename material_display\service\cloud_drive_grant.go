package service

import (
	"context"
	"fmt"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/clouddrive"
	pkgDropbox "e.coding.intlgame.com/ptc/aix-backend/common/pkg/dropbox"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/dropbox"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"golang.org/x/oauth2"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/spf13/cast"
)

// CloudDriveGrant 网盘自助授权
func CloudDriveGrant(ctx *gin.Context, req *pb.CloudDriveGrantReq, rsp *pb.CloudDriveGrantRsp) error {
	depot, err := buildCloudDriveDepot(ctx, req)
	if err != nil {
		return err
	}

	// 创建素材库按游戏分表的各种表
	err = createCloudDriveTables(ctx, req.GetGameCode())
	if err != nil {
		return err
	}

	// 保存网盘信息
	fields := []string{
		"depot_id",
		"public_token",
		"arthub_code",
		"type",
		"google_service_account",
		"priority",
		"focus_dirs",
		"is_full_synced",
		"last_increment_sync_date",
		"drive_status",
		"extra_info",
		"update_date",
	}
	err = data.UpsertDepotByFields(ctx, depot, fields)
	if err != nil {
		return fmt.Errorf("data.UpsertDepotByFields err: %v", err)
	}

	// 网盘授权成功后，删除自动化上传任务
	err = data.DelAutomaticSyncTaskRule(ctx, req.GetGameCode())
	if err != nil {
		log.ErrorContextf(ctx, "data.DelAutomaticSyncTaskRule err: %v", err)
	}

	// cancel掉正在上传的任务
	err = data.CancelUploadTaskByGame(ctx, req.GetGameCode())
	if err != nil {
		log.ErrorContextf(ctx, "data.CancelUploadTaskByGame err: %v", err)
	}
	return nil
}

func buildCloudDriveDepot(ctx context.Context, req *pb.CloudDriveGrantReq) (*model.ArthubDepot, error) {
	if req.GetGameCode() == "" || req.GetRootId() == "" {
		return nil, fmt.Errorf("game_code or root_id is empty")
	}

	depot := &model.ArthubDepot{
		DepotId:  req.GetRootId(),
		GameCode: req.GetGameCode(),
		Type:     int(req.GetType()),
	}

	var err error
	if req.GetType() == utils.GAME_DEPOT_TYPE_ARTHUB {
		err = buildArthubDepot(ctx, req, depot)
	} else if req.GetType() == utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER {
		err = buildGoogleDriveDepot(ctx, req, depot)
	} else if req.GetType() == utils.GAME_DEPOT_TYPE_DROPBOX {
		err = buildDropboxDepot(ctx, req, depot)
	} else {
		err = fmt.Errorf("cloud drive type %d not support", req.GetType())
	}
	if err != nil {
		return nil, err
	}

	// 网盘状态设置为初始化中
	depot.DriveStatus = constant.CloudDriveStatusInitializing
	// 查找当前最大的proirity, 赋值为 proirity+1, 让该网盘的同步优先
	curMaxPriority, err := data.GetDeportMaxPriority(ctx, depot.Type)
	if err != nil {
		return nil, fmt.Errorf("data.GetDeportMaxPriority err: %v", err)
	}
	depot.Priority = curMaxPriority + 1

	depot.UpdateDate = utils.FormatDefault(time.Now())
	return depot, err
}

func buildArthubDepot(ctx context.Context, req *pb.CloudDriveGrantReq, depot *model.ArthubDepot) error {
	depot.Type = utils.GAME_DEPOT_TYPE_ARTHUB

	auth := req.GetArthubAuth()
	if auth.GetArthubCode() == "" || auth.GetPublicToken() == "" {
		return fmt.Errorf("auth token or public token is empty")
	}
	depot.ArthubCode = auth.GetArthubCode()
	depot.PublicToken = auth.GetPublicToken()

	// 查一下目录信息
	rootID := cast.ToUint64(req.GetRootId())
	meta := []string{
		"id", "name", "type",
	}
	briefRsp, err := arthub.GetNodeBriefByID(ctx, auth.GetArthubCode(), auth.GetPublicToken(), meta, []uint64{rootID})
	if err != nil {
		return fmt.Errorf("arthub.GetNodeBriefByID err: %v", err)
	}
	for _, node := range briefRsp.Result.Items {
		if node.ID == rootID {
			if node.Type != "directory" && node.Type != "project" && node.Type != "depot" {
				return fmt.Errorf("arthub root: %v is not folder", node.Name)
			}
			if node.Status != "normal" {
				return fmt.Errorf("arthub root: %v status: %v is not normal", node.Name, node.Status)
			}

			return nil
		}
	}

	return fmt.Errorf("arthub root id: %v not found", req.GetRootId())
}

func buildGoogleDriveDepot(ctx context.Context, req *pb.CloudDriveGrantReq, depot *model.ArthubDepot) error {
	depot.Type = utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER
	depot.ExtraInfo.GoogleDrive = &model.GoogleDriveExtraInfo{
		IsOauth2Grant: true,
	}

	auth := req.GetGoogleDriveAuth()
	configToken := &model.Oauth2ConfigToken{
		Config: &model.Oauth2Config{
			ClientID:     auth.GetConfig().GetClientId(),
			ClientSecret: auth.GetConfig().GetClientSecret(),
			RedirectURL:  auth.GetConfig().GetRedirectUri(),
			Endpoint: model.Oauth2Endpoint{
				AuthURL:  auth.GetConfig().GetAuthUri(),
				TokenURL: auth.GetConfig().GetTokenUri(),
			},
		},
		Token: &oauth2.Token{
			AccessToken:  auth.GetToken().GetAccessToken(),
			RefreshToken: auth.GetToken().GetRefreshToken(),
			TokenType:    auth.GetToken().GetTokenType(),
		},
	}
	// refresh token 不能为空
	if auth.GetToken().GetRefreshToken() == "" {
		return fmt.Errorf("google drive auth refresh_token is empty")
	}
	err := configToken.Token.Expiry.UnmarshalText([]byte(auth.GetToken().GetExpiry()))
	if err != nil {
		return fmt.Errorf("token expiry: %v, err: %v", auth.GetToken().GetExpiry(), err)
	}
	depot.GoogleServiceAccount = utils.ToJson(configToken)

	// 查一下目录信息
	svc, err := clouddrive.NewGoogleDriveServiceByDepot(ctx, depot)
	if err != nil {
		return fmt.Errorf("clouddrive.NewGoogleDriveServiceByDepot err: %v", err)
	}
	file, err := svc.Files.Get(req.GetRootId()).Context(ctx).Do()
	if err != nil {
		return fmt.Errorf("google drive Files.Get err: %v", err)
	}
	if file.MimeType != "application/vnd.google-apps.folder" {
		return fmt.Errorf("google drive root: %v is not folder", file.Name)
	}
	return nil
}

func buildDropboxDepot(ctx context.Context, req *pb.CloudDriveGrantReq, depot *model.ArthubDepot) error {
	depot.Type = utils.GAME_DEPOT_TYPE_DROPBOX
	// 用全量同步标志来判断是否根据congtent hash拉取详情 (既类似增量同步)
	depot.IsFullSynced = 1

	dropboxTokenAPI := conf.GetBizConf().CloudDrive.DropboxTokenOpenAPI
	// 查询一下目录信息是否ok
	svc, err := pkgDropbox.NewService(pkgDropbox.Config{
		GameCode:  req.GetGameCode(),
		TokenHost: dropboxTokenAPI.TokenHost,
		AppID:     dropboxTokenAPI.AppID,
		SigKey:    dropboxTokenAPI.SigKey,
	})
	if err != nil {
		return fmt.Errorf("ropbox.NewService err: %v", err)
	}

	getReq := &dropbox.GetMetadataReq{
		FileID: req.GetRootId(),
	}
	meta, err := svc.GetMetadata(ctx, getReq)
	if err != nil {
		return fmt.Errorf("dropbox.GetMetadata err: %v", err)
	}
	// 不是目录
	if !dropbox.IsFolderTag(meta.Tag) {
		return fmt.Errorf("%s is not folder", meta.Name)
	}
	return nil
}

func createCloudDriveTables(ctx context.Context, gameCode string) error {
	dbDepot, err := repo.GetDepot(ctx, gameCode)
	if err != nil {
		return err
	}
	// depot不为空，已经创建过了
	if dbDepot != nil {
		return nil
	}

	baseGameCode := conf.GetBizConf().CloudDrive.CreateTablesBaseGame
	if baseGameCode == "" {
		return fmt.Errorf("create tables base game is empty")
	}
	for _, table := range constant.DepotTablesPrefix {
		newTable := fmt.Sprintf("%s%s", table, gameCode)
		baseTable := fmt.Sprintf("%s%s", table, baseGameCode)

		db := postgresql.GetDBWithContext(ctx)
		_, err := db.Exec("CREATE TABLE IF NOT EXISTS ? (LIKE ? INCLUDING ALL)",
			pg.SafeQuery(newTable), pg.SafeQuery(baseTable))
		if err != nil {
			return fmt.Errorf("create table %s err: %v", newTable, err)
		}
	}

	return nil
}
