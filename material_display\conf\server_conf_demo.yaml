# 服务配置
server:
  http_port: 8090 # http服务端口
  read_timeout: 60 # 读超时
  write_timeout: 60 # 写超时
  run_mode: "debug" # 运行模式, debug或release

# 日志配置
log: 
  level: "debug" # 日志级别
  file_name: "logs/serverlog.log" # 日志名称
  max_size: 512 # 日志文件大小, 单位MB
  max_backup: 10 # 日志文件数量
  max_age: 30 # 最长保留时间, 单位天
  line_length_limit: 3072 # 单条日志最大长度
  stdout: true # 是否需要标准输出
  json_format: false # 打印日志格式, 是否需要使用json

# 数据库相关配置
database:
  postgresql: # pg配置
    url: "postgres://postgres:123456@127.0.0.1:5432/local?sslmode=disable" # URL
 

# cos配置
cos:
  bucket_url: "https://xxxxx.cos.ap-singapore.myqcloud.com"
  secret_id: "xxxxxx"
  secret_key: "xxxxx"

# 定时任务是否禁用(全局标志)
cron_task_disable: false
# 定时任务
cron_task:
  - name: LoadAllDepotToken # 定时加载depot
    cron: "*/5 * * * *" # crontab
    start_at_once: true # 是否在服务启动时立刻执行