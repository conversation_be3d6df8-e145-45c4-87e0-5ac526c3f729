package service

import (
	"context"
	"fmt"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// SyncAssetLabelForGameCode 同步某个game code下的素材标签, 注意是素材标签而不是广告素材标签
func SyncAssetLabelForGameCode(game_code string, overviews []pgmodel.CreativeOverview) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncAssetLabelForGameCode start, game code: %s", game_code)

	start_time := time.Now()
	defer func() {
		cost := time.Since(start_time)
		log.InfoContextf(ctx, "SyncAssetLabelForGameCode end, game code: %s, cost: %v", game_code, cost)
	}()

	prefix2assets, err := getPrefixToVirtualAssets(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getPrefixToVirtualAssets failed: %s", err)
		return
	}

	log.InfoContextf(ctx, "get prefix number %d for game code: %s", len(prefix2assets), game_code)
	if len(prefix2assets) == 0 {
		return
	}

	log.InfoContextf(ctx, "get overview with names number: %d for game code: %s", len(overviews), game_code)
	if len(overviews) == 0 {
		return
	}

	labels, err := genAssetLabelFromOverviewsAndPrefixMap(ctx, overviews, prefix2assets)
	if err != nil {
		log.ErrorContextf(ctx, "genAssetLabelFromOverviewsAndPrefixMap failed: %s", err)
		return
	}

	log.InfoContextf(ctx, "get asset labels number: %d for game code: %s", len(labels), game_code)
	if len(labels) == 0 {
		return
	}

	err = upsertAssetLabels(ctx, game_code, labels)
	if err != nil {
		log.ErrorContextf(ctx, "upsertAssetLabels failed: %s", err)
	}
}

// genAssetLabelFromOverviewsAndPrefixMap 生成素材标签列表
func genAssetLabelFromOverviewsAndPrefixMap(ctx context.Context, overviews []pgmodel.CreativeOverview, prefix2assets map[string][]pgmodel.VirtualAsset) ([]pgmodel.AssetLabel, error) {
	var labels []pgmodel.AssetLabel
	author := "material_sync.sync_asset_label_by_prefix"
	for _, overview := range overviews {
		if len(overview.AssetName) < 7 {
			continue
		}

		prefix := overview.AssetName[:7]
		assets, ok := prefix2assets[prefix]
		if !ok {
			continue
		}

		for _, label := range genAssetLabelFromOverviewAndVirtualAssets(author, overview, assets) {
			if len(label.LabelName) == 0 || len(label.FirstLabel) == 0 || len(label.SecondLabel) == 0 || label.SecondLabel == "-" {
				continue
			}

			labels = append(labels, label)
		}
	}

	return labels, nil
}

// genAssetLabelFromOverviewAndVirtualAssets 生成素材标签
func genAssetLabelFromOverviewAndVirtualAssets(author string, overview pgmodel.CreativeOverview, assets []pgmodel.VirtualAsset) []pgmodel.AssetLabel {
	var labels []pgmodel.AssetLabel
	var label pgmodel.AssetLabel
	label.AssetId = overview.AssetID
	for _, asset := range assets {
		label.LabelName = asset.LabelName
		label.FirstLabel = asset.FirstLabel
		label.SecondLabel = asset.SecondLabel
		label.CreateBy = author
		label.UpdateBy = author

		labels = append(labels, label)
	}

	return labels
}

// getPrefixToVirtualAssets 获取前缀到虚拟素材列表的映射
func getPrefixToVirtualAssets(ctx context.Context, game_code string) (map[string][]pgmodel.VirtualAsset, error) {
	var assets []pgmodel.VirtualAsset
	table_name := pgmodel.GetVirtualAssetTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&assets).Table(table_name)
	pg_query.Column("asset_id", "label_name", "first_label", "second_label")
	pg_query.Where("asset_type=2") // 前缀
	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select virtual asset failed: %s", err)
	}

	prefix2assets := make(map[string][]pgmodel.VirtualAsset)
	for _, asset := range assets {
		prefix2assets[asset.AssetId] = append(prefix2assets[asset.AssetId], asset)
	}

	return prefix2assets, nil
}

// getOverviewsWithName 获取待名称的素材
func getOverviewsWithName(ctx context.Context, game_code string) ([]pgmodel.CreativeOverview, error) {
	var overviews []pgmodel.CreativeOverview
	table_name := pgmodel.GetCreativeOverviewTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&overviews).Table(table_name)
	pg_query.Column("asset_id", "asset_name")
	pg_query.Where("asset_status=1")          // 正常状态素材
	pg_query.Where("length(asset_name) >= 5") // 需要具有基本的前缀长度
	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select overviews with name failed: %s", err)
	}

	return overviews, nil
}

// upsertAssetLabels 插入或更新素材标签
func upsertAssetLabels(ctx context.Context, game_code string, labels []pgmodel.AssetLabel) error {
	time_now := time.Now().Format("2006-01-02 15:04:05")
	new_labels := make([]pgmodel.AssetLabel, 0, len(labels))
	for _, label := range labels {
		label.CreateTime = time_now
		label.UpdateTime = time_now
		new_labels = append(new_labels, label)
	}

	table_name := pgmodel.GetAssetLabelTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&new_labels).Table(table_name)
	pg_query.OnConflict("(asset_id, label_name, first_label, second_label) do nothing")
	_, err := pg_query.Insert()
	if err != nil {
		return fmt.Errorf("insert asset labels failed: %s", err)
	}

	return nil
}
