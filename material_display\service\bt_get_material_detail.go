// Package service 服务接口实现代码
package service

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// BtGetMaterialInfoDetail 批量拉取素材详细信息
func BtGetMaterialInfoDetail(ctx *gin.Context,
	req *pb.BtGetMaterialInfoDetailReq, rsp *pb.BtGetMaterialInfoDetailRsp) error {
	assetIDList := req.GetAssetIds()
	if len(assetIDList) == 0 || len(assetIDList) > 100 {
		return errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM),
			"BtGetMaterialInfoDetail len(assetIDList) == 0 || len(assetIDList) > 100")
	}
	// 拉取素材库配置信息
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	depot, err := data.GetDepot(gameCode)
	if err != nil {
		return errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB),
			"BtGetMaterialInfoDetail data.GetDepot err:%v", err)
	}

	// 去素材overview信息
	overviews, err := data.QueryCreativeOverviewByIDList(ctx, gameCode, assetIDList)
	if err != nil {
		return errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB),
			"BtGetMaterialInfoDetail data.QueryCreativeOverviewByIDList err:%v", err)
	}
	metas := arthubOverViews2MaterialMetas(ctx, overviews)
	// 填充detail和preview信息
	err = fillMeterialMetasNew(ctx, metas, gameCode, depot.ArthubCode, depot.Type)
	if err != nil {
		return errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB),
			"BtGetMaterialInfoDetail fillMeterialMetasNew err:%v", err)
	}

	rsp.Materials = metas
	return nil
}

// 填充素材返回结果
func fillMeterialMetasNew(ctx *gin.Context, metas []*pb.MaterialMeta, gameCode, arthubCode string, gameDepotType int) error {
	var assetIDs []string
	var previewAssetIDs []string // 只查素材状态正常的缩略图
	for _, meta := range metas {
		assetIDs = append(assetIDs, meta.GetAssetId())
		if meta.GetAssetStatus() == arthub.ARTHUB_ASSET_STATUS_NORMAL {
			previewAssetIDs = append(previewAssetIDs, meta.GetAssetId())
		}
	}

	if len(assetIDs) == 0 {
		return nil
	}
	assetID2Detail, err := materialDetails(ctx, assetIDs, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "fillMeterialMetasNew, mediaMaterialDetails failed, err: %s", err.Error())
		return err
	}

	var urlRsp []AssetInfo
	if gameDepotType == utils.GAME_DEPOT_TYPE_ARTHUB && len(previewAssetIDs) > 0 { // 如果是arthub，则调用arthub接口获取素材的url，否则取原来的url
		urlRsp, _, err = getAssetPreviewUrl(ctx, gameCode, arthubCode, previewAssetIDs)
		if err != nil {
			log.ErrorContextf(ctx, "fillMeterialMetasNew, getAssetPreviewUrl failed, err: %s", err.Error())
			return err
		}
	}

	for _, meta := range metas {
		detail := assetID2Detail[meta.GetAssetId()]
		if detail == nil {
			continue
		}
		meta.Duration = detail.Duration
		meta.Formate = detail.Format

		for _, asset := range urlRsp {
			if cast.ToString(asset.ID) == meta.AssetId {
				meta.PreviewUrl = asset.PreviewUrl
				break
			}
		}

		meta.MaterialExt = detail2proto(ctx, detail)
	}
	return nil
}
