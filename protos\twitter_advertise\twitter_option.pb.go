// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0-devel
// 	protoc        v3.21.1
// source: twitter_advertise/twitter_option.proto

package twitter_advertise

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FundingInstrument struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                   // funding sources id
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"` // 名称
}

func (x *FundingInstrument) Reset() {
	*x = FundingInstrument{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundingInstrument) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundingInstrument) ProtoMessage() {}

func (x *FundingInstrument) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundingInstrument.ProtoReflect.Descriptor instead.
func (*FundingInstrument) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{0}
}

func (x *FundingInstrument) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FundingInstrument) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type AppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppStoreIdentifier string `protobuf:"bytes,1,opt,name=app_store_identifier,json=appStoreIdentifier,proto3" json:"app_store_identifier,omitempty"` // app id
	OsType             string `protobuf:"bytes,2,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`                                       // app 平台
}

func (x *AppInfo) Reset() {
	*x = AppInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppInfo) ProtoMessage() {}

func (x *AppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppInfo.ProtoReflect.Descriptor instead.
func (*AppInfo) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{1}
}

func (x *AppInfo) GetAppStoreIdentifier() string {
	if x != nil {
		return x.AppStoreIdentifier
	}
	return ""
}

func (x *AppInfo) GetOsType() string {
	if x != nil {
		return x.OsType
	}
	return ""
}

type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                           // 地区名称
	CountryCode    string `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`          // 地区所在国家
	LoactionType   string `protobuf:"bytes,3,opt,name=loaction_type,json=loactionType,proto3" json:"loaction_type,omitempty"`       // 地区分类 city country
	TargetingValue string `protobuf:"bytes,4,opt,name=targeting_value,json=targetingValue,proto3" json:"targeting_value,omitempty"` // 值，在设置时，填入该值
	TargetingType  string `protobuf:"bytes,5,opt,name=targeting_type,json=targetingType,proto3" json:"targeting_type,omitempty"`    // 类型
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{2}
}

func (x *Location) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Location) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *Location) GetLoactionType() string {
	if x != nil {
		return x.LoactionType
	}
	return ""
}

func (x *Location) GetTargetingValue() string {
	if x != nil {
		return x.TargetingValue
	}
	return ""
}

func (x *Location) GetTargetingType() string {
	if x != nil {
		return x.TargetingType
	}
	return ""
}

type Language struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                           // 名称
	TargetingValue string `protobuf:"bytes,2,opt,name=targeting_value,json=targetingValue,proto3" json:"targeting_value,omitempty"` // 值，在设置时，填入该值
	TargetingType  string `protobuf:"bytes,3,opt,name=targeting_type,json=targetingType,proto3" json:"targeting_type,omitempty"`    // 类型
}

func (x *Language) Reset() {
	*x = Language{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Language) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Language) ProtoMessage() {}

func (x *Language) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Language.ProtoReflect.Descriptor instead.
func (*Language) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{3}
}

func (x *Language) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Language) GetTargetingValue() string {
	if x != nil {
		return x.TargetingValue
	}
	return ""
}

func (x *Language) GetTargetingType() string {
	if x != nil {
		return x.TargetingType
	}
	return ""
}

type OsVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                           // 名称
	TargetingValue string `protobuf:"bytes,2,opt,name=targeting_value,json=targetingValue,proto3" json:"targeting_value,omitempty"` // 值，在设置时，填入该值
	TargetingType  string `protobuf:"bytes,3,opt,name=targeting_type,json=targetingType,proto3" json:"targeting_type,omitempty"`    // 类型
	Number         string `protobuf:"bytes,4,opt,name=number,proto3" json:"number,omitempty"`
	OsType         string `protobuf:"bytes,5,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
}

func (x *OsVersion) Reset() {
	*x = OsVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OsVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OsVersion) ProtoMessage() {}

func (x *OsVersion) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OsVersion.ProtoReflect.Descriptor instead.
func (*OsVersion) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{4}
}

func (x *OsVersion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OsVersion) GetTargetingValue() string {
	if x != nil {
		return x.TargetingValue
	}
	return ""
}

func (x *OsVersion) GetTargetingType() string {
	if x != nil {
		return x.TargetingType
	}
	return ""
}

func (x *OsVersion) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *OsVersion) GetOsType() string {
	if x != nil {
		return x.OsType
	}
	return ""
}

type AudienceMetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId          string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                            // app id
	OsType         string `protobuf:"bytes,2,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`                         // 系统类型，eg:ANDROID, IOS
	EventType      string `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`                // 事件类型，
	LookbackWindow string `protobuf:"bytes,4,opt,name=lookback_window,json=lookbackWindow,proto3" json:"lookback_window,omitempty"` // 回顾周期，eg: "1", "7", "14", "30", 空表示all
}

func (x *AudienceMetaData) Reset() {
	*x = AudienceMetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceMetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceMetaData) ProtoMessage() {}

func (x *AudienceMetaData) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceMetaData.ProtoReflect.Descriptor instead.
func (*AudienceMetaData) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{5}
}

func (x *AudienceMetaData) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *AudienceMetaData) GetOsType() string {
	if x != nil {
		return x.OsType
	}
	return ""
}

func (x *AudienceMetaData) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *AudienceMetaData) GetLookbackWindow() string {
	if x != nil {
		return x.LookbackWindow
	}
	return ""
}

type CustomAudience struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                          // audience id，在设置时，填入该值
	Name         string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                      // 名称
	Targetable   bool              `protobuf:"varint,3,opt,name=targetable,proto3" json:"targetable,omitempty"`                         // 状态
	Metadata     *AudienceMetaData `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`                              //
	AudienceType string            `protobuf:"bytes,5,opt,name=audience_type,json=audienceType,proto3" json:"audience_type,omitempty"`  // 类型
	AudienceSize int64             `protobuf:"varint,6,opt,name=audience_size,json=audienceSize,proto3" json:"audience_size,omitempty"` // 观众数量
}

func (x *CustomAudience) Reset() {
	*x = CustomAudience{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomAudience) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomAudience) ProtoMessage() {}

func (x *CustomAudience) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomAudience.ProtoReflect.Descriptor instead.
func (*CustomAudience) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{6}
}

func (x *CustomAudience) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CustomAudience) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomAudience) GetTargetable() bool {
	if x != nil {
		return x.Targetable
	}
	return false
}

func (x *CustomAudience) GetMetadata() *AudienceMetaData {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CustomAudience) GetAudienceType() string {
	if x != nil {
		return x.AudienceType
	}
	return ""
}

func (x *CustomAudience) GetAudienceSize() int64 {
	if x != nil {
		return x.AudienceSize
	}
	return 0
}

type Interest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                           // 名称
	TargetingValue string `protobuf:"bytes,2,opt,name=targeting_value,json=targetingValue,proto3" json:"targeting_value,omitempty"` // 值，在设置时，填入该值
	TargetingType  string `protobuf:"bytes,3,opt,name=targeting_type,json=targetingType,proto3" json:"targeting_type,omitempty"`    // 类型
}

func (x *Interest) Reset() {
	*x = Interest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Interest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Interest) ProtoMessage() {}

func (x *Interest) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Interest.ProtoReflect.Descriptor instead.
func (*Interest) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{7}
}

func (x *Interest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Interest) GetTargetingValue() string {
	if x != nil {
		return x.TargetingValue
	}
	return ""
}

func (x *Interest) GetTargetingType() string {
	if x != nil {
		return x.TargetingType
	}
	return ""
}

type WebEventTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 名称
	Id           string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`     // id 值
	Type         string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"` // 类型 eg：CUSTOM
	WebsiteTagId string `protobuf:"bytes,4,opt,name=website_tag_id,json=websiteTagId,proto3" json:"website_tag_id,omitempty"`
	Status       string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *WebEventTag) Reset() {
	*x = WebEventTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_option_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebEventTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebEventTag) ProtoMessage() {}

func (x *WebEventTag) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_option_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebEventTag.ProtoReflect.Descriptor instead.
func (*WebEventTag) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_option_proto_rawDescGZIP(), []int{8}
}

func (x *WebEventTag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WebEventTag) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WebEventTag) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WebEventTag) GetWebsiteTagId() string {
	if x != nil {
		return x.WebsiteTagId
	}
	return ""
}

func (x *WebEventTag) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_twitter_advertise_twitter_option_proto protoreflect.FileDescriptor

var file_twitter_advertise_twitter_option_proto_rawDesc = []byte{
	0x0a, 0x26, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x2f, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x22, 0x45, 0x0a, 0x11, 0x46,
	0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x54, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a,
	0x14, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x70, 0x70,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x08, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x6c, 0x6f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x6e, 0x0a, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xa0, 0x01, 0x0a, 0x09, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6f,
	0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x10, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x6f, 0x6f, 0x6b,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6c, 0x6f, 0x6f, 0x6b, 0x62, 0x61, 0x63, 0x6b, 0x57, 0x69, 0x6e, 0x64, 0x6f,
	0x77, 0x22, 0xdf, 0x01, 0x0a, 0x0e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x77, 0x69,
	0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0x6e, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x0b, 0x57, 0x65, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x77,
	0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x54, 0x61, 0x67, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_twitter_advertise_twitter_option_proto_rawDescOnce sync.Once
	file_twitter_advertise_twitter_option_proto_rawDescData = file_twitter_advertise_twitter_option_proto_rawDesc
)

func file_twitter_advertise_twitter_option_proto_rawDescGZIP() []byte {
	file_twitter_advertise_twitter_option_proto_rawDescOnce.Do(func() {
		file_twitter_advertise_twitter_option_proto_rawDescData = protoimpl.X.CompressGZIP(file_twitter_advertise_twitter_option_proto_rawDescData)
	})
	return file_twitter_advertise_twitter_option_proto_rawDescData
}

var file_twitter_advertise_twitter_option_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_twitter_advertise_twitter_option_proto_goTypes = []interface{}{
	(*FundingInstrument)(nil), // 0: twitter_advertise.FundingInstrument
	(*AppInfo)(nil),           // 1: twitter_advertise.AppInfo
	(*Location)(nil),          // 2: twitter_advertise.Location
	(*Language)(nil),          // 3: twitter_advertise.Language
	(*OsVersion)(nil),         // 4: twitter_advertise.OsVersion
	(*AudienceMetaData)(nil),  // 5: twitter_advertise.AudienceMetaData
	(*CustomAudience)(nil),    // 6: twitter_advertise.CustomAudience
	(*Interest)(nil),          // 7: twitter_advertise.Interest
	(*WebEventTag)(nil),       // 8: twitter_advertise.WebEventTag
}
var file_twitter_advertise_twitter_option_proto_depIdxs = []int32{
	5, // 0: twitter_advertise.CustomAudience.metadata:type_name -> twitter_advertise.AudienceMetaData
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_twitter_advertise_twitter_option_proto_init() }
func file_twitter_advertise_twitter_option_proto_init() {
	if File_twitter_advertise_twitter_option_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_twitter_advertise_twitter_option_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundingInstrument); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Language); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OsVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceMetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomAudience); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Interest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_option_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebEventTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_twitter_advertise_twitter_option_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_twitter_advertise_twitter_option_proto_goTypes,
		DependencyIndexes: file_twitter_advertise_twitter_option_proto_depIdxs,
		MessageInfos:      file_twitter_advertise_twitter_option_proto_msgTypes,
	}.Build()
	File_twitter_advertise_twitter_option_proto = out.File
	file_twitter_advertise_twitter_option_proto_rawDesc = nil
	file_twitter_advertise_twitter_option_proto_goTypes = nil
	file_twitter_advertise_twitter_option_proto_depIdxs = nil
}
