package service

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
)

// SyncOnlineInfoTotal ...
func SyncOnlineInfoTotal(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/SyncOnlineInfoTotal")

	// 这里加锁，避免同时去处理
	key := "cron/SyncOnlineInfoTotal"
	locked, _ := redis.GetMaterialSyncLock(ctx, key, 2*time.Hour)
	if !locked {
		log.WarningContextf(ctx, "SyncOnlineInfoTotal GetLock false")
		return
	}
	// 释放锁
	defer redis.DelMaterialSyncLock(ctx, key)

	start := time.Now()
	SyncOnlineInfoTotalLoop(ctx)
	log.InfoContextf(ctx, "SyncOnlineInfoTotal end, cost: %v", time.Since(start))
}

// SyncOnlineInfoTotalLoop ...
func SyncOnlineInfoTotalLoop(ctx context.Context) {
	log.DebugContextf(ctx, "SyncOnlineInfoTotalLoop start")

	st := time.Now()
	game_codes, err := getGameCodes(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getGameCodes failed: %s", err)
		return
	}

	for _, game_code := range game_codes {
		// 这里渠道素材标签同步，使用旧标签
		SyncOnlineInfoTotalLoopForGameCode(game_code)

		// 20240425 这里标签不在绑定到具体的素材上，不应用下面逻辑了
		// 新标签体系规则应用到渠道素材，会覆盖上面的标签，以新的标签规则的标签为准
		// newCtx := log.NewSessionIDContext()
		// ApplyGameLabelRulesToChannel(newCtx, game_code)
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "SyncOnlineInfoTotalLoop end, cost: %v", cost)
}

// SyncOnlineInfoTotalLoopForGameCode ...
func SyncOnlineInfoTotalLoopForGameCode(game_code string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncOnlineInfoTotalLoopForGameCode start, game code: %s", game_code)

	st := time.Now()

	key2asset_id, err := GetChannelAssetKeyToAssetID(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getChannelAssetKeyToAssetID failed: %s", err)
		return
	}

	syncCreativeRecommendOnline(game_code, key2asset_id)      // 同步素材推荐需要用到的上线数据
	syncCreativeRecommendImpression(game_code, key2asset_id)  // 同步素材推荐的曝光集, 即impression > 0的素材
	SyncOverviewOnlineStatusGameCode(game_code, key2asset_id) // 同步overview表中的素材上线状态

	// 20240117 去掉这里的渠道素材标签同步， 改为标签规则应用
	// SyncChannelAssetLabelForGameCode(game_code, key2asset_id) // 同步渠道素材的标签信息
	// SyncVirtualChannelAssetLabelsForGameCode(game_code)       // 同步某个游戏的虚拟素材信息, 主要用于生成素材标签
	// DeleteRedundantLabelForGameCode(game_code) // 删除冗余标签信息

	duration := time.Since(st)
	log.DebugContextf(ctx, "SyncOnlineInfoTotalLoopForGameCode end, game code: %s, cost: %s", game_code, duration)
}

// GetChannelAssetKeyToAssetID 获取渠道素材key到asset_id映射
func GetChannelAssetKeyToAssetID(ctx context.Context, game_code string) (map[string]string, error) {
	var records []pgmodel.CreativeRecommendAssetMap
	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(pgmodel.GetCreativeRecommendAssetMapTableName(game_code))
	pg_query.Column("aix_asset_id", "channel_type", "channel_account_id", "channel_asset_id")
	pg_query.Where("aix_asset_id != '' and aix_asset_id is not null")
	pg_query.Where("channel_account_id != '' and channel_account_id is not null")
	pg_query.Where("channel_asset_id != '' and channel_asset_id is not null")

	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select asset maps failed: %s", err)
	}

	key2asset_id := make(map[string]string)
	for _, record := range records {
		key := genChannelAssetKey(record.ChannelType, record.ChannelAccountID, record.ChannelAssetId)
		key2asset_id[key] = record.AixAssetId
	}

	return key2asset_id, nil
}

// genChannelAssetKey 生成渠道端素材唯一key
func genChannelAssetKey(channel_type int32, channel_account_id, channel_asset_id string) string {
	return fmt.Sprintf("%d:%s:%s", channel_type, channel_account_id, channel_asset_id)
}

// getGoogleAdResourceName ...
func getGoogleAdResourceName(account_id, asset_id string) string {
	return fmt.Sprintf("customers/%s/assets/%s", account_id, asset_id)
}

// latestAdAsset 最近在线广告数据结构体
type latestAdAsset struct {
	AssetName    string // 素材名称
	AccountId    string // 账号ID, 在facebook中, asset_id对于图片来说就是image_hash，对于视频来说就是video_id; 对于google, account_id是从resource_name中提取的一个字段
	AssetId      string // 素材ID
	MainCountry  string // 主要国家
	Network      string // 渠道, google或者facebook
	CountryList  string // 国家列表
	AdgroupId    string // adgroup_id
	AdgroupName  string // Adgroup_name
	ChannelType  int32  // 渠道类型, 1-google; 2-facebook
	CampaignName string // campaign名称
}

// getValidAssetsForDigging 获取用于数据挖掘的素材列表
func getValidAssetsForDigging(ctx context.Context, game_code string) ([]pgmodel.CreativeOverview, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.CreativeOverview{})
	table_name := pgmodel.GetCreativeOverviewTableName(game_code)
	pg_query.Table(table_name)
	pg_query.Column("asset_id")
	pg_query.Column("asset_name")
	pg_query.Column("full_path_name")
	pg_query.Where("length(asset_name)>0")
	pg_query.Where("asset_status=1")

	var overviews []pgmodel.CreativeOverview
	err := pg_query.Select(&overviews)
	if err != nil {
		return nil, fmt.Errorf("select creative overviews failed: %s", err)
	}
	log.DebugContextf(ctx, "get overviews number: %d", len(overviews))

	return overviews, nil
}

// getUploadForDigging 获取用于数据挖掘的上传素材列表
//
//lint:ignore U1000 Ignore unused function temporarily for debugging
func getUploadForDigging(ctx context.Context, game_code string) ([]pgmodel.CreativeMediaUpload, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.CreativeMediaUpload{})
	var uploads []pgmodel.CreativeMediaUpload
	err := pg_query.Select(&uploads)
	if err != nil {
		return nil, fmt.Errorf("select creative uploads failed: %s", err)
	}

	return uploads, nil
}

// getPureName ...
func getPureName(input_name string) string {
	extension := filepath.Ext(input_name)
	name := input_name[0 : len(input_name)-len(extension)]
	return name
}

// getKebabName 通过短线连接的名称
func getKebabName(input_name string) string {
	return strings.ReplaceAll(input_name, " ", "-")
}
