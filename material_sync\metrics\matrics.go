package metrics

import "e.coding.intlgame.com/ptc/aix-backend/common/metrics/ginmetrics"

const (
	MetricCreativeTotalVideoImage = "creativeTotalVideoImage"
	MetricCreativeMissCover       = "creativeMissCover"
	MetricCreativeMissMeta        = "creativeMissMeta"
	AssetMap                      = "assetMap"
)

func Setup() {
	monitor := ginmetrics.GetMonitor()

	monitor.AddMetric(&ginmetrics.Metric{
		Type:        ginmetrics.Gauge,
		Name:        "creativeTotalVideoImage",
		Description: "视频图片类型素材数量",
		Labels:      []string{"game", "format"},
	})

	monitor.AddMetric(&ginmetrics.Metric{
		Type:        ginmetrics.Gauge,
		Name:        "creativeMissCover",
		Description: "缺少封面的视频素材数量",
		Labels:      []string{"game"},
	})

	monitor.AddMetric(&ginmetrics.Metric{
		Type:        ginmetrics.Gauge,
		Name:        "creativeMissMeta",
		Description: "缺少meta的视频图片素材数量",
		Labels:      []string{"game", "format"},
	})

	monitor.AddMetric(&ginmetrics.Metric{
		Type:        ginmetrics.Gauge,
		Name:        AssetMap,
		Description: "素材关联情况统计",
		// game_code即Aix Game Code
		// channel表示广告渠道: Google, Facebook
		// count_type表示统计类型:
		//   Total: 全部广告素材;
		//   ResourceNameMapped: 通过resource_name映射到的广告素材;
		//   NameMapped: 通过名称映射到的广告素材;
		//   ContentMapped: 通过素材内容映射到的广告素材;
		//   Unmapped: 未匹配成功的广告素材;
		Labels: []string{"game_code", "channel", "count_type"},
	})
}
