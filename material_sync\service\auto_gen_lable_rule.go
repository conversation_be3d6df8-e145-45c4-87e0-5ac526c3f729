package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	channelassetRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/channel_asset"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"github.com/thoas/go-funk"
)

// AutoGenLableRule 自动生成标签规则
func AutoGenLableRule(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/AutoGenLableRule")
	log.DebugContextf(ctx, "AutoGenLableRule start")

	key := "cron/AutoGenLableRule"
	locked, _ := redis.GetMaterialSyncLock(ctx, key, 2*time.Hour)
	if !locked {
		log.WarningContextf(ctx, "AutoGenLableRule GetLock false")
		return
	}
	// 释放锁
	defer redis.DelMaterialSyncLock(ctx, key)

	start := time.Now()
	// 拉取所有的素材库
	depots, err := repo.GetAllDepots(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "AutoGenLableRule GetAllDepots err:%v", err)
		return
	}

	for _, depot := range depots {
		newCtx := log.NewSessionIDContext()
		autoGenGameLableRule(newCtx, depot)
	}

	log.DebugContextf(ctx, "AutoGenLableRule end, cost:%v", time.Since(start))
}

type ruleGen struct {
	reg      *regexp.Regexp
	ruleType int
}

type autoGenRuleConfig struct {
	ruleType       int
	serialSplitReg *regexp.Regexp
	rules          []*ruleGen
}

// 自动生成某个游戏的标签规则
func autoGenGameLableRule(ctx context.Context, depot *model.ArthubDepot) error {
	log.DebugContextf(ctx, "autoGenGameLableRule begin gameCode:%v, SerialMode:%v, SerialSplitReg:%v,  LableRuleGen:%+v",
		depot.GameCode, depot.SerialMode, depot.SerialSplitReg, depot.LableRuleGen)

	var err error
	// 自生成规则列表
	autoConfig := &autoGenRuleConfig{}
	autoConfig.ruleType, err = repo.GetDepotLabelRuleType(ctx, depot)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRule repo.GetDepotLabelRuleType err:%v", err)
		return err
	}
	autoConfig.serialSplitReg, err = repo.GetDepotSerialSplitReg(ctx, depot)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRule repo.GetDepotSerialSplitReg err:%v", err)
		return err
	}

	if autoConfig.ruleType == constant.LabelRulePrefix {
		for _, r := range depot.LableRuleGen {
			log.DebugContextf(ctx, "autoGenGameLableRule r:%+v", r)
			if r.Type == constant.LabelRulePrefix {
				reg, err := regexp.Compile(r.Regex)
				if err != nil {
					log.ErrorContextf(ctx, "autoGenGameLableRule regexp.Compile Regex:%v, err:%v", r.Regex, err)
					return err
				}

				t := &ruleGen{
					reg:      reg,
					ruleType: r.Type,
				}
				autoConfig.rules = append(autoConfig.rules, t)
			}
		}
	}
	if autoConfig.ruleType != constant.LabelRuleUnicode &&
		len(autoConfig.rules) == 0 {
		return nil
	}

	// 拉取游戏已有的标签规则
	rules, err := repo.GetAllLabelRulesByGameCode(ctx, depot.GameCode)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRule repo.GetAllLabelRulesByGameCode err:%v", err)
		return err
	}
	// 纪录已有的标签规则
	existRules := make(map[string]bool)
	for _, r := range rules {
		key := fmt.Sprintf("%v,%v", r.Rule, r.Type)
		existRules[key] = true
	}
	// 从aix library自动生成标签规则
	err = autoGenGameLableRuleFromAix(ctx, depot.GameCode, autoConfig, existRules)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRuleFromAix err:%v", err)
		return err
	}

	// 重新拉取一下拉取游戏已有的标签规则，因为上面从aix library会生成新的标签规则
	newRules, err := repo.GetAllLabelRulesByGameCode(ctx, depot.GameCode)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRule repo.GetAllLabelRulesByGameCode err:%v", err)
		return err
	}
	// 纪录最新的已有的标签规则
	newExistRules := make(map[string]bool)
	for _, r := range newRules {
		key := fmt.Sprintf("%v,%v", r.Rule, r.Type)
		newExistRules[key] = true
	}
	// 从渠道素材库自动生成标签规则
	err = autoGenGameLableRuleFromChannel(ctx, depot.GameCode, autoConfig, newExistRules)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRuleFromChannel err:%v", err)
		return err
	}

	return nil
}

// 从aix library自动生成标签规则
func autoGenGameLableRuleFromAix(ctx context.Context, gameCode string, autoConfig *autoGenRuleConfig, existRules map[string]bool) error {
	log.DebugContextf(ctx, "autoGenGameLableRuleFromAix begin gameCode:%v", gameCode)

	// 读取已有的一二级标签体系
	creativeLabels, err := data.GetGameCreativeLabels(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRuleFromAix data.GetGameCreativeLabels err:%v", err)
		return err
	}
	// 保存标签体系，后面要特殊处理
	creativeLabelMap := make(map[string]*creativeLabelStatus)
	for _, c := range creativeLabels {
		t := &creativeLabelStatus{
			label:   c,
			changed: false,
		}
		creativeLabelMap[c.Name] = t
	}

	// 获取游戏解析素材名称到标签模板， 如果没有则返回nil
	template := conf.GetGameParseNameToLabelTemplate(gameCode)

	// 纪录生成的新标签规则
	newRules := make(map[string]*model.TbAssetLabelRule)
	// 纪录新标签规则的素材
	ruleAssets := make(map[string][]string)
	// 遍历aix library素材
	limit := 1000
	for offset := 0; offset < 10000000; offset = offset + limit {
		overviews, err := data.QueryPageCreativeOverviewColumns(ctx, gameCode, offset, limit, []string{"asset_id", "asset_name"})
		if err != nil {
			log.ErrorContextf(ctx, "autoGenGameLableRuleFromAix data.QueryPageCreativeOverviewColumns err:%v", err)
			return err
		}

		for _, overview := range overviews {
			name, ruleType := checkAssetMatchAutoRule(overview.AssetName, autoConfig)
			if name == "" {
				continue
			}

			key := fmt.Sprintf("%v,%v", name, ruleType)
			// 已存在的标签规则， 不处理
			if _, ok := existRules[key]; ok {
				continue
			}

			if _, ok := newRules[key]; !ok {
				t := &model.TbAssetLabelRule{
					GameCode:   gameCode,
					Rule:       name,
					Type:       int32(ruleType),
					CreateTime: time.Now(),
					CreateUser: "material_sync_auto",
					UpdateTime: time.Now(),
					UpdateUser: "material_sync_auto",
				}
				log.DebugContextf(ctx, "autoGenGameLableRuleFromAix new TbAssetLabelRule:%+v", t)
				newRules[key] = t
			}

			if template != nil {
				tmp := newRules[key]
				nameToLabels := parseAssetNameLabels(overview.AssetName, template)
				log.DebugContextf(ctx, "autoGenGameLableRuleFromAix gameCode:%v, rule:%v, assetName:%v parseAssetNameLabels:%+v",
					gameCode, tmp.Rule, overview.AssetName, nameToLabels)
				for _, label := range nameToLabels {
					tmp.Labels = append(tmp.Labels, label)
					// 判断是否需要更新标签体系
					if t, ok := creativeLabelMap[label.FirstLabel]; ok {
						if !funk.ContainsString(t.label.Options, label.SecondLabel) {
							t.label.UpdatedAt = time.Now()
							t.label.Updater = "ParseName"
							t.label.Options = append(t.label.Options, label.SecondLabel)
							t.changed = true
						}
					} else {
						t = &creativeLabelStatus{
							label: &model.CreativeLabel{
								GameCode:  gameCode,
								Name:      label.FirstLabel,
								CreatedAt: time.Now(),
								Creator:   "ParseName",
								UpdatedAt: time.Now(),
								Updater:   "ParseName",
							},
							changed: true,
						}
						t.label.Options = append(t.label.Options, label.SecondLabel)
						creativeLabelMap[label.FirstLabel] = t
					}
				}
			}

			ruleAssets[key] = append(ruleAssets[key], overview.AssetID)
		}

		// 没数据了
		if len(overviews) < limit {
			break
		}
	}

	// 纪录需要保存的新标签规则
	var saveRules []*model.TbAssetLabelRule
	for _, r := range newRules {
		saveRules = append(saveRules, r)

		// 去查一下新标签规则对应素材是否有标签，有的话，全部组合起来
		key := fmt.Sprintf("%v,%v", r.Rule, r.Type)
		assetIDs := ruleAssets[key]
		log.DebugContextf(ctx, "autoGenGameLableRuleFromAix fill labels, rule:%+v, assetIDs:%+v", r, assetIDs)
		if len(assetIDs) > 0 {
			// id去重
			assetIDs = funk.UniqString(assetIDs)
			labels, err := data.GetBatchAssetsLables(ctx, gameCode, assetIDs)
			if err != nil {
				log.ErrorContextf(ctx, "autoGenGameLableRuleFromAix data.GetBatchAssetsLables err:%v", err)
				return err
			}

			for _, label := range labels {
				t := model.AssetLabel{
					LabelName:   "", // 固定为空
					FirstLabel:  label.FirstLabel,
					SecondLabel: label.SecondLabel,
				}
				r.Labels = append(r.Labels, t)
			}
		}

		// 标签去重
		removeRuleDuplicateLables(r)
	}

	err = repo.UpsertBatchLabelRule(ctx, saveRules)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRuleFromAix repo.UpsertBatchLabelRule err:%v", err)
		return err
	}

	// 有变化更新标签体系
	var saves []*model.CreativeLabel
	for _, t := range creativeLabelMap {
		if t.changed {
			saves = append(saves, t.label)
		}
	}
	if len(saves) > 0 {
		err = data.UpsertCreativeLabels(ctx, saves)
		if err != nil {
			log.ErrorContextf(ctx, "data.UpsertCreativeLabels err:%v", err)
		}
	}
	return nil
}

// 从channel素材自动生成标签规则
func autoGenGameLableRuleFromChannel(ctx context.Context, gameCode string, autoConfig *autoGenRuleConfig, existRules map[string]bool) error {
	log.DebugContextf(ctx, "autoGenGameLableRuleFromChannel begin gameCode:%v", gameCode)

	// 读取已有的一二级标签体系
	creativeLabels, err := data.GetGameCreativeLabels(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRuleFromAix data.GetGameCreativeLabels err:%v", err)
		return err
	}
	// 保存标签体系，后面要特殊处理
	creativeLabelMap := make(map[string]*creativeLabelStatus)
	for _, c := range creativeLabels {
		t := &creativeLabelStatus{
			label:   c,
			changed: false,
		}
		creativeLabelMap[c.Name] = t
	}

	// 获取游戏解析素材名称到标签模板， 如果没有则返回nil
	template := conf.GetGameParseNameToLabelTemplate(gameCode)

	// 纪录生成的新标签规则
	newRules := make(map[string]*model.TbAssetLabelRule)
	// 纪录新标签规则的素材
	ruleAssets := make(map[string][]string)
	// 从渠道素材总表中拉取素材
	startID := int64(0)
	limit := 1000
	// 防止死循环
	for i := 0; i < 10000; i++ {
		channelAssets, err := channelassetRepo.GetPageChanneAssetsFromAll(ctx, gameCode, startID, limit)
		if err != nil {
			log.ErrorContextf(ctx, "autoGenGameLableRuleFromChannel GetPageChanneAssetsFromAll error: %v", err)
			return err
		}

		for _, asset := range channelAssets {
			// 往前推进startID
			if startID < asset.ID {
				startID = asset.ID
			}

			name, ruleType := checkAssetMatchAutoRule(asset.AssetName, autoConfig)
			if name == "" {
				continue
			}
			key := fmt.Sprintf("%v,%v", name, ruleType)
			// 已存在的标签规则， 不处理
			if _, ok := existRules[key]; ok {
				continue
			}

			if _, ok := newRules[key]; !ok {
				t := &model.TbAssetLabelRule{
					GameCode:   gameCode,
					Rule:       name,
					Type:       int32(ruleType),
					CreateTime: time.Now(),
					CreateUser: "material_sync_auto",
					UpdateTime: time.Now(),
					UpdateUser: "material_sync_auto",
				}
				log.DebugContextf(ctx, "autoGenGameLableRuleFromChannel new TbAssetLabelRule:%+v", t)
				newRules[key] = t
			}

			if template != nil {
				tmp := newRules[key]
				nameToLabels := parseAssetNameLabels(asset.AssetName, template)
				log.DebugContextf(ctx, "autoGenGameLableRuleFromChannel gameCode:%v, rule:%v, assetName:%v parseAssetNameLabels:%+v",
					gameCode, tmp.Rule, asset.AssetName, nameToLabels)
				for _, label := range nameToLabels {
					tmp.Labels = append(tmp.Labels, label)
					// 判断是否需要更新标签体系
					if t, ok := creativeLabelMap[label.FirstLabel]; ok {
						if !funk.ContainsString(t.label.Options, label.SecondLabel) {
							t.label.UpdatedAt = time.Now()
							t.label.Updater = "ParseName"
							t.label.Options = append(t.label.Options, label.SecondLabel)
							t.changed = true
						}
					} else {
						t = &creativeLabelStatus{
							label: &model.CreativeLabel{
								GameCode:  gameCode,
								Name:      label.FirstLabel,
								CreatedAt: time.Now(),
								Creator:   "ParseName",
								UpdatedAt: time.Now(),
								Updater:   "ParseName",
							},
							changed: true,
						}
						t.label.Options = append(t.label.Options, label.SecondLabel)
						creativeLabelMap[label.FirstLabel] = t
					}
				}
			}

			ruleAssets[key] = append(ruleAssets[key], asset.ChannelAssetID)
		}

		// 没数据了
		if len(channelAssets) < limit {
			break
		}
	}

	// 纪录需要保存的新标签规则
	var saveRules []*model.TbAssetLabelRule
	for _, r := range newRules {
		saveRules = append(saveRules, r)

		// 去查一下新标签规则对应素材是否有标签，有的话，全部组合起来
		key := fmt.Sprintf("%v,%v", r.Rule, r.Type)
		assetIDs := ruleAssets[key]
		log.DebugContextf(ctx, "autoGenGameLableRuleFromChannel fill labels, rule:%+v, assetIDs:%+v", r, assetIDs)
		if len(assetIDs) > 0 {
			// id去重
			assetIDs = funk.UniqString(assetIDs)
			labels, err := data.GetBatchChannelAssetsLables(ctx, gameCode, assetIDs)
			if err != nil {
				log.ErrorContextf(ctx, "autoGenGameLableRuleFromChannel data.GetBatchChannelAssetsLables err:%v", err)
				return err
			}

			for _, label := range labels {
				t := model.AssetLabel{
					LabelName:   "", // 固定为空
					FirstLabel:  label.FirstLabel,
					SecondLabel: label.SecondLabel,
				}
				r.Labels = append(r.Labels, t)
			}
		}

		// 标签去重
		removeRuleDuplicateLables(r)
	}

	err = repo.UpsertBatchLabelRule(ctx, saveRules)
	if err != nil {
		log.ErrorContextf(ctx, "autoGenGameLableRuleFromChannel repo.UpsertBatchLabelRule err:%v", err)
		return err
	}

	// 有变化更新标签体系
	var saves []*model.CreativeLabel
	for _, t := range creativeLabelMap {
		if t.changed {
			saves = append(saves, t.label)
		}
	}
	if len(saves) > 0 {
		err = data.UpsertCreativeLabels(ctx, saves)
		if err != nil {
			log.ErrorContextf(ctx, "data.UpsertCreativeLabels err:%v", err)
		}
	}

	return nil
}

// 判断素材是否符合自动生成标签规则
func checkAssetMatchAutoRule(assetName string, autoConfig *autoGenRuleConfig) (string, int) {
	if autoConfig.ruleType == constant.LabelRuleUnicode {
		// unicode 模式
		name := removeFileExtension(assetName)
		// 去掉一些特殊后缀
		for _, suffix := range conf.GetBizConf().SerialRemoveSuffix {
			if strings.HasSuffix(name, suffix) {
				name = strings.TrimSuffix(name, suffix)
				break
			}
		}

		// 分割
		parts := autoConfig.serialSplitReg.Split(name, -1)
		// 取最后一段
		name = utils.LastString(parts)
		// unicode模式检查和归一化
		normalizeName, ok := model.CheckAndNormalizeLabelRuleName(constant.LabelRuleUnicode, name, false)
		if !ok {
			return "", 0
		}
		return normalizeName, constant.LabelRuleUnicode
	} else if autoConfig.ruleType == constant.LabelRulePrefix {
		for _, r := range autoConfig.rules {
			if r.ruleType == constant.LabelRulePrefix {
				matches := r.reg.FindStringSubmatch(assetName)
				if len(matches) >= 1 && matches[1] != "" {
					return matches[1], r.ruleType
				}
			}
		}
	}

	return "", 0
}
