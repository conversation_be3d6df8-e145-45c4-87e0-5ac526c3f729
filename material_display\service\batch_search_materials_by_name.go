package service

import (
	"sync"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/thoas/go-funk"
)

// BatchSearchMaterialsByName 批量素材名字搜索
func BatchSearchMaterialsByName(ctx *gin.Context, req *pb.BatchSearchMaterialsByNameReq, rsp *pb.BatchSearchMaterialsByNameRsp) error {
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	assetNames := req.GetAssetNames()
	if gameCode == "" || len(assetNames) > 50 {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "param error")
	}

	depot, err := data.GetDepotWithContext(ctx, gameCode)
	if err != nil {
		return err
	}
	// 获取素材库的标签规则类型和分割符
	ruleType, _ := repo.GetDepotLabelRuleType(ctx, depot)
	splitReg, err := repo.GetDepotSerialSplitReg(ctx, depot)
	if err != nil {
		log.ErrorContextf(ctx, "BatchSearchMaterialsByName repo.GetDepotSerialSplitReg err:%v", err)
		return err
	}

	// 1. 先按照名字完全匹配查
	allOverviews, err := data.QueryCreativeOverviewByName(ctx, gameCode, assetNames)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.QueryCreativeOverviewByName err:%v", err)
	}

	// 素材名到素材id的映射
	nameToAssetMap := make(map[string]string)
	for _, overview := range allOverviews {
		nameToAssetMap[overview.AssetName] = overview.AssetID
	}

	// 通过名字没有找到素材的
	var pendings []string
	for _, name := range assetNames {
		if _, ok := nameToAssetMap[name]; !ok {
			pendings = append(pendings, name)
		}
	}
	log.DebugContextf(ctx, "BatchSearchMaterialsByName count, assetNames:%v pendings:%d", len(assetNames), len(pendings))

	// 并发处理
	chunks := funk.ChunkStrings(pendings, 20)
	for _, chunk := range chunks {
		var mutex sync.Mutex
		var handlers []func() error
		// 2. 名字全匹配没有找到，匹配编号前缀
		for _, p := range chunk {
			tmpName := p // 拷贝
			matchName := getRuleRegMatchName(gameCode, ruleType, splitReg, tmpName)
			log.DebugContextf(ctx, "p:%v, matchName:%v", tmpName, matchName)
			if matchName != "" {
				handlers = append(handlers, func() error {
					// matchName已规则归一化处理

					rule := &model.TbAssetLabelRule{
						GameCode: gameCode,
						Type:     int32(ruleType),
						Rule:     matchName,
					}
					// 取一条即可
					overviews, err := repo.GetCreativeOverviewsByRuleNotCount(ctx, rule, 0, 1)
					if err != nil {
						return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetCreativeOverviewsByRule err:%v", err)
					}
					if len(overviews) > 0 {
						overview := overviews[0]
						mutex.Lock()
						nameToAssetMap[tmpName] = overview.AssetID
						allOverviews = append(allOverviews, overview)
						mutex.Unlock()
					}
					return nil
				})
			}
		}

		if len(handlers) > 0 {
			err = utils.GoAndWait(ctx, handlers...)
			if err != nil {
				return err
			}
		}
	}

	metaMap := make(map[string]*pb.MaterialMeta)
	if len(allOverviews) > 0 {
		metas := fillMeterialMetas(ctx,
			"", arthubOverViews2MaterialMetas(ctx, allOverviews), depot.Type, depot.GameCode, depot.ArthubCode)
		for _, m := range metas {
			metaMap[m.AssetId] = m
		}
	}

	// 按顺序返回素材信息， 没有查到的填空object
	for _, name := range assetNames {
		assetID := nameToAssetMap[name]
		if m, ok := metaMap[assetID]; ok {
			// 这里深拷贝一个新的， 因为可能会有多个name匹配到同一个素材
			tmp := &pb.MaterialMeta{}
			copier.CopyWithOption(tmp, m, copier.Option{DeepCopy: true})
			tmp.AnotherName = name // 其他名字，前端通过这里来匹配
			rsp.Materials = append(rsp.Materials, tmp)
		} else {
			rsp.Materials = append(rsp.Materials, &pb.MaterialMeta{})
		}
	}
	return nil
}
