// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: unity_advertise/unity_advertise.proto

package unity_advertise

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// POST /api/v1/unity_advertise/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_unity_advertise_unity_advertise_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_unity_advertise_unity_advertise_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_unity_advertise_unity_advertise_proto_rawDescGZIP(), []int{0}
}

func (x *SayHiReq) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Msg    string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_unity_advertise_unity_advertise_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_unity_advertise_unity_advertise_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_unity_advertise_unity_advertise_proto_rawDescGZIP(), []int{1}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SayHiRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 基础结构 渠道账号信
type Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	Name      string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GameId    string `protobuf:"bytes,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	StoreId   string `protobuf:"bytes,4,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
}

func (x *Account) Reset() {
	*x = Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_unity_advertise_unity_advertise_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_unity_advertise_unity_advertise_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_unity_advertise_unity_advertise_proto_rawDescGZIP(), []int{2}
}

func (x *Account) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Account) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Account) GetGameId() string {
	if x != nil {
		return x.GameId
	}
	return ""
}

func (x *Account) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

// 增加游戏渠道账号(只能pod里面请求) POST /api/v1/unity_advertise/add_media_accounts
type AddMediaAccountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode           string     `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	OrganizationCoreId string     `protobuf:"bytes,2,opt,name=organization_core_id,json=organizationCoreId,proto3" json:"organization_core_id,omitempty"`
	KeyId              string     `protobuf:"bytes,3,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	SecretKey          string     `protobuf:"bytes,4,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	Accounts           []*Account `protobuf:"bytes,5,rep,name=accounts,proto3" json:"accounts,omitempty"`
}

func (x *AddMediaAccountsReq) Reset() {
	*x = AddMediaAccountsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_unity_advertise_unity_advertise_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMediaAccountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMediaAccountsReq) ProtoMessage() {}

func (x *AddMediaAccountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_unity_advertise_unity_advertise_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMediaAccountsReq.ProtoReflect.Descriptor instead.
func (*AddMediaAccountsReq) Descriptor() ([]byte, []int) {
	return file_unity_advertise_unity_advertise_proto_rawDescGZIP(), []int{3}
}

func (x *AddMediaAccountsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *AddMediaAccountsReq) GetOrganizationCoreId() string {
	if x != nil {
		return x.OrganizationCoreId
	}
	return ""
}

func (x *AddMediaAccountsReq) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *AddMediaAccountsReq) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *AddMediaAccountsReq) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type AddMediaAccountsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *AddMediaAccountsRsp) Reset() {
	*x = AddMediaAccountsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_unity_advertise_unity_advertise_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMediaAccountsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMediaAccountsRsp) ProtoMessage() {}

func (x *AddMediaAccountsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_unity_advertise_unity_advertise_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMediaAccountsRsp.ProtoReflect.Descriptor instead.
func (*AddMediaAccountsRsp) Descriptor() ([]byte, []int) {
	return file_unity_advertise_unity_advertise_proto_rawDescGZIP(), []int{4}
}

func (x *AddMediaAccountsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 查询渠道账号列表 POST /api/v1/unity_advertise/get_media_accounts
type GetMediaAccountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 必填 游戏标识game_code
}

func (x *GetMediaAccountsReq) Reset() {
	*x = GetMediaAccountsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_unity_advertise_unity_advertise_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsReq) ProtoMessage() {}

func (x *GetMediaAccountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_unity_advertise_unity_advertise_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsReq.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsReq) Descriptor() ([]byte, []int) {
	return file_unity_advertise_unity_advertise_proto_rawDescGZIP(), []int{5}
}

func (x *GetMediaAccountsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

type GetMediaAccountsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Accounts []*Account  `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
}

func (x *GetMediaAccountsRsp) Reset() {
	*x = GetMediaAccountsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_unity_advertise_unity_advertise_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsRsp) ProtoMessage() {}

func (x *GetMediaAccountsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_unity_advertise_unity_advertise_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsRsp.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsRsp) Descriptor() ([]byte, []int) {
	return file_unity_advertise_unity_advertise_proto_rawDescGZIP(), []int{6}
}

func (x *GetMediaAccountsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetMediaAccountsRsp) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

var File_unity_advertise_unity_advertise_proto protoreflect.FileDescriptor

var file_unity_advertise_unity_advertise_proto_rawDesc = []byte{
	0x0a, 0x25, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2f, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69,
	0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1c, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x73, 0x67, 0x22, 0x41, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x70, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x22, 0xd0, 0x01, 0x0a, 0x13, 0x41, 0x64,
	0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30,
	0x0a, 0x14, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x72, 0x65, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x34, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x3a, 0x0a, 0x13,
	0x41, 0x64, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x32, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x70, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x75, 0x6e, 0x69,
	0x74, 0x79, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x42, 0x3e,
	0x5a, 0x3c, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67,
	0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_unity_advertise_unity_advertise_proto_rawDescOnce sync.Once
	file_unity_advertise_unity_advertise_proto_rawDescData = file_unity_advertise_unity_advertise_proto_rawDesc
)

func file_unity_advertise_unity_advertise_proto_rawDescGZIP() []byte {
	file_unity_advertise_unity_advertise_proto_rawDescOnce.Do(func() {
		file_unity_advertise_unity_advertise_proto_rawDescData = protoimpl.X.CompressGZIP(file_unity_advertise_unity_advertise_proto_rawDescData)
	})
	return file_unity_advertise_unity_advertise_proto_rawDescData
}

var file_unity_advertise_unity_advertise_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_unity_advertise_unity_advertise_proto_goTypes = []interface{}{
	(*SayHiReq)(nil),            // 0: unity_advertise.SayHiReq
	(*SayHiRsp)(nil),            // 1: unity_advertise.SayHiRsp
	(*Account)(nil),             // 2: unity_advertise.Account
	(*AddMediaAccountsReq)(nil), // 3: unity_advertise.AddMediaAccountsReq
	(*AddMediaAccountsRsp)(nil), // 4: unity_advertise.AddMediaAccountsRsp
	(*GetMediaAccountsReq)(nil), // 5: unity_advertise.GetMediaAccountsReq
	(*GetMediaAccountsRsp)(nil), // 6: unity_advertise.GetMediaAccountsRsp
	(*aix.Result)(nil),          // 7: aix.Result
}
var file_unity_advertise_unity_advertise_proto_depIdxs = []int32{
	7, // 0: unity_advertise.SayHiRsp.result:type_name -> aix.Result
	2, // 1: unity_advertise.AddMediaAccountsReq.accounts:type_name -> unity_advertise.Account
	7, // 2: unity_advertise.AddMediaAccountsRsp.result:type_name -> aix.Result
	7, // 3: unity_advertise.GetMediaAccountsRsp.result:type_name -> aix.Result
	2, // 4: unity_advertise.GetMediaAccountsRsp.accounts:type_name -> unity_advertise.Account
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_unity_advertise_unity_advertise_proto_init() }
func file_unity_advertise_unity_advertise_proto_init() {
	if File_unity_advertise_unity_advertise_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_unity_advertise_unity_advertise_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_unity_advertise_unity_advertise_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_unity_advertise_unity_advertise_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_unity_advertise_unity_advertise_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMediaAccountsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_unity_advertise_unity_advertise_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMediaAccountsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_unity_advertise_unity_advertise_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_unity_advertise_unity_advertise_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_unity_advertise_unity_advertise_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_unity_advertise_unity_advertise_proto_goTypes,
		DependencyIndexes: file_unity_advertise_unity_advertise_proto_depIdxs,
		MessageInfos:      file_unity_advertise_unity_advertise_proto_msgTypes,
	}.Build()
	File_unity_advertise_unity_advertise_proto = out.File
	file_unity_advertise_unity_advertise_proto_rawDesc = nil
	file_unity_advertise_unity_advertise_proto_goTypes = nil
	file_unity_advertise_unity_advertise_proto_depIdxs = nil
}
