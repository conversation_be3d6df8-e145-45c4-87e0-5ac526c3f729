package service

import (
	"context"
	"regexp"
	"strings"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

// 历史标签规则解析素材名得到标签
func parseNameToOldLabelRules(ctx context.Context, gameCode string) {
	for _, template := range conf.GetBizConf().ParseNameToLabelTemplates {
		if template.GameCode == gameCode {
			parseNameToGameOldLabelRules(ctx, template)
			return
		}
	}
}

// 历史标签规则解析素材名得到标签
func parseNameToGameOldLabelRules(ctx context.Context, template *conf.ParseNameToLabelTemplate) {
	gameCode := template.GameCode
	rules, err := repo.GetAllLabelRulesByGameCode(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "parseNameToGameOldLabelRules repo.GetAllLabelRulesByGameCode err:%v", err)
		return
	}

	// 读取已有的一二级标签体系
	creativeLabels, err := data.GetGameCreativeLabels(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "parseNameToGameOldLabelRules data.GetGameCreativeLabels err:%v", err)
		return
	}
	// 保存标签体系，后面要特殊处理
	creativeLabelMap := make(map[string]*creativeLabelStatus)
	for _, c := range creativeLabels {
		t := &creativeLabelStatus{
			label:   c,
			changed: false,
		}
		creativeLabelMap[c.Name] = t
	}

	var replaceFirstLabel []string
	for _, p := range template.Parts {
		replaceFirstLabel = append(replaceFirstLabel, p.FirstLabel)
	}

	// 分批执行
	chunks := funk.Chunk(rules, 500).([][]*model.TbAssetLabelRule)
	for _, chunk := range chunks {
		var changeRules []*model.TbAssetLabelRule
		for _, rule := range chunk {
			log.DebugContextf(ctx, "parseNameToOneLabelRule begin, id:%v, rule:%v, type:%v, template:%v",
				rule.ID, rule.Rule, rule.Type, utils.ToJson(template))
			labels, err := parseNameToOneLabelRule(ctx, rule, template)
			if err != nil {
				log.ErrorContextf(ctx, "parseNameToOneLabelRule err:%v", err)
				return
			}
			log.DebugContextf(ctx, "parseNameToOneLabelRule end, rule:%v, labels:%+v", rule.Rule, labels)
			// 有通过名字解析到标签
			if len(labels) > 0 {
				// 去掉旧的标签
				var tmps []model.AssetLabel
				for _, label := range rule.Labels {
					if !funk.ContainsString(replaceFirstLabel, label.FirstLabel) {
						tmps = append(tmps, label)
					}
				}
				rule.Labels = tmps

				for _, label := range labels {
					// 添加新的标签
					rule.Labels = append(rule.Labels, label)

					// 判断是否需要更新标签体系
					if t, ok := creativeLabelMap[label.FirstLabel]; ok {
						if !funk.ContainsString(t.label.Options, label.SecondLabel) {
							t.label.UpdatedAt = time.Now()
							t.label.Updater = "ParseName"
							t.label.Options = append(t.label.Options, label.SecondLabel)
							t.changed = true
						}
					} else {
						t = &creativeLabelStatus{
							label: &model.CreativeLabel{
								GameCode:  gameCode,
								Name:      label.FirstLabel,
								CreatedAt: time.Now(),
								Creator:   "ParseName",
								UpdatedAt: time.Now(),
								Updater:   "ParseName",
							},
							changed: true,
						}
						t.label.Options = append(t.label.Options, label.SecondLabel)
						creativeLabelMap[label.FirstLabel] = t
					}
				}

				removeRuleDuplicateLables(rule)
				changeRules = append(changeRules, rule)
			}
		}

		// 只修改标签
		err = repo.UpsertBatchLabelRuleByFields(ctx, changeRules, []string{"labels"})
		if err != nil {
			log.ErrorContextf(ctx, "parseNameToGameOldLabelRules repo.UpsertBatchLabelRuleByFields err:%v", err)
			return
		}
	}

	// 有变化更新标签体系
	var saves []*model.CreativeLabel
	for _, t := range creativeLabelMap {
		if t.changed {
			saves = append(saves, t.label)
		}
	}
	if len(saves) > 0 {
		err = data.UpsertCreativeLabels(ctx, saves)
		if err != nil {
			log.ErrorContextf(ctx, "data.UpsertCreativeLabels err:%v", err)
		}
	}

	return
}

func parseNameToOneLabelRule(ctx context.Context, rule *model.TbAssetLabelRule, template *conf.ParseNameToLabelTemplate) ([]model.AssetLabel, error) {
	// 根据标签规则获取素材， 取20个素材
	views, err := repo.GetCreativeOverviewsByRuleNotCount(ctx, rule, 0, 20)
	if err != nil {
		log.ErrorContextf(ctx, "parseNameToOneLabelRule repo.GetCreativeOverviewsByRuleNotCount error: %v", err)
		return nil, err
	}

	var labels []model.AssetLabel
	for _, v := range views {
		tmp := parseAssetNameLabels(v.AssetName, template)
		labels = append(labels, tmp...)
	}

	return labels, nil
}

var splitReg = regexp.MustCompile(`[-_\s]`)

// 解析素材名得到标签
// tapd: https://tapd.woa.com/tapd_fe/20427967/story/detail/1020427967120590055
//
// 按照模板来解析
// eg:
// game_code: "aov_nsa"
// min_split_parts: 11 # 分割后至少多少段
// parts:
//   - index: 5 # 分割后的第几段，从1开始
//     first_label: "素材角色" # 对应的一级标签名称
//     separator: "&" #该二级标签的分割符，为空表示不分割
//     to_title_case: true # 二级标签是否转成title case
//   - index: 10 # 分割后的第几段，从1开始
//     first_label: "供应商" # 对应的一级标签名称
//   - index: 11 # 分割后的第几段，从1开始
//     first_label: "对接人员" # 对应的一级标签名称
//
/* eg:
// game_code: "hok_prod"
// parts_separator: "-" # 分割符, 为空则采用serial name分割方式
// min_split_parts: 9 # 分割后至少多少段
// parts:
//   - index: 4 # 分割后的第几段，从1开始
//     first_label: "制作方式" # 对应的一级标签名称
//   - index: 5 # 分割后的第几段，从1开始
//     first_label: "素材主题" # 对应的一级标签名称
//     separator: "+" # 该二级标签的分割符，为空表示不分割
//     match_items_num: 2 # 分割后需要匹配的段数，0表示不用匹配
//     want_items_index: # 匹配后需要取哪些数据，下标从1开始, 为空则全部取
//       - 2
*/
func parseAssetNameLabels(name string, template *conf.ParseNameToLabelTemplate) (labels []model.AssetLabel) {
	var parts []string
	// 分割符, 为空则采用serial name分割方式
	if template.PartsSeparator != "" {
		parts = strings.Split(name, template.PartsSeparator)
	} else {
		parts = splitReg.Split(name, -1)
	}
	// 分割后如果少于模板要求的最小段数， 说明对不上，不处理
	if len(parts) < template.MinSplitParts {
		return
	}

	for _, p := range template.Parts {
		s := strings.TrimSpace(parts[p.Index-1])
		seconds := splitAndTrim(s, p.Separator)
		// 分割后需要匹配的段数，0表示不用匹配
		if p.MatchItemsNum > 0 && len(seconds) != p.MatchItemsNum {
			continue
		}
		var ss []string
		// 匹配后需要取哪些数据，下标从1开始, 为空则全部取
		if len(p.WantItemsIndex) > 0 {
			for _, index := range p.WantItemsIndex {
				if index <= len(seconds) {
					ss = append(ss, seconds[index-1])
				}
			}
		} else {
			ss = seconds
		}

		for _, s := range ss {
			// 如果有正则匹配，需要匹配上
			if p.CompileMatchReg != nil &&
				!p.CompileMatchReg.MatchString(s) {
				continue
			}

			if p.DurationMap {
				// 需要时长映射处理
				// Duration需增加映射，二级标签为<10s、 15-35s、 >35s、 10-14s、 NA
				s = strings.ToLower(s)
				// 去掉s
				s = strings.ReplaceAll(s, "s", "")
				duration := cast.ToInt(s)
				if duration < 10 {
					s = "<10s"
				} else if duration >= 10 && duration <= 14 {
					s = "10-14s"
				} else if duration >= 15 && duration <= 35 {
					s = "15-35s"
				} else {
					s = ">35s"
				}

			} else if p.ToTitleCase {
				s = strings.Title(strings.ToLower(s))
			}
			labels = append(labels, model.AssetLabel{
				FirstLabel:  p.FirstLabel,
				SecondLabel: s,
			})
		}
	}

	return
}
