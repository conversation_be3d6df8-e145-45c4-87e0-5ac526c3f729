syntax = "proto3";

package google_sdk;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/google_sdk";

// comment[公共结构体]
message Result {
    uint32 error_code     = 1; // comment[错误码]; default[0]
    string error_message  = 2; // comment[错误短语]; default[success]
}

enum ErrorCode {
    SUCCESS                = 0; // comment[成功]
    SYSTEM_ERROR           = 8000; // comment[系统错误]
    CALL_GOOGLE_ADS_FAILED = 8001; // comment[请求google ads失败]
}