{"name": "go-struct-analyzer", "version": "1.0.0", "description": "Go结构体依赖分析器 - 递归分析Go结构体的依赖关系", "main": "struct-analyzer.js", "bin": {"go-struct-analyze": "./quick-analyze.js", "struct-analyze": "./struct-analyzer.js"}, "scripts": {"test": "node test-analyzer.js", "analyze": "node struct-analyzer.js", "quick": "node quick-analyze.js", "help": "node quick-analyze.js --help"}, "keywords": ["go", "golang", "struct", "dependency", "analysis", "protobuf", "parser"], "author": "AI Assistant", "license": "MIT", "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/go-struct-analyzer.git"}, "bugs": {"url": "https://github.com/your-username/go-struct-analyzer/issues"}, "homepage": "https://github.com/your-username/go-struct-analyzer#readme"}