// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: google_drive_sync/google_drive_sync.proto

package google_drive_sync

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_google_drive_sync_google_drive_sync_proto_rawDescGZIP(), []int{0}
}

func (x *SayHiReq) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Msg    string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_google_drive_sync_google_drive_sync_proto_rawDescGZIP(), []int{1}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SayHiRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

// 手动同步素材status, POST, /api/v1/google_drive_sync/sync_material
type SyncMaterialReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`  // game_code
	NodeId   string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`        // 节点ID
	NodeType int32  `protobuf:"varint,3,opt,name=node_type,json=nodeType,proto3" json:"node_type,omitempty"` // 节点类型, 1-google drive dir
}

func (x *SyncMaterialReq) Reset() {
	*x = SyncMaterialReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncMaterialReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMaterialReq) ProtoMessage() {}

func (x *SyncMaterialReq) ProtoReflect() protoreflect.Message {
	mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMaterialReq.ProtoReflect.Descriptor instead.
func (*SyncMaterialReq) Descriptor() ([]byte, []int) {
	return file_google_drive_sync_google_drive_sync_proto_rawDescGZIP(), []int{2}
}

func (x *SyncMaterialReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *SyncMaterialReq) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *SyncMaterialReq) GetNodeType() int32 {
	if x != nil {
		return x.NodeType
	}
	return 0
}

type SyncMaterialRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SyncMaterialRsp) Reset() {
	*x = SyncMaterialRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncMaterialRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMaterialRsp) ProtoMessage() {}

func (x *SyncMaterialRsp) ProtoReflect() protoreflect.Message {
	mi := &file_google_drive_sync_google_drive_sync_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMaterialRsp.ProtoReflect.Descriptor instead.
func (*SyncMaterialRsp) Descriptor() ([]byte, []int) {
	return file_google_drive_sync_google_drive_sync_proto_rawDescGZIP(), []int{3}
}

func (x *SyncMaterialRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_google_drive_sync_google_drive_sync_proto protoreflect.FileDescriptor

var file_google_drive_sync_google_drive_sync_proto_rawDesc = []byte{
	0x0a, 0x29, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x1a, 0x1c,
	0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1c, 0x0a, 0x08,
	0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x41, 0x0a, 0x08, 0x53, 0x61,
	0x79, 0x48, 0x69, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x64, 0x0a,
	0x0f, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x36, 0x0a, 0x0f, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x40, 0x5a, 0x3e, 0x65,
	0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_drive_sync_google_drive_sync_proto_rawDescOnce sync.Once
	file_google_drive_sync_google_drive_sync_proto_rawDescData = file_google_drive_sync_google_drive_sync_proto_rawDesc
)

func file_google_drive_sync_google_drive_sync_proto_rawDescGZIP() []byte {
	file_google_drive_sync_google_drive_sync_proto_rawDescOnce.Do(func() {
		file_google_drive_sync_google_drive_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_drive_sync_google_drive_sync_proto_rawDescData)
	})
	return file_google_drive_sync_google_drive_sync_proto_rawDescData
}

var file_google_drive_sync_google_drive_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_google_drive_sync_google_drive_sync_proto_goTypes = []interface{}{
	(*SayHiReq)(nil),        // 0: google_drive_sync.SayHiReq
	(*SayHiRsp)(nil),        // 1: google_drive_sync.SayHiRsp
	(*SyncMaterialReq)(nil), // 2: google_drive_sync.SyncMaterialReq
	(*SyncMaterialRsp)(nil), // 3: google_drive_sync.SyncMaterialRsp
	(*aix.Result)(nil),      // 4: aix.Result
}
var file_google_drive_sync_google_drive_sync_proto_depIdxs = []int32{
	4, // 0: google_drive_sync.SayHiRsp.result:type_name -> aix.Result
	4, // 1: google_drive_sync.SyncMaterialRsp.result:type_name -> aix.Result
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_google_drive_sync_google_drive_sync_proto_init() }
func file_google_drive_sync_google_drive_sync_proto_init() {
	if File_google_drive_sync_google_drive_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_drive_sync_google_drive_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_drive_sync_google_drive_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_drive_sync_google_drive_sync_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncMaterialReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_drive_sync_google_drive_sync_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncMaterialRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_drive_sync_google_drive_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_drive_sync_google_drive_sync_proto_goTypes,
		DependencyIndexes: file_google_drive_sync_google_drive_sync_proto_depIdxs,
		MessageInfos:      file_google_drive_sync_google_drive_sync_proto_msgTypes,
	}.Build()
	File_google_drive_sync_google_drive_sync_proto = out.File
	file_google_drive_sync_google_drive_sync_proto_rawDesc = nil
	file_google_drive_sync_google_drive_sync_proto_goTypes = nil
	file_google_drive_sync_google_drive_sync_proto_depIdxs = nil
}
