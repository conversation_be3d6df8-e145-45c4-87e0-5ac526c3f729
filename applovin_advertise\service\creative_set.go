package service

import (
	"context"

	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/data"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/applovin"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise"
)

// GetCreativeSetByCampaign 拉取campaign下的creative_set列表
func GetCreativeSetByCampaign(ctx context.Context,
	req *pb.GetCreativeSetByCampaignReq, rsp *pb.GetCreativeSetByCampaignRsp) error {
	if req.GetGameCode() == "" || req.GetAccountId() == "" || req.GetCampaignId() == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code, account_id, campaign_id is required")
	}

	account, err := data.GetOneMediaAccounts(ctx, req.GetGameCode(), req.GetAccountId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.GetOneMediaAccounts err:%v", err)
	}

	client := newApplovinAPIClient(account)
	creativeSets, err := client.GetCreativeSetsByCampaign(ctx, req.GetCampaignId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "client.GetCreativeSetsByCampaign err:%v", err)
	}
	for _, c := range creativeSets {
		rsp.CreativeSets = append(rsp.CreativeSets, &pb.CreativeSet{
			CreativeSetId:   c.ID,
			CampaignId:      c.CampaignID,
			CreativeSetName: c.Name,
			Languages:       c.Languages,
			Countries:       c.Countries,
			ProductPage:     c.ProductPage,
		})
	}
	return nil
}

// CreateCreativeSet 创建creative_set
func CreateCreativeSet(ctx context.Context,
	req *pb.CreateCreativeSetReq, rsp *pb.CreateCreativeSetRsp) error {
	if req.GetGameCode() == "" || req.GetAccountId() == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code, account_id is required")
	}
	creativeSet := req.GetCreativeSet()
	if creativeSet.GetCampaignId() == "" ||
		creativeSet.GetCreativeSetName() == "" ||
		len(creativeSet.GetLanguages()) == 0 {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "campaign_id, creative_set_name, languages is required")
	}

	account, err := data.GetOneMediaAccounts(ctx, req.GetGameCode(), req.GetAccountId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.GetOneMediaAccounts err:%v", err)
	}
	client := newApplovinAPIClient(account)
	createReq := &applovin.CreativeSet{
		CampaignID: creativeSet.GetCampaignId(),
		Name:       creativeSet.GetCreativeSetName(),
		Languages:  creativeSet.GetLanguages(),
		Countries:  creativeSet.GetCountries(),
	}
	creatigeRsp, err := client.CreateCreativeSet(ctx, createReq)
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "client.CreateCreativeSet err:%v", err)
	}
	rsp.CreativeSet = &pb.CreativeSet{
		CreativeSetId:   creatigeRsp.ID,
		CampaignId:      creatigeRsp.CampaignID,
		CreativeSetName: creatigeRsp.Name,
		Languages:       creatigeRsp.Languages,
		Countries:       creatigeRsp.Countries,
		ProductPage:     creatigeRsp.ProductPage,
	}
	return nil
}
