package bigquery

import (
	"context"
	"fmt"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	bq "e.coding.intlgame.com/ptc/aix-backend/common/pkg/bigquery"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"github.com/thoas/go-funk"
)

const (
	bqTableAssetRuleLabels = "tb_asset_rule_labels"
)

// SyncRuleLabels 将标签规则结构化存储到bq表
func SyncRuleLabels(ctx context.Context, gameCode string,
	deleteRows []*model.TbAssetRuleLabels, lables []*model.TbAssetRuleLabels) error {
	// 没有开启bigquery， 直接返回
	if !setting.GetConf().UseBigquery {
		return nil
	}
	if len(deleteRows) == 0 || len(lables) == 0 {
		return nil
	}

	var rules []string
	for _, label := range deleteRows {
		rules = append(rules, label.Rule)
	}

	proxy := bq.GetProxy(ctx, gameCode)
	// 先删除
	deleteWheres := map[string]interface{}{
		"game_code = ?": gameCode,
		"rule in (?)":   rules,
	}
	err := proxy.DeleteRows(ctx, bqTableAssetRuleLabels, deleteWheres)
	if err != nil {
		return fmt.Errorf("bigquery proxy.DeleteRows err:%v", err)
	}

	// 分批插入新的标签
	chunks := funk.Chunk(lables, 200).([][]*model.TbAssetRuleLabels)
	for _, chunk := range chunks {
		err = proxy.InsertStructs(ctx, bqTableAssetRuleLabels, chunk)
		if err != nil {
			return fmt.Errorf("bigquery proxy.InsertStructs err:%v", err)
		}
	}

	return nil
}

// DeleteRuleLabelsBeforTime 删除标签规则结构化bigquery表历史数据
func DeleteRuleLabelsBeforTime(ctx context.Context, gameCode string, beforeTime time.Time) error {
	// 没有开启bigquery， 直接返回
	if !setting.GetConf().UseBigquery {
		return nil
	}

	proxy := bq.GetProxy(ctx, gameCode)
	// 先删除
	deleteWheres := map[string]interface{}{
		"game_code = ?":   gameCode,
		"update_time < ?": utils.FormatDefault(beforeTime),
	}
	err := proxy.DeleteRows(ctx, bqTableAssetRuleLabels, deleteWheres)
	if err != nil {
		return fmt.Errorf("bigquery proxy.DeleteRows err:%v", err)
	}

	return nil
}
