package main

import (
	"context"
	"strconv"
	"testing"
	"time"

	commoncon "e.coding.intlgame.com/ptc/aix-backend/common/constant"
	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	ckdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/service"
)

// initSetting ...
func initSetting() {
	setting.Setup()
	log.Setup()
	pgdb.Setup()
	conf.LoadBizConf()
	clickhouse.SetupOrm()
}

func TestFilterMappedYoutubeID(t *testing.T) {
	initSetting()
	ctx := log.NewSessionIDContext()
	game_code := "test"
	// insertMockMediaMap(ctx, game_code)

	var youtube_ids []string
	for i := 90; i < 110; i++ {
		youtube_ids = append(youtube_ids, strconv.Itoa(i))
	}

	var err error
	youtube_ids, err = service.FilterMappedYoutubeID(ctx, game_code, youtube_ids)
	if err != nil {
		t.Log(err.Error())
		return
	}

	t.Logf("filtered youtube_ids: %v", youtube_ids)
}

func TestSyncNameLabel3(t *testing.T) {
	initSetting()
	game_code := "pubgm"

	service.SyncChannelAssetLabelToCkDailyRemoveDup(game_code)
}

func TestSyncNameLabel2(t *testing.T) {
	initSetting()
	game_code := "pubgm"

	service.WriteName2label(game_code)
}

func TestSyncLabel2ck(t *testing.T) {
	initSetting()
	game_code := "test"

	service.SyncChannelAssetLabelToCkDailyRemoveDup(game_code)
}

func TestLatestPublishYoutubeIDs(t *testing.T) {
	initSetting()
	ctx := log.NewSessionIDContext()
	game_code := "pubgm"

	youtube_ids, err := service.LatestPublishYoutubeIDs(ctx, game_code)
	if err != nil {
		t.Log(err.Error())
		return
	}

	t.Logf("get ids number: %d, %v", len(youtube_ids), youtube_ids[0:10])
}

// insertMockMediaMap 插入mock数据
// lint:ignore U1000 Ignore unused function temporarily for debugging
func insertMockMediaMap(ctx context.Context, game_code string) error {
	var records []pgmodel.CreativeRecommendMediaContentMap
	for i := 0; i < 100; i++ {
		var record pgmodel.CreativeRecommendMediaContentMap
		record.AixAssetId = strconv.Itoa(i)
		record.MediaID = strconv.Itoa(i)
		record.StorageType = 5
		author := "tigerhuli"
		record.CreateBy = author
		record.UpdateBy = author
		time_now := time.Now().Format("2006-01-02 15:04:05")
		record.CreateTime = time_now
		record.UpdateTime = time_now

		records = append(records, record)
	}

	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(pgmodel.GetCreativeRecommendMediaContentMapTableName(game_code))
	pg_query.OnConflict("(media_id) do nothing")
	_, err := pg_query.Insert()
	if err != nil {
		return err
	}

	return nil
}

func TestSyncCreativeRecommendMediaContentMapForGameCode(t *testing.T) {
	initSetting()
	service.SyncCreativeRecommendMediaContentMapForGameCode("pubgm")
}

func TestCallAddMappings(t *testing.T) {
	initSetting()
	ctx := log.NewSessionIDContext()
	youtube_ids := []string{"1iek0U2keGA", "AfrnAXovl_4", "17Ty10kBwuQ", "E3R3Hf2C9W0", "0C0zivBRGhQ", "AQYbAbqSPYU", "Eeb96-uTDEw", "08yT2Xq3ebA", "Jhy-2A4ovrU", "2VFblZ022oI", "LNeLvYRu5WE", "3tp0DH3j_P8", "JTiFOE92MwQ", "JYjPuupfmiQ", "OY3mLXvQUu8", "RnYi-XreyA0", "LaHjn2Domf4", "V3xBebKeAeE", "W2qlh1x6lbE", "MJxf4nwpQqQ", "WnckvQN7VaE", "M41OWu7K94A", "O_K2kgvb9sE", "YmlFvmjOQHM", "ZDGmyLJh08M", "fU9NLUsQ8uk", "ZdvNTG6RoqY", "gFOiZCub53A", "_EJ961FOtEQ", "hjHAAoH9E54", "bmslc7AzFRk", "azj5IRlz_xo", "iGyghoSYFyI", "iSgozt3ZW1A", "dPElqBJVJ3I", "mF8bnBexHtI", "k9knAQ27hhM", "zYlcBXiemqg", "oT1rhe8ht2k", "vQPOpJeS6_8", "qV7RacDF8Mc", "qXwxyUKRJpY", "u6N1P8c24VA", "us7XB_8hxgY"}
	err := service.CallAddMappings(ctx, "hok_prod", youtube_ids)
	if err != nil {
		t.Log(err.Error())
		return
	}
}

func TestSyncAssetMapTask(t *testing.T) {
	initSetting()
	service.SyncAssetMap("nikke")
}

func TestSyncCreativeRecommendOnlineAndImpression(t *testing.T) {
	initSetting()
	service.SyncOnlineInfoTotalLoopForGameCode("pubgm")
}

func TestLogFormat(t *testing.T) {
	initSetting()
	ctx := log.NewSessionIDContext()
	ctx = context.WithValue(ctx, commoncon.HeaderUserName, "xxxxx")
	log.DebugContextf(ctx, "this is a debug log")
	log.InfoContextf(ctx, "this is an info log")
	log.WarningContextf(ctx, "this is a warning log")
	log.ErrorContextf(ctx, "this is an error log")
}

func TestSyncVirtualAssetLabelsForGameCode(t *testing.T) {
	initSetting()
	service.SyncVirtualChannelAssetLabelsForGameCode("pubgm")
}

func TestDeleteRedundantLabelForGameCode(t *testing.T) {
	initSetting()
	service.DeleteRedundantLabelForGameCode("test")
}

func TestNeedMediaContentMapSync(t *testing.T) {
	initSetting()
	if service.NeedMediaContentMapSync("pubgm") {
		panic("get wrong config")
	}

	if !service.NeedMediaContentMapSync("hok_prod") {
		panic("get wrong config")
	}
}

func TestSyncAssetInfoTotalLoopForGameCode(t *testing.T) {
	initSetting()
	service.SyncAssetInfoTotalLoopForGameCode("pubgm")
}

func TestSyncChannelAssetLabelToCkDaily(t *testing.T) {
	initSetting()
	service.SyncChannelAssetLabelToCkDaily("hok_prod")
}

func TestSyncChannelAssetLabelToCk(t *testing.T) {
	initSetting()
	service.SyncChannelAssetLabelToCk(context.Background())
}

func TestSyncOverviewOnlineStatusHistoryGameCode(t *testing.T) {
	initSetting()

	game_code := "hok_prod"
	start_time, _ := time.Parse("2006-01-02 15:04:05", "2022-08-05 15:04:05")
	end_time, _ := time.Parse("2006-01-02 15:04:05", "2022-09-20 15:04:05")
	service.SyncOverviewOnlineStatusHistoryGameCode(game_code, start_time, end_time)
}

func TestSyncImpressionDateHistory(t *testing.T) {
	initSetting()

	service.SyncImpressionDateHistory()
}

func TestGetCKClient(t *testing.T) {
	initSetting()

	ctx := context.Background()
	ckdb.GetGORMDBWithGameCode(ctx, "hok_prod")
}
