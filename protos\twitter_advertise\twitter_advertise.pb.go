// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: twitter_advertise/twitter_advertise.proto

package twitter_advertise

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 基础结构 渠道账号信息
type MediaAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId     string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`              // 渠道账号id
	AccountName   string `protobuf:"bytes,2,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`        // 渠道账号名称
	AccountStatus int32  `protobuf:"varint,3,opt,name=account_status,json=accountStatus,proto3" json:"account_status,omitempty"` // 账号状态，0表示无效，1表示生效
}

func (x *MediaAccount) Reset() {
	*x = MediaAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaAccount) ProtoMessage() {}

func (x *MediaAccount) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaAccount.ProtoReflect.Descriptor instead.
func (*MediaAccount) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{0}
}

func (x *MediaAccount) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MediaAccount) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *MediaAccount) GetAccountStatus() int32 {
	if x != nil {
		return x.AccountStatus
	}
	return 0
}

type Campaign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode            string     `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                    // 游戏id
	AccountId           string     `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                 // 渠道账户id
	Name                string     `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                            // 广告系列名称
	InnerCampaignId     string     `protobuf:"bytes,4,opt,name=inner_campaign_id,json=innerCampaignId,proto3" json:"inner_campaign_id,omitempty"`             // 广告系列内部id
	MediaCampaignId     string     `protobuf:"bytes,5,opt,name=media_campaign_id,json=mediaCampaignId,proto3" json:"media_campaign_id,omitempty"`             // 广告系列渠道id
	TotalBudgetAmount   string     `protobuf:"bytes,6,opt,name=total_budget_amount,json=totalBudgetAmount,proto3" json:"total_budget_amount,omitempty"`       // 广告系列总预算 单位：美元 可不填
	DailyBudgetAmount   string     `protobuf:"bytes,7,opt,name=daily_budget_amount,json=dailyBudgetAmount,proto3" json:"daily_budget_amount,omitempty"`       // 广告系列每日预算金额 单位：美元
	BudgetOptimization  string     `protobuf:"bytes,8,opt,name=budget_optimization,json=budgetOptimization,proto3" json:"budget_optimization,omitempty"`      // 广告系列优化类型 eg: CAMPAIGN, LINE_ITEM
	FundingInstrumentId string     `protobuf:"bytes,9,opt,name=funding_instrument_id,json=fundingInstrumentId,proto3" json:"funding_instrument_id,omitempty"` // 广告账号下对应的支付账号id
	StandardDelivery    bool       `protobuf:"varint,10,opt,name=standard_delivery,json=standardDelivery,proto3" json:"standard_delivery,omitempty"`          // 广告标准支付
	Objective           string     `protobuf:"bytes,11,opt,name=objective,proto3" json:"objective,omitempty"`                                                 // 广告系列类型 eg: APP_INSTALLS, WEBSITE_CLICKS
	Status              string     `protobuf:"bytes,12,opt,name=status,proto3" json:"status,omitempty"`                                                       // 广告系列状态
	Creator             string     `protobuf:"bytes,13,opt,name=creator,proto3" json:"creator,omitempty"`                                                     // 广告系列创建者 前端不关心
	FailedReason        string     `protobuf:"bytes,14,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`                       // 广告系列发布失败原因
	CreateTime          string     `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                             // 广告创建时间，如果是已发布，则是广告在渠道端创建的时间
	UpdateTime          string     `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                             // 广告更新时间
	Deleted             bool       `protobuf:"varint,17,opt,name=deleted,proto3" json:"deleted,omitempty"`                                                    // 广告是否被删除，eg true(已被删除)， false(未被删除)
	AdGroups            []*AdGroup `protobuf:"bytes,18,rep,name=ad_groups,json=adGroups,proto3" json:"ad_groups,omitempty"`                                   // 广告系列下的广告组
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{1}
}

func (x *Campaign) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *Campaign) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Campaign) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Campaign) GetInnerCampaignId() string {
	if x != nil {
		return x.InnerCampaignId
	}
	return ""
}

func (x *Campaign) GetMediaCampaignId() string {
	if x != nil {
		return x.MediaCampaignId
	}
	return ""
}

func (x *Campaign) GetTotalBudgetAmount() string {
	if x != nil {
		return x.TotalBudgetAmount
	}
	return ""
}

func (x *Campaign) GetDailyBudgetAmount() string {
	if x != nil {
		return x.DailyBudgetAmount
	}
	return ""
}

func (x *Campaign) GetBudgetOptimization() string {
	if x != nil {
		return x.BudgetOptimization
	}
	return ""
}

func (x *Campaign) GetFundingInstrumentId() string {
	if x != nil {
		return x.FundingInstrumentId
	}
	return ""
}

func (x *Campaign) GetStandardDelivery() bool {
	if x != nil {
		return x.StandardDelivery
	}
	return false
}

func (x *Campaign) GetObjective() string {
	if x != nil {
		return x.Objective
	}
	return ""
}

func (x *Campaign) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Campaign) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Campaign) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *Campaign) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Campaign) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Campaign) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *Campaign) GetAdGroups() []*AdGroup {
	if x != nil {
		return x.AdGroups
	}
	return nil
}

type AdGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode                  string             `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                                        // 游戏id
	AccountId                 string             `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                                     // 渠道账户id
	Name                      string             `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                // 广告组名称
	InnerCampaignId           string             `protobuf:"bytes,4,opt,name=inner_campaign_id,json=innerCampaignId,proto3" json:"inner_campaign_id,omitempty"`                                 // 上层campaign内部id
	MediaCampaignId           string             `protobuf:"bytes,5,opt,name=media_campaign_id,json=mediaCampaignId,proto3" json:"media_campaign_id,omitempty"`                                 // 上层campaign渠道id
	InnerAdGroupId            string             `protobuf:"bytes,6,opt,name=inner_ad_group_id,json=innerAdGroupId,proto3" json:"inner_ad_group_id,omitempty"`                                  // 广告组内部id
	MediaAdGroupId            string             `protobuf:"bytes,7,opt,name=media_ad_group_id,json=mediaAdGroupId,proto3" json:"media_ad_group_id,omitempty"`                                  // 广告组渠道id
	AndroidAppStoreIdentifier string             `protobuf:"bytes,8,opt,name=android_app_store_identifier,json=androidAppStoreIdentifier,proto3" json:"android_app_store_identifier,omitempty"` // android app_id
	IosAppStoreIdentifier     string             `protobuf:"bytes,9,opt,name=ios_app_store_identifier,json=iosAppStoreIdentifier,proto3" json:"ios_app_store_identifier,omitempty"`             // ios app_id
	Objective                 string             `protobuf:"bytes,10,opt,name=objective,proto3" json:"objective,omitempty"`                                                                     // 广告类型，APP_INSTALLS, WEBSITE_CLICKS
	ProductGoal               string             `protobuf:"bytes,11,opt,name=product_goal,json=productGoal,proto3" json:"product_goal,omitempty"`                                              // 下层广告类型 eg: promoted_tweet
	BudgetSchedule            *BudgetSchedule    `protobuf:"bytes,12,opt,name=budget_schedule,json=budgetSchedule,proto3" json:"budget_schedule,omitempty"`                                     // 发布时间
	Delivery                  *Delivery          `protobuf:"bytes,13,opt,name=delivery,proto3" json:"delivery,omitempty"`                                                                       // 发布策略
	Demographics              *Demographics      `protobuf:"bytes,14,opt,name=demographics,proto3" json:"demographics,omitempty"`                                                               // 人口统计资料
	Devices                   *Devices           `protobuf:"bytes,15,opt,name=devices,proto3" json:"devices,omitempty"`                                                                         // 广告受众的设备
	Audiences                 *Audience          `protobuf:"bytes,16,opt,name=audiences,proto3" json:"audiences,omitempty"`                                                                     // 广告受众
	TargetingFeatures         *TargetingFeatures `protobuf:"bytes,17,opt,name=targeting_features,json=targetingFeatures,proto3" json:"targeting_features,omitempty"`                            // 广告受众的特点
	Placements                []string           `protobuf:"bytes,18,rep,name=placements,proto3" json:"placements,omitempty"`                                                                   // 广告投放的位置
	AudiencePlatform          *AudiencePlatform  `protobuf:"bytes,19,opt,name=audience_platform,json=audiencePlatform,proto3" json:"audience_platform,omitempty"`                               // 广告组的受众平台
	Status                    string             `protobuf:"bytes,20,opt,name=status,proto3" json:"status,omitempty"`                                                                           // 广告组状态
	Creator                   string             `protobuf:"bytes,21,opt,name=creator,proto3" json:"creator,omitempty"`                                                                         // 广告组创建者 前端不关心
	PrimaryWebEventTag        string             `protobuf:"bytes,22,opt,name=primary_web_event_tag,json=primaryWebEventTag,proto3" json:"primary_web_event_tag,omitempty"`                     // 广告组 转换事件， conversion类型使用
	FailedReason              string             `protobuf:"bytes,23,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`                                           // 广告组 发布失败原因
	CreateTime                string             `protobuf:"bytes,24,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                                                 // 广告组创建时间，如果是已发布，则是广告在渠道端创建的时间
	UpdateTime                string             `protobuf:"bytes,25,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                                                 // 广告组更新时间
	Deleted                   bool               `protobuf:"varint,26,opt,name=deleted,proto3" json:"deleted,omitempty"`                                                                        // 广告是否被删除，eg true(已被删除)， false(未被删除)
	Ads                       []*Ad              `protobuf:"bytes,27,rep,name=ads,proto3" json:"ads,omitempty"`                                                                                 // 广告组下的广告
}

func (x *AdGroup) Reset() {
	*x = AdGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdGroup) ProtoMessage() {}

func (x *AdGroup) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdGroup.ProtoReflect.Descriptor instead.
func (*AdGroup) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{2}
}

func (x *AdGroup) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *AdGroup) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AdGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AdGroup) GetInnerCampaignId() string {
	if x != nil {
		return x.InnerCampaignId
	}
	return ""
}

func (x *AdGroup) GetMediaCampaignId() string {
	if x != nil {
		return x.MediaCampaignId
	}
	return ""
}

func (x *AdGroup) GetInnerAdGroupId() string {
	if x != nil {
		return x.InnerAdGroupId
	}
	return ""
}

func (x *AdGroup) GetMediaAdGroupId() string {
	if x != nil {
		return x.MediaAdGroupId
	}
	return ""
}

func (x *AdGroup) GetAndroidAppStoreIdentifier() string {
	if x != nil {
		return x.AndroidAppStoreIdentifier
	}
	return ""
}

func (x *AdGroup) GetIosAppStoreIdentifier() string {
	if x != nil {
		return x.IosAppStoreIdentifier
	}
	return ""
}

func (x *AdGroup) GetObjective() string {
	if x != nil {
		return x.Objective
	}
	return ""
}

func (x *AdGroup) GetProductGoal() string {
	if x != nil {
		return x.ProductGoal
	}
	return ""
}

func (x *AdGroup) GetBudgetSchedule() *BudgetSchedule {
	if x != nil {
		return x.BudgetSchedule
	}
	return nil
}

func (x *AdGroup) GetDelivery() *Delivery {
	if x != nil {
		return x.Delivery
	}
	return nil
}

func (x *AdGroup) GetDemographics() *Demographics {
	if x != nil {
		return x.Demographics
	}
	return nil
}

func (x *AdGroup) GetDevices() *Devices {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *AdGroup) GetAudiences() *Audience {
	if x != nil {
		return x.Audiences
	}
	return nil
}

func (x *AdGroup) GetTargetingFeatures() *TargetingFeatures {
	if x != nil {
		return x.TargetingFeatures
	}
	return nil
}

func (x *AdGroup) GetPlacements() []string {
	if x != nil {
		return x.Placements
	}
	return nil
}

func (x *AdGroup) GetAudiencePlatform() *AudiencePlatform {
	if x != nil {
		return x.AudiencePlatform
	}
	return nil
}

func (x *AdGroup) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AdGroup) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *AdGroup) GetPrimaryWebEventTag() string {
	if x != nil {
		return x.PrimaryWebEventTag
	}
	return ""
}

func (x *AdGroup) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *AdGroup) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *AdGroup) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *AdGroup) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *AdGroup) GetAds() []*Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

type BudgetSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DailyBudgetAmount string `protobuf:"bytes,1,opt,name=daily_budget_amount,json=dailyBudgetAmount,proto3" json:"daily_budget_amount,omitempty"` // 一天的费用上限 单位：美元
	TotalBudgetAmount string `protobuf:"bytes,2,opt,name=total_budget_amount,json=totalBudgetAmount,proto3" json:"total_budget_amount,omitempty"` // 总共的费用上线 单位：美元
	StartTime         string `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                           // 开始时间
	EndTime           string `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                                 // 结束时间
}

func (x *BudgetSchedule) Reset() {
	*x = BudgetSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BudgetSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BudgetSchedule) ProtoMessage() {}

func (x *BudgetSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BudgetSchedule.ProtoReflect.Descriptor instead.
func (*BudgetSchedule) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{3}
}

func (x *BudgetSchedule) GetDailyBudgetAmount() string {
	if x != nil {
		return x.DailyBudgetAmount
	}
	return ""
}

func (x *BudgetSchedule) GetTotalBudgetAmount() string {
	if x != nil {
		return x.TotalBudgetAmount
	}
	return ""
}

func (x *BudgetSchedule) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *BudgetSchedule) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type Delivery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Goal             string `protobuf:"bytes,1,opt,name=goal,proto3" json:"goal,omitempty"`                                                  // 优化目标 eg: WEBSITE_CONVERSIONS(conversions), APP_INSTALLS
	BidStrategy      string `protobuf:"bytes,2,opt,name=bid_strategy,json=bidStrategy,proto3" json:"bid_strategy,omitempty"`                 // 策略 eg: MAX, AUTO, TARGET
	BidAmount        string `protobuf:"bytes,3,opt,name=bid_amount,json=bidAmount,proto3" json:"bid_amount,omitempty"`                       // 每次出价费用 单位：美元
	PayBy            string `protobuf:"bytes,4,opt,name=pay_by,json=payBy,proto3" json:"pay_by,omitempty"`                                   // 付费标准 eg: IMPRESSION,APP_CLICKS
	StandardDelivery bool   `protobuf:"varint,5,opt,name=standard_delivery,json=standardDelivery,proto3" json:"standard_delivery,omitempty"` // 标准优化
}

func (x *Delivery) Reset() {
	*x = Delivery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Delivery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Delivery) ProtoMessage() {}

func (x *Delivery) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Delivery.ProtoReflect.Descriptor instead.
func (*Delivery) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{4}
}

func (x *Delivery) GetGoal() string {
	if x != nil {
		return x.Goal
	}
	return ""
}

func (x *Delivery) GetBidStrategy() string {
	if x != nil {
		return x.BidStrategy
	}
	return ""
}

func (x *Delivery) GetBidAmount() string {
	if x != nil {
		return x.BidAmount
	}
	return ""
}

func (x *Delivery) GetPayBy() string {
	if x != nil {
		return x.PayBy
	}
	return ""
}

func (x *Delivery) GetStandardDelivery() bool {
	if x != nil {
		return x.StandardDelivery
	}
	return false
}

type Demographics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender    string   `protobuf:"bytes,1,opt,name=gender,proto3" json:"gender,omitempty"`                      // eg:为空表示所有，female, male
	MinAge    uint32   `protobuf:"varint,2,opt,name=min_age,json=minAge,proto3" json:"min_age,omitempty"`       // 最小年龄
	MaxAge    uint32   `protobuf:"varint,3,opt,name=max_age,json=maxAge,proto3" json:"max_age,omitempty"`       // 最大年龄
	Locations []string `protobuf:"bytes,4,rep,name=locations,proto3" json:"locations,omitempty"`                // 受众位置
	Languages []string `protobuf:"bytes,5,rep,name=languages,proto3" json:"languages,omitempty"`                // 受众语言
	AgeRange  bool     `protobuf:"varint,6,opt,name=age_range,json=ageRange,proto3" json:"age_range,omitempty"` // 是否指定年龄段，当 age_range= true时，min_age和max_age才有意义
}

func (x *Demographics) Reset() {
	*x = Demographics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Demographics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Demographics) ProtoMessage() {}

func (x *Demographics) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Demographics.ProtoReflect.Descriptor instead.
func (*Demographics) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{5}
}

func (x *Demographics) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Demographics) GetMinAge() uint32 {
	if x != nil {
		return x.MinAge
	}
	return 0
}

func (x *Demographics) GetMaxAge() uint32 {
	if x != nil {
		return x.MaxAge
	}
	return 0
}

func (x *Demographics) GetLocations() []string {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *Demographics) GetLanguages() []string {
	if x != nil {
		return x.Languages
	}
	return nil
}

func (x *Demographics) GetAgeRange() bool {
	if x != nil {
		return x.AgeRange
	}
	return false
}

type Devices struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform   string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`                        // 设备系统 eg:0 (iOS), 1 (Android) 默认为0
	Version    string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`                          // 系统版本 通过api从渠道拉取
	Wifi       string `protobuf:"bytes,3,opt,name=wifi,proto3" json:"wifi,omitempty"`                                // eg: 0 (所有) 1(wifi)
	ActiveType uint32 `protobuf:"varint,4,opt,name=active_type,json=activeType,proto3" json:"active_type,omitempty"` // eg: 0 (默认表示不勾选) 1(within the last) 2(more than)
	Months     string `protobuf:"bytes,5,opt,name=months,proto3" json:"months,omitempty"`                            // eg: 月份，1,2,3,4,5,6
}

func (x *Devices) Reset() {
	*x = Devices{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Devices) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Devices) ProtoMessage() {}

func (x *Devices) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Devices.ProtoReflect.Descriptor instead.
func (*Devices) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{6}
}

func (x *Devices) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *Devices) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Devices) GetWifi() string {
	if x != nil {
		return x.Wifi
	}
	return ""
}

func (x *Devices) GetActiveType() uint32 {
	if x != nil {
		return x.ActiveType
	}
	return 0
}

func (x *Devices) GetMonths() string {
	if x != nil {
		return x.Months
	}
	return ""
}

type Audience struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Include   []string `protobuf:"bytes,1,rep,name=include,proto3" json:"include,omitempty"`                       // 包含对象
	Exclude   []string `protobuf:"bytes,2,rep,name=exclude,proto3" json:"exclude,omitempty"`                       // 不包含对象
	LookAlike bool     `protobuf:"varint,3,opt,name=look_alike,json=lookAlike,proto3" json:"look_alike,omitempty"` // 勾选了 Include look-alikes 为true 否则false
}

func (x *Audience) Reset() {
	*x = Audience{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Audience) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Audience) ProtoMessage() {}

func (x *Audience) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Audience.ProtoReflect.Descriptor instead.
func (*Audience) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{7}
}

func (x *Audience) GetInclude() []string {
	if x != nil {
		return x.Include
	}
	return nil
}

func (x *Audience) GetExclude() []string {
	if x != nil {
		return x.Exclude
	}
	return nil
}

func (x *Audience) GetLookAlike() bool {
	if x != nil {
		return x.LookAlike
	}
	return false
}

type TargetingFeatures struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyWordsInclude []string `protobuf:"bytes,1,rep,name=key_words_include,json=keyWordsInclude,proto3" json:"key_words_include,omitempty"` // 包含关键字
	KeyWordsExclude []string `protobuf:"bytes,2,rep,name=key_words_exclude,json=keyWordsExclude,proto3" json:"key_words_exclude,omitempty"` // 排除关键字
	Follower        []string `protobuf:"bytes,3,rep,name=follower,proto3" json:"follower,omitempty"`                                        // @对象
	Interests       []string `protobuf:"bytes,4,rep,name=interests,proto3" json:"interests,omitempty"`                                      // 受众的兴趣
}

func (x *TargetingFeatures) Reset() {
	*x = TargetingFeatures{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetingFeatures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetingFeatures) ProtoMessage() {}

func (x *TargetingFeatures) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetingFeatures.ProtoReflect.Descriptor instead.
func (*TargetingFeatures) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{8}
}

func (x *TargetingFeatures) GetKeyWordsInclude() []string {
	if x != nil {
		return x.KeyWordsInclude
	}
	return nil
}

func (x *TargetingFeatures) GetKeyWordsExclude() []string {
	if x != nil {
		return x.KeyWordsExclude
	}
	return nil
}

func (x *TargetingFeatures) GetFollower() []string {
	if x != nil {
		return x.Follower
	}
	return nil
}

func (x *TargetingFeatures) GetInterests() []string {
	if x != nil {
		return x.Interests
	}
	return nil
}

type Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode       string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                       // 游戏id
	AccountId      string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                    // 渠道账户id
	Name           string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                               // 广告名称
	InnerAdGroupId string `protobuf:"bytes,4,opt,name=inner_ad_group_id,json=innerAdGroupId,proto3" json:"inner_ad_group_id,omitempty"` // 上层ad_group内部id
	MediaAdGroupId string `protobuf:"bytes,5,opt,name=media_ad_group_id,json=mediaAdGroupId,proto3" json:"media_ad_group_id,omitempty"` // 上层ad_group渠道id
	InnerAdId      string `protobuf:"bytes,7,opt,name=inner_ad_id,json=innerAdId,proto3" json:"inner_ad_id,omitempty"`                  // 广告组内部id
	MediaAdId      string `protobuf:"bytes,8,opt,name=media_ad_id,json=mediaAdId,proto3" json:"media_ad_id,omitempty"`                  // 广告组渠道id
	Status         string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`                                           // 广告状态
	AdType         string `protobuf:"bytes,10,opt,name=ad_type,json=adType,proto3" json:"ad_type,omitempty"`                            // 广告类型，当前 promoted_tweet (后续会接入media_creatives类型)
	Creator        string `protobuf:"bytes,11,opt,name=creator,proto3" json:"creator,omitempty"`                                        // 广告创建者, 前端不关心
	Objective      string `protobuf:"bytes,12,opt,name=objective,proto3" json:"objective,omitempty"`                                    // 广告分类，在创建ad时必填，其他时候无用
	FailedReason   string `protobuf:"bytes,13,opt,name=failed_reason,json=failedReason,proto3" json:"failed_reason,omitempty"`          // 广告 发布失败原因
	CreateTime     string `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                // 广告创建时间，如果是已发布，则是广告在渠道端创建的时间
	UpdateTime     string `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                // 广告更新时间
	Deleted        bool   `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`                                       // 广告是否被删除，eg true(已被删除)， false(未被删除)
	InnerStatus    string `protobuf:"bytes,17,opt,name=inner_status,json=innerStatus,proto3" json:"inner_status,omitempty"`             // 广告资源状态（用于匹配资源发布任务状态）
	// tweet 类型
	Tweet *Tweet `protobuf:"bytes,18,opt,name=tweet,proto3" json:"tweet,omitempty"` // tweet内容
}

func (x *Ad) Reset() {
	*x = Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad) ProtoMessage() {}

func (x *Ad) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad.ProtoReflect.Descriptor instead.
func (*Ad) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{9}
}

func (x *Ad) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *Ad) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Ad) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Ad) GetInnerAdGroupId() string {
	if x != nil {
		return x.InnerAdGroupId
	}
	return ""
}

func (x *Ad) GetMediaAdGroupId() string {
	if x != nil {
		return x.MediaAdGroupId
	}
	return ""
}

func (x *Ad) GetInnerAdId() string {
	if x != nil {
		return x.InnerAdId
	}
	return ""
}

func (x *Ad) GetMediaAdId() string {
	if x != nil {
		return x.MediaAdId
	}
	return ""
}

func (x *Ad) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Ad) GetAdType() string {
	if x != nil {
		return x.AdType
	}
	return ""
}

func (x *Ad) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Ad) GetObjective() string {
	if x != nil {
		return x.Objective
	}
	return ""
}

func (x *Ad) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *Ad) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Ad) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Ad) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *Ad) GetInnerStatus() string {
	if x != nil {
		return x.InnerStatus
	}
	return ""
}

func (x *Ad) GetTweet() *Tweet {
	if x != nil {
		return x.Tweet
	}
	return nil
}

type Destination struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                  string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                                                                    // eg：APP
	GoogleplayAppId       string `protobuf:"bytes,2,opt,name=googleplay_app_id,json=googleplayAppId,proto3" json:"googleplay_app_id,omitempty"`                     // android app_id
	IosAppStoreIdentifier string `protobuf:"bytes,3,opt,name=ios_app_store_identifier,json=iosAppStoreIdentifier,proto3" json:"ios_app_store_identifier,omitempty"` // ios app_id
}

func (x *Destination) Reset() {
	*x = Destination{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Destination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Destination) ProtoMessage() {}

func (x *Destination) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Destination.ProtoReflect.Descriptor instead.
func (*Destination) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{10}
}

func (x *Destination) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Destination) GetGoogleplayAppId() string {
	if x != nil {
		return x.GoogleplayAppId
	}
	return ""
}

func (x *Destination) GetIosAppStoreIdentifier() string {
	if x != nil {
		return x.IosAppStoreIdentifier
	}
	return ""
}

type Follower struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                   // id
	IdStr           string `protobuf:"bytes,2,opt,name=id_str,json=idStr,proto3" json:"id_str,omitempty"`                                 // id str
	Name            string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                // 名称
	ScreenName      string `protobuf:"bytes,4,opt,name=screen_name,json=screenName,proto3" json:"screen_name,omitempty"`                  // handle name
	ProfileImageUrl string `protobuf:"bytes,5,opt,name=profile_image_url,json=profileImageUrl,proto3" json:"profile_image_url,omitempty"` // 头像
}

func (x *Follower) Reset() {
	*x = Follower{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Follower) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Follower) ProtoMessage() {}

func (x *Follower) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Follower.ProtoReflect.Descriptor instead.
func (*Follower) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{11}
}

func (x *Follower) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Follower) GetIdStr() string {
	if x != nil {
		return x.IdStr
	}
	return ""
}

func (x *Follower) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Follower) GetScreenName() string {
	if x != nil {
		return x.ScreenName
	}
	return ""
}

func (x *Follower) GetProfileImageUrl() string {
	if x != nil {
		return x.ProfileImageUrl
	}
	return ""
}

type AudiencePlatform struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Format           []string `protobuf:"bytes,1,rep,name=format,proto3" json:"format,omitempty"`                                             // 默认全选：
	AdCategories     []string `protobuf:"bytes,2,rep,name=ad_categories,json=adCategories,proto3" json:"ad_categories,omitempty"`             // 广告的分类
	AdvertiserDomain string   `protobuf:"bytes,3,opt,name=advertiser_domain,json=advertiserDomain,proto3" json:"advertiser_domain,omitempty"` // website domain
	IsOpen           bool     `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`                              // 是否勾选audiecePlatform
}

func (x *AudiencePlatform) Reset() {
	*x = AudiencePlatform{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudiencePlatform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudiencePlatform) ProtoMessage() {}

func (x *AudiencePlatform) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudiencePlatform.ProtoReflect.Descriptor instead.
func (*AudiencePlatform) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{12}
}

func (x *AudiencePlatform) GetFormat() []string {
	if x != nil {
		return x.Format
	}
	return nil
}

func (x *AudiencePlatform) GetAdCategories() []string {
	if x != nil {
		return x.AdCategories
	}
	return nil
}

func (x *AudiencePlatform) GetAdvertiserDomain() string {
	if x != nil {
		return x.AdvertiserDomain
	}
	return ""
}

func (x *AudiencePlatform) GetIsOpen() bool {
	if x != nil {
		return x.IsOpen
	}
	return false
}

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                    // id
	Name            string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                // 名称
	Username        string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`                                        // handle name
	ScreenName      string `protobuf:"bytes,4,opt,name=screen_name,json=screenName,proto3" json:"screen_name,omitempty"`                  // handle name 和user_name 一个含义，两者不同时使用
	ProfileImageUrl string `protobuf:"bytes,5,opt,name=profile_image_url,json=profileImageUrl,proto3" json:"profile_image_url,omitempty"` // 头像
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{13}
}

func (x *User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetScreenName() string {
	if x != nil {
		return x.ScreenName
	}
	return ""
}

func (x *User) GetProfileImageUrl() string {
	if x != nil {
		return x.ProfileImageUrl
	}
	return ""
}

type Tweet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                    // tweet id
	Name            string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                // tweet name
	TweetType       string       `protobuf:"bytes,3,opt,name=tweet_type,json=tweetType,proto3" json:"tweet_type,omitempty"`                     // tweet 类型
	FullText        string       `protobuf:"bytes,4,opt,name=full_text,json=fullText,proto3" json:"full_text,omitempty"`                        // tweet 文本
	User            *User        `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`                                                // 广告用户
	CallToAction    string       `protobuf:"bytes,6,opt,name=call_to_action,json=callToAction,proto3" json:"call_to_action,omitempty"`          // eg:INSTALL, SHOP, CONNECT...
	PrimaryAppStore string       `protobuf:"bytes,7,opt,name=primary_app_store,json=primaryAppStore,proto3" json:"primary_app_store,omitempty"` //
	IsSingle        bool         `protobuf:"varint,8,opt,name=is_single,json=isSingle,proto3" json:"is_single,omitempty"`                       // eg: sigle(true) or carousel(false)
	Destination     *Destination `protobuf:"bytes,9,opt,name=destination,proto3" json:"destination,omitempty"`
	MediaDatas      []*MediaData `protobuf:"bytes,10,rep,name=media_datas,json=mediaDatas,proto3" json:"media_datas,omitempty"` // 素材
	CardId          string       `protobuf:"bytes,11,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`             // 后台使用，前端忽略
}

func (x *Tweet) Reset() {
	*x = Tweet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Tweet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tweet) ProtoMessage() {}

func (x *Tweet) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tweet.ProtoReflect.Descriptor instead.
func (*Tweet) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{14}
}

func (x *Tweet) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Tweet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tweet) GetTweetType() string {
	if x != nil {
		return x.TweetType
	}
	return ""
}

func (x *Tweet) GetFullText() string {
	if x != nil {
		return x.FullText
	}
	return ""
}

func (x *Tweet) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Tweet) GetCallToAction() string {
	if x != nil {
		return x.CallToAction
	}
	return ""
}

func (x *Tweet) GetPrimaryAppStore() string {
	if x != nil {
		return x.PrimaryAppStore
	}
	return ""
}

func (x *Tweet) GetIsSingle() bool {
	if x != nil {
		return x.IsSingle
	}
	return false
}

func (x *Tweet) GetDestination() *Destination {
	if x != nil {
		return x.Destination
	}
	return nil
}

func (x *Tweet) GetMediaDatas() []*MediaData {
	if x != nil {
		return x.MediaDatas
	}
	return nil
}

func (x *Tweet) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

type MediaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MediaKey       string `protobuf:"bytes,1,opt,name=media_key,json=mediaKey,proto3" json:"media_key,omitempty"`                     // media id
	Type           string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`                                             // media type eg:IMAGE,VIDEO
	Url            string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`                                               // video or image url
	VideoPosterUrl string `protobuf:"bytes,4,opt,name=video_poster_url,json=videoPosterUrl,proto3" json:"video_poster_url,omitempty"` // VIDEO 封面url
	HeadLine       string `protobuf:"bytes,5,opt,name=head_line,json=headLine,proto3" json:"head_line,omitempty"`                     // conversions 类型时使用
	WebsiteUrl     string `protobuf:"bytes,6,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`               // conversions 类型时使用
	Width          uint32 `protobuf:"varint,7,opt,name=width,proto3" json:"width,omitempty"`                                          // 宽
	Height         uint32 `protobuf:"varint,8,opt,name=height,proto3" json:"height,omitempty"`                                        // 高
}

func (x *MediaData) Reset() {
	*x = MediaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaData) ProtoMessage() {}

func (x *MediaData) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaData.ProtoReflect.Descriptor instead.
func (*MediaData) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{15}
}

func (x *MediaData) GetMediaKey() string {
	if x != nil {
		return x.MediaKey
	}
	return ""
}

func (x *MediaData) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MediaData) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *MediaData) GetVideoPosterUrl() string {
	if x != nil {
		return x.VideoPosterUrl
	}
	return ""
}

func (x *MediaData) GetHeadLine() string {
	if x != nil {
		return x.HeadLine
	}
	return ""
}

func (x *MediaData) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

func (x *MediaData) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *MediaData) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type AccountCampaignInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId       string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                     // 必填 渠道账号id
	InnerCampaignId string `protobuf:"bytes,2,opt,name=inner_campaign_id,json=innerCampaignId,proto3" json:"inner_campaign_id,omitempty"` // 必填 内部inner_campaign_id
	MediaCampaignId string `protobuf:"bytes,3,opt,name=media_campaign_id,json=mediaCampaignId,proto3" json:"media_campaign_id,omitempty"` // 必填 渠道media_campaign_id 优先处理
}

func (x *AccountCampaignInfo) Reset() {
	*x = AccountCampaignInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountCampaignInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountCampaignInfo) ProtoMessage() {}

func (x *AccountCampaignInfo) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountCampaignInfo.ProtoReflect.Descriptor instead.
func (*AccountCampaignInfo) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{16}
}

func (x *AccountCampaignInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AccountCampaignInfo) GetInnerCampaignId() string {
	if x != nil {
		return x.InnerCampaignId
	}
	return ""
}

func (x *AccountCampaignInfo) GetMediaCampaignId() string {
	if x != nil {
		return x.MediaCampaignId
	}
	return ""
}

type PromotableUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId             string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                       // 可发布广告的user账号
	Id                 string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                                                             // 当前promotable user id
	PromotableUserType string `protobuf:"bytes,3,opt,name=promotable_user_type,json=promotableUserType,proto3" json:"promotable_user_type,omitempty"` // promotable user 的权限类型
}

func (x *PromotableUser) Reset() {
	*x = PromotableUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PromotableUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PromotableUser) ProtoMessage() {}

func (x *PromotableUser) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PromotableUser.ProtoReflect.Descriptor instead.
func (*PromotableUser) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{17}
}

func (x *PromotableUser) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PromotableUser) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PromotableUser) GetPromotableUserType() string {
	if x != nil {
		return x.PromotableUserType
	}
	return ""
}

// 查询渠道账号列表, POST, /api/v1/twitter_advertise/get_media_accounts
type GetMediaAccountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 必填 游戏标识game_code
}

func (x *GetMediaAccountsReq) Reset() {
	*x = GetMediaAccountsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsReq) ProtoMessage() {}

func (x *GetMediaAccountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsReq.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{18}
}

func (x *GetMediaAccountsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

type GetMediaAccountsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                     // 返回结果
	GameCode string          `protobuf:"bytes,2,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 游戏标识game_code
	Accounts []*MediaAccount `protobuf:"bytes,3,rep,name=accounts,proto3" json:"accounts,omitempty"`                 // 账号信息列表
}

func (x *GetMediaAccountsRsp) Reset() {
	*x = GetMediaAccountsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsRsp) ProtoMessage() {}

func (x *GetMediaAccountsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsRsp.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{19}
}

func (x *GetMediaAccountsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetMediaAccountsRsp) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetMediaAccountsRsp) GetAccounts() []*MediaAccount {
	if x != nil {
		return x.Accounts
	}
	return nil
}

// 获取指定的campaigns POST /api/v1/twitter_advertise/get_published_campaigns_summary
type GetPublishedCampaignsSummaryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode    string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`          // 必填 游戏标识game_code
	AccountId   string   `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`       // 必填 渠道账号id
	CampaignIds []string `protobuf:"bytes,3,rep,name=campaign_ids,json=campaignIds,proto3" json:"campaign_ids,omitempty"` // 选填campaign_id列表，为空时拉account下的所有campaings(当前不可用，默认为空，拉所有的campaigns)
}

func (x *GetPublishedCampaignsSummaryReq) Reset() {
	*x = GetPublishedCampaignsSummaryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPublishedCampaignsSummaryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPublishedCampaignsSummaryReq) ProtoMessage() {}

func (x *GetPublishedCampaignsSummaryReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPublishedCampaignsSummaryReq.ProtoReflect.Descriptor instead.
func (*GetPublishedCampaignsSummaryReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{20}
}

func (x *GetPublishedCampaignsSummaryReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetPublishedCampaignsSummaryReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetPublishedCampaignsSummaryReq) GetCampaignIds() []string {
	if x != nil {
		return x.CampaignIds
	}
	return nil
}

type GetPublishedCampaignsSummaryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Campaigns []*Campaign `protobuf:"bytes,2,rep,name=campaigns,proto3" json:"campaigns,omitempty"` // Campaign列表信息
}

func (x *GetPublishedCampaignsSummaryRsp) Reset() {
	*x = GetPublishedCampaignsSummaryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPublishedCampaignsSummaryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPublishedCampaignsSummaryRsp) ProtoMessage() {}

func (x *GetPublishedCampaignsSummaryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPublishedCampaignsSummaryRsp.ProtoReflect.Descriptor instead.
func (*GetPublishedCampaignsSummaryRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{21}
}

func (x *GetPublishedCampaignsSummaryRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetPublishedCampaignsSummaryRsp) GetCampaigns() []*Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

// 获取指定的ad_groups POST /api/v1/twitter_advertise/get_published_ad_groups_summary
type GetPublishedAdGroupsSummaryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode   string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`         // 必填 游戏标识game_code
	AccountId  string   `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`      // 必填 渠道账号id
	AdGroupIds []string `protobuf:"bytes,3,rep,name=ad_group_ids,json=adGroupIds,proto3" json:"ad_group_ids,omitempty"` // 选填 ad_group_id列表,不填时拉account下的所有ad_group(当前不可用，默认为空，拉所有的ad_groups)
}

func (x *GetPublishedAdGroupsSummaryReq) Reset() {
	*x = GetPublishedAdGroupsSummaryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPublishedAdGroupsSummaryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPublishedAdGroupsSummaryReq) ProtoMessage() {}

func (x *GetPublishedAdGroupsSummaryReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPublishedAdGroupsSummaryReq.ProtoReflect.Descriptor instead.
func (*GetPublishedAdGroupsSummaryReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{22}
}

func (x *GetPublishedAdGroupsSummaryReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetPublishedAdGroupsSummaryReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetPublishedAdGroupsSummaryReq) GetAdGroupIds() []string {
	if x != nil {
		return x.AdGroupIds
	}
	return nil
}

type GetPublishedAdGroupsSummaryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                     // 返回结果
	AdGroups []*AdGroup  `protobuf:"bytes,2,rep,name=ad_groups,json=adGroups,proto3" json:"ad_groups,omitempty"` // AdGroup列表信息
}

func (x *GetPublishedAdGroupsSummaryRsp) Reset() {
	*x = GetPublishedAdGroupsSummaryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPublishedAdGroupsSummaryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPublishedAdGroupsSummaryRsp) ProtoMessage() {}

func (x *GetPublishedAdGroupsSummaryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPublishedAdGroupsSummaryRsp.ProtoReflect.Descriptor instead.
func (*GetPublishedAdGroupsSummaryRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{23}
}

func (x *GetPublishedAdGroupsSummaryRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetPublishedAdGroupsSummaryRsp) GetAdGroups() []*AdGroup {
	if x != nil {
		return x.AdGroups
	}
	return nil
}

// 获取指定的ads POST /api/v1/twitter_advertise/get_published_ads_summary
type GetPublishedAdsSummaryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识game_code
	AccountId string   `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 渠道账号id
	AdIds     []string `protobuf:"bytes,3,rep,name=ad_ids,json=adIds,proto3" json:"ad_ids,omitempty"`             // 选填 ad_id列表，不填时拉account下的所有ad(当前不可用，默认为空，拉所有的ad)
}

func (x *GetPublishedAdsSummaryReq) Reset() {
	*x = GetPublishedAdsSummaryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPublishedAdsSummaryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPublishedAdsSummaryReq) ProtoMessage() {}

func (x *GetPublishedAdsSummaryReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPublishedAdsSummaryReq.ProtoReflect.Descriptor instead.
func (*GetPublishedAdsSummaryReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{24}
}

func (x *GetPublishedAdsSummaryReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetPublishedAdsSummaryReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetPublishedAdsSummaryReq) GetAdIds() []string {
	if x != nil {
		return x.AdIds
	}
	return nil
}

type GetPublishedAdsSummaryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Ads    []*Ad       `protobuf:"bytes,2,rep,name=ads,proto3" json:"ads,omitempty"`       // Ads列表信息
}

func (x *GetPublishedAdsSummaryRsp) Reset() {
	*x = GetPublishedAdsSummaryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPublishedAdsSummaryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPublishedAdsSummaryRsp) ProtoMessage() {}

func (x *GetPublishedAdsSummaryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPublishedAdsSummaryRsp.ProtoReflect.Descriptor instead.
func (*GetPublishedAdsSummaryRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{25}
}

func (x *GetPublishedAdsSummaryRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetPublishedAdsSummaryRsp) GetAds() []*Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

// 创建草稿Campaign POST /api/v1/twitter_advertise/create_campaign
type CreateCampaignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Campaign *Campaign `protobuf:"bytes,1,opt,name=campaign,proto3" json:"campaign,omitempty"` // 必填 待创建campaign信息
}

func (x *CreateCampaignReq) Reset() {
	*x = CreateCampaignReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCampaignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCampaignReq) ProtoMessage() {}

func (x *CreateCampaignReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCampaignReq.ProtoReflect.Descriptor instead.
func (*CreateCampaignReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{26}
}

func (x *CreateCampaignReq) GetCampaign() *Campaign {
	if x != nil {
		return x.Campaign
	}
	return nil
}

type CreateCampaignRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`     // 返回结果
	Campaign *Campaign   `protobuf:"bytes,2,opt,name=campaign,proto3" json:"campaign,omitempty"` // 新campaign 信息
}

func (x *CreateCampaignRsp) Reset() {
	*x = CreateCampaignRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCampaignRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCampaignRsp) ProtoMessage() {}

func (x *CreateCampaignRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCampaignRsp.ProtoReflect.Descriptor instead.
func (*CreateCampaignRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{27}
}

func (x *CreateCampaignRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CreateCampaignRsp) GetCampaign() *Campaign {
	if x != nil {
		return x.Campaign
	}
	return nil
}

// 创建草稿ad_group POST /api/v1/twitter_advertise/create_ad_group
type CreateAdGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdGroup *AdGroup `protobuf:"bytes,1,opt,name=ad_group,json=adGroup,proto3" json:"ad_group,omitempty"` // 必填 待创建ad_group信息
}

func (x *CreateAdGroupReq) Reset() {
	*x = CreateAdGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAdGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdGroupReq) ProtoMessage() {}

func (x *CreateAdGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdGroupReq.ProtoReflect.Descriptor instead.
func (*CreateAdGroupReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{28}
}

func (x *CreateAdGroupReq) GetAdGroup() *AdGroup {
	if x != nil {
		return x.AdGroup
	}
	return nil
}

type CreateAdGroupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	AdGroup *AdGroup    `protobuf:"bytes,2,opt,name=ad_group,json=adGroup,proto3" json:"ad_group,omitempty"` // 新ad_group 信息
}

func (x *CreateAdGroupRsp) Reset() {
	*x = CreateAdGroupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAdGroupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdGroupRsp) ProtoMessage() {}

func (x *CreateAdGroupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdGroupRsp.ProtoReflect.Descriptor instead.
func (*CreateAdGroupRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{29}
}

func (x *CreateAdGroupRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CreateAdGroupRsp) GetAdGroup() *AdGroup {
	if x != nil {
		return x.AdGroup
	}
	return nil
}

// 创建草稿ad POST /api/v1/twitter_advertise/create_ad
type CreateAdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ad *Ad `protobuf:"bytes,1,opt,name=ad,proto3" json:"ad,omitempty"` // 必填 待创建ad信息
}

func (x *CreateAdReq) Reset() {
	*x = CreateAdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdReq) ProtoMessage() {}

func (x *CreateAdReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdReq.ProtoReflect.Descriptor instead.
func (*CreateAdReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{30}
}

func (x *CreateAdReq) GetAd() *Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

type CreateAdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Ad     *Ad         `protobuf:"bytes,2,opt,name=ad,proto3" json:"ad,omitempty"`         // 新ad 信息
}

func (x *CreateAdRsp) Reset() {
	*x = CreateAdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdRsp) ProtoMessage() {}

func (x *CreateAdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdRsp.ProtoReflect.Descriptor instead.
func (*CreateAdRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{31}
}

func (x *CreateAdRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CreateAdRsp) GetAd() *Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

// 查询 campaign及其ad_group,ad树状列表, POST, /api/v1/twitter_advertise/get_campaign_trees
type GetCampaignTreesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode         string                 `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                         // 必填 游戏标识
	Media            string                 `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                               // 必填 渠道标识 eg:Twitter
	AccountCampaigns []*AccountCampaignInfo `protobuf:"bytes,3,rep,name=account_campaigns,json=accountCampaigns,proto3" json:"account_campaigns,omitempty"` // 账号+campaign_id列表
}

func (x *GetCampaignTreesReq) Reset() {
	*x = GetCampaignTreesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignTreesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignTreesReq) ProtoMessage() {}

func (x *GetCampaignTreesReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignTreesReq.ProtoReflect.Descriptor instead.
func (*GetCampaignTreesReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{32}
}

func (x *GetCampaignTreesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetCampaignTreesReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *GetCampaignTreesReq) GetAccountCampaigns() []*AccountCampaignInfo {
	if x != nil {
		return x.AccountCampaigns
	}
	return nil
}

type GetCampaignTreesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Campaigns []*Campaign `protobuf:"bytes,2,rep,name=campaigns,proto3" json:"campaigns,omitempty"` // 获取完整的广告树
}

func (x *GetCampaignTreesRsp) Reset() {
	*x = GetCampaignTreesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignTreesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignTreesRsp) ProtoMessage() {}

func (x *GetCampaignTreesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignTreesRsp.ProtoReflect.Descriptor instead.
func (*GetCampaignTreesRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{33}
}

func (x *GetCampaignTreesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetCampaignTreesRsp) GetCampaigns() []*Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

// 更新 campaign(渠道或草稿)， POST, /api/v1/twitter_advertise/update_campaign
type UpdateCampaignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Campaign *Campaign `protobuf:"bytes,1,opt,name=campaign,proto3" json:"campaign,omitempty"` // 更新的数据
	Fields   []string  `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`     // 指定更新的字段，为空则全量更新所有字段 eg:account_id, name, total_budget_amount, daily_budget_amount, budget_optimization, status
}

func (x *UpdateCampaignReq) Reset() {
	*x = UpdateCampaignReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCampaignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignReq) ProtoMessage() {}

func (x *UpdateCampaignReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignReq.ProtoReflect.Descriptor instead.
func (*UpdateCampaignReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{34}
}

func (x *UpdateCampaignReq) GetCampaign() *Campaign {
	if x != nil {
		return x.Campaign
	}
	return nil
}

func (x *UpdateCampaignReq) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type UpdateCampaignRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`     // 返回结果
	Campaign *Campaign   `protobuf:"bytes,2,opt,name=campaign,proto3" json:"campaign,omitempty"` // 更新后 campaign 信息 eg:account_id, name, android_app_store_identifier, ios_app_store_identifier, budget_schedule, delivery, demographics,devices, audiences, targeting_features, placements, status
}

func (x *UpdateCampaignRsp) Reset() {
	*x = UpdateCampaignRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCampaignRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignRsp) ProtoMessage() {}

func (x *UpdateCampaignRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignRsp.ProtoReflect.Descriptor instead.
func (*UpdateCampaignRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{35}
}

func (x *UpdateCampaignRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *UpdateCampaignRsp) GetCampaign() *Campaign {
	if x != nil {
		return x.Campaign
	}
	return nil
}

// 更新 ad_group(渠道或草稿)， POST, /api/v1/twitter_advertise/update_ad_group
type UpdateAdGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdGroup *AdGroup `protobuf:"bytes,1,opt,name=ad_group,json=adGroup,proto3" json:"ad_group,omitempty"` // 更新的数据
	Fields  []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`                  // 指定更新的字段，为空则全量更新所有字段
}

func (x *UpdateAdGroupReq) Reset() {
	*x = UpdateAdGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAdGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdGroupReq) ProtoMessage() {}

func (x *UpdateAdGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdGroupReq.ProtoReflect.Descriptor instead.
func (*UpdateAdGroupReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateAdGroupReq) GetAdGroup() *AdGroup {
	if x != nil {
		return x.AdGroup
	}
	return nil
}

func (x *UpdateAdGroupReq) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type UpdateAdGroupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	AdGroup *AdGroup    `protobuf:"bytes,2,opt,name=ad_group,json=adGroup,proto3" json:"ad_group,omitempty"` // 更新后 ad_group 信息
}

func (x *UpdateAdGroupRsp) Reset() {
	*x = UpdateAdGroupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAdGroupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdGroupRsp) ProtoMessage() {}

func (x *UpdateAdGroupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdGroupRsp.ProtoReflect.Descriptor instead.
func (*UpdateAdGroupRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateAdGroupRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *UpdateAdGroupRsp) GetAdGroup() *AdGroup {
	if x != nil {
		return x.AdGroup
	}
	return nil
}

// 更新 ad(渠道或草稿)， POST, /api/v1/twitter_advertise/update_ad
type UpdateAdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ad     *Ad      `protobuf:"bytes,1,opt,name=ad,proto3" json:"ad,omitempty"`         // 更新的数据
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"` // 指定更新的字段，为空则全量更新所有字段 草稿 可更新字段 eg:"account_id", "name", "status", "full_text", "call_to_action","primary_app_store", "destination", "media_datas", "media_type", "user" 已发布广告可更新字段 eg: "name", "full_text", "call_to_action"
}

func (x *UpdateAdReq) Reset() {
	*x = UpdateAdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdReq) ProtoMessage() {}

func (x *UpdateAdReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdReq.ProtoReflect.Descriptor instead.
func (*UpdateAdReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateAdReq) GetAd() *Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

func (x *UpdateAdReq) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

type UpdateAdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Ad     *Ad         `protobuf:"bytes,2,opt,name=ad,proto3" json:"ad,omitempty"`         // 更新后 ad 信息
}

func (x *UpdateAdRsp) Reset() {
	*x = UpdateAdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAdRsp) ProtoMessage() {}

func (x *UpdateAdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAdRsp.ProtoReflect.Descriptor instead.
func (*UpdateAdRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateAdRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *UpdateAdRsp) GetAd() *Ad {
	if x != nil {
		return x.Ad
	}
	return nil
}

// 发布广告(包括所属上层adgroup, campaign发布),POST, /api/v1/twitter_advertise/publish_ad_link
type PublishAdLinkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`      // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`   // 必填 账户id
	InnerAdId string `protobuf:"bytes,3,opt,name=inner_ad_id,json=innerAdId,proto3" json:"inner_ad_id,omitempty"` // 必填 待发布的ad id
}

func (x *PublishAdLinkReq) Reset() {
	*x = PublishAdLinkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishAdLinkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishAdLinkReq) ProtoMessage() {}

func (x *PublishAdLinkReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishAdLinkReq.ProtoReflect.Descriptor instead.
func (*PublishAdLinkReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{40}
}

func (x *PublishAdLinkReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *PublishAdLinkReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *PublishAdLinkReq) GetInnerAdId() string {
	if x != nil {
		return x.InnerAdId
	}
	return ""
}

type PublishAdLinkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                            // 返回结果
	MediaCampaignId string      `protobuf:"bytes,2,opt,name=media_campaign_id,json=mediaCampaignId,proto3" json:"media_campaign_id,omitempty"` // 渠道campaign_id
	MediaAdGroupId  string      `protobuf:"bytes,3,opt,name=media_ad_group_id,json=mediaAdGroupId,proto3" json:"media_ad_group_id,omitempty"`  // 渠道ad_group_id
	MediaAdId       string      `protobuf:"bytes,4,opt,name=media_ad_id,json=mediaAdId,proto3" json:"media_ad_id,omitempty"`                   // 渠道ad_id
}

func (x *PublishAdLinkRsp) Reset() {
	*x = PublishAdLinkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishAdLinkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishAdLinkRsp) ProtoMessage() {}

func (x *PublishAdLinkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishAdLinkRsp.ProtoReflect.Descriptor instead.
func (*PublishAdLinkRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{41}
}

func (x *PublishAdLinkRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *PublishAdLinkRsp) GetMediaCampaignId() string {
	if x != nil {
		return x.MediaCampaignId
	}
	return ""
}

func (x *PublishAdLinkRsp) GetMediaAdGroupId() string {
	if x != nil {
		return x.MediaAdGroupId
	}
	return ""
}

func (x *PublishAdLinkRsp) GetMediaAdId() string {
	if x != nil {
		return x.MediaAdId
	}
	return ""
}

type GetFundingSourcesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
}

func (x *GetFundingSourcesReq) Reset() {
	*x = GetFundingSourcesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFundingSourcesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFundingSourcesReq) ProtoMessage() {}

func (x *GetFundingSourcesReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFundingSourcesReq.ProtoReflect.Descriptor instead.
func (*GetFundingSourcesReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{42}
}

func (x *GetFundingSourcesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetFundingSourcesReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetFundingSourcesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result         *aix.Result          `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	FundingSources []*FundingInstrument `protobuf:"bytes,2,rep,name=funding_sources,json=fundingSources,proto3" json:"funding_sources,omitempty"`
}

func (x *GetFundingSourcesRsp) Reset() {
	*x = GetFundingSourcesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFundingSourcesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFundingSourcesRsp) ProtoMessage() {}

func (x *GetFundingSourcesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFundingSourcesRsp.ProtoReflect.Descriptor instead.
func (*GetFundingSourcesRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{43}
}

func (x *GetFundingSourcesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetFundingSourcesRsp) GetFundingSources() []*FundingInstrument {
	if x != nil {
		return x.FundingSources
	}
	return nil
}

type GetAccountAppsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
}

func (x *GetAccountAppsReq) Reset() {
	*x = GetAccountAppsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountAppsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountAppsReq) ProtoMessage() {}

func (x *GetAccountAppsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountAppsReq.ProtoReflect.Descriptor instead.
func (*GetAccountAppsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{44}
}

func (x *GetAccountAppsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetAccountAppsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetAccountAppsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Apps   []*AppInfo  `protobuf:"bytes,2,rep,name=apps,proto3" json:"apps,omitempty"`
}

func (x *GetAccountAppsRsp) Reset() {
	*x = GetAccountAppsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountAppsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountAppsRsp) ProtoMessage() {}

func (x *GetAccountAppsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountAppsRsp.ProtoReflect.Descriptor instead.
func (*GetAccountAppsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{45}
}

func (x *GetAccountAppsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAccountAppsRsp) GetApps() []*AppInfo {
	if x != nil {
		return x.Apps
	}
	return nil
}

type GetLocationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
}

func (x *GetLocationsReq) Reset() {
	*x = GetLocationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationsReq) ProtoMessage() {}

func (x *GetLocationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationsReq.ProtoReflect.Descriptor instead.
func (*GetLocationsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{46}
}

func (x *GetLocationsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetLocationsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetLocationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Locations []*Location `protobuf:"bytes,2,rep,name=locations,proto3" json:"locations,omitempty"`
}

func (x *GetLocationsRsp) Reset() {
	*x = GetLocationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationsRsp) ProtoMessage() {}

func (x *GetLocationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationsRsp.ProtoReflect.Descriptor instead.
func (*GetLocationsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{47}
}

func (x *GetLocationsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetLocationsRsp) GetLocations() []*Location {
	if x != nil {
		return x.Locations
	}
	return nil
}

type GetLanguagesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
	Query     string `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`                          // 选填 查询关键字
}

func (x *GetLanguagesReq) Reset() {
	*x = GetLanguagesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguagesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguagesReq) ProtoMessage() {}

func (x *GetLanguagesReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguagesReq.ProtoReflect.Descriptor instead.
func (*GetLanguagesReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{48}
}

func (x *GetLanguagesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetLanguagesReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetLanguagesReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type GetLanguagesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Languages []*Language `protobuf:"bytes,2,rep,name=languages,proto3" json:"languages,omitempty"`
}

func (x *GetLanguagesRsp) Reset() {
	*x = GetLanguagesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLanguagesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLanguagesRsp) ProtoMessage() {}

func (x *GetLanguagesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLanguagesRsp.ProtoReflect.Descriptor instead.
func (*GetLanguagesRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{49}
}

func (x *GetLanguagesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetLanguagesRsp) GetLanguages() []*Language {
	if x != nil {
		return x.Languages
	}
	return nil
}

type GetOsVersionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
}

func (x *GetOsVersionsReq) Reset() {
	*x = GetOsVersionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOsVersionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOsVersionsReq) ProtoMessage() {}

func (x *GetOsVersionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOsVersionsReq.ProtoReflect.Descriptor instead.
func (*GetOsVersionsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{50}
}

func (x *GetOsVersionsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetOsVersionsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetOsVersionsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result     *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	OsVersions []*OsVersion `protobuf:"bytes,2,rep,name=os_versions,json=osVersions,proto3" json:"os_versions,omitempty"`
}

func (x *GetOsVersionsRsp) Reset() {
	*x = GetOsVersionsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOsVersionsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOsVersionsRsp) ProtoMessage() {}

func (x *GetOsVersionsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOsVersionsRsp.ProtoReflect.Descriptor instead.
func (*GetOsVersionsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{51}
}

func (x *GetOsVersionsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetOsVersionsRsp) GetOsVersions() []*OsVersion {
	if x != nil {
		return x.OsVersions
	}
	return nil
}

type GetAudiencesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
	Query     string `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`                          // 选填 查询关键字
}

func (x *GetAudiencesReq) Reset() {
	*x = GetAudiencesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAudiencesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudiencesReq) ProtoMessage() {}

func (x *GetAudiencesReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudiencesReq.ProtoReflect.Descriptor instead.
func (*GetAudiencesReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{52}
}

func (x *GetAudiencesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetAudiencesReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetAudiencesReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type GetAudiencesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result       `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Audiences []*CustomAudience `protobuf:"bytes,2,rep,name=audiences,proto3" json:"audiences,omitempty"`
}

func (x *GetAudiencesRsp) Reset() {
	*x = GetAudiencesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAudiencesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAudiencesRsp) ProtoMessage() {}

func (x *GetAudiencesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAudiencesRsp.ProtoReflect.Descriptor instead.
func (*GetAudiencesRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{53}
}

func (x *GetAudiencesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAudiencesRsp) GetAudiences() []*CustomAudience {
	if x != nil {
		return x.Audiences
	}
	return nil
}

type GetInterestsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
	Query     string `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`                          // 选填 查询关键字
}

func (x *GetInterestsReq) Reset() {
	*x = GetInterestsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInterestsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInterestsReq) ProtoMessage() {}

func (x *GetInterestsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInterestsReq.ProtoReflect.Descriptor instead.
func (*GetInterestsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{54}
}

func (x *GetInterestsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetInterestsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetInterestsReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type GetInterestsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Interests []*Interest `protobuf:"bytes,2,rep,name=interests,proto3" json:"interests,omitempty"`
}

func (x *GetInterestsRsp) Reset() {
	*x = GetInterestsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInterestsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInterestsRsp) ProtoMessage() {}

func (x *GetInterestsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInterestsRsp.ProtoReflect.Descriptor instead.
func (*GetInterestsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{55}
}

func (x *GetInterestsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetInterestsRsp) GetInterests() []*Interest {
	if x != nil {
		return x.Interests
	}
	return nil
}

type GetWebEventTagsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
}

func (x *GetWebEventTagsReq) Reset() {
	*x = GetWebEventTagsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebEventTagsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebEventTagsReq) ProtoMessage() {}

func (x *GetWebEventTagsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebEventTagsReq.ProtoReflect.Descriptor instead.
func (*GetWebEventTagsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{56}
}

func (x *GetWebEventTagsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetWebEventTagsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetWebEventTagsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result       *aix.Result    `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	WebEventTags []*WebEventTag `protobuf:"bytes,2,rep,name=web_event_tags,json=webEventTags,proto3" json:"web_event_tags,omitempty"`
}

func (x *GetWebEventTagsRsp) Reset() {
	*x = GetWebEventTagsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWebEventTagsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWebEventTagsRsp) ProtoMessage() {}

func (x *GetWebEventTagsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWebEventTagsRsp.ProtoReflect.Descriptor instead.
func (*GetWebEventTagsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{57}
}

func (x *GetWebEventTagsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetWebEventTagsRsp) GetWebEventTags() []*WebEventTag {
	if x != nil {
		return x.WebEventTags
	}
	return nil
}

// 获取广告业务配置信息, POST, /api/v1/facebook_advertise/GetBizConfig
type GetBizConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Catelog string `protobuf:"bytes,1,opt,name=catelog,proto3" json:"catelog,omitempty"`
	Key     string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetBizConfigReq) Reset() {
	*x = GetBizConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBizConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBizConfigReq) ProtoMessage() {}

func (x *GetBizConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBizConfigReq.ProtoReflect.Descriptor instead.
func (*GetBizConfigReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{58}
}

func (x *GetBizConfigReq) GetCatelog() string {
	if x != nil {
		return x.Catelog
	}
	return ""
}

func (x *GetBizConfigReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GetBizConfigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result      `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Data   *structpb.Struct `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"` // 返回的业务数据, 参数不同返回内容格式会有所不同
}

func (x *GetBizConfigRsp) Reset() {
	*x = GetBizConfigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBizConfigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBizConfigRsp) ProtoMessage() {}

func (x *GetBizConfigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBizConfigRsp.ProtoReflect.Descriptor instead.
func (*GetBizConfigRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{59}
}

func (x *GetBizConfigRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetBizConfigRsp) GetData() *structpb.Struct {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReloadRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *ReloadRsp) Reset() {
	*x = ReloadRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReloadRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReloadRsp) ProtoMessage() {}

func (x *ReloadRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReloadRsp.ProtoReflect.Descriptor instead.
func (*ReloadRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{60}
}

func (x *ReloadRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type DeleteDraftsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InnerIds      []string `protobuf:"bytes,1,rep,name=inner_ids,json=innerIds,proto3" json:"inner_ids,omitempty"`                // 想要删除的campaign,ad_group或者ad的inner id
	AdvertiseType string   `protobuf:"bytes,2,opt,name=advertise_type,json=advertiseType,proto3" json:"advertise_type,omitempty"` // 类型：campaign, ad_group, ad
}

func (x *DeleteDraftsReq) Reset() {
	*x = DeleteDraftsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDraftsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDraftsReq) ProtoMessage() {}

func (x *DeleteDraftsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDraftsReq.ProtoReflect.Descriptor instead.
func (*DeleteDraftsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{61}
}

func (x *DeleteDraftsReq) GetInnerIds() []string {
	if x != nil {
		return x.InnerIds
	}
	return nil
}

func (x *DeleteDraftsReq) GetAdvertiseType() string {
	if x != nil {
		return x.AdvertiseType
	}
	return ""
}

type DeleteDraftsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *DeleteDraftsRsp) Reset() {
	*x = DeleteDraftsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDraftsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDraftsRsp) ProtoMessage() {}

func (x *DeleteDraftsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDraftsRsp.ProtoReflect.Descriptor instead.
func (*DeleteDraftsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{62}
}

func (x *DeleteDraftsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type GetPromotableUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
}

func (x *GetPromotableUserReq) Reset() {
	*x = GetPromotableUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPromotableUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPromotableUserReq) ProtoMessage() {}

func (x *GetPromotableUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPromotableUserReq.ProtoReflect.Descriptor instead.
func (*GetPromotableUserReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{63}
}

func (x *GetPromotableUserReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetPromotableUserReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetPromotableUserRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Users  []*User     `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *GetPromotableUserRsp) Reset() {
	*x = GetPromotableUserRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPromotableUserRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPromotableUserRsp) ProtoMessage() {}

func (x *GetPromotableUserRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPromotableUserRsp.ProtoReflect.Descriptor instead.
func (*GetPromotableUserRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{64}
}

func (x *GetPromotableUserRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetPromotableUserRsp) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetFollowersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
	Query     string `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`                          // 选填 查询关键字
}

func (x *GetFollowersReq) Reset() {
	*x = GetFollowersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersReq) ProtoMessage() {}

func (x *GetFollowersReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersReq.ProtoReflect.Descriptor instead.
func (*GetFollowersReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{65}
}

func (x *GetFollowersReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetFollowersReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetFollowersReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type GetFollowersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Followers []*Follower `protobuf:"bytes,2,rep,name=followers,proto3" json:"followers,omitempty"`
}

func (x *GetFollowersRsp) Reset() {
	*x = GetFollowersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFollowersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFollowersRsp) ProtoMessage() {}

func (x *GetFollowersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFollowersRsp.ProtoReflect.Descriptor instead.
func (*GetFollowersRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{66}
}

func (x *GetFollowersRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetFollowersRsp) GetFollowers() []*Follower {
	if x != nil {
		return x.Followers
	}
	return nil
}

type GetUsersReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识
	AccountId string   `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 账户id
	Ids       []string `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`                              // 必填，用户id
}

func (x *GetUsersReq) Reset() {
	*x = GetUsersReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersReq) ProtoMessage() {}

func (x *GetUsersReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersReq.ProtoReflect.Descriptor instead.
func (*GetUsersReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{67}
}

func (x *GetUsersReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetUsersReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetUsersReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type GetUsersRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Users  []*User     `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *GetUsersRsp) Reset() {
	*x = GetUsersRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUsersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersRsp) ProtoMessage() {}

func (x *GetUsersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersRsp.ProtoReflect.Descriptor instead.
func (*GetUsersRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{68}
}

func (x *GetUsersRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetUsersRsp) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

type StatusStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"` // 状态
	Num    int32  `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`      // 个数
}

func (x *StatusStatistic) Reset() {
	*x = StatusStatistic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusStatistic) ProtoMessage() {}

func (x *StatusStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusStatistic.ProtoReflect.Descriptor instead.
func (*StatusStatistic) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{69}
}

func (x *StatusStatistic) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StatusStatistic) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

type CampaignStatusStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AdGroupStatistics []*StatusStatistic `protobuf:"bytes,2,rep,name=ad_group_statistics,json=adGroupStatistics,proto3" json:"ad_group_statistics,omitempty"`
	AdStatistics      []*StatusStatistic `protobuf:"bytes,3,rep,name=ad_statistics,json=adStatistics,proto3" json:"ad_statistics,omitempty"`
}

func (x *CampaignStatusStatistics) Reset() {
	*x = CampaignStatusStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignStatusStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignStatusStatistics) ProtoMessage() {}

func (x *CampaignStatusStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignStatusStatistics.ProtoReflect.Descriptor instead.
func (*CampaignStatusStatistics) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{70}
}

func (x *CampaignStatusStatistics) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CampaignStatusStatistics) GetAdGroupStatistics() []*StatusStatistic {
	if x != nil {
		return x.AdGroupStatistics
	}
	return nil
}

func (x *CampaignStatusStatistics) GetAdStatistics() []*StatusStatistic {
	if x != nil {
		return x.AdStatistics
	}
	return nil
}

// 获取campaign子级状态统计, POST, /api/v1/twitter_advertise/get_campaign_status_statistics
type GetCampaignStatusStatisticsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 业务名
	InnerIds []string `protobuf:"bytes,2,rep,name=inner_ids,json=innerIds,proto3" json:"inner_ids,omitempty"` // 内部campaign id
	MediaIds []string `protobuf:"bytes,3,rep,name=media_ids,json=mediaIds,proto3" json:"media_ids,omitempty"` // 已发布campaign id
}

func (x *GetCampaignStatusStatisticsReq) Reset() {
	*x = GetCampaignStatusStatisticsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignStatusStatisticsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignStatusStatisticsReq) ProtoMessage() {}

func (x *GetCampaignStatusStatisticsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignStatusStatisticsReq.ProtoReflect.Descriptor instead.
func (*GetCampaignStatusStatisticsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{71}
}

func (x *GetCampaignStatusStatisticsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetCampaignStatusStatisticsReq) GetInnerIds() []string {
	if x != nil {
		return x.InnerIds
	}
	return nil
}

func (x *GetCampaignStatusStatisticsReq) GetMediaIds() []string {
	if x != nil {
		return x.MediaIds
	}
	return nil
}

type GetCampaignStatusStatisticsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          *aix.Result                 `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	InnerStatistics []*CampaignStatusStatistics `protobuf:"bytes,2,rep,name=inner_statistics,json=innerStatistics,proto3" json:"inner_statistics,omitempty"` // 状态统计结果列表, 结果与请求inner_ids顺序一致
	MediaStatistics []*CampaignStatusStatistics `protobuf:"bytes,3,rep,name=media_statistics,json=mediaStatistics,proto3" json:"media_statistics,omitempty"` // 状态统计结果列表, 结果与请求media_ids顺序一致
}

func (x *GetCampaignStatusStatisticsRsp) Reset() {
	*x = GetCampaignStatusStatisticsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignStatusStatisticsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignStatusStatisticsRsp) ProtoMessage() {}

func (x *GetCampaignStatusStatisticsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignStatusStatisticsRsp.ProtoReflect.Descriptor instead.
func (*GetCampaignStatusStatisticsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{72}
}

func (x *GetCampaignStatusStatisticsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetCampaignStatusStatisticsRsp) GetInnerStatistics() []*CampaignStatusStatistics {
	if x != nil {
		return x.InnerStatistics
	}
	return nil
}

func (x *GetCampaignStatusStatisticsRsp) GetMediaStatistics() []*CampaignStatusStatistics {
	if x != nil {
		return x.MediaStatistics
	}
	return nil
}

type AdGroupStatusStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AdStatistics []*StatusStatistic `protobuf:"bytes,2,rep,name=ad_statistics,json=adStatistics,proto3" json:"ad_statistics,omitempty"`
}

func (x *AdGroupStatusStatistics) Reset() {
	*x = AdGroupStatusStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdGroupStatusStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdGroupStatusStatistics) ProtoMessage() {}

func (x *AdGroupStatusStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdGroupStatusStatistics.ProtoReflect.Descriptor instead.
func (*AdGroupStatusStatistics) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{73}
}

func (x *AdGroupStatusStatistics) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AdGroupStatusStatistics) GetAdStatistics() []*StatusStatistic {
	if x != nil {
		return x.AdStatistics
	}
	return nil
}

// 获取ad_group子级状态统计, POST, /api/v1/twitter_advertise/get_ad_group_status_statistics
type GetAdGroupStatusStatisticsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 业务名
	InnerIds []string `protobuf:"bytes,2,rep,name=inner_ids,json=innerIds,proto3" json:"inner_ids,omitempty"` // 内部ad_group id
	MediaIds []string `protobuf:"bytes,3,rep,name=media_ids,json=mediaIds,proto3" json:"media_ids,omitempty"` // 已发布ad_group id
}

func (x *GetAdGroupStatusStatisticsReq) Reset() {
	*x = GetAdGroupStatusStatisticsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdGroupStatusStatisticsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdGroupStatusStatisticsReq) ProtoMessage() {}

func (x *GetAdGroupStatusStatisticsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdGroupStatusStatisticsReq.ProtoReflect.Descriptor instead.
func (*GetAdGroupStatusStatisticsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{74}
}

func (x *GetAdGroupStatusStatisticsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetAdGroupStatusStatisticsReq) GetInnerIds() []string {
	if x != nil {
		return x.InnerIds
	}
	return nil
}

func (x *GetAdGroupStatusStatisticsReq) GetMediaIds() []string {
	if x != nil {
		return x.MediaIds
	}
	return nil
}

type GetAdGroupStatusStatisticsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          *aix.Result                `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	InnerStatistics []*AdGroupStatusStatistics `protobuf:"bytes,2,rep,name=inner_statistics,json=innerStatistics,proto3" json:"inner_statistics,omitempty"` // 状态统计结果列表, 结果与请求inner_ids顺序一致
	MediaStatistics []*AdGroupStatusStatistics `protobuf:"bytes,3,rep,name=media_statistics,json=mediaStatistics,proto3" json:"media_statistics,omitempty"` // 状态统计结果列表, 结果与请求media_ids顺序一致
}

func (x *GetAdGroupStatusStatisticsRsp) Reset() {
	*x = GetAdGroupStatusStatisticsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdGroupStatusStatisticsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdGroupStatusStatisticsRsp) ProtoMessage() {}

func (x *GetAdGroupStatusStatisticsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdGroupStatusStatisticsRsp.ProtoReflect.Descriptor instead.
func (*GetAdGroupStatusStatisticsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{75}
}

func (x *GetAdGroupStatusStatisticsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAdGroupStatusStatisticsRsp) GetInnerStatistics() []*AdGroupStatusStatistics {
	if x != nil {
		return x.InnerStatistics
	}
	return nil
}

func (x *GetAdGroupStatusStatisticsRsp) GetMediaStatistics() []*AdGroupStatusStatistics {
	if x != nil {
		return x.MediaStatistics
	}
	return nil
}

type SqlCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Column   string   `protobuf:"bytes,1,opt,name=column,proto3" json:"column,omitempty"`
	Operator string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"` // 目前只支持: IN, =, LIKE, OR_IN; 条件之间是 AND
	Value    []string `protobuf:"bytes,3,rep,name=value,proto3" json:"value,omitempty"`
}

func (x *SqlCondition) Reset() {
	*x = SqlCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SqlCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SqlCondition) ProtoMessage() {}

func (x *SqlCondition) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SqlCondition.ProtoReflect.Descriptor instead.
func (*SqlCondition) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{76}
}

func (x *SqlCondition) GetColumn() string {
	if x != nil {
		return x.Column
	}
	return ""
}

func (x *SqlCondition) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *SqlCondition) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

// 拉取草稿数据, POST, /api/v1/twitter_advertise/get_drafts
type GetDraftsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Conditions    []*SqlCondition `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"`
	Offset        int32           `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`                                   // 拉取偏移
	Limit         int32           `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`                                     // 拉取条数
	AdvertiseType string          `protobuf:"bytes,4,opt,name=advertise_type,json=advertiseType,proto3" json:"advertise_type,omitempty"` // 类型：campaign, ad_group, ad
}

func (x *GetDraftsReq) Reset() {
	*x = GetDraftsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDraftsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDraftsReq) ProtoMessage() {}

func (x *GetDraftsReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDraftsReq.ProtoReflect.Descriptor instead.
func (*GetDraftsReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{77}
}

func (x *GetDraftsReq) GetConditions() []*SqlCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *GetDraftsReq) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetDraftsReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetDraftsReq) GetAdvertiseType() string {
	if x != nil {
		return x.AdvertiseType
	}
	return ""
}

// 草稿数据
type DraftView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode        string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                         // 业务名
	AccountId       string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                      // account账号
	InnerCampaignId string `protobuf:"bytes,3,opt,name=inner_campaign_id,json=innerCampaignId,proto3" json:"inner_campaign_id,omitempty"`  // 内部 campaign_id
	MediaCampaignId string `protobuf:"bytes,4,opt,name=media_campaign_id,json=mediaCampaignId,proto3" json:"media_campaign_id,omitempty"`  // 渠道 campaign_id
	CampaignName    string `protobuf:"bytes,5,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`             // campaign 名
	Region          string `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`                                             // campaign 投放地区缩写
	Country         string `protobuf:"bytes,7,opt,name=country,proto3" json:"country,omitempty"`                                           // campaign 投放国家缩写
	Platform        string `protobuf:"bytes,8,opt,name=platform,proto3" json:"platform,omitempty"`                                         // Campaign 投放的系统, 比如ios, and, pc, all
	CampaignStatus  string `protobuf:"bytes,9,opt,name=campaign_status,json=campaignStatus,proto3" json:"campaign_status,omitempty"`       // campaign 状态
	InnerAdGroupId  string `protobuf:"bytes,10,opt,name=inner_ad_group_id,json=innerAdGroupId,proto3" json:"inner_ad_group_id,omitempty"`  // 内部 ad_group_id
	MediaAdGroupId  string `protobuf:"bytes,11,opt,name=media_ad_group_id,json=mediaAdGroupId,proto3" json:"media_ad_group_id,omitempty"`  // 渠道 ad_group_id
	AdGroupName     string `protobuf:"bytes,12,opt,name=ad_group_name,json=adGroupName,proto3" json:"ad_group_name,omitempty"`             // ad_group 名
	AdGroupStatus   string `protobuf:"bytes,13,opt,name=ad_group_status,json=adGroupStatus,proto3" json:"ad_group_status,omitempty"`       // ad_group 状态
	InnerAdId       string `protobuf:"bytes,14,opt,name=inner_ad_id,json=innerAdId,proto3" json:"inner_ad_id,omitempty"`                   // 内部 ad_id
	MediaAdId       string `protobuf:"bytes,15,opt,name=media_ad_id,json=mediaAdId,proto3" json:"media_ad_id,omitempty"`                   // 渠道 ad_id
	AdName          string `protobuf:"bytes,16,opt,name=ad_name,json=adName,proto3" json:"ad_name,omitempty"`                              // ad 名
	AdStatus        string `protobuf:"bytes,17,opt,name=ad_status,json=adStatus,proto3" json:"ad_status,omitempty"`                        // ad 状态
	AixCampaignType string `protobuf:"bytes,18,opt,name=aix_campaign_type,json=aixCampaignType,proto3" json:"aix_campaign_type,omitempty"` // aix campaign type(objective)
}

func (x *DraftView) Reset() {
	*x = DraftView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DraftView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DraftView) ProtoMessage() {}

func (x *DraftView) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DraftView.ProtoReflect.Descriptor instead.
func (*DraftView) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{78}
}

func (x *DraftView) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *DraftView) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DraftView) GetInnerCampaignId() string {
	if x != nil {
		return x.InnerCampaignId
	}
	return ""
}

func (x *DraftView) GetMediaCampaignId() string {
	if x != nil {
		return x.MediaCampaignId
	}
	return ""
}

func (x *DraftView) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *DraftView) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *DraftView) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *DraftView) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *DraftView) GetCampaignStatus() string {
	if x != nil {
		return x.CampaignStatus
	}
	return ""
}

func (x *DraftView) GetInnerAdGroupId() string {
	if x != nil {
		return x.InnerAdGroupId
	}
	return ""
}

func (x *DraftView) GetMediaAdGroupId() string {
	if x != nil {
		return x.MediaAdGroupId
	}
	return ""
}

func (x *DraftView) GetAdGroupName() string {
	if x != nil {
		return x.AdGroupName
	}
	return ""
}

func (x *DraftView) GetAdGroupStatus() string {
	if x != nil {
		return x.AdGroupStatus
	}
	return ""
}

func (x *DraftView) GetInnerAdId() string {
	if x != nil {
		return x.InnerAdId
	}
	return ""
}

func (x *DraftView) GetMediaAdId() string {
	if x != nil {
		return x.MediaAdId
	}
	return ""
}

func (x *DraftView) GetAdName() string {
	if x != nil {
		return x.AdName
	}
	return ""
}

func (x *DraftView) GetAdStatus() string {
	if x != nil {
		return x.AdStatus
	}
	return ""
}

func (x *DraftView) GetAixCampaignType() string {
	if x != nil {
		return x.AixCampaignType
	}
	return ""
}

type GetDraftsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Drafts []*DraftView `protobuf:"bytes,2,rep,name=drafts,proto3" json:"drafts,omitempty"` // 草稿列表
}

func (x *GetDraftsRsp) Reset() {
	*x = GetDraftsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDraftsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDraftsRsp) ProtoMessage() {}

func (x *GetDraftsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDraftsRsp.ProtoReflect.Descriptor instead.
func (*GetDraftsRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{79}
}

func (x *GetDraftsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetDraftsRsp) GetDrafts() []*DraftView {
	if x != nil {
		return x.Drafts
	}
	return nil
}

type AdvertisingStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InnerId   string `protobuf:"bytes,1,opt,name=inner_id,json=innerId,proto3" json:"inner_id,omitempty"`
	MediaId   string `protobuf:"bytes,2,opt,name=media_id,json=mediaId,proto3" json:"media_id,omitempty"`
	Status    string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"` // 参照枚举 Status
	AccountId string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
}

func (x *AdvertisingStatus) Reset() {
	*x = AdvertisingStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdvertisingStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvertisingStatus) ProtoMessage() {}

func (x *AdvertisingStatus) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvertisingStatus.ProtoReflect.Descriptor instead.
func (*AdvertisingStatus) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{80}
}

func (x *AdvertisingStatus) GetInnerId() string {
	if x != nil {
		return x.InnerId
	}
	return ""
}

func (x *AdvertisingStatus) GetMediaId() string {
	if x != nil {
		return x.MediaId
	}
	return ""
}

func (x *AdvertisingStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AdvertisingStatus) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

// 更新已发布广告状态, POST, /api/v1/twitter_advertise/change_published_advertising_status
type ChangePublishedAdvertisingStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode      string               `protobuf:"bytes,1,opt,name=gameCode,proto3" json:"gameCode,omitempty"`
	Status        []*AdvertisingStatus `protobuf:"bytes,2,rep,name=status,proto3" json:"status,omitempty"`
	AdvertiseType string               `protobuf:"bytes,3,opt,name=advertise_type,json=advertiseType,proto3" json:"advertise_type,omitempty"` // 类型：campaign, ad_group, ad
}

func (x *ChangePublishedAdvertisingStatusReq) Reset() {
	*x = ChangePublishedAdvertisingStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePublishedAdvertisingStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePublishedAdvertisingStatusReq) ProtoMessage() {}

func (x *ChangePublishedAdvertisingStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePublishedAdvertisingStatusReq.ProtoReflect.Descriptor instead.
func (*ChangePublishedAdvertisingStatusReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{81}
}

func (x *ChangePublishedAdvertisingStatusReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *ChangePublishedAdvertisingStatusReq) GetStatus() []*AdvertisingStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ChangePublishedAdvertisingStatusReq) GetAdvertiseType() string {
	if x != nil {
		return x.AdvertiseType
	}
	return ""
}

type ChangePublishedAdvertisingStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result          `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Status []*AdvertisingStatus `protobuf:"bytes,2,rep,name=status,proto3" json:"status,omitempty"` // 返回修改成功的campaign状态
}

func (x *ChangePublishedAdvertisingStatusRsp) Reset() {
	*x = ChangePublishedAdvertisingStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePublishedAdvertisingStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePublishedAdvertisingStatusRsp) ProtoMessage() {}

func (x *ChangePublishedAdvertisingStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePublishedAdvertisingStatusRsp.ProtoReflect.Descriptor instead.
func (*ChangePublishedAdvertisingStatusRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{82}
}

func (x *ChangePublishedAdvertisingStatusRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ChangePublishedAdvertisingStatusRsp) GetStatus() []*AdvertisingStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

// 创建campaign 树, POST, /api/v1/twitter_advertise/create_campaign_tree
type CreateCampaignTreeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CampaignTree *Campaign `protobuf:"bytes,1,opt,name=campaign_tree,json=campaignTree,proto3" json:"campaign_tree,omitempty"`
}

func (x *CreateCampaignTreeReq) Reset() {
	*x = CreateCampaignTreeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCampaignTreeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCampaignTreeReq) ProtoMessage() {}

func (x *CreateCampaignTreeReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCampaignTreeReq.ProtoReflect.Descriptor instead.
func (*CreateCampaignTreeReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{83}
}

func (x *CreateCampaignTreeReq) GetCampaignTree() *Campaign {
	if x != nil {
		return x.CampaignTree
	}
	return nil
}

type CreateCampaignTreeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result       *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	CampaignTree *Campaign   `protobuf:"bytes,2,opt,name=campaign_tree,json=campaignTree,proto3" json:"campaign_tree,omitempty"` // 创建成果的campaign树形结构
}

func (x *CreateCampaignTreeRsp) Reset() {
	*x = CreateCampaignTreeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCampaignTreeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCampaignTreeRsp) ProtoMessage() {}

func (x *CreateCampaignTreeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCampaignTreeRsp.ProtoReflect.Descriptor instead.
func (*CreateCampaignTreeRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{84}
}

func (x *CreateCampaignTreeRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CreateCampaignTreeRsp) GetCampaignTree() *Campaign {
	if x != nil {
		return x.CampaignTree
	}
	return nil
}

// 创建ad_group 树, POST, /api/v1/twitter_advertise/create_ad_group_tree
type CreateAdGroupTreeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdGroupTree *AdGroup `protobuf:"bytes,1,opt,name=ad_group_tree,json=adGroupTree,proto3" json:"ad_group_tree,omitempty"`
}

func (x *CreateAdGroupTreeReq) Reset() {
	*x = CreateAdGroupTreeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAdGroupTreeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdGroupTreeReq) ProtoMessage() {}

func (x *CreateAdGroupTreeReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdGroupTreeReq.ProtoReflect.Descriptor instead.
func (*CreateAdGroupTreeReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{85}
}

func (x *CreateAdGroupTreeReq) GetAdGroupTree() *AdGroup {
	if x != nil {
		return x.AdGroupTree
	}
	return nil
}

type CreateAdGroupTreeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result      *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	AdGroupTree *AdGroup    `protobuf:"bytes,2,opt,name=ad_group_tree,json=adGroupTree,proto3" json:"ad_group_tree,omitempty"`
}

func (x *CreateAdGroupTreeRsp) Reset() {
	*x = CreateAdGroupTreeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAdGroupTreeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAdGroupTreeRsp) ProtoMessage() {}

func (x *CreateAdGroupTreeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAdGroupTreeRsp.ProtoReflect.Descriptor instead.
func (*CreateAdGroupTreeRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{86}
}

func (x *CreateAdGroupTreeRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CreateAdGroupTreeRsp) GetAdGroupTree() *AdGroup {
	if x != nil {
		return x.AdGroupTree
	}
	return nil
}

type CancelPublishingReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode      string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                // 游戏业务
	InnerId       string `protobuf:"bytes,2,opt,name=inner_id,json=innerId,proto3" json:"inner_id,omitempty"`                   // inner_id
	AdvertiseType string `protobuf:"bytes,3,opt,name=advertise_type,json=advertiseType,proto3" json:"advertise_type,omitempty"` // 广告类型
}

func (x *CancelPublishingReq) Reset() {
	*x = CancelPublishingReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPublishingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPublishingReq) ProtoMessage() {}

func (x *CancelPublishingReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPublishingReq.ProtoReflect.Descriptor instead.
func (*CancelPublishingReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{87}
}

func (x *CancelPublishingReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CancelPublishingReq) GetInnerId() string {
	if x != nil {
		return x.InnerId
	}
	return ""
}

func (x *CancelPublishingReq) GetAdvertiseType() string {
	if x != nil {
		return x.AdvertiseType
	}
	return ""
}

type CancelPublishingRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *CancelPublishingRsp) Reset() {
	*x = CancelPublishingRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPublishingRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPublishingRsp) ProtoMessage() {}

func (x *CancelPublishingRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPublishingRsp.ProtoReflect.Descriptor instead.
func (*CancelPublishingRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{88}
}

func (x *CancelPublishingRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type CopyCampaignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode        string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                        // 游戏业务
	AccountId       string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                     // 渠道账号id
	InnerCampaignId string `protobuf:"bytes,3,opt,name=inner_campaign_id,json=innerCampaignId,proto3" json:"inner_campaign_id,omitempty"` // 复制 内部 inner_campaign_id
	CampaignId      string `protobuf:"bytes,4,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`                  // 复制 渠道 campaign_id
	CopyType        string `protobuf:"bytes,5,opt,name=copy_type,json=copyType,proto3" json:"copy_type,omitempty"`                        // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

func (x *CopyCampaignReq) Reset() {
	*x = CopyCampaignReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyCampaignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyCampaignReq) ProtoMessage() {}

func (x *CopyCampaignReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyCampaignReq.ProtoReflect.Descriptor instead.
func (*CopyCampaignReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{89}
}

func (x *CopyCampaignReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CopyCampaignReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CopyCampaignReq) GetInnerCampaignId() string {
	if x != nil {
		return x.InnerCampaignId
	}
	return ""
}

func (x *CopyCampaignReq) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *CopyCampaignReq) GetCopyType() string {
	if x != nil {
		return x.CopyType
	}
	return ""
}

type CopyCampaignRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	InnerCampaignId string      `protobuf:"bytes,2,opt,name=inner_campaign_id,json=innerCampaignId,proto3" json:"inner_campaign_id,omitempty"` // 生成复制后的草稿 inner_campaign_id
}

func (x *CopyCampaignRsp) Reset() {
	*x = CopyCampaignRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyCampaignRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyCampaignRsp) ProtoMessage() {}

func (x *CopyCampaignRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyCampaignRsp.ProtoReflect.Descriptor instead.
func (*CopyCampaignRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{90}
}

func (x *CopyCampaignRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CopyCampaignRsp) GetInnerCampaignId() string {
	if x != nil {
		return x.InnerCampaignId
	}
	return ""
}

type CopyAdgroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode       string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                     // 游戏业务
	AccountId      string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                  // 渠道账号id
	InnerAdgroupId string `protobuf:"bytes,3,opt,name=inner_adgroup_id,json=innerAdgroupId,proto3" json:"inner_adgroup_id,omitempty"` // 复制 内部 inner_adgroup_id
	AdgroupId      string `protobuf:"bytes,4,opt,name=adgroup_id,json=adgroupId,proto3" json:"adgroup_id,omitempty"`                  // 复制 渠道 adgroup_id
	CopyType       string `protobuf:"bytes,5,opt,name=copy_type,json=copyType,proto3" json:"copy_type,omitempty"`                     // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

func (x *CopyAdgroupReq) Reset() {
	*x = CopyAdgroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAdgroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAdgroupReq) ProtoMessage() {}

func (x *CopyAdgroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAdgroupReq.ProtoReflect.Descriptor instead.
func (*CopyAdgroupReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{91}
}

func (x *CopyAdgroupReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CopyAdgroupReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CopyAdgroupReq) GetInnerAdgroupId() string {
	if x != nil {
		return x.InnerAdgroupId
	}
	return ""
}

func (x *CopyAdgroupReq) GetAdgroupId() string {
	if x != nil {
		return x.AdgroupId
	}
	return ""
}

func (x *CopyAdgroupReq) GetCopyType() string {
	if x != nil {
		return x.CopyType
	}
	return ""
}

type CopyAdgroupRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result         *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	InnerAdgroupId string      `protobuf:"bytes,2,opt,name=inner_adgroup_id,json=innerAdgroupId,proto3" json:"inner_adgroup_id,omitempty"` // 生成复制后的草稿 inner_adgroup_id
}

func (x *CopyAdgroupRsp) Reset() {
	*x = CopyAdgroupRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAdgroupRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAdgroupRsp) ProtoMessage() {}

func (x *CopyAdgroupRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAdgroupRsp.ProtoReflect.Descriptor instead.
func (*CopyAdgroupRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{92}
}

func (x *CopyAdgroupRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CopyAdgroupRsp) GetInnerAdgroupId() string {
	if x != nil {
		return x.InnerAdgroupId
	}
	return ""
}

type CopyAdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`      // 游戏业务
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`   // 渠道账号id
	InnerAdId string `protobuf:"bytes,3,opt,name=inner_ad_id,json=innerAdId,proto3" json:"inner_ad_id,omitempty"` // 复制 内部 inner_ad_id
	AdId      string `protobuf:"bytes,4,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                  // 复制 渠道 ad_id
}

func (x *CopyAdReq) Reset() {
	*x = CopyAdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAdReq) ProtoMessage() {}

func (x *CopyAdReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAdReq.ProtoReflect.Descriptor instead.
func (*CopyAdReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{93}
}

func (x *CopyAdReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CopyAdReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CopyAdReq) GetInnerAdId() string {
	if x != nil {
		return x.InnerAdId
	}
	return ""
}

func (x *CopyAdReq) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

type CopyAdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	InnerAdId string      `protobuf:"bytes,2,opt,name=inner_ad_id,json=innerAdId,proto3" json:"inner_ad_id,omitempty"` //  生成复制后的草稿 inner_ad_id
}

func (x *CopyAdRsp) Reset() {
	*x = CopyAdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyAdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyAdRsp) ProtoMessage() {}

func (x *CopyAdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyAdRsp.ProtoReflect.Descriptor instead.
func (*CopyAdRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{94}
}

func (x *CopyAdRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CopyAdRsp) GetInnerAdId() string {
	if x != nil {
		return x.InnerAdId
	}
	return ""
}

// 手动触发属性数据同步
type SyncStrategyDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCodes  []string `protobuf:"bytes,1,rep,name=game_codes,json=gameCodes,proto3" json:"game_codes,omitempty"`
	AccountIds []string `protobuf:"bytes,2,rep,name=account_ids,json=accountIds,proto3" json:"account_ids,omitempty"`
}

func (x *SyncStrategyDataReq) Reset() {
	*x = SyncStrategyDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncStrategyDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStrategyDataReq) ProtoMessage() {}

func (x *SyncStrategyDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStrategyDataReq.ProtoReflect.Descriptor instead.
func (*SyncStrategyDataReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{95}
}

func (x *SyncStrategyDataReq) GetGameCodes() []string {
	if x != nil {
		return x.GameCodes
	}
	return nil
}

func (x *SyncStrategyDataReq) GetAccountIds() []string {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

type SyncStrategyDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SyncStrategyDataRsp) Reset() {
	*x = SyncStrategyDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncStrategyDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStrategyDataRsp) ProtoMessage() {}

func (x *SyncStrategyDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStrategyDataRsp.ProtoReflect.Descriptor instead.
func (*SyncStrategyDataRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{96}
}

func (x *SyncStrategyDataRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 拉取渠道图片视频素材信息 POST, /api/v1/twitter_advertise/get_media_image_video
type GetMediaImageVideoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识game_code
	AccountId string   `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 渠道账号id
	ImageIds  []string `protobuf:"bytes,3,rep,name=image_ids,json=imageIds,proto3" json:"image_ids,omitempty"`    // 图片id列表
	VideoIds  []string `protobuf:"bytes,4,rep,name=video_ids,json=videoIds,proto3" json:"video_ids,omitempty"`    // 视频id列表
}

func (x *GetMediaImageVideoReq) Reset() {
	*x = GetMediaImageVideoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaImageVideoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaImageVideoReq) ProtoMessage() {}

func (x *GetMediaImageVideoReq) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaImageVideoReq.ProtoReflect.Descriptor instead.
func (*GetMediaImageVideoReq) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{97}
}

func (x *GetMediaImageVideoReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetMediaImageVideoReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetMediaImageVideoReq) GetImageIds() []string {
	if x != nil {
		return x.ImageIds
	}
	return nil
}

func (x *GetMediaImageVideoReq) GetVideoIds() []string {
	if x != nil {
		return x.VideoIds
	}
	return nil
}

// 图片视频信息
type ImageVideo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                         // 图片视频id
	Type         int32  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                                    // 1-视频，2-图片
	Name         string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                     // 名称
	Width        int32  `protobuf:"varint,4,opt,name=width,proto3" json:"width,omitempty"`                                  // 宽
	Height       int32  `protobuf:"varint,5,opt,name=height,proto3" json:"height,omitempty"`                                // 高
	ThumbnailUrl string `protobuf:"bytes,6,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"` // 缩略图
	VideoUrl     string `protobuf:"bytes,7,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`             // 视频预览地址
}

func (x *ImageVideo) Reset() {
	*x = ImageVideo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageVideo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageVideo) ProtoMessage() {}

func (x *ImageVideo) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageVideo.ProtoReflect.Descriptor instead.
func (*ImageVideo) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{98}
}

func (x *ImageVideo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ImageVideo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ImageVideo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ImageVideo) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ImageVideo) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *ImageVideo) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *ImageVideo) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

type GetMediaImageVideoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result   `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	TraceId string        `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
	Assets  []*ImageVideo `protobuf:"bytes,3,rep,name=assets,proto3" json:"assets,omitempty"`                  // 素材信息
}

func (x *GetMediaImageVideoRsp) Reset() {
	*x = GetMediaImageVideoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaImageVideoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaImageVideoRsp) ProtoMessage() {}

func (x *GetMediaImageVideoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_twitter_advertise_twitter_advertise_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaImageVideoRsp.ProtoReflect.Descriptor instead.
func (*GetMediaImageVideoRsp) Descriptor() ([]byte, []int) {
	return file_twitter_advertise_twitter_advertise_proto_rawDescGZIP(), []int{99}
}

func (x *GetMediaImageVideoRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetMediaImageVideoRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *GetMediaImageVideoRsp) GetAssets() []*ImageVideo {
	if x != nil {
		return x.Assets
	}
	return nil
}

var File_twitter_advertise_twitter_advertise_proto protoreflect.FileDescriptor

var file_twitter_advertise_twitter_advertise_proto_rawDesc = []byte{
	0x0a, 0x29, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x2f, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x74, 0x77, 0x69,
	0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x1a, 0x23,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2f, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x77, 0x0a, 0x0c, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xae, 0x05, 0x0a, 0x08, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x75, 0x64, 0x67, 0x65,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x42, 0x75, 0x64, 0x67, 0x65,
	0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x62, 0x75, 0x64, 0x67, 0x65,
	0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69,
	0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x75, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72,
	0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x61, 0x64,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x08, 0x61, 0x64, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x22, 0xd3, 0x09, 0x0a, 0x07, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x29, 0x0a, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x61, 0x64, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x3f, 0x0a,
	0x1c, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x19, 0x61, 0x6e, 0x64, 0x72, 0x6f, 0x69, 0x64, 0x41, 0x70, 0x70, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x37,
	0x0a, 0x18, 0x69, 0x6f, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x69, 0x6f, 0x73, 0x41, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x47, 0x6f, 0x61, 0x6c, 0x12, 0x4a, 0x0a, 0x0f, 0x62, 0x75, 0x64, 0x67,
	0x65, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x0e, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72,
	0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x52, 0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x12, 0x43, 0x0a,
	0x0c, 0x64, 0x65, 0x6d, 0x6f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x69, 0x63, 0x73, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x6d, 0x6f, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x69, 0x63, 0x73, 0x52, 0x0c, 0x64, 0x65, 0x6d, 0x6f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x69,
	0x63, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52,
	0x07, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x09, 0x61, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77,
	0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x53, 0x0a, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x63,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x12, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6c,
	0x61, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x50, 0x0a, 0x11, 0x61, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x10, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x15,
	0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x77, 0x65, 0x62, 0x5f, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x57, 0x65, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x12,
	0x23, 0x0a, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x12, 0x27, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x22, 0xaa, 0x01, 0x0a, 0x0e, 0x42, 0x75,
	0x64, 0x67, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x13,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x64, 0x61, 0x69, 0x6c, 0x79,
	0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x08, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x6f, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x67, 0x6f, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x69, 0x64, 0x5f, 0x73,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x69, 0x64, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x69,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x61, 0x79,
	0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x42, 0x79,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x74, 0x61,
	0x6e, 0x64, 0x61, 0x72, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x22, 0xb1, 0x01,
	0x0a, 0x0c, 0x44, 0x65, 0x6d, 0x6f, 0x67, 0x72, 0x61, 0x70, 0x68, 0x69, 0x63, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x41, 0x67, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x67, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x22, 0x8c, 0x01, 0x0a, 0x07, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x69, 0x66, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x77, 0x69, 0x66, 0x69, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73,
	0x22, 0x5d, 0x0a, 0x08, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x69,
	0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x6f, 0x6b, 0x5f, 0x61, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6c, 0x6f, 0x6f, 0x6b, 0x41, 0x6c, 0x69, 0x6b, 0x65, 0x22,
	0xa5, 0x01, 0x0a, 0x11, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x65, 0x79, 0x5f, 0x77, 0x6f, 0x72,
	0x64, 0x73, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0f, 0x6b, 0x65, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6b, 0x65, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x5f, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6b, 0x65,
	0x79, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x22, 0xa7, 0x04, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x29,
	0x0a, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x11, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x5f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x64, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x41, 0x64, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x61, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x41, 0x64, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x77, 0x65, 0x65, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x54, 0x77, 0x65, 0x65, 0x74, 0x52, 0x05, 0x74, 0x77, 0x65, 0x65,
	0x74, 0x22, 0x86, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x41, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x6f, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6f, 0x73, 0x41, 0x70, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x92, 0x01, 0x0a, 0x08, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x64, 0x5f, 0x73, 0x74,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x64, 0x53, 0x74, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x22,
	0x95, 0x01, 0x0a, 0x10, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x5f,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x72, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x17,
	0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x22, 0x93, 0x01, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x9d, 0x03,
	0x0a, 0x05, 0x54, 0x77, 0x65, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x77, 0x65, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x74, 0x77, 0x65, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x6f, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61,
	0x6c, 0x6c, 0x54, 0x6f, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x70,
	0x70, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x73, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x53, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x77, 0x69,
	0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x22, 0xe4, 0x01,
	0x0a, 0x09, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x28,
	0x0a, 0x10, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50,
	0x6f, 0x73, 0x74, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x65, 0x61, 0x64,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x65, 0x61,
	0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x73,
	0x69, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x69,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30,
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x32, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3b,
	0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x81,
	0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69,
	0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x73, 0x22, 0x7e, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0c, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x73, 0x22, 0x7e, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x61, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74,
	0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x08, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x22, 0x6e, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x64, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x61, 0x64, 0x49,
	0x64, 0x73, 0x22, 0x69, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x64, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x27, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x22, 0x4c, 0x0a,
	0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x22, 0x71, 0x0a, 0x11, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x22, 0x49,
	0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x12, 0x35, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x07, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x6e, 0x0a, 0x10, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x07, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x34, 0x0a, 0x0b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x64, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x02, 0x61, 0x64, 0x22,
	0x59, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x64, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52, 0x02, 0x61, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x53, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x22, 0x75, 0x0a, 0x13, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x73, 0x22, 0x64, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x71, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x52, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x22, 0x61, 0x0a, 0x10, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x35,
	0x0a, 0x08, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x07, 0x61, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x6e, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x07, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x4c, 0x0a,
	0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x02,
	0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x52,
	0x02, 0x61, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0x59, 0x0a, 0x0b, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x25, 0x0a, 0x02, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x77,
	0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e,
	0x41, 0x64, 0x52, 0x02, 0x61, 0x64, 0x22, 0x6e, 0x0a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x41, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67,
	0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x6e,
	0x65, 0x72, 0x41, 0x64, 0x49, 0x64, 0x22, 0xae, 0x01, 0x0a, 0x10, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x41, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69,
	0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x11,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x5f, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x41, 0x64, 0x49, 0x64, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x46, 0x75,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x46, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4d, 0x0a, 0x0f, 0x66, 0x75, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x22, 0x4f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x68, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x04, 0x61, 0x70, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x61,
	0x70, 0x70, 0x73, 0x22, 0x4d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x71, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x63, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x71, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x39, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x22, 0x4e, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x76, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x0b, 0x6f, 0x73, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74, 0x77,
	0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e,
	0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6f, 0x73, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x63, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x77, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x3f, 0x0a, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x22, 0x63, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x71, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69,
	0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x39, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x22, 0x50, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x57, 0x65, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x7f, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x57, 0x65, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x44, 0x0a, 0x0e, 0x77, 0x65, 0x62, 0x5f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x57, 0x65, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67,
	0x52, 0x0c, 0x77, 0x65, 0x62, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x73, 0x22, 0x3d,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x42, 0x69, 0x7a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61, 0x74, 0x65, 0x6c, 0x6f, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x74, 0x65, 0x6c, 0x6f, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x63, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x42, 0x69, 0x7a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x30, 0x0a, 0x09, 0x52, 0x65, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x55, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x72,
	0x61, 0x66, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x36, 0x0a, 0x0f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x72, 0x61, 0x66, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x6d, 0x6f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x22, 0x63, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x22, 0x71, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69,
	0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x39, 0x0a, 0x09, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72,
	0x52, 0x09, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x73, 0x22, 0x5b, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61,
	0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67,
	0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x61, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x05,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x77,
	0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x3b, 0x0a, 0x0f, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0xc7, 0x01, 0x0a, 0x18, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x52, 0x0a, 0x13, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x11, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x47, 0x0a, 0x0d, 0x61, 0x64, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x52, 0x0c, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x22, 0x77, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64, 0x73, 0x22, 0xf5, 0x01, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x10, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x0f, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x56, 0x0a, 0x10, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x52, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x22, 0x72, 0x0a, 0x17, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x47,
	0x0a, 0x0d, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x0c, 0x61, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x22, 0x76, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64, 0x73, 0x22,
	0xf2, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x55, 0x0a, 0x10, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x0f, 0x69, 0x6e,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x55, 0x0a,
	0x10, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x22, 0x58, 0x0a, 0x0c, 0x53, 0x71, 0x6c, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xa4,
	0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x44, 0x72, 0x61, 0x66, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x3f, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x53, 0x71, 0x6c, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xff, 0x04, 0x0a, 0x09, 0x44, 0x72, 0x61, 0x66, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x69, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x29,
	0x0a, 0x11, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x41, 0x64, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x61,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x41, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x61,
	0x69, 0x78, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x69, 0x78, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x69, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x44, 0x72,
	0x61, 0x66, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x06,
	0x64, 0x72, 0x61, 0x66, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x74,
	0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x2e, 0x44, 0x72, 0x61, 0x66, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x64, 0x72, 0x61, 0x66,
	0x74, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x11, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa6, 0x01, 0x0a, 0x23, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x77, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x88,
	0x01, 0x0a, 0x23, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x77,
	0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x59, 0x0a, 0x15, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x40, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x74,
	0x72, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x54, 0x72, 0x65, 0x65, 0x22, 0x7e, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x74,
	0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x74, 0x77, 0x69, 0x74,
	0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x54, 0x72, 0x65, 0x65, 0x22, 0x56, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3e, 0x0a, 0x0d,
	0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x72, 0x65, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x0b, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x72, 0x65, 0x65, 0x22, 0x7b, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x72, 0x65,
	0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x61, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0b, 0x61, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x72, 0x65, 0x65, 0x22, 0x74, 0x0a, 0x13, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x3a, 0x0a, 0x13, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x69, 0x6e, 0x67, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x0f,
	0x43, 0x6f, 0x70, 0x79, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x69,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x70,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x62, 0x0a, 0x0f, 0x43, 0x6f, 0x70, 0x79, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a, 0x0a,
	0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x22, 0xb2, 0x01, 0x0a, 0x0e, 0x43, 0x6f,
	0x70, 0x79, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x70, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x70, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5f,
	0x0a, 0x0e, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61,
	0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22,
	0x7c, 0x0a, 0x09, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x22, 0x50, 0x0a,
	0x09, 0x43, 0x6f, 0x70, 0x79, 0x41, 0x64, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x1e, 0x0a, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x64, 0x49, 0x64, 0x22,
	0x55, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x3a, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74,
	0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x49,
	0x64, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x0a, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x68, 0x75, 0x6d, 0x62,
	0x6e, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0x8e, 0x01, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x77, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x77, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_twitter_advertise_twitter_advertise_proto_rawDescOnce sync.Once
	file_twitter_advertise_twitter_advertise_proto_rawDescData = file_twitter_advertise_twitter_advertise_proto_rawDesc
)

func file_twitter_advertise_twitter_advertise_proto_rawDescGZIP() []byte {
	file_twitter_advertise_twitter_advertise_proto_rawDescOnce.Do(func() {
		file_twitter_advertise_twitter_advertise_proto_rawDescData = protoimpl.X.CompressGZIP(file_twitter_advertise_twitter_advertise_proto_rawDescData)
	})
	return file_twitter_advertise_twitter_advertise_proto_rawDescData
}

var file_twitter_advertise_twitter_advertise_proto_msgTypes = make([]protoimpl.MessageInfo, 100)
var file_twitter_advertise_twitter_advertise_proto_goTypes = []interface{}{
	(*MediaAccount)(nil),                        // 0: twitter_advertise.MediaAccount
	(*Campaign)(nil),                            // 1: twitter_advertise.Campaign
	(*AdGroup)(nil),                             // 2: twitter_advertise.AdGroup
	(*BudgetSchedule)(nil),                      // 3: twitter_advertise.BudgetSchedule
	(*Delivery)(nil),                            // 4: twitter_advertise.Delivery
	(*Demographics)(nil),                        // 5: twitter_advertise.Demographics
	(*Devices)(nil),                             // 6: twitter_advertise.Devices
	(*Audience)(nil),                            // 7: twitter_advertise.Audience
	(*TargetingFeatures)(nil),                   // 8: twitter_advertise.TargetingFeatures
	(*Ad)(nil),                                  // 9: twitter_advertise.Ad
	(*Destination)(nil),                         // 10: twitter_advertise.Destination
	(*Follower)(nil),                            // 11: twitter_advertise.Follower
	(*AudiencePlatform)(nil),                    // 12: twitter_advertise.AudiencePlatform
	(*User)(nil),                                // 13: twitter_advertise.User
	(*Tweet)(nil),                               // 14: twitter_advertise.Tweet
	(*MediaData)(nil),                           // 15: twitter_advertise.MediaData
	(*AccountCampaignInfo)(nil),                 // 16: twitter_advertise.AccountCampaignInfo
	(*PromotableUser)(nil),                      // 17: twitter_advertise.PromotableUser
	(*GetMediaAccountsReq)(nil),                 // 18: twitter_advertise.GetMediaAccountsReq
	(*GetMediaAccountsRsp)(nil),                 // 19: twitter_advertise.GetMediaAccountsRsp
	(*GetPublishedCampaignsSummaryReq)(nil),     // 20: twitter_advertise.GetPublishedCampaignsSummaryReq
	(*GetPublishedCampaignsSummaryRsp)(nil),     // 21: twitter_advertise.GetPublishedCampaignsSummaryRsp
	(*GetPublishedAdGroupsSummaryReq)(nil),      // 22: twitter_advertise.GetPublishedAdGroupsSummaryReq
	(*GetPublishedAdGroupsSummaryRsp)(nil),      // 23: twitter_advertise.GetPublishedAdGroupsSummaryRsp
	(*GetPublishedAdsSummaryReq)(nil),           // 24: twitter_advertise.GetPublishedAdsSummaryReq
	(*GetPublishedAdsSummaryRsp)(nil),           // 25: twitter_advertise.GetPublishedAdsSummaryRsp
	(*CreateCampaignReq)(nil),                   // 26: twitter_advertise.CreateCampaignReq
	(*CreateCampaignRsp)(nil),                   // 27: twitter_advertise.CreateCampaignRsp
	(*CreateAdGroupReq)(nil),                    // 28: twitter_advertise.CreateAdGroupReq
	(*CreateAdGroupRsp)(nil),                    // 29: twitter_advertise.CreateAdGroupRsp
	(*CreateAdReq)(nil),                         // 30: twitter_advertise.CreateAdReq
	(*CreateAdRsp)(nil),                         // 31: twitter_advertise.CreateAdRsp
	(*GetCampaignTreesReq)(nil),                 // 32: twitter_advertise.GetCampaignTreesReq
	(*GetCampaignTreesRsp)(nil),                 // 33: twitter_advertise.GetCampaignTreesRsp
	(*UpdateCampaignReq)(nil),                   // 34: twitter_advertise.UpdateCampaignReq
	(*UpdateCampaignRsp)(nil),                   // 35: twitter_advertise.UpdateCampaignRsp
	(*UpdateAdGroupReq)(nil),                    // 36: twitter_advertise.UpdateAdGroupReq
	(*UpdateAdGroupRsp)(nil),                    // 37: twitter_advertise.UpdateAdGroupRsp
	(*UpdateAdReq)(nil),                         // 38: twitter_advertise.UpdateAdReq
	(*UpdateAdRsp)(nil),                         // 39: twitter_advertise.UpdateAdRsp
	(*PublishAdLinkReq)(nil),                    // 40: twitter_advertise.PublishAdLinkReq
	(*PublishAdLinkRsp)(nil),                    // 41: twitter_advertise.PublishAdLinkRsp
	(*GetFundingSourcesReq)(nil),                // 42: twitter_advertise.GetFundingSourcesReq
	(*GetFundingSourcesRsp)(nil),                // 43: twitter_advertise.GetFundingSourcesRsp
	(*GetAccountAppsReq)(nil),                   // 44: twitter_advertise.GetAccountAppsReq
	(*GetAccountAppsRsp)(nil),                   // 45: twitter_advertise.GetAccountAppsRsp
	(*GetLocationsReq)(nil),                     // 46: twitter_advertise.GetLocationsReq
	(*GetLocationsRsp)(nil),                     // 47: twitter_advertise.GetLocationsRsp
	(*GetLanguagesReq)(nil),                     // 48: twitter_advertise.GetLanguagesReq
	(*GetLanguagesRsp)(nil),                     // 49: twitter_advertise.GetLanguagesRsp
	(*GetOsVersionsReq)(nil),                    // 50: twitter_advertise.GetOsVersionsReq
	(*GetOsVersionsRsp)(nil),                    // 51: twitter_advertise.GetOsVersionsRsp
	(*GetAudiencesReq)(nil),                     // 52: twitter_advertise.GetAudiencesReq
	(*GetAudiencesRsp)(nil),                     // 53: twitter_advertise.GetAudiencesRsp
	(*GetInterestsReq)(nil),                     // 54: twitter_advertise.GetInterestsReq
	(*GetInterestsRsp)(nil),                     // 55: twitter_advertise.GetInterestsRsp
	(*GetWebEventTagsReq)(nil),                  // 56: twitter_advertise.GetWebEventTagsReq
	(*GetWebEventTagsRsp)(nil),                  // 57: twitter_advertise.GetWebEventTagsRsp
	(*GetBizConfigReq)(nil),                     // 58: twitter_advertise.GetBizConfigReq
	(*GetBizConfigRsp)(nil),                     // 59: twitter_advertise.GetBizConfigRsp
	(*ReloadRsp)(nil),                           // 60: twitter_advertise.ReloadRsp
	(*DeleteDraftsReq)(nil),                     // 61: twitter_advertise.DeleteDraftsReq
	(*DeleteDraftsRsp)(nil),                     // 62: twitter_advertise.DeleteDraftsRsp
	(*GetPromotableUserReq)(nil),                // 63: twitter_advertise.GetPromotableUserReq
	(*GetPromotableUserRsp)(nil),                // 64: twitter_advertise.GetPromotableUserRsp
	(*GetFollowersReq)(nil),                     // 65: twitter_advertise.GetFollowersReq
	(*GetFollowersRsp)(nil),                     // 66: twitter_advertise.GetFollowersRsp
	(*GetUsersReq)(nil),                         // 67: twitter_advertise.GetUsersReq
	(*GetUsersRsp)(nil),                         // 68: twitter_advertise.GetUsersRsp
	(*StatusStatistic)(nil),                     // 69: twitter_advertise.StatusStatistic
	(*CampaignStatusStatistics)(nil),            // 70: twitter_advertise.CampaignStatusStatistics
	(*GetCampaignStatusStatisticsReq)(nil),      // 71: twitter_advertise.GetCampaignStatusStatisticsReq
	(*GetCampaignStatusStatisticsRsp)(nil),      // 72: twitter_advertise.GetCampaignStatusStatisticsRsp
	(*AdGroupStatusStatistics)(nil),             // 73: twitter_advertise.AdGroupStatusStatistics
	(*GetAdGroupStatusStatisticsReq)(nil),       // 74: twitter_advertise.GetAdGroupStatusStatisticsReq
	(*GetAdGroupStatusStatisticsRsp)(nil),       // 75: twitter_advertise.GetAdGroupStatusStatisticsRsp
	(*SqlCondition)(nil),                        // 76: twitter_advertise.SqlCondition
	(*GetDraftsReq)(nil),                        // 77: twitter_advertise.GetDraftsReq
	(*DraftView)(nil),                           // 78: twitter_advertise.DraftView
	(*GetDraftsRsp)(nil),                        // 79: twitter_advertise.GetDraftsRsp
	(*AdvertisingStatus)(nil),                   // 80: twitter_advertise.AdvertisingStatus
	(*ChangePublishedAdvertisingStatusReq)(nil), // 81: twitter_advertise.ChangePublishedAdvertisingStatusReq
	(*ChangePublishedAdvertisingStatusRsp)(nil), // 82: twitter_advertise.ChangePublishedAdvertisingStatusRsp
	(*CreateCampaignTreeReq)(nil),               // 83: twitter_advertise.CreateCampaignTreeReq
	(*CreateCampaignTreeRsp)(nil),               // 84: twitter_advertise.CreateCampaignTreeRsp
	(*CreateAdGroupTreeReq)(nil),                // 85: twitter_advertise.CreateAdGroupTreeReq
	(*CreateAdGroupTreeRsp)(nil),                // 86: twitter_advertise.CreateAdGroupTreeRsp
	(*CancelPublishingReq)(nil),                 // 87: twitter_advertise.CancelPublishingReq
	(*CancelPublishingRsp)(nil),                 // 88: twitter_advertise.CancelPublishingRsp
	(*CopyCampaignReq)(nil),                     // 89: twitter_advertise.CopyCampaignReq
	(*CopyCampaignRsp)(nil),                     // 90: twitter_advertise.CopyCampaignRsp
	(*CopyAdgroupReq)(nil),                      // 91: twitter_advertise.CopyAdgroupReq
	(*CopyAdgroupRsp)(nil),                      // 92: twitter_advertise.CopyAdgroupRsp
	(*CopyAdReq)(nil),                           // 93: twitter_advertise.CopyAdReq
	(*CopyAdRsp)(nil),                           // 94: twitter_advertise.CopyAdRsp
	(*SyncStrategyDataReq)(nil),                 // 95: twitter_advertise.SyncStrategyDataReq
	(*SyncStrategyDataRsp)(nil),                 // 96: twitter_advertise.SyncStrategyDataRsp
	(*GetMediaImageVideoReq)(nil),               // 97: twitter_advertise.GetMediaImageVideoReq
	(*ImageVideo)(nil),                          // 98: twitter_advertise.ImageVideo
	(*GetMediaImageVideoRsp)(nil),               // 99: twitter_advertise.GetMediaImageVideoRsp
	(*aix.Result)(nil),                          // 100: aix.Result
	(*FundingInstrument)(nil),                   // 101: twitter_advertise.FundingInstrument
	(*AppInfo)(nil),                             // 102: twitter_advertise.AppInfo
	(*Location)(nil),                            // 103: twitter_advertise.Location
	(*Language)(nil),                            // 104: twitter_advertise.Language
	(*OsVersion)(nil),                           // 105: twitter_advertise.OsVersion
	(*CustomAudience)(nil),                      // 106: twitter_advertise.CustomAudience
	(*Interest)(nil),                            // 107: twitter_advertise.Interest
	(*WebEventTag)(nil),                         // 108: twitter_advertise.WebEventTag
	(*structpb.Struct)(nil),                     // 109: google.protobuf.Struct
}
var file_twitter_advertise_twitter_advertise_proto_depIdxs = []int32{
	2,   // 0: twitter_advertise.Campaign.ad_groups:type_name -> twitter_advertise.AdGroup
	3,   // 1: twitter_advertise.AdGroup.budget_schedule:type_name -> twitter_advertise.BudgetSchedule
	4,   // 2: twitter_advertise.AdGroup.delivery:type_name -> twitter_advertise.Delivery
	5,   // 3: twitter_advertise.AdGroup.demographics:type_name -> twitter_advertise.Demographics
	6,   // 4: twitter_advertise.AdGroup.devices:type_name -> twitter_advertise.Devices
	7,   // 5: twitter_advertise.AdGroup.audiences:type_name -> twitter_advertise.Audience
	8,   // 6: twitter_advertise.AdGroup.targeting_features:type_name -> twitter_advertise.TargetingFeatures
	12,  // 7: twitter_advertise.AdGroup.audience_platform:type_name -> twitter_advertise.AudiencePlatform
	9,   // 8: twitter_advertise.AdGroup.ads:type_name -> twitter_advertise.Ad
	14,  // 9: twitter_advertise.Ad.tweet:type_name -> twitter_advertise.Tweet
	13,  // 10: twitter_advertise.Tweet.user:type_name -> twitter_advertise.User
	10,  // 11: twitter_advertise.Tweet.destination:type_name -> twitter_advertise.Destination
	15,  // 12: twitter_advertise.Tweet.media_datas:type_name -> twitter_advertise.MediaData
	100, // 13: twitter_advertise.GetMediaAccountsRsp.result:type_name -> aix.Result
	0,   // 14: twitter_advertise.GetMediaAccountsRsp.accounts:type_name -> twitter_advertise.MediaAccount
	100, // 15: twitter_advertise.GetPublishedCampaignsSummaryRsp.result:type_name -> aix.Result
	1,   // 16: twitter_advertise.GetPublishedCampaignsSummaryRsp.campaigns:type_name -> twitter_advertise.Campaign
	100, // 17: twitter_advertise.GetPublishedAdGroupsSummaryRsp.result:type_name -> aix.Result
	2,   // 18: twitter_advertise.GetPublishedAdGroupsSummaryRsp.ad_groups:type_name -> twitter_advertise.AdGroup
	100, // 19: twitter_advertise.GetPublishedAdsSummaryRsp.result:type_name -> aix.Result
	9,   // 20: twitter_advertise.GetPublishedAdsSummaryRsp.ads:type_name -> twitter_advertise.Ad
	1,   // 21: twitter_advertise.CreateCampaignReq.campaign:type_name -> twitter_advertise.Campaign
	100, // 22: twitter_advertise.CreateCampaignRsp.result:type_name -> aix.Result
	1,   // 23: twitter_advertise.CreateCampaignRsp.campaign:type_name -> twitter_advertise.Campaign
	2,   // 24: twitter_advertise.CreateAdGroupReq.ad_group:type_name -> twitter_advertise.AdGroup
	100, // 25: twitter_advertise.CreateAdGroupRsp.result:type_name -> aix.Result
	2,   // 26: twitter_advertise.CreateAdGroupRsp.ad_group:type_name -> twitter_advertise.AdGroup
	9,   // 27: twitter_advertise.CreateAdReq.ad:type_name -> twitter_advertise.Ad
	100, // 28: twitter_advertise.CreateAdRsp.result:type_name -> aix.Result
	9,   // 29: twitter_advertise.CreateAdRsp.ad:type_name -> twitter_advertise.Ad
	16,  // 30: twitter_advertise.GetCampaignTreesReq.account_campaigns:type_name -> twitter_advertise.AccountCampaignInfo
	100, // 31: twitter_advertise.GetCampaignTreesRsp.result:type_name -> aix.Result
	1,   // 32: twitter_advertise.GetCampaignTreesRsp.campaigns:type_name -> twitter_advertise.Campaign
	1,   // 33: twitter_advertise.UpdateCampaignReq.campaign:type_name -> twitter_advertise.Campaign
	100, // 34: twitter_advertise.UpdateCampaignRsp.result:type_name -> aix.Result
	1,   // 35: twitter_advertise.UpdateCampaignRsp.campaign:type_name -> twitter_advertise.Campaign
	2,   // 36: twitter_advertise.UpdateAdGroupReq.ad_group:type_name -> twitter_advertise.AdGroup
	100, // 37: twitter_advertise.UpdateAdGroupRsp.result:type_name -> aix.Result
	2,   // 38: twitter_advertise.UpdateAdGroupRsp.ad_group:type_name -> twitter_advertise.AdGroup
	9,   // 39: twitter_advertise.UpdateAdReq.ad:type_name -> twitter_advertise.Ad
	100, // 40: twitter_advertise.UpdateAdRsp.result:type_name -> aix.Result
	9,   // 41: twitter_advertise.UpdateAdRsp.ad:type_name -> twitter_advertise.Ad
	100, // 42: twitter_advertise.PublishAdLinkRsp.result:type_name -> aix.Result
	100, // 43: twitter_advertise.GetFundingSourcesRsp.result:type_name -> aix.Result
	101, // 44: twitter_advertise.GetFundingSourcesRsp.funding_sources:type_name -> twitter_advertise.FundingInstrument
	100, // 45: twitter_advertise.GetAccountAppsRsp.result:type_name -> aix.Result
	102, // 46: twitter_advertise.GetAccountAppsRsp.apps:type_name -> twitter_advertise.AppInfo
	100, // 47: twitter_advertise.GetLocationsRsp.result:type_name -> aix.Result
	103, // 48: twitter_advertise.GetLocationsRsp.locations:type_name -> twitter_advertise.Location
	100, // 49: twitter_advertise.GetLanguagesRsp.result:type_name -> aix.Result
	104, // 50: twitter_advertise.GetLanguagesRsp.languages:type_name -> twitter_advertise.Language
	100, // 51: twitter_advertise.GetOsVersionsRsp.result:type_name -> aix.Result
	105, // 52: twitter_advertise.GetOsVersionsRsp.os_versions:type_name -> twitter_advertise.OsVersion
	100, // 53: twitter_advertise.GetAudiencesRsp.result:type_name -> aix.Result
	106, // 54: twitter_advertise.GetAudiencesRsp.audiences:type_name -> twitter_advertise.CustomAudience
	100, // 55: twitter_advertise.GetInterestsRsp.result:type_name -> aix.Result
	107, // 56: twitter_advertise.GetInterestsRsp.interests:type_name -> twitter_advertise.Interest
	100, // 57: twitter_advertise.GetWebEventTagsRsp.result:type_name -> aix.Result
	108, // 58: twitter_advertise.GetWebEventTagsRsp.web_event_tags:type_name -> twitter_advertise.WebEventTag
	100, // 59: twitter_advertise.GetBizConfigRsp.result:type_name -> aix.Result
	109, // 60: twitter_advertise.GetBizConfigRsp.data:type_name -> google.protobuf.Struct
	100, // 61: twitter_advertise.ReloadRsp.result:type_name -> aix.Result
	100, // 62: twitter_advertise.DeleteDraftsRsp.result:type_name -> aix.Result
	100, // 63: twitter_advertise.GetPromotableUserRsp.result:type_name -> aix.Result
	13,  // 64: twitter_advertise.GetPromotableUserRsp.users:type_name -> twitter_advertise.User
	100, // 65: twitter_advertise.GetFollowersRsp.result:type_name -> aix.Result
	11,  // 66: twitter_advertise.GetFollowersRsp.followers:type_name -> twitter_advertise.Follower
	100, // 67: twitter_advertise.GetUsersRsp.result:type_name -> aix.Result
	13,  // 68: twitter_advertise.GetUsersRsp.users:type_name -> twitter_advertise.User
	69,  // 69: twitter_advertise.CampaignStatusStatistics.ad_group_statistics:type_name -> twitter_advertise.StatusStatistic
	69,  // 70: twitter_advertise.CampaignStatusStatistics.ad_statistics:type_name -> twitter_advertise.StatusStatistic
	100, // 71: twitter_advertise.GetCampaignStatusStatisticsRsp.result:type_name -> aix.Result
	70,  // 72: twitter_advertise.GetCampaignStatusStatisticsRsp.inner_statistics:type_name -> twitter_advertise.CampaignStatusStatistics
	70,  // 73: twitter_advertise.GetCampaignStatusStatisticsRsp.media_statistics:type_name -> twitter_advertise.CampaignStatusStatistics
	69,  // 74: twitter_advertise.AdGroupStatusStatistics.ad_statistics:type_name -> twitter_advertise.StatusStatistic
	100, // 75: twitter_advertise.GetAdGroupStatusStatisticsRsp.result:type_name -> aix.Result
	73,  // 76: twitter_advertise.GetAdGroupStatusStatisticsRsp.inner_statistics:type_name -> twitter_advertise.AdGroupStatusStatistics
	73,  // 77: twitter_advertise.GetAdGroupStatusStatisticsRsp.media_statistics:type_name -> twitter_advertise.AdGroupStatusStatistics
	76,  // 78: twitter_advertise.GetDraftsReq.conditions:type_name -> twitter_advertise.SqlCondition
	100, // 79: twitter_advertise.GetDraftsRsp.result:type_name -> aix.Result
	78,  // 80: twitter_advertise.GetDraftsRsp.drafts:type_name -> twitter_advertise.DraftView
	80,  // 81: twitter_advertise.ChangePublishedAdvertisingStatusReq.status:type_name -> twitter_advertise.AdvertisingStatus
	100, // 82: twitter_advertise.ChangePublishedAdvertisingStatusRsp.result:type_name -> aix.Result
	80,  // 83: twitter_advertise.ChangePublishedAdvertisingStatusRsp.status:type_name -> twitter_advertise.AdvertisingStatus
	1,   // 84: twitter_advertise.CreateCampaignTreeReq.campaign_tree:type_name -> twitter_advertise.Campaign
	100, // 85: twitter_advertise.CreateCampaignTreeRsp.result:type_name -> aix.Result
	1,   // 86: twitter_advertise.CreateCampaignTreeRsp.campaign_tree:type_name -> twitter_advertise.Campaign
	2,   // 87: twitter_advertise.CreateAdGroupTreeReq.ad_group_tree:type_name -> twitter_advertise.AdGroup
	100, // 88: twitter_advertise.CreateAdGroupTreeRsp.result:type_name -> aix.Result
	2,   // 89: twitter_advertise.CreateAdGroupTreeRsp.ad_group_tree:type_name -> twitter_advertise.AdGroup
	100, // 90: twitter_advertise.CancelPublishingRsp.result:type_name -> aix.Result
	100, // 91: twitter_advertise.CopyCampaignRsp.result:type_name -> aix.Result
	100, // 92: twitter_advertise.CopyAdgroupRsp.result:type_name -> aix.Result
	100, // 93: twitter_advertise.CopyAdRsp.result:type_name -> aix.Result
	100, // 94: twitter_advertise.SyncStrategyDataRsp.result:type_name -> aix.Result
	100, // 95: twitter_advertise.GetMediaImageVideoRsp.result:type_name -> aix.Result
	98,  // 96: twitter_advertise.GetMediaImageVideoRsp.assets:type_name -> twitter_advertise.ImageVideo
	97,  // [97:97] is the sub-list for method output_type
	97,  // [97:97] is the sub-list for method input_type
	97,  // [97:97] is the sub-list for extension type_name
	97,  // [97:97] is the sub-list for extension extendee
	0,   // [0:97] is the sub-list for field type_name
}

func init() { file_twitter_advertise_twitter_advertise_proto_init() }
func file_twitter_advertise_twitter_advertise_proto_init() {
	if File_twitter_advertise_twitter_advertise_proto != nil {
		return
	}
	file_twitter_advertise_twitter_option_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_twitter_advertise_twitter_advertise_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Campaign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BudgetSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Delivery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Demographics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Devices); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Audience); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetingFeatures); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Destination); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Follower); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudiencePlatform); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Tweet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountCampaignInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PromotableUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPublishedCampaignsSummaryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPublishedCampaignsSummaryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPublishedAdGroupsSummaryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPublishedAdGroupsSummaryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPublishedAdsSummaryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPublishedAdsSummaryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCampaignReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCampaignRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAdGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAdGroupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignTreesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignTreesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCampaignReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCampaignRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAdGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAdGroupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishAdLinkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishAdLinkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFundingSourcesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFundingSourcesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountAppsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountAppsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguagesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLanguagesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOsVersionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOsVersionsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAudiencesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAudiencesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInterestsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInterestsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebEventTagsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWebEventTagsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBizConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBizConfigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReloadRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDraftsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDraftsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPromotableUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPromotableUserRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFollowersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUsersRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusStatistic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignStatusStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignStatusStatisticsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignStatusStatisticsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdGroupStatusStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAdGroupStatusStatisticsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAdGroupStatusStatisticsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SqlCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDraftsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DraftView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDraftsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdvertisingStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePublishedAdvertisingStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePublishedAdvertisingStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCampaignTreeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCampaignTreeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAdGroupTreeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAdGroupTreeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelPublishingReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelPublishingRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyCampaignReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyCampaignRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAdgroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAdgroupRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyAdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncStrategyDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncStrategyDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaImageVideoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageVideo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_twitter_advertise_twitter_advertise_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaImageVideoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_twitter_advertise_twitter_advertise_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   100,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_twitter_advertise_twitter_advertise_proto_goTypes,
		DependencyIndexes: file_twitter_advertise_twitter_advertise_proto_depIdxs,
		MessageInfos:      file_twitter_advertise_twitter_advertise_proto_msgTypes,
	}.Build()
	File_twitter_advertise_twitter_advertise_proto = out.File
	file_twitter_advertise_twitter_advertise_proto_rawDesc = nil
	file_twitter_advertise_twitter_advertise_proto_goTypes = nil
	file_twitter_advertise_twitter_advertise_proto_depIdxs = nil
}
