package data

import (
	"context"
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// GetCreativeMediaDirectoryByID 获取上传媒体根目录
func GetCreativeMediaDirectoryByID(ctx context.Context,
	gameCode string, directoryID string) (*model.CreativeMediaDirectory, error) {
	table := getCreativeMediaDirectoryTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	directory := &model.CreativeMediaDirectory{}
	query := db.Model(directory).Table(table)
	err := query.Where("id = ?", directoryID).First()
	return directory, err
}

// UpsertRootCreativeMediaDirectory 插入上传媒体根目录，忽略已存在
func UpsertRootCreativeMediaDirectory(ctx context.Context, gameCode string, d *model.CreativeMediaDirectory) error {
	table := getCreativeMediaDirectoryTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	_, err := db.Model(d).Table(table).OnConflict("(id) DO NOTHING").Insert()
	return err
}

// UpsertCreativeMediaDirectory 插入上传媒体子目录，忽略已存在
func UpsertCreativeMediaDirectory(ctx context.Context, gameCode string, d *model.CreativeMediaDirectory) error {
	table := getCreativeMediaDirectoryTable(gameCode)

	db := postgresql.GetDBWithContext(ctx)
	// 显式要求返回 id
	_, err := db.Model(d).Table(table).OnConflict("(name, parent_name) DO UPDATE SET name=EXCLUDED.name").Returning("id").Insert()
	return err
}

// GetAllCreativeMediaDirectory 拉取所有的上传媒体目录
func GetAllCreativeMediaDirectory(ctx context.Context, gameCode string) ([]*model.CreativeMediaDirectory, error) {
	table := getCreativeMediaDirectoryTable(gameCode)

	var founds []*model.CreativeMediaDirectory
	db := postgresql.GetDBWithContext(ctx)
	err := db.Model(&model.CreativeMediaDirectory{}).Table(table).Select(&founds)
	return founds, err
}

// UpdateCreativeMediaDirectoryCount 更新上传媒体目录统计数据
func UpdateCreativeMediaDirectoryCount(ctx context.Context, gameCode string, d *model.CreativeMediaDirectory) error {
	table := getCreativeMediaDirectoryTable(gameCode)

	db := postgresql.GetDBWithContext(ctx)
	_, err := db.Model(d).Table(table).Where("id = ?", d.ID).
		Column("direct_child_count", "total_leaf_count", "direct_directory_count").Update()

	return err
}

// ---- 内部函数 ---
func getCreativeMediaDirectoryTable(gameCode string) string {
	return fmt.Sprintf("arthub_sync.tb_creative_media_directory_%s", gameCode)
}
