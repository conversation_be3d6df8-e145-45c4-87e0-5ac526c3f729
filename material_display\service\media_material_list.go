// Package service 服务接口实现代码
package service

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/scylladb/go-set"
	"github.com/thoas/go-funk"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// MediaMaterialList 拉取素材列表
func MediaMaterialList(ctx *gin.Context, req *pb.MediaMaterialListReq, rsp *pb.MediaMaterialListRsp) error {
	log.InfoContextf(ctx, "MediaMaterialListReq!!!: %+v", req)
	if req.GetAccountId() == "" || req.GetGameCode() == "" {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "param error: AccountId and GameCode cannot be empty")
	}
	var overviews []*model.CreativeOverview
	var mediaUpload []*model.CreativeMediaUpload
	var err error
	var total uint32
	gameCode := req.GetGameCode()
	for _, gameMap := range conf.GetBizConf().GameCodeMap {
		if gameCode == gameMap.GameCode {
			gameCode = gameMap.DstGameCode
			break
		}
	}

	arthubCode := ""
	gameDepotType := 0
	depotCache, ok := cache.DepotTbArthubDepotCache[gameCode]
	if ok {
		arthubCode = depotCache.ArthubCode
		gameDepotType = depotCache.Type
	} else {
		depot, err := GetArthubToken(ctx, gameCode)
		if err != nil {
			log.ErrorContextf(ctx, "error data.GetMaterialList GetArthubToken, gameCode: %v, err: %v", gameCode, err)
			return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR), "system error")
		}
		arthubCode = depot.ArthubCode
		gameDepotType = depot.Type
	}

	// 过滤为空的素材名称
	assetNames := funk.FilterString(req.GetAssetNames(), func(s string) bool {
		return s != ""
	})
	assetNames = funk.UniqString(assetNames)

	// google渠道的图片，需要根据campaign_sub_type过滤图片ratio
	var imgRatio []string
	if req.GetMedia() == 1 && req.GetFormatType() == 2 {
		imgRatio = getGoogleSupportImgRatio(req.GetCampaignSubType())
	}
	total, overviews, mediaUpload, err = mediaMaterialList(ctx, req.GetOffset(), req.GetCount(), req.GetFormatType(),
		gameCode, req.GetAccountId(), req.GetMedia(), imgRatio, req.GetKeyword(), assetNames, req.GetAssetRatios())
	if err != nil {
		log.ErrorContextf(ctx, "mediaMaterialList, err: %v", err)
		return err
	}

	log.DebugContextf(ctx, "MediaMaterialList overviews:%+v", overviews)

	overviewMap := make(map[string]*model.CreativeOverview)
	for _, overview := range overviews {
		overviewMap[overview.AssetID] = overview
	}
	rspMetaList := []*pb.MediaMaterialMeta{}
	for _, media := range mediaUpload {
		overview := overviewMap[media.AssetId]
		if overview == nil {
			continue
		}
		meta := arthubOverView2MediaMaterialMeta(overview)
		meta.ResourceName = media.ResourceName
		meta.CoverHash = media.CoverResourceName
		meta.AssetRatio = media.AssetRatio
		rspMetaList = append(rspMetaList, meta)
	}

	rsp.Materials = fillMediaMeterialMetas(ctx, rspMetaList, gameCode, arthubCode, gameDepotType)
	rsp.Total = total
	return nil
}

// 获取google campaign 支持的图片规则， Image sizes for image ads
func getGoogleSupportImgRatio(campaignSubType uint32) []string {
	var strCampaignSubType string
	if campaignSubType == 1 {
		strCampaignSubType = "app_install"
	} else if campaignSubType == 2 {
		strCampaignSubType = "app_engagement"
	}

	if ratio, ok := conf.GetBizConf().GoogleImgRatio[strCampaignSubType]; ok {
		return ratio
	}
	return nil
}

// arthub数据转aix数据
func arthubOverView2MediaMaterialMeta(overview *model.CreativeOverview) *pb.MediaMaterialMeta {
	material := &pb.MediaMaterialMeta{}
	material.AssetId = overview.AssetID
	material.Name = overview.AssetName
	material.Status = overview.Status
	material.UplineDate = overview.UplineDate
	material.OfflineDate = overview.OfflineDate
	material.OnlineDays = overview.OnlineDays
	material.CreateDate = overview.CreateDate
	material.AssetStatus = int32(overview.AssetStatus)
	material.OnlineStatus = int32(overview.OnlineStatus)
	material.OnlineDate = overview.OnlineDate
	material.PreviewUrl = overview.PreviewURL
	return material
}

// 批量精确搜索媒体素材信息
func queryMediaExactAssetInfo(ctx *gin.Context, formatType uint32, gameCode, accountID string,
	channel uint32, imgRatios, assetNames, assetRatios []string,
) (uint32, []string, []*model.CreativeMediaUpload, error) {
	// 精确查询素材名称列表 tableCreativeOverview
	assetInfos, err := data.QueryCreativeOverviewByKeyWords(ctx, gameCode, assetNames)
	if err != nil {
		return 0, nil, nil, err
	}
	// 根据批量素材名称批量精确搜索
	assetIds := make([]string, 0, len(assetInfos))
	for _, item := range assetInfos {
		// 已删除素材不显示
		if item.AssetID != "" && item.AssetStatus == 1 {
			assetIds = append(assetIds, item.AssetID)
		}
	}
	assetIds = funk.UniqString(assetIds)

	// 根据搜索获取的素材id，结合渠道等其他参数，查询已上传素材
	uploads, err := data.QueryCreativeMediaUploadByAssetId(ctx, accountID, channel, formatType, gameCode, imgRatios, assetIds, assetRatios)
	if err != nil {
		return 0, nil, nil, err
	}
	assetIds = []string{}
	// resource_name去重过滤
	s := set.NewStringSet()
	for _, item := range uploads {
		if !s.Has(item.ResourceName) {
			s.Add(item.ResourceName)
			assetIds = append(assetIds, item.AssetId)
		}
	}
	assetIds = funk.UniqString(assetIds)

	return uint32(len(uploads)), assetIds, uploads, nil
}

// 批量精确搜索素材信息
func queryExactAssetInfo(ctx *gin.Context, formatType uint32, gameCode, accountId string,
	channel uint32, imgRatios, assetNames, assetRatios []string,
) (uint32, []string, []*model.CreativeMediaUpload, error) {
	// 精确查询素材名称列表 tableCreativeOverview
	assetInfos, err := data.QueryCreativeOverviewByKeyWords(ctx, gameCode, assetNames)
	if err != nil {
		return 0, nil, nil, err
	}
	// 根据批量素材名称批量精确搜索
	assetIds := make([]string, 0, len(assetInfos))
	for _, item := range assetInfos {
		// 已删除素材不显示
		if item.AssetID != "" && item.AssetStatus == 1 {
			assetIds = append(assetIds, item.AssetID)
		}
	}
	assetIds = funk.UniqString(assetIds)

	// 根据搜索获取的素材id，结合渠道等其他参数，查询已上传素材
	uploads, err := data.QueryCreativeMediaUploadByAssetId(ctx, accountId, channel, formatType, gameCode, imgRatios, assetIds, assetRatios)
	if err != nil {
		return 0, nil, nil, err
	}
	assetIds = []string{}
	for _, item := range uploads {
		if item.AssetId != "" {
			assetIds = append(assetIds, item.AssetId)
		}
	}
	assetIds = funk.UniqString(assetIds)

	return uint32(len(uploads)), assetIds, uploads, nil
}

// 根据条件查询媒体素材id列表相关信息
func queryMediaAssetInfo(ctx *gin.Context, offset, count, formatType uint32, gameCode, accountID string,
	channel uint32, imgRatios []string, keyword string, assetNames []string, assetRatios []string,
) (uint32, []string, []*model.CreativeMediaUpload, error) {
	// tableCreativeMediaUpload := "arthub_sync.tb_creative_media_upload"
	tableCreativeOverview := "arthub_sync.tb_creative_overview_" + gameCode

	db := postgresql.GetDBWithContext(ctx)
	subQuery := db.Model(&model.CreativeMediaUpload{})
	subQuery.Column("*")
	// 对resource_name去重
	subQuery.ColumnExpr("row_number() over(partition by resource_name order by update_time desc) rn")
	if gameCode != "" {
		subQuery.Where("game_code = ?", gameCode)
	}
	if accountID != "" {
		subQuery.Where("account_id = ?", accountID)
	}
	if channel > 0 {
		subQuery.Where("channel = ?", channel)
	}
	if formatType > 0 {
		subQuery.Where("creative_media_upload.format_type = ?", formatType)
	}
	if len(imgRatios) > 0 {
		var ratios []string
		for _, ratio := range imgRatios {
			ratios = append(ratios, fmt.Sprintf("'%s'", ratio))
		}
		subQuery.WhereIn("img_ratio in ?", ratios)
	}

	if channel == constant.MediaGoogle && formatType == 0 && len(assetRatios) > 0 {
		// google渠道要特殊处理, 查所有素材类型时，需要单独过滤图片asset_ratios

		// 要么是视频
		// 要么是图片，满足ratio
		subQuery.Where("creative_media_upload.format_type = ? or asset_ratio in (?)", constant.AssetVideoType, pg.In(assetRatios))
	} else if len(assetRatios) > 0 {
		subQuery.WhereIn("asset_ratio IN (?)", assetRatios)
	}
	// 连表查询，已删除的素材不展示
	subQuery.Join(fmt.Sprintf("LEFT JOIN %s", tableCreativeOverview))
	subQuery.JoinOn(fmt.Sprintf("creative_media_upload.asset_id = %s.asset_id", tableCreativeOverview))

	// 已删除素材不展示
	subQuery.Where(fmt.Sprintf("%s.asset_status = 1 ", tableCreativeOverview))
	if keyword != "" {
		words := strings.Split(keyword, " ")
		if len(words) == 1 {
			newKeyword := strings.ReplaceAll(keyword, "%", "\\%")
			newKeyword = strings.ToLower(newKeyword)
			filter := fmt.Sprintf("lower(%s.asset_name) like '%%%s%%' ", tableCreativeOverview, newKeyword)
			subQuery.Where(filter)
		} else {
			filter := fmt.Sprintf("arthub_sync.split_asset_name(%s.asset_name) @> arthub_sync.split_asset_name('%s')", tableCreativeOverview, keyword)
			subQuery.Where(filter)
		}
	}

	var founds []*model.CreativeMediaUpload
	query := db.Model().TableExpr("(?) t", subQuery)
	// resource_name分组排序row_number = 1， 去重
	query.Where("rn = 1")
	query.Offset(int(offset)).Limit(int(count))
	total, err := query.Order("update_time desc nulls last").SelectAndCount(&founds)
	if err != nil {
		return 0, nil, nil, err
	}
	// 请求overview列表
	assetIDList := []string{}
	for _, asset := range founds {
		assetIDList = append(assetIDList, asset.AssetId)
	}

	return uint32(total), funk.UniqString(assetIDList), founds, nil
}

// 根据条件查询素材id列表相关信息
func queryAssetInfo(ctx *gin.Context, offset, count, formatType uint32, gameCode, accountID string,
	channel uint32, imgRatios []string, keyword string, assetNames []string, assetRatios []string,
) (uint32, []string, []*model.CreativeMediaUpload, error) {
	var mediaUpload []*model.CreativeMediaUpload
	var err error
	var total QueryCount
	whereList := []string{}
	paramList := []interface{}{}
	tableCreativeMediaUpload := "arthub_sync.tb_creative_media_upload"
	tableCreativeOverview := "arthub_sync.tb_creative_overview_" + gameCode
	if formatType > 0 {
		whereList = append(whereList, tableCreativeMediaUpload+".format_type = ?")
		paramList = append(paramList, formatType)
	}
	if gameCode != "" {
		whereList = append(whereList, tableCreativeMediaUpload+".game_code = ?")
		paramList = append(paramList, gameCode)
	}
	if accountID != "" {
		whereList = append(whereList, tableCreativeMediaUpload+".account_id = ?")
		paramList = append(paramList, accountID)
	}
	if channel > 0 {
		whereList = append(whereList, tableCreativeMediaUpload+".channel = ?")
		paramList = append(paramList, channel)
	}
	if len(imgRatios) > 0 {
		var ratios []string
		for _, ratio := range imgRatios {
			ratios = append(ratios, fmt.Sprintf("'%s'", ratio))
		}
		whereList = append(whereList, fmt.Sprintf("img_ratio in (%s)", strings.Join(ratios, ",")))
	}

	var ratios []string
	for _, ratio := range assetRatios {
		if len(ratio) == 0 {
			continue
		}
		ratios = append(ratios, fmt.Sprintf("'%s'", ratio))
	}
	if channel == constant.MediaGoogle && formatType == 0 && len(ratios) > 0 {
		// google渠道要特殊处理, 查所有素材类型时，需要单独过滤图片asset_ratios

		// 要么是视频
		// 要么是图片，满足ratio
		filter := fmt.Sprintf("%s.format_type = %d or %s.asset_ratio in (%s)", tableCreativeMediaUpload, constant.AssetVideoType, tableCreativeMediaUpload, strings.Join(ratios, ","))
		whereList = append(whereList, fmt.Sprintf("(%s)", filter))
	} else if len(ratios) > 0 {
		whereList = append(whereList, fmt.Sprintf("%s.asset_ratio in (%s)", tableCreativeMediaUpload, strings.Join(ratios, ",")))
	}

	sql := ""
	// 连表查询，已删除的素材不展示
	sql = fmt.Sprintf("from %s ", tableCreativeMediaUpload)
	sql += fmt.Sprintf("LEFT OUTER JOIN %s ", tableCreativeOverview)
	sql += fmt.Sprintf("ON %s.asset_id=%s.asset_id where %s ", tableCreativeMediaUpload, tableCreativeOverview, strings.Join(whereList, " and "))
	// 已删除素材不展示
	sql += fmt.Sprintf("and %s.asset_status = 1 ", tableCreativeOverview)
	if keyword != "" {
		words := strings.Split(keyword, " ")
		if len(words) == 1 {
			new_keyword := strings.ReplaceAll(keyword, "%", "\\%")
			new_keyword = strings.ToLower(new_keyword)
			sql += fmt.Sprintf("and lower(%s.asset_name) like '%%%s%%' ", tableCreativeOverview, new_keyword)
			paramList = append(paramList, keyword)
		} else {
			sql += fmt.Sprintf("and arthub_sync.split_asset_name(%s.asset_name) @> arthub_sync.split_asset_name('%s')", tableCreativeOverview, keyword)
			paramList = append(paramList, keyword)
		}

	}

	// 查询总数sql
	countSql := fmt.Sprintf("select count(*) %s", sql)
	// 请求到asset_id总数
	db := postgresql.GetDBWithContext(ctx)
	_, err = db.Query(&total, countSql, paramList...)
	if err != nil {
		return 0, nil, nil, fmt.Errorf("query creative mediaUpload total failed, err: %v", err)
	}

	//whereSql = " where " + strings.Join(whereList, " and ") + fmt.Sprintf(" offset %d limit %d", offset, count)
	// 查询数据列表sql
	selectSql := fmt.Sprintf("select %s.* %s ", tableCreativeMediaUpload, sql)
	selectSql += fmt.Sprintf("order by %s.update_time desc nulls last offset %d limit %d", tableCreativeMediaUpload, offset, count)
	// 请求asset_id列表
	_, err = db.Query(&mediaUpload, selectSql, paramList...)
	if err != nil {
		return 0, nil, nil, fmt.Errorf("query creative mediaUpload failed, err: %v", err)
	}

	// 请求overview列表
	assetIDList := []string{}
	assetIDSet := make(map[string]struct{})
	for _, asset := range mediaUpload {
		if _, exist := assetIDSet[asset.AssetId]; exist {
			continue
		}
		assetIDSet[asset.AssetId] = struct{}{}
		assetIDList = append(assetIDList, asset.AssetId)
	}
	return total.Count, assetIDList, mediaUpload, nil
}

// 获取用户上传的素材列表
func mediaMaterialList(ctx *gin.Context, offset, count, formatType uint32, gameCode, accountID string,
	channel uint32, imgRatios []string, keyword string, assetNames []string, assetRatios []string,
) (uint32, []*model.CreativeOverview, []*model.CreativeMediaUpload, error) {
	var (
		err         error
		total       uint32
		assetIDList []string
		mediaUpload []*model.CreativeMediaUpload
		overviews   []*model.CreativeOverview
	)
	if len(assetNames) == 0 {
		// 素材名称未指定，分页查询查询素材信息
		total, assetIDList, mediaUpload, err = queryMediaAssetInfo(ctx, offset, count,
			formatType, gameCode, accountID, channel, imgRatios, keyword, assetNames, assetRatios)
		if err != nil {
			log.ErrorContextf(ctx, "queryMediaAssetInfo, err: %v", err)
			return 0, overviews, nil, err
		}
	} else {
		// 素材名称已指定，批量精确搜索素材名称
		total, assetIDList, mediaUpload, err = queryMediaExactAssetInfo(ctx,
			formatType, gameCode, accountID, channel, imgRatios, assetNames, assetRatios)
		if err != nil {
			log.ErrorContextf(ctx, "queryMediaExactAssetInfo, err: %v", err)
			return 0, overviews, nil, err
		}
	}

	// 无素材id
	if len(assetIDList) <= 0 {
		return 0, overviews, nil, nil
	}
	// 查询素材信息
	err = postgresql.GetDBWithContext(ctx).Model(&overviews).
		Table(fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", gameCode)).
		WhereIn("asset_id IN (?)", assetIDList).
		Select()
	if err != nil {
		return 0, nil, mediaUpload, fmt.Errorf("query creative overview total failed, err: %v", err)
	}

	if total == 0 {
		total = uint32(len(overviews))
	}
	return total, overviews, mediaUpload, nil
}

// 获取用户上传的素材详情列表
func mediaMaterialDetails(ctx *gin.Context, assetIDs []string, gameCode string) (map[string]*model.CreativeAssetDetails, error) {
	var details []*model.CreativeAssetDetails
	//var assetIDsStr, splitor string
	//for _, assetID := range assetIDs {
	//	assetIDsStr += splitor + assetID
	//	splitor = ","
	//}
	//assetIDsStr = fmt.Sprintf("(%s)", assetIDsStr)
	//
	//_, err := postgresql.Pgdb.Query(&details, fmt.Sprintf("SELECT * FROM %s.tb_creative_details_%s WHERE asset_id in %s", "arthub_sync", gameCode, assetIDsStr))
	err := postgresql.GetDBWithContext(ctx).Model(&details).
		Table(fmt.Sprintf("%s.tb_creative_details_%s", "arthub_sync", gameCode)).
		WhereIn("asset_id IN (?)", assetIDs).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "get media material details failed, assetIDs: %v, err: %v", assetIDs, err.Error())
		return nil, err
	}

	assetID2Detail := make(map[string]*model.CreativeAssetDetails)
	for _, detail := range details {
		assetID2Detail[detail.AssetID] = detail
	}

	return assetID2Detail, nil
}

// 填充返回结果
func fillMediaMeterialMetas(ctx *gin.Context, metas []*pb.MediaMaterialMeta, gameCode, arthubCode string, gameDepotType int) []*pb.MediaMaterialMeta {
	var assetIDs []string
	var assetPreviewUrlIDs []string
	for _, meta := range metas {
		assetIDs = append(assetIDs, meta.GetAssetId())
		if meta.AssetStatus == arthub.ARTHUB_ASSET_STATUS_NORMAL {
			assetPreviewUrlIDs = append(assetPreviewUrlIDs, meta.GetAssetId())
		}
	}

	if len(assetIDs) == 0 {
		return nil
	}
	assetID2Detail, err := mediaMaterialDetails(ctx, assetIDs, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "mediaMaterialDetails failed, err: %s", err.Error())
		return metas
	}

	var urlRsp []AssetInfo
	if gameDepotType == utils.GAME_DEPOT_TYPE_ARTHUB { // 如果是arthub，则调用arthub接口获取素材的url，否则取原来的url
		urlRsp, _, err = getAssetPreviewUrl(ctx, gameCode, arthubCode, assetPreviewUrlIDs)
		if err != nil {
			log.ErrorContextf(ctx, "getAssetPreviewUrl failed, err: %s", err.Error())
			return metas
		}
	}

	for _, meta := range metas {
		detail := assetID2Detail[meta.GetAssetId()]
		if detail == nil {
			continue
		}
		meta.Format = detail.Format
		meta.Duration = detail.Duration

		for _, asset := range urlRsp {
			if strconv.FormatUint(asset.ID, 10) == meta.AssetId {
				meta.PreviewUrl = asset.PreviewUrl
				break
			}
		}

		materialDetail := &pb.MediaMaterialMetaDetail{}
		materialDetail.AspectRatio = detail.AspectRatio
		materialDetail.BitRate = detail.BitRate
		materialDetail.Cover = detail.Cover
		materialDetail.Creator = detail.Creator
		materialDetail.CompressionFormat = detail.CompressionFormat
		materialDetail.FrameRate = detail.FrameRate
		materialDetail.High = detail.High
		materialDetail.ManualFirstLabel = detail.ManualFirstLabel
		materialDetail.ManualSecondLabel = detail.ManualSecondLabel
		materialDetail.RobotFirstLabel = detail.RobotFirstLabel
		materialDetail.RobotSecondLabel = detail.RobotSecondLabel
		materialDetail.Size = detail.Size
		materialDetail.Updater = detail.Updater
		materialDetail.UpdateDate = detail.UpdateDate
		materialDetail.Width = detail.Width
		meta.Detail = materialDetail
	}
	return metas
}
