package service

import (
	"context"
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	channelassetRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/channel_asset"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
)

/* 素材新标签体系：
 * 1. 标签规则，表：arthub_sync.tb_asset_label_rule, 可由业务在页面上自己编辑标签规则
 * 2. 规则可应用于aix library
 * 3. 规则可应用于渠道素材
 */

// NotifyApplyLabelRuleToAix 对外接口，调用此接口，将标签规则应用到aix library
func NotifyApplyLabelRuleToAix(ctx context.Context,
	req *pb.NotifyApplyLabelRuleToAixReq, rsp *pb.NotifyApplyLabelRuleToAixRsp) error {
	rule, err := repo.GetLabelRuleByGameCodeAndID(ctx, req.GetGameCode(), req.GetRuleId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"NotifyApplyLabelRuleToAix repo.GetLabelRuleByGameCodeAndID err:%v", err)
	}

	err = ApplyLabelRuleToAix(ctx, rule)
	if err != nil {
		log.WarningContextf(ctx, "NotifyApplyLabelRuleToAix ApplyLabelRuleToAix err:%v", err)
	}

	err = ApplyLabelRuleToChannel(ctx, rule)
	if err != nil {
		log.WarningContextf(ctx, "NotifyApplyLabelRuleToAix ApplyLabelRuleToChannel err:%v", err)
	}
	return nil
}

// ApplyGameLabelRulesToAix 将游戏下的全部标签规则应用到aix library
func ApplyGameLabelRulesToAix(ctx context.Context, gameCode string) error {
	ctx = log.SetXAPIPath(ctx, "ApplyGameLabelRulesToAix")
	rules, err := repo.GetAllLabelRulesByGameCode(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "ApplyGameLabelRulesToAix repo.GetAllLabelRulesByGameCode err:%v", err)
		return err
	}

	for _, rule := range rules {
		log.DebugContextf(ctx, "ApplyLabelRuleToAix begin rule:%+v", rule)
		ApplyLabelRuleToAix(ctx, rule)
	}

	return nil
}

// ApplyLabelRuleToAix 标签规则应用到aix library
func ApplyLabelRuleToAix(ctx context.Context, rule *model.TbAssetLabelRule) error {
	ctx = log.SetXAPIPath(ctx, "ApplyLabelRuleToAix")
	log.DebugContextf(ctx, "ApplyLabelRuleToAix begin, rule:%+v", rule)
	// 规则没有设置标签，直接返回
	if len(rule.Labels) == 0 {
		return nil
	}

	// 每次处理100条
	limit := 100
	// 循环获取规则下的素材
	for offset := 0; offset < 1000000; offset = offset + limit {
		views, err := repo.GetCreativeOverviewsByRuleNotCount(ctx, rule, offset, limit)
		if err != nil {
			log.ErrorContextf(ctx, "ApplyLabelRuleToAix repo.GetCreativeOverviewsByRuleNotCount error: %v", err)
			return err
		}

		var assetIDs []string
		var assetLabels []*model.AssetLabel
		for _, view := range views {
			assetIDs = append(assetIDs, view.AssetID)
			for _, ruleLable := range rule.Labels {
				t := &model.AssetLabel{
					AssetId:     view.AssetID,
					LabelName:   "", // 标签规则的label_name固定为空，已不再使用
					FirstLabel:  ruleLable.FirstLabel,
					SecondLabel: ruleLable.SecondLabel,
					CreateBy:    rule.CreateUser,
					UpdateBy:    fmt.Sprintf("material_sync_rule_%v", rule.ID),
					CreateTime:  utils.GetNowStr(),
					UpdateTime:  utils.GetNowStr(),
				}
				assetLabels = append(assetLabels, t)
			}
		}

		// 插入aix素材标签，会覆盖之前的旧标签
		err = data.InsertAssetLablesTransaction(ctx, rule.GameCode, assetIDs, assetLabels)
		if err != nil {
			log.ErrorContextf(ctx, "ApplyLabelRuleToAix data.InsertAssetLablesTransaction error: %v", err)
			return err
		}

		// 没数据了
		if len(views) < limit {
			break
		}
	}
	return nil
}

// ApplyLabelRuleToChannel 将某个标签规则应用到channel渠道素材
func ApplyLabelRuleToChannel(ctx context.Context, rule *model.TbAssetLabelRule) error {
	ctx = log.SetXAPIPath(ctx, "ApplyLabelRuleToChannel")
	log.DebugContextf(ctx, "ApplyLabelRuleToChannel begin, rule:%+v", rule)
	// 规则没有设置标签，直接返回
	if len(rule.Labels) == 0 {
		return nil
	}

	// 每次处理100条
	limit := 100
	// 循环获取规则下的素材
	for offset := 0; offset < 10000; offset = offset + limit {
		channelAssets, err := channelassetRepo.GetChannelAssetsByRuleNotCount(ctx, rule, offset, limit)
		if err != nil {
			log.ErrorContextf(ctx, "ApplyLabelRuleToChannel channelassetRepo.GetChannelAssetsByRuleNotCount error: %v", err)
			return err
		}

		// 本次需要删除的那些记录行，符合条件 (channel_type, channel_account_id, channel_asset_id)
		var deleteRows []*model.ChannelAssetLabel
		// 本次需要插入的数据
		var labels []*model.ChannelAssetLabel
		for _, asset := range channelAssets {
			row := &model.ChannelAssetLabel{
				ChannelType:      asset.ChannelType,
				ChannelAccountID: asset.ChannelAccountID,
				ChannelAssetId:   asset.ChannelAssetID,
			}
			deleteRows = append(deleteRows, row)

			for _, ruleLable := range rule.Labels {
				t := &model.ChannelAssetLabel{
					ChannelType:      asset.ChannelType,
					ChannelAccountID: asset.ChannelAccountID,
					ChannelAssetId:   asset.ChannelAssetID,
					LabelName:        "", // 标签规则的label_name固定为空，已不再使用
					FirstLabel:       ruleLable.FirstLabel,
					SecondLabel:      ruleLable.SecondLabel,
					CreateBy:         rule.CreateUser,
					UpdateBy:         fmt.Sprintf("material_sync_rule_%v", rule.ID),
					CreateTime:       utils.GetNowStr(),
					UpdateTime:       utils.GetNowStr(),
				}
				labels = append(labels, t)
			}
		}

		err = data.InsertChannelAssetLablesTransaction(ctx, rule.GameCode, deleteRows, labels)
		if err != nil {
			log.ErrorContextf(ctx, "ApplyLabelRuleToChannel data.InsertChannelAssetLablesTransactionNew error: %v", err)
			return err
		}

		// 没数据了
		if len(channelAssets) < limit {
			break
		}
	}

	return nil
}

// ApplyGameLabelRulesToChannel 将游戏下的全部标签规则应用到channel渠道素材
func ApplyGameLabelRulesToChannel(ctx context.Context, gameCode string) error {
	ctx = log.SetXAPIPath(ctx, "ApplyGameLabelRulesToChannel")
	log.DebugContextf(ctx, "ApplyGameLabelRulesToChannel begin, gameCode:%v", gameCode)
	rules, err := repo.GetAllLabelRulesByGameCode(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "ApplyGameLabelRulesToChannel repo.GetAllLabelRulesByGameCode err:%v", err)
		return err
	}
	for _, rule := range rules {
		log.DebugContextf(ctx, "ApplyGameLabelRulesToChannel begin rule:%+v", rule)
		ApplyLabelRuleToChannel(ctx, rule)
	}

	return nil
}
