// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.1
// source: material_overview/material_overview.proto

package material_overview

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DateNode 图标数据节点
type DateNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date int32   `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"` // 日期，型如 20220210
	Num  float64 `protobuf:"fixed64,2,opt,name=num,proto3" json:"num,omitempty"`  // 浮点数据
}

func (x *DateNode) Reset() {
	*x = DateNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateNode) ProtoMessage() {}

func (x *DateNode) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateNode.ProtoReflect.Descriptor instead.
func (*DateNode) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{0}
}

func (x *DateNode) GetDate() int32 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *DateNode) GetNum() float64 {
	if x != nil {
		return x.Num
	}
	return 0
}

// Country 国家
type Country struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountryCode string `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"` // 国家缩写，用于请求
	EnName      string `protobuf:"bytes,2,opt,name=en_name,json=enName,proto3" json:"en_name,omitempty"`                // 国家英文名
	ChName      string `protobuf:"bytes,3,opt,name=ch_name,json=chName,proto3" json:"ch_name,omitempty"`                // 国家中文名
}

func (x *Country) Reset() {
	*x = Country{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Country) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Country) ProtoMessage() {}

func (x *Country) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Country.ProtoReflect.Descriptor instead.
func (*Country) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{1}
}

func (x *Country) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *Country) GetEnName() string {
	if x != nil {
		return x.EnName
	}
	return ""
}

func (x *Country) GetChName() string {
	if x != nil {
		return x.ChName
	}
	return ""
}

// 获取顶部汇总数据, POST, /api/v1/material_overview/general_data
type GeneralDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GeneralDataReq) Reset() {
	*x = GeneralDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneralDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneralDataReq) ProtoMessage() {}

func (x *GeneralDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneralDataReq.ProtoReflect.Descriptor instead.
func (*GeneralDataReq) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{2}
}

type GeneralDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result            *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                                   // 返回结果
	IeggTotalMaterial int32       `protobuf:"varint,2,opt,name=iegg_total_material,json=ieggTotalMaterial,proto3" json:"iegg_total_material,omitempty"` // iegg素材库总量
	OnlineNumList     []*DateNode `protobuf:"bytes,3,rep,name=online_num_list,json=onlineNumList,proto3" json:"online_num_list,omitempty"`              // 素材全球在线数量折线 日期从大到小排序
	ImpressionNumList []*DateNode `protobuf:"bytes,4,rep,name=impression_num_list,json=impressionNumList,proto3" json:"impression_num_list,omitempty"`  // 素材总曝光量折线 日期从大到小排序
}

func (x *GeneralDataRsp) Reset() {
	*x = GeneralDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneralDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneralDataRsp) ProtoMessage() {}

func (x *GeneralDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneralDataRsp.ProtoReflect.Descriptor instead.
func (*GeneralDataRsp) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{3}
}

func (x *GeneralDataRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GeneralDataRsp) GetIeggTotalMaterial() int32 {
	if x != nil {
		return x.IeggTotalMaterial
	}
	return 0
}

func (x *GeneralDataRsp) GetOnlineNumList() []*DateNode {
	if x != nil {
		return x.OnlineNumList
	}
	return nil
}

func (x *GeneralDataRsp) GetImpressionNumList() []*DateNode {
	if x != nil {
		return x.ImpressionNumList
	}
	return nil
}

// 较上周新增-下降数据, POST, /api/v1/material_overview/incr_decr_data
type IncrDecrDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Country []string `protobuf:"bytes,1,rep,name=country,proto3" json:"country,omitempty"` // 国家筛选列表，全球不用传
}

func (x *IncrDecrDataReq) Reset() {
	*x = IncrDecrDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncrDecrDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncrDecrDataReq) ProtoMessage() {}

func (x *IncrDecrDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncrDecrDataReq.ProtoReflect.Descriptor instead.
func (*IncrDecrDataReq) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{4}
}

func (x *IncrDecrDataReq) GetCountry() []string {
	if x != nil {
		return x.Country
	}
	return nil
}

type IncrDecrDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result            *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                                   // 返回结果
	IeggTotalMaterial int32       `protobuf:"varint,2,opt,name=iegg_total_material,json=ieggTotalMaterial,proto3" json:"iegg_total_material,omitempty"` // iegg素材库总量
	IncrNum           []*DateNode `protobuf:"bytes,3,rep,name=incr_num,json=incrNum,proto3" json:"incr_num,omitempty"`                                  // 较上周新增折线 日期从大到小排序
	DecrNum           []*DateNode `protobuf:"bytes,4,rep,name=decr_num,json=decrNum,proto3" json:"decr_num,omitempty"`                                  // 较上周下架折线 日期从大到小排序
	CountryList       []*Country  `protobuf:"bytes,5,rep,name=country_list,json=countryList,proto3" json:"country_list,omitempty"`                      // 国家下拉列表
}

func (x *IncrDecrDataRsp) Reset() {
	*x = IncrDecrDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncrDecrDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncrDecrDataRsp) ProtoMessage() {}

func (x *IncrDecrDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncrDecrDataRsp.ProtoReflect.Descriptor instead.
func (*IncrDecrDataRsp) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{5}
}

func (x *IncrDecrDataRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *IncrDecrDataRsp) GetIeggTotalMaterial() int32 {
	if x != nil {
		return x.IeggTotalMaterial
	}
	return 0
}

func (x *IncrDecrDataRsp) GetIncrNum() []*DateNode {
	if x != nil {
		return x.IncrNum
	}
	return nil
}

func (x *IncrDecrDataRsp) GetDecrNum() []*DateNode {
	if x != nil {
		return x.DecrNum
	}
	return nil
}

func (x *IncrDecrDataRsp) GetCountryList() []*Country {
	if x != nil {
		return x.CountryList
	}
	return nil
}

// DataItem 数据节点
type DataItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                              // 数据节点名称
	DataType  int32   `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`     // 数据类型 0-整数 1-float
	IntData   int32   `protobuf:"varint,3,opt,name=int_data,json=intData,proto3" json:"int_data,omitempty"`        // int数据, data_type = 0 时有效
	FloatData float64 `protobuf:"fixed64,4,opt,name=float_data,json=floatData,proto3" json:"float_data,omitempty"` // float数据, data_type = 1 时有效
}

func (x *DataItem) Reset() {
	*x = DataItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataItem) ProtoMessage() {}

func (x *DataItem) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataItem.ProtoReflect.Descriptor instead.
func (*DataItem) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{6}
}

func (x *DataItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DataItem) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *DataItem) GetIntData() int32 {
	if x != nil {
		return x.IntData
	}
	return 0
}

func (x *DataItem) GetFloatData() float64 {
	if x != nil {
		return x.FloatData
	}
	return 0
}

// 素材主题分析, POST, /api/v1/material_overview/asset_theme_analyze
type AssetThemeAnalyzeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LabelType uint32 `protobuf:"varint,1,opt,name=label_type,json=labelType,proto3" json:"label_type,omitempty"` // 请求的素材主题类型 1-人工标签 2-机器标签
}

func (x *AssetThemeAnalyzeReq) Reset() {
	*x = AssetThemeAnalyzeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetThemeAnalyzeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetThemeAnalyzeReq) ProtoMessage() {}

func (x *AssetThemeAnalyzeReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetThemeAnalyzeReq.ProtoReflect.Descriptor instead.
func (*AssetThemeAnalyzeReq) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{7}
}

func (x *AssetThemeAnalyzeReq) GetLabelType() uint32 {
	if x != nil {
		return x.LabelType
	}
	return 0
}

type ChartItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string      `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                         // 表横坐标名称
	DataList []*DataItem `protobuf:"bytes,2,rep,name=data_list,json=dataList,proto3" json:"data_list,omitempty"` // 数据类型列表
}

func (x *ChartItem) Reset() {
	*x = ChartItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChartItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChartItem) ProtoMessage() {}

func (x *ChartItem) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChartItem.ProtoReflect.Descriptor instead.
func (*ChartItem) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{8}
}

func (x *ChartItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChartItem) GetDataList() []*DataItem {
	if x != nil {
		return x.DataList
	}
	return nil
}

type AssetThemeAnalyzeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                        // 返回结果
	LabelList []*ChartItem `protobuf:"bytes,2,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"` // 标签列表 data_list名称列表: num-素材数量 ctr-点击率 cvr-转化率
}

func (x *AssetThemeAnalyzeRsp) Reset() {
	*x = AssetThemeAnalyzeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetThemeAnalyzeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetThemeAnalyzeRsp) ProtoMessage() {}

func (x *AssetThemeAnalyzeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetThemeAnalyzeRsp.ProtoReflect.Descriptor instead.
func (*AssetThemeAnalyzeRsp) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{9}
}

func (x *AssetThemeAnalyzeRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AssetThemeAnalyzeRsp) GetLabelList() []*ChartItem {
	if x != nil {
		return x.LabelList
	}
	return nil
}

// 素材观看时长分析, POST, /api/v1/material_overview/video_played_time_analysis
type VideoPlayedTimeAnalysisReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountryCodeList []string `protobuf:"bytes,1,rep,name=country_code_list,json=countryCodeList,proto3" json:"country_code_list,omitempty"` // 国家筛选列表，空列表表示全部国家
	LanguageList    []string `protobuf:"bytes,2,rep,name=language_list,json=languageList,proto3" json:"language_list,omitempty"`            // 语言列表, 空列表表示全部语言
	NeedDataList    []int32  `protobuf:"varint,3,rep,packed,name=need_data_list,json=needDataList,proto3" json:"need_data_list,omitempty"`  // 需要的数据, 1表示watch_count_distribution, 2表示country_watch_count_distribution, 3表示country_watch_duration
}

func (x *VideoPlayedTimeAnalysisReq) Reset() {
	*x = VideoPlayedTimeAnalysisReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoPlayedTimeAnalysisReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayedTimeAnalysisReq) ProtoMessage() {}

func (x *VideoPlayedTimeAnalysisReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayedTimeAnalysisReq.ProtoReflect.Descriptor instead.
func (*VideoPlayedTimeAnalysisReq) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{10}
}

func (x *VideoPlayedTimeAnalysisReq) GetCountryCodeList() []string {
	if x != nil {
		return x.CountryCodeList
	}
	return nil
}

func (x *VideoPlayedTimeAnalysisReq) GetLanguageList() []string {
	if x != nil {
		return x.LanguageList
	}
	return nil
}

func (x *VideoPlayedTimeAnalysisReq) GetNeedDataList() []int32 {
	if x != nil {
		return x.NeedDataList
	}
	return nil
}

type VideoPlayedTimeAnalysisRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result                        *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                                                                        // 返回结果
	WatchCountDistribution        []*ChartItem `protobuf:"bytes,2,rep,name=watch_count_distribution,json=watchCountDistribution,proto3" json:"watch_count_distribution,omitempty"`                        // 不同观看时长人数分布
	CountryWatchCountDistribution []*ChartItem `protobuf:"bytes,3,rep,name=country_watch_count_distribution,json=countryWatchCountDistribution,proto3" json:"country_watch_count_distribution,omitempty"` // 对比分析, 即不同国家不同观看时长人数分布
	CountryWatchDuration          []*ChartItem `protobuf:"bytes,4,rep,name=country_watch_duration,json=countryWatchDuration,proto3" json:"country_watch_duration,omitempty"`                              // 对比分析, 不同国家观看素材时长分布
}

func (x *VideoPlayedTimeAnalysisRsp) Reset() {
	*x = VideoPlayedTimeAnalysisRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_overview_material_overview_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoPlayedTimeAnalysisRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayedTimeAnalysisRsp) ProtoMessage() {}

func (x *VideoPlayedTimeAnalysisRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_overview_material_overview_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayedTimeAnalysisRsp.ProtoReflect.Descriptor instead.
func (*VideoPlayedTimeAnalysisRsp) Descriptor() ([]byte, []int) {
	return file_material_overview_material_overview_proto_rawDescGZIP(), []int{11}
}

func (x *VideoPlayedTimeAnalysisRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *VideoPlayedTimeAnalysisRsp) GetWatchCountDistribution() []*ChartItem {
	if x != nil {
		return x.WatchCountDistribution
	}
	return nil
}

func (x *VideoPlayedTimeAnalysisRsp) GetCountryWatchCountDistribution() []*ChartItem {
	if x != nil {
		return x.CountryWatchCountDistribution
	}
	return nil
}

func (x *VideoPlayedTimeAnalysisRsp) GetCountryWatchDuration() []*ChartItem {
	if x != nil {
		return x.CountryWatchDuration
	}
	return nil
}

var File_material_overview_material_overview_proto protoreflect.FileDescriptor

var file_material_overview_material_overview_proto_rawDesc = []byte{
	0x0a, 0x29, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x1c,
	0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x30, 0x0a, 0x08,
	0x44, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x6e, 0x75, 0x6d, 0x22, 0x5e,
	0x0a, 0x07, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x10,
	0x0a, 0x0e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71,
	0x22, 0xf7, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x65, 0x67, 0x67,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x69, 0x65, 0x67, 0x67, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x43, 0x0a, 0x0f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x0d,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a,
	0x13, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x11, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x2b, 0x0a, 0x0f, 0x49, 0x6e,
	0x63, 0x72, 0x44, 0x65, 0x63, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x22, 0x95, 0x02, 0x0a, 0x0f, 0x49, 0x6e, 0x63, 0x72,
	0x44, 0x65, 0x63, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69,
	0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x2e, 0x0a, 0x13, 0x69, 0x65, 0x67, 0x67, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x69,
	0x65, 0x67, 0x67, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x12, 0x36, 0x0a, 0x08, 0x69, 0x6e, 0x63, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x07, 0x69, 0x6e, 0x63, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x36, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x72,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x64, 0x65, 0x63, 0x72, 0x4e, 0x75, 0x6d,
	0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x75, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x69, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6c, 0x6f, 0x61, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x35, 0x0a, 0x14, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54,
	0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x59, 0x0a,
	0x09, 0x43, 0x68, 0x61, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08,
	0x64, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x78, 0x0a, 0x14, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x68,
	0x61, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x93, 0x01, 0x0a, 0x1a, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x6e, 0x65, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xd4, 0x02, 0x0a, 0x1a, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x69, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x56, 0x0a, 0x18,
	0x77, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x16, 0x77, 0x61,
	0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x65, 0x0a, 0x20, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x77, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x1d, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x57, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x16, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x77, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x43, 0x68, 0x61, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x14, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x57, 0x61, 0x74, 0x63, 0x68, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x40, 0x5a, 0x3e, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c,
	0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78,
	0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_material_overview_material_overview_proto_rawDescOnce sync.Once
	file_material_overview_material_overview_proto_rawDescData = file_material_overview_material_overview_proto_rawDesc
)

func file_material_overview_material_overview_proto_rawDescGZIP() []byte {
	file_material_overview_material_overview_proto_rawDescOnce.Do(func() {
		file_material_overview_material_overview_proto_rawDescData = protoimpl.X.CompressGZIP(file_material_overview_material_overview_proto_rawDescData)
	})
	return file_material_overview_material_overview_proto_rawDescData
}

var file_material_overview_material_overview_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_material_overview_material_overview_proto_goTypes = []interface{}{
	(*DateNode)(nil),                   // 0: material_overview.DateNode
	(*Country)(nil),                    // 1: material_overview.Country
	(*GeneralDataReq)(nil),             // 2: material_overview.GeneralDataReq
	(*GeneralDataRsp)(nil),             // 3: material_overview.GeneralDataRsp
	(*IncrDecrDataReq)(nil),            // 4: material_overview.IncrDecrDataReq
	(*IncrDecrDataRsp)(nil),            // 5: material_overview.IncrDecrDataRsp
	(*DataItem)(nil),                   // 6: material_overview.DataItem
	(*AssetThemeAnalyzeReq)(nil),       // 7: material_overview.AssetThemeAnalyzeReq
	(*ChartItem)(nil),                  // 8: material_overview.ChartItem
	(*AssetThemeAnalyzeRsp)(nil),       // 9: material_overview.AssetThemeAnalyzeRsp
	(*VideoPlayedTimeAnalysisReq)(nil), // 10: material_overview.VideoPlayedTimeAnalysisReq
	(*VideoPlayedTimeAnalysisRsp)(nil), // 11: material_overview.VideoPlayedTimeAnalysisRsp
	(*aix.Result)(nil),                 // 12: aix.Result
}
var file_material_overview_material_overview_proto_depIdxs = []int32{
	12, // 0: material_overview.GeneralDataRsp.result:type_name -> aix.Result
	0,  // 1: material_overview.GeneralDataRsp.online_num_list:type_name -> material_overview.DateNode
	0,  // 2: material_overview.GeneralDataRsp.impression_num_list:type_name -> material_overview.DateNode
	12, // 3: material_overview.IncrDecrDataRsp.result:type_name -> aix.Result
	0,  // 4: material_overview.IncrDecrDataRsp.incr_num:type_name -> material_overview.DateNode
	0,  // 5: material_overview.IncrDecrDataRsp.decr_num:type_name -> material_overview.DateNode
	1,  // 6: material_overview.IncrDecrDataRsp.country_list:type_name -> material_overview.Country
	6,  // 7: material_overview.ChartItem.data_list:type_name -> material_overview.DataItem
	12, // 8: material_overview.AssetThemeAnalyzeRsp.result:type_name -> aix.Result
	8,  // 9: material_overview.AssetThemeAnalyzeRsp.label_list:type_name -> material_overview.ChartItem
	12, // 10: material_overview.VideoPlayedTimeAnalysisRsp.result:type_name -> aix.Result
	8,  // 11: material_overview.VideoPlayedTimeAnalysisRsp.watch_count_distribution:type_name -> material_overview.ChartItem
	8,  // 12: material_overview.VideoPlayedTimeAnalysisRsp.country_watch_count_distribution:type_name -> material_overview.ChartItem
	8,  // 13: material_overview.VideoPlayedTimeAnalysisRsp.country_watch_duration:type_name -> material_overview.ChartItem
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_material_overview_material_overview_proto_init() }
func file_material_overview_material_overview_proto_init() {
	if File_material_overview_material_overview_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_material_overview_material_overview_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Country); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneralDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneralDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncrDecrDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncrDecrDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetThemeAnalyzeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChartItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetThemeAnalyzeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoPlayedTimeAnalysisReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_overview_material_overview_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoPlayedTimeAnalysisRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_material_overview_material_overview_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_material_overview_material_overview_proto_goTypes,
		DependencyIndexes: file_material_overview_material_overview_proto_depIdxs,
		MessageInfos:      file_material_overview_material_overview_proto_msgTypes,
	}.Build()
	File_material_overview_material_overview_proto = out.File
	file_material_overview_material_overview_proto_rawDesc = nil
	file_material_overview_material_overview_proto_goTypes = nil
	file_material_overview_material_overview_proto_depIdxs = nil
}
