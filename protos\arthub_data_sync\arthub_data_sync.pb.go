// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: arthub_data_sync/arthub_data_sync.proto

package arthub_data_sync

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 内部接口 触发定时任务, POST, /api/v1/arthub_data_sync/cron_trigger
type CronTriggerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CronName string `protobuf:"bytes,1,opt,name=cron_name,json=cronName,proto3" json:"cron_name,omitempty"` // 定时任务名字
}

func (x *CronTriggerReq) Reset() {
	*x = CronTriggerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronTriggerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronTriggerReq) ProtoMessage() {}

func (x *CronTriggerReq) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronTriggerReq.ProtoReflect.Descriptor instead.
func (*CronTriggerReq) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{0}
}

func (x *CronTriggerReq) GetCronName() string {
	if x != nil {
		return x.CronName
	}
	return ""
}

type CronTriggerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *CronTriggerRsp) Reset() {
	*x = CronTriggerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronTriggerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronTriggerRsp) ProtoMessage() {}

func (x *CronTriggerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronTriggerRsp.ProtoReflect.Descriptor instead.
func (*CronTriggerRsp) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{1}
}

func (x *CronTriggerRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 内部测试接口, POST, /api/v1/arthub_data_sync/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArthubCode string   `protobuf:"bytes,1,opt,name=arthub_code,json=arthubCode,proto3" json:"arthub_code,omitempty"` // arthub code
	Token      string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`                             // token
	Ids        []uint64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`                         // 素材列表
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{2}
}

func (x *SayHiReq) GetArthubCode() string {
	if x != nil {
		return x.ArthubCode
	}
	return ""
}

func (x *SayHiReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SayHiReq) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{3}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 同步素材status, POST, /api/v1/arthub_data_sync/sync_status
type SyncStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncStatusReq) Reset() {
	*x = SyncStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStatusReq) ProtoMessage() {}

func (x *SyncStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStatusReq.ProtoReflect.Descriptor instead.
func (*SyncStatusReq) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{4}
}

type SyncStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SyncStatusRsp) Reset() {
	*x = SyncStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStatusRsp) ProtoMessage() {}

func (x *SyncStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStatusRsp.ProtoReflect.Descriptor instead.
func (*SyncStatusRsp) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{5}
}

func (x *SyncStatusRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 同步素材status, POST, /api/v1/arthub_data_sync/sync_material
type SyncMaterialReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`  // game_code
	NodeId   string `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`        // 节点ID
	NodeType int32  `protobuf:"varint,3,opt,name=node_type,json=nodeType,proto3" json:"node_type,omitempty"` // 节点类型, 1-arthub dir
}

func (x *SyncMaterialReq) Reset() {
	*x = SyncMaterialReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncMaterialReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMaterialReq) ProtoMessage() {}

func (x *SyncMaterialReq) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMaterialReq.ProtoReflect.Descriptor instead.
func (*SyncMaterialReq) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{6}
}

func (x *SyncMaterialReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *SyncMaterialReq) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *SyncMaterialReq) GetNodeType() int32 {
	if x != nil {
		return x.NodeType
	}
	return 0
}

type SyncMaterialRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result      *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                               // 返回结果
	ExpetedTime int32       `protobuf:"varint,2,opt,name=expeted_time,json=expetedTime,proto3" json:"expeted_time,omitempty"` // 预期时间(秒)
}

func (x *SyncMaterialRsp) Reset() {
	*x = SyncMaterialRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncMaterialRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMaterialRsp) ProtoMessage() {}

func (x *SyncMaterialRsp) ProtoReflect() protoreflect.Message {
	mi := &file_arthub_data_sync_arthub_data_sync_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMaterialRsp.ProtoReflect.Descriptor instead.
func (*SyncMaterialRsp) Descriptor() ([]byte, []int) {
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP(), []int{7}
}

func (x *SyncMaterialRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SyncMaterialRsp) GetExpetedTime() int32 {
	if x != nil {
		return x.ExpetedTime
	}
	return 0
}

var File_arthub_data_sync_arthub_data_sync_proto protoreflect.FileDescriptor

var file_arthub_data_sync_arthub_data_sync_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x72, 0x74, 0x68, 0x75, 0x62, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x2f, 0x61, 0x72, 0x74, 0x68, 0x75, 0x62, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x72, 0x74, 0x68, 0x75,
	0x62, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x1a, 0x1c, 0x61, 0x69, 0x78,
	0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x2d, 0x0a, 0x0e, 0x43, 0x72, 0x6f,
	0x6e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x72, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x72, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x35, 0x0a, 0x0e, 0x43, 0x72, 0x6f, 0x6e,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x53, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x72, 0x74, 0x68, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x72, 0x74, 0x68, 0x75, 0x62, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x22, 0x2f, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x0f, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x22, 0x34, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x64, 0x0a, 0x0f,
	0x53, 0x79, 0x6e, 0x63, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x59, 0x0a, 0x0f, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78,
	0x70, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x3f, 0x5a,
	0x3d, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x72,
	0x74, 0x68, 0x75, 0x62, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_arthub_data_sync_arthub_data_sync_proto_rawDescOnce sync.Once
	file_arthub_data_sync_arthub_data_sync_proto_rawDescData = file_arthub_data_sync_arthub_data_sync_proto_rawDesc
)

func file_arthub_data_sync_arthub_data_sync_proto_rawDescGZIP() []byte {
	file_arthub_data_sync_arthub_data_sync_proto_rawDescOnce.Do(func() {
		file_arthub_data_sync_arthub_data_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_arthub_data_sync_arthub_data_sync_proto_rawDescData)
	})
	return file_arthub_data_sync_arthub_data_sync_proto_rawDescData
}

var file_arthub_data_sync_arthub_data_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_arthub_data_sync_arthub_data_sync_proto_goTypes = []interface{}{
	(*CronTriggerReq)(nil),  // 0: arthub_data_sync.CronTriggerReq
	(*CronTriggerRsp)(nil),  // 1: arthub_data_sync.CronTriggerRsp
	(*SayHiReq)(nil),        // 2: arthub_data_sync.SayHiReq
	(*SayHiRsp)(nil),        // 3: arthub_data_sync.SayHiRsp
	(*SyncStatusReq)(nil),   // 4: arthub_data_sync.SyncStatusReq
	(*SyncStatusRsp)(nil),   // 5: arthub_data_sync.SyncStatusRsp
	(*SyncMaterialReq)(nil), // 6: arthub_data_sync.SyncMaterialReq
	(*SyncMaterialRsp)(nil), // 7: arthub_data_sync.SyncMaterialRsp
	(*aix.Result)(nil),      // 8: aix.Result
}
var file_arthub_data_sync_arthub_data_sync_proto_depIdxs = []int32{
	8, // 0: arthub_data_sync.CronTriggerRsp.result:type_name -> aix.Result
	8, // 1: arthub_data_sync.SayHiRsp.result:type_name -> aix.Result
	8, // 2: arthub_data_sync.SyncStatusRsp.result:type_name -> aix.Result
	8, // 3: arthub_data_sync.SyncMaterialRsp.result:type_name -> aix.Result
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_arthub_data_sync_arthub_data_sync_proto_init() }
func file_arthub_data_sync_arthub_data_sync_proto_init() {
	if File_arthub_data_sync_arthub_data_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CronTriggerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CronTriggerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncMaterialReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_arthub_data_sync_arthub_data_sync_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncMaterialRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_arthub_data_sync_arthub_data_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_arthub_data_sync_arthub_data_sync_proto_goTypes,
		DependencyIndexes: file_arthub_data_sync_arthub_data_sync_proto_depIdxs,
		MessageInfos:      file_arthub_data_sync_arthub_data_sync_proto_msgTypes,
	}.Build()
	File_arthub_data_sync_arthub_data_sync_proto = out.File
	file_arthub_data_sync_arthub_data_sync_proto_rawDesc = nil
	file_arthub_data_sync_arthub_data_sync_proto_goTypes = nil
	file_arthub_data_sync_arthub_data_sync_proto_depIdxs = nil
}
