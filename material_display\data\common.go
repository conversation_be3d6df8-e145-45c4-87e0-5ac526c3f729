package data

import (
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
)

// OrNormalizeUTCTime 根据配置决定是否将时间转为UTC时间
func OrNormalizeUTCTime(t time.Time) time.Time {
	if conf.GetBizConf().NormalizeUTCTime {
		return t.UTC()
	}
	return t
}

// OrNormalizeUTCTimeStr 根据配置决定是否将时间字符串转为UTC时间字符串, layout: 2006-01-02 15:04:05
func OrNormalizeUTCTimeStr(s string) string {
	if conf.GetBizConf().NormalizeUTCTime {
		t, err := utils.ParseTime(s)
		if err != nil {
			return s
		}
		return utils.FormatDefault(t.UTC())
	}
	return s
}

// GetNowStrOrUTC 获取当前时间字符串，根据配置是否转成UTC
func GetNowStrOrUTC() string {
	t := time.Now()
	if conf.GetBizConf().NormalizeUTCTime {
		t = t.UTC()
	}

	return utils.FormatDefault(t)
}
