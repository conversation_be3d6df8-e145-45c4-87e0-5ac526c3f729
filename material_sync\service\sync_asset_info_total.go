package service

import (
	"context"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
)

// SyncAssetInfoTotal ...
func SyncAssetInfoTotal(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/SyncAssetInfoTotal")

	// 这里加锁，避免同时去处理
	key := "cron/SyncAssetInfoTotal"
	locked, _ := redis.GetMaterialSyncLock(ctx, key, 2*time.Hour)
	if !locked {
		log.WarningContextf(ctx, "SyncAssetInfoTotal GetLock false")
		return
	}
	// 释放锁
	defer redis.DelMaterialSyncLock(ctx, key)

	start := time.Now()
	SyncAssetInfoTotalLoop(ctx)
	log.InfoContextf(ctx, "SyncAssetInfoTotal end, cost: %v", time.Since(start))
}

// SyncAssetInfoTotalLoop ...
func SyncAssetInfoTotalLoop(ctx context.Context) {
	log.DebugContextf(ctx, "SyncAssetInfoTotalLoop start")

	st := time.Now()
	game_codes, err := getGameCodes(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getGameCodes failed: %s", err)
		return
	}

	for _, game_code := range game_codes {
		// 旧的标签数据，在这里同步
		SyncAssetInfoTotalLoopForGameCode(game_code)

		// 20241227, 注释掉， 新的标签体系不用将标签绑定到具体的某个素材上
		// 新标签体系规则应用到aix library，这里会覆盖上面的标签，以新的标签规则的标签为准
		// newCtx := log.NewSessionIDContext()
		// ApplyGameLabelRulesToAix(newCtx, game_code)
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "SyncAssetInfoTotalLoop end, cost: %v", cost)
}

// SyncAssetInfoTotalLoopForGameCode ...
func SyncAssetInfoTotalLoopForGameCode(game_code string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncAssetInfoTotalLoopForGameCode start, game code: %s", game_code)

	st := time.Now()
	defer func() {
		duration := time.Since(st)
		log.DebugContextf(ctx, "SyncAssetInfoTotalLoopForGameCode end, game code: %s, cost: %s", game_code, duration)
	}()

	overviews, err := getOverviewsWithName(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getOverviewsWithName failed: %s", err)
	}

	log.InfoContextf(ctx, "get overview number: %d", len(overviews))

	if len(overviews) == 0 {
		return
	}

	// 20240117 去掉这里的aix素材标签同步， 改为标签规则应用
	// SyncAssetLabelForGameCode(game_code, overviews)
	// SyncAssetLabelMaterialNumberForGameCode(game_code, overviews)
}
