package data

import (
	"context"
	"fmt"
	"strings"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"

	"github.com/go-pg/pg/v10"
)

// QueryCreativeOverviewByIDList 根据id列表，查素材overview
func QueryCreativeOverviewByIDList(ctx context.Context,
	gameCode string, idList []string) ([]*model.CreativeOverview, error) {
	table := getCreativeOverviewTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	var founds []*model.CreativeOverview
	err := db.Model(&model.CreativeOverview{}).Table(table).
		WhereIn("asset_id in (?)", idList).Select(&founds)
	if err != nil {
		return nil, err
	}

	return founds, nil
}

// UpdateCreativeOverviewUploadMediaInfo 事务中： 更新上传媒体信息
func UpdateCreativeOverviewUploadMediaInfo(tx *pg.Tx, gameCode string, v *model.CreativeOverview) error {
	table := getCreativeOverviewTable(gameCode)
	// sql = fmt.Sprintf(`UPDATE %s.tb_creative_overview_%s SET media_directory_id = ?,
	// 	media_directory_name = ? , media_full_path_name = ? , media_full_path_id = ?,
	// 	upload_enable = 1 , upload_to_channel = ?, country = ?, upline_date = ?, status = 1
	// 	WHERE asset_id = ?`, "arthub_sync", depot.GameCode)
	// 		_, err = tx.Exec(sql, parentId, parentName, fullPathId, fullPathName, strings.Join(uploadToChannel, ","),
	// 			strings.Join(assetReq.GetCountry(), ","), time.Now().Unix(),
	// 			item.AssetId)

	// 2022-06-20 素材支持重复上传，且能上传到不同目录，media相关的字段都不再使用
	_, err := tx.Model(v).Table(table).
		Where("asset_id = ?", v.AssetID).
		Column("upload_enable", "upload_state", "upload_to_channel", "country", "upline_date", "status").
		Update()

	return err
}

func getCreativeOverviewTable(gameCode string) string {
	return fmt.Sprintf("arthub_sync.tb_creative_overview_%s", gameCode)
}

// QueryCreativeOverviewByName 根据素材名称列表，批量精确查询素材overview
func QueryCreativeOverviewByName(ctx context.Context,
	gameCode string, assetNames []string) ([]*model.CreativeOverview, error) {
	table := getCreativeOverviewTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	var founds []*model.CreativeOverview
	err := db.Model(&model.CreativeOverview{}).Table(table).
		Where("asset_status = ?", arthub.ARTHUB_ASSET_STATUS_NORMAL). // 只查正常状态的素材
		WhereIn("asset_name in (?)", assetNames).Select(&founds)
	if err != nil {
		return nil, err
	}

	return founds, nil
}

// QueryCreativeOverviewByKeyWords 通过关键词搜索素材名称
func QueryCreativeOverviewByKeyWords(ctx context.Context, gameCode string, assetNames []string) ([]*model.CreativeOverview, error) {
	var overviews []*model.CreativeOverview
	tableName := model.GetCreativeOverviewTableName(gameCode)
	pgQuery := postgresql.GetDBWithContext(ctx).Model(&overviews).Table(tableName)
	pgQuery.WhereGroup(func(q *pg.Query) (*pg.Query, error) {
		for _, text := range assetNames {
			q = q.WhereOr(fmt.Sprintf("arthub_sync.split_asset_name(asset_name) @> arthub_sync.split_asset_name('%s')", text))
			words := strings.Split(text, " ")
			if len(words) == 1 {
				firstWord := words[0]
				q = q.WhereOr(fmt.Sprintf("asset_name like '%s%%'", firstWord))
			}
		}

		return q, nil
	})

	err := pgQuery.Select()
	if err != nil {
		return nil, fmt.Errorf("select overview with key words failed: %s", err)
	}

	return overviews, nil
}
