const { analyzeStruct, GoStructAnalyzer } = require('./struct-analyzer.js');

/**
 * 测试结构体分析器
 */
function runTests() {
    console.log('开始测试Go结构体分析器...\n');
    
    // 测试1: 分析material_display.GetMaterialInfoRsp
    console.log('测试1: 分析material_display.GetMaterialInfoRsp结构体');
    console.log('-'.repeat(40));

    try {
        const result1 = analyzeStruct('material_display.GetMaterialInfoRsp', '.');

        if (result1 && result1.size > 0) {
            console.log('✅ 测试1通过 - 成功分析material_display.GetMaterialInfoRsp');

            // 验证是否包含预期的结构体
            const expectedStructs = ['GetMaterialInfoRsp', 'Result', 'MaterialExt', 'Universal', 'Label', 'Statistics', 'Video'];
            const foundStructs = Array.from(result1.keys());

            console.log('\n预期结构体:', expectedStructs);
            console.log('实际找到:', foundStructs);

            const missing = expectedStructs.filter(s => !foundStructs.includes(s));
            if (missing.length === 0) {
                console.log('✅ 所有预期结构体都已找到');
            } else {
                console.log('⚠️  缺少结构体:', missing);
            }
        } else {
            console.log('❌ 测试1失败 - 未找到任何结构体');
        }
    } catch (error) {
        console.log('❌ 测试1出错:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // 测试2: 分析material_display.GetKeywordListReq
    console.log('测试2: 分析material_display.GetKeywordListReq结构体');
    console.log('-'.repeat(40));

    try {
        const result2 = analyzeStruct('material_display.GetKeywordListReq', '.');

        if (result2 && result2.size > 0) {
            console.log('✅ 测试2通过 - 成功分析material_display.GetKeywordListReq');
            console.log('找到的结构体:', Array.from(result2.keys()));
        } else {
            console.log('❌ 测试2失败 - 未找到任何结构体');
        }
    } catch (error) {
        console.log('❌ 测试2出错:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');

    // 测试3: 分析material_sync.UploadVirtualAssetsReq
    console.log('测试3: 分析material_sync.UploadVirtualAssetsReq结构体');
    console.log('-'.repeat(40));

    try {
        const result3 = analyzeStruct('material_sync.UploadVirtualAssetsReq', '.');

        if (result3 && result3.size > 0) {
            console.log('✅ 测试3通过 - 成功分析material_sync.UploadVirtualAssetsReq');
            console.log('找到的结构体:', Array.from(result3.keys()));

            // 验证是否包含VirtualAsset依赖
            if (result3.has('VirtualAsset')) {
                console.log('✅ 正确识别了VirtualAsset依赖');
            } else {
                console.log('⚠️  未找到VirtualAsset依赖');
            }
        } else {
            console.log('❌ 测试3失败 - 未找到任何结构体');
        }
    } catch (error) {
        console.log('❌ 测试3出错:', error.message);
    }

    console.log('\n' + '='.repeat(60) + '\n');

    // 测试4: 测试不存在的结构体
    console.log('测试4: 分析不存在的结构体');
    console.log('-'.repeat(40));

    try {
        const result4 = analyzeStruct('material_display.NonExistentStruct', '.');

        if (!result4 || result4.size === 0) {
            console.log('✅ 测试4通过 - 正确处理不存在的结构体');
        } else {
            console.log('❌ 测试4失败 - 不应该找到不存在的结构体');
        }
    } catch (error) {
        console.log('✅ 测试4通过 - 正确抛出错误:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // 测试5: 直接使用分析器类
    console.log('测试5: 直接使用GoStructAnalyzer类');
    console.log('-'.repeat(40));
    
    try {
        const analyzer = new GoStructAnalyzer();
        analyzer.initializePackagePaths('.');
        
        // 测试解析单个文件
        const materialStructs = analyzer.parseGoFile('./protos/material_display/material_display.pb.go');
        const aixStructs = analyzer.parseGoFile('./protos/aix/aix_common_message.pb.go');
        
        console.log(`Material Display包中找到 ${materialStructs.size} 个结构体`);
        console.log(`AIX包中找到 ${aixStructs.size} 个结构体`);
        
        if (materialStructs.size > 0 && aixStructs.size > 0) {
            console.log('✅ 测试5通过 - 成功解析Go文件');
        } else {
            console.log('❌ 测试5失败 - 解析Go文件失败');
        }
    } catch (error) {
        console.log('❌ 测试5出错:', error.message);
    }
    
    console.log('\n测试完成!');
}

/**
 * 演示用法示例
 */
function demonstrateUsage() {
    console.log('\n' + '='.repeat(60));
    console.log('使用示例演示');
    console.log('='.repeat(60));
    
    console.log('\n1. 命令行使用:');
    console.log('   node struct-analyzer.js GetMaterialInfoRsp');
    console.log('   node struct-analyzer.js MaterialExt .');
    
    console.log('\n2. 作为模块使用:');
    console.log(`
const { analyzeStruct, GoStructAnalyzer } = require('./struct-analyzer.js');

// 分析结构体依赖
const dependencies = analyzeStruct('GetMaterialInfoRsp', '.');

// 或者使用分析器类
const analyzer = new GoStructAnalyzer();
const result = analyzer.analyzeStructDependencies('GetMaterialInfoRsp', 'material_display', '.');
`);
    
    console.log('\n3. 输出格式:');
    console.log('   - 控制台输出: 显示找到的结构体列表和完整定义');
    console.log('   - 返回值: Map对象，包含所有相关结构体的详细信息');
}

// 运行测试
if (require.main === module) {
    runTests();
    demonstrateUsage();
}

module.exports = {
    runTests,
    demonstrateUsage
};
