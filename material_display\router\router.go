package router

import (
	"bytes"
	"encoding/json"
	"net/http"

	"e.coding.intlgame.com/ptc/aix-backend/common/metrics/ginmetrics"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pkgMiddleware "e.coding.intlgame.com/ptc/aix-backend/common/pkg/middleware"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/preprocess"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/service"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// InitRouter 初始化路由
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(pkgMiddleware.GinLogger())
	r.Use(pkgMiddleware.GinRecovery(true))

	// prometheus metrics
	m := ginmetrics.GetMonitor()
	m.Use(r)

	// 变更类接口，接入审计
	r.Use(pkgMiddleware.GinOperationAudit("material_display", []string{
		"directory_create", "set_material_info", "upload_to_arthub",
		"add_label_rule", "modify_label_rule_second_label", "modify_rule_content_labels", "delete_label_rule",
		"add_upload_to_channel_task", "cancel_upload_to_channel_task", "resume_upload_to_channel_task",
		"add_automatic_sync_task_rule", "change_automatic_sync_task_rule_status", "delete_automatic_sync_task_rule"}))

	serverAPI := r.Group("api/v1/material_display/")

	// 素材展示
	serverAPI.POST("/material_list", func(ctx *gin.Context) {
		ctx.Set("session_id", uuid.New().String())
		req, rsp := &pb.MaterialListReq{}, &pb.MaterialListRsp{}
		httpCode, code := preprocess.BindAndValid(ctx, req)
		log.DebugContextf(ctx, "material list httpCode: %d, code: %d, req: %+v", httpCode, code, req)

		err := service.MaterialList(ctx, req, rsp)
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 素材详情
	serverAPI.POST("/get_material_info", func(ctx *gin.Context) {
		ctx.Set("session_id", uuid.New().String())
		req, rsp := &pb.GetMaterialInfoReq{}, &pb.GetMaterialInfoRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.GetMaterialInfo(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 批量素材详情
	serverAPI.POST("/bt_get_material_info", func(ctx *gin.Context) {
		ctx.Set("session_id", uuid.New().String())
		req, rsp := &pb.BtGetMaterialInfoReq{}, &pb.BtGetMaterialInfoRsp{}
		httpCode, code := preprocess.BindAndValid(ctx, req)
		log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)
		err := service.BtGetMaterialInfo(ctx, req, rsp)
		if err != nil {
			log.ErrorContextf(ctx, "service.MaterialList failed, err: %v", err)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 素材展示搜索,可选搜索素材库或广告库
	serverAPI.POST("/search_material", searchMaterials)

	// 素材名字搜索
	serverAPI.POST("/search_materials_by_name", func(ctx *gin.Context) {
		req, rsp := &pb.SearchMaterialsByNameReq{}, &pb.SearchMaterialsByNameRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.SearchMaterialsByName(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 批量素材名字搜索
	serverAPI.POST("/batch_search_materials_by_name", func(ctx *gin.Context) {
		req, rsp := &pb.BatchSearchMaterialsByNameReq{}, &pb.BatchSearchMaterialsByNameRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.BatchSearchMaterialsByName(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 获取素材目录列表
	serverAPI.POST("/directory_list", directoryList)

	// 获取指定目录信息
	serverAPI.POST("/directory_get", directoryGet)

	// 新建目录
	serverAPI.POST("/directory_create", directoryCreate)

	// 移动素材到其他目录
	// serverApi.POST("/materials_move", materialsMove)

	// 获取媒体素材目录信息
	serverAPI.POST("/media_directory_list", mediaDirectoryList)

	// 获取媒体素材目录信息test
	serverAPI.POST("/media_directory_list_test", mediaDirectoryList)

	// 素材详情
	serverAPI.POST("/set_material_info", setMaterialInfo)

	// 上传素材
	serverAPI.POST("/upload_to_channel", func(ctx *gin.Context) {
		ctx.Set("session_id", uuid.New().String())
		req, rsp := &pb.UploadToChannelReq{}, &pb.UploadToChannelRsp{}
		httpCode, code := preprocess.BindAndValid(ctx, req)
		if httpCode != http.StatusOK {
			log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
			rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "invalid param"}
			preprocess.Response(ctx, rsp)
			return
		}
		log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)
		err := service.UploadToChannel(ctx, req, rsp)
		if err != nil {
			log.DebugContextf(ctx, "service.UploadToChannel failed, err: %v", err)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 用户上传素材到arthub数据同步
	serverAPI.POST("/upload_to_arthub", uploadToArthub)

	// 获取arthub token信息
	serverAPI.POST("/get_depot_token", getDepotToken)

	// 获取arthub素材临时下载url
	serverAPI.POST("/generate_arthub_temp_download_url", generateArthubTempDownloadURL)

	// 获取arthub gamecode列表信息
	serverAPI.POST("/list_depot", listDepot)

	// 获取媒体列表
	serverAPI.POST("/media_material_list", mediaMaterialList)

	// 获取其他信息
	serverAPI.POST("/get_ext_info", func(ctx *gin.Context) {
		ctx.Set("session_id", uuid.New().String())
		req, rsp := &pb.GetExtInfoReq{}, &pb.GetExtInfoRsp{}
		httpCode, code := preprocess.BindAndValid(ctx, req)
		log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)
		err := service.GetExtInfo(ctx, req, rsp)
		if err != nil {
			log.ErrorContextf(ctx, "service.GetExtInfo failed, err: %v", err)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})
	// 通过目录获取媒体列表
	serverAPI.POST("/media_material_by_directory_id", mediaMaterialByDirectoryID)

	// 根据ID(resource_name)列表查询素材名称列表
	serverAPI.POST("/media_material_name_list", mediaMaterialNameList)

	// 增加上传任务
	serverAPI.POST("/add_upload_to_channel_task", func(ctx *gin.Context) {
		req, rsp := &pb.AddUploadToChannelTaskReq{}, &pb.AddUploadToChannelTaskRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.AddUploadToChannelTask(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 取消上传任务
	serverAPI.POST("/cancel_upload_to_channel_task", func(ctx *gin.Context) {
		req, rsp := &pb.CancelUploadToChannelTaskReq{}, &pb.CancelUploadToChannelTaskRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.CancelUploadToChannelTask(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 重启上传任务
	serverAPI.POST("/resume_upload_to_channel_task", func(ctx *gin.Context) {
		req, rsp := &pb.ResumeUploadToChannelTaskReq{}, &pb.ResumeUploadToChannelTaskRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.ResumeUploadToChannelTask(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 获取提示关键词
	serverAPI.POST("/get_keyword_list", func(ctx *gin.Context) {
		ctx.Set("session_id", uuid.New().String())
		req, rsp := &pb.GetKeywordListReq{}, &pb.GetKeywordListRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			rsp, err = service.GetKeywordList(ctx, req)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 批量拉取素材详细信息
	serverAPI.POST("/bt_get_material_info_detail", func(ctx *gin.Context) {
		req, rsp := &pb.BtGetMaterialInfoDetailReq{}, &pb.BtGetMaterialInfoDetailRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.BtGetMaterialInfoDetail(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 新标签体系接口 ---- 开始 ----

	// 增加/更新标签规则
	serverAPI.POST("/add_label_rule", func(ctx *gin.Context) {
		req, rsp := &pb.AddLabelRuleReq{}, &pb.AddLabelRuleRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.AddLabelRule(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 更新asset层级标签
	serverAPI.POST("/update_asset_level_labels", func(ctx *gin.Context) {
		req, rsp := &pb.UpdateAssetLevelLabelsReq{}, &pb.UpdateAssetLevelLabelsRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.UpdateAssetLevelLabels(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 修改标签规则二级标签
	serverAPI.POST("/modify_label_rule_second_label", func(ctx *gin.Context) {
		req, rsp := &pb.ModifyLabelRuleSecondLabelReq{}, &pb.ModifyLabelRuleSecondLabelRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.ModifyLabelRuleSecondLabel(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 增加标签规则机器内容标签
	serverAPI.POST("/modify_rule_content_labels", func(ctx *gin.Context) {
		req, rsp := &pb.ModifyRuleContentLabelsReq{}, &pb.ModifyRuleContentLabelsRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.ModifyRuleContentLabels(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 删除标签规则
	serverAPI.POST("/delete_label_rule", func(ctx *gin.Context) {
		req, rsp := &pb.DeleteLabelRuleReq{}, &pb.DeleteLabelRuleRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.DeleteLabelRule(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 拉取标签规则
	serverAPI.POST("/get_label_rules", func(ctx *gin.Context) {
		req, rsp := &pb.GetLabelRulesReq{}, &pb.GetLabelRulesRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.GetLabelRules(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 导出标签规则
	serverAPI.POST("/download_label_rules", func(ctx *gin.Context) {
		req, rsp := &pb.DownloadLabelRulesReq{}, &pb.DownloadLabelRulesRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.DownloadLabelRules(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 拉取标签规则下的素材
	serverAPI.POST("/get_assets_by_label_rule", func(ctx *gin.Context) {
		req, rsp := &pb.GetAssetsByLabelRuleReq{}, &pb.GetAssetsByLabelRuleRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.GetAssetsByLabelRule(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 拉取标签规则下的渠道素材
	serverAPI.POST("/get_channel_assets_by_label_rule", func(ctx *gin.Context) {
		req, rsp := &pb.GetChannelAssetsByLabelRuleReq{}, &pb.GetChannelAssetsByLabelRuleRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.GetChannelAssetsByLabelRule(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 提交标签规则批量打标任务
	serverAPI.POST("/add_label_rule_task", func(ctx *gin.Context) {
		req, rsp := &pb.AddLabelRuleTaskReq{}, &pb.AddLabelRuleTaskRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.AddLabelRuleTask(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// V2提交标签规则批量打标任务(支持serial和asset层级)
	serverAPI.POST("/add_label_rule_task_v2", func(ctx *gin.Context) {
		req, rsp := &pb.AddLabelRuleTaskV2Req{}, &pb.AddLabelRuleTaskV2Rsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.AddLabelRuleTaskV2(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 新标签体系接口 ---- 结束 ----

	// 检查自动化上传任务规则名称是否重复
	serverAPI.POST("/check_automatic_sync_task_rule_name", func(ctx *gin.Context) {
		req, rsp := &pb.CheckAutomaticSyncTaskRuleNameReq{}, &pb.CheckAutomaticSyncTaskRuleNameRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.CheckAutomaticSyncTaskRuleName(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 增加/更新自动化上传任务规则
	serverAPI.POST("/add_automatic_sync_task_rule", func(ctx *gin.Context) {
		req, rsp := &pb.AddAutomaticSyncTaskRuleReq{}, &pb.AddAutomaticSyncTaskRuleRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.AddAutomaticSyncTaskRule(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	serverAPI.POST("/get_automatic_sync_task_rule", func(ctx *gin.Context) {
		req, rsp := &pb.GetAutomaticSyncTaskRuleReq{}, &pb.GetAutomaticSyncTaskRuleRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.GetAutomaticSyncTaskRule(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 更新自动化上传任务规则状态
	serverAPI.POST("/change_automatic_sync_task_rule_status", func(ctx *gin.Context) {
		req, rsp := &pb.ChangeAutomaticSyncTaskRuleStatusReq{}, &pb.ChangeAutomaticSyncTaskRuleStatusRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.ChangeAutomaticSyncTaskRuleStatus(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 删除自动化上传任务规则
	serverAPI.POST("/delete_automatic_sync_task_rule", func(ctx *gin.Context) {
		req, rsp := &pb.DeleteAutomaticSyncTaskRuleReq{}, &pb.DeleteAutomaticSyncTaskRuleRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.DeleteAutomaticSyncTaskRule(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})
	// 自动化上传接口 ---- 结束 ----

	// 搜索素材目录, POST, /api/v1/material_display/directory_search
	serverAPI.POST("/directory_search", func(ctx *gin.Context) {
		req, rsp := &pb.DirectorySearchReq{}, &pb.DirectorySearchRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.DirectorySearch(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 网盘接入授权 POST /api/v1/material_display/cloud_drive_grant
	serverAPI.POST("/cloud_drive_grant", func(ctx *gin.Context) {
		req, rsp := &pb.CloudDriveGrantReq{}, &pb.CloudDriveGrantRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.DebugContextf(ctx, "BindRequest req:%+v, err:%v", req, err)
		if err == nil {
			err = service.CloudDriveGrant(ctx, req, rsp)
		}
		errs.FullRsp(rsp, err)
		preprocess.Response(ctx, rsp)
	})

	return r
}

// searchMaterials 搜索素材
func searchMaterials(ctx *gin.Context) {
	req, rsp := &pb.SearchMaterialsReq{}, &pb.SearchMaterialsRsp{}
	err := preprocess.BindAndValidReturnError(ctx, req)
	log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
	if err == nil {
		err = service.SearchMaterials(ctx, req, rsp)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

func directoryGet(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req := &pb.DirectoryGetReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)

	if httpCode != http.StatusOK {
		resp := &pb.DirectoryGetRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}
	resp, err := service.DirectoryGet(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get material firectory by id, req: %v, err: %v", req, err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

// directoryList ...
func directoryList(ctx *gin.Context) {
	req := &pb.DirectoryListReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)

	if httpCode != http.StatusOK {
		resp := &pb.DirectoryListRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}

	resp, err := service.DirectoryList(ctx, req)
	if err != nil {
		resp = &pb.DirectoryListRsp{}
		log.ErrorContextf(ctx, "Fail to get material firectory list, req: %v, err: %v", req, err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

// directoryCreate ...
func directoryCreate(ctx *gin.Context) {
	req := &pb.DirectoryCreateReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req.ParentId: %v, req.Name: %v", httpCode, code, req.GetParentId(), req.Name)

	if httpCode != http.StatusOK {
		resp := &pb.DirectoryCreateRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}

	resp, err := service.DirectoryCreate(ctx, req)
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

func materialsMove(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req := &pb.MaterialMoveReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req.Ids: %v, req.OtherParentId: %+v",
		httpCode, code, req.GetIds(), req.GetOtherParentId())

	if httpCode != http.StatusOK {
		resp := &pb.MaterialMoveRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}
	resp, err := service.MoveMaterialToOtherDirectory(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to move material, err: %v", err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

func mediaDirectoryList(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req := &pb.MediaDirectoryListReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req.GetParentId: %v",
		httpCode, code, req.GetParentId())

	if httpCode != http.StatusOK {
		resp := &pb.MediaDirectoryListRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}
	resp, err := service.ListMediaMaterialDirectoryInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to move material, err: %v", err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

func getDepotToken(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req := &pb.GetDepotTokenReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, game_codes: %v",
		httpCode, code, req.GetGameCodeList())

	if httpCode != http.StatusOK {
		resp := &pb.GetDepotTokenRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}

	resp, err := service.GetDepotInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get depot information, err: %v", err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

func listDepot(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req := &pb.ListDepotReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, offset: %v, limit: %v",
		httpCode, code, req.GetOffset(), req.GetLimit())

	if httpCode != http.StatusOK {
		resp := &pb.ListDepotRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}

	resp, err := service.ListDepotInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get depot information, err: %v", err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

func generateArthubTempDownloadURL(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req := &pb.GenerateArthubTempDownloadUrlReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req:%v", httpCode, code, req)
	//debug
	items := req.GetItems()
	log.DebugContextf(ctx, "generateArthubTempDownloadURL request items length: %d", len(items))
	for idx := range items {
		if items[idx] != nil {
			log.DebugContextf(ctx, "generateArthubTempDownloadURL idx: %d, ,item: %v", idx, items[idx])
		}
	}

	if httpCode != http.StatusOK {
		resp := &pb.GetDepotTokenRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}

	resp, err := service.GenerateArthubTempDownloadUrl(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get arthub signed url information, err: %v", err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: err.Error()}
	}
	errs.FullRsp(resp, err)
	//preprocess.Response(ctx, resp)
	// 这里返回的数据里包含arthub签名后的url，preprocess.Response函数会去转义特殊字符
	// 所以这里使用原生json包屏蔽特殊字符转义问题
	respBytes, _ := jsonMarshal(resp)
	ctx.Writer.WriteHeader(http.StatusOK)
	ctx.Writer.Write(respBytes)
}

func uploadToArthub(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req := &pb.UploadToArthubReq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, asset id list: %v",
		httpCode, code, req.GetAssetIdList())

	if httpCode != http.StatusOK {
		resp := &pb.UploadToArthubRsp{}
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, resp)
		return
	}

	resp, err := service.UploadToArthub(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "Failed to sync db after uploaded, err: %v", err)
		resp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}
	errs.FullRsp(resp, err)
	preprocess.Response(ctx, resp)
}

func mediaMaterialList(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req, rsp := &pb.MediaMaterialListReq{}, &pb.MediaMaterialListRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)

	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.MediaMaterialList(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get mediaMaterialList information, err: %v", err)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
	}

	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// 通过目录获取媒体列表
func mediaMaterialByDirectoryID(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req, rsp := &pb.MediaMaterialByDirectoryIdReq{}, &pb.MediaMaterialByDirectoryIdRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)

	err := service.MediaMaterialByDirectoryId(ctx, req, rsp)
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// 通过目录获取媒体列表
func mediaMaterialNameList(ctx *gin.Context) {
	ctx.Set("session_id", uuid.New().String())
	req, rsp := &pb.MediaMaterialNameListReq{}, &pb.MediaMaterialNameListRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "httpCode: %d, code: %d, req: %+v", httpCode, code, req)

	err := service.MediaMaterialNameList(ctx, req, rsp)
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// 特殊字符不转义
func jsonMarshal(t interface{}) ([]byte, error) {
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	err := encoder.Encode(t)
	return buffer.Bytes(), err
}

// setMaterialInfo 设置素材信息
func setMaterialInfo(ctx *gin.Context) {
	req, rsp := &pb.SetMaterialInfoReq{}, &pb.SetMaterialInfoRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "set material info httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), ErrorMessage: "system error"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.SetMaterialInfo(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.SetMaterialInfo failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}
