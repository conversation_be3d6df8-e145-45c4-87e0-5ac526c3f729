package rpc

import (
	"context"
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise"
)

// ApplovinCreateCreativeSet applovin创建广告creative set
func ApplovinCreateCreativeSet(ctx context.Context, creator string, req *pb.CreateCreativeSetReq) (*pb.CreativeSet, error) {
	url := fmt.Sprintf("%s/api/v1/applovin_advertise/create_creative_set", conf.GetBizConf().ApplovinAdvertiseHost)

	request := newRequest(ctx).SetHeader(constant.HeaderUserName, creator)
	rlt := &pb.CreateCreativeSetRsp{}
	response, err := request.SetBody(req).SetResult(rlt).Post(url)
	if err != nil {
		return nil, err
	}
	if !response.IsSuccess() {
		return nil, fmt.Errorf("ApplovinCreateCreativeSet failed, status:%d, body:%s", response.StatusCode(), response.String())
	}

	if rlt.GetResult().GetErrorCode() != 0 {
		return nil, fmt.Errorf("ApplovinCreateCreativeSet failed, code: %v, msg: %v",
			rlt.GetResult().GetErrorCode(), rlt.GetResult().GetErrorMessage())
	}

	return rlt.GetCreativeSet(), nil
}
