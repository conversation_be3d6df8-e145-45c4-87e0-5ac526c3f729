package service

import (
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// StartMediaContentMap 启动UA视频联调
func StartMediaContentMap(ctx *gin.Context, req *material_sync.StartMediaContentMapReq, rsp *material_sync.StartMediaContentMapRsp) error {
	game_code := ctx.Request.Header.Get("game")

	go SyncCreativeRecommendMediaContentMapForGameCode(game_code)

	return nil
}
