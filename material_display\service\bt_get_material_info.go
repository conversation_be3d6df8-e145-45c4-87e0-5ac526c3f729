// Package service 服务接口实现代码
package service

import (
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
)

// BtGetMaterialInfo 批量拉取素材详情
func BtGetMaterialInfo(ctx *gin.Context, req *pb.BtGetMaterialInfoReq, rsp *pb.BtGetMaterialInfoRsp) error {
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if gameCode == "" {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	for _, assetID := range req.GetAssetIdList() {
		detail, err := data.GetMaterialDetail(ctx, gameCode, assetID)
		if err != nil {
			return errs.New(int(pbAix.DemoServerErrCode_DEMO_SERVER_QUERY_DB_ERROR), "error asset_id")
		}
		rsp.MaterialExtList = append(rsp.MaterialExtList, detail2proto(ctx, detail))
	}
	return nil
}
