package rpc

import (
	"context"
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
	"github.com/thoas/go-funk"
)

type labelOption struct {
	Name     string   `json:"name"`
	Multiple bool     `json:"multiple"`
	Options  []string `json:"options"`
}

type creativeHubConfig struct {
	ManualLabel map[string][]labelOption `json:"manual_label"`
}

type getFirstLabelOptionData struct {
	CreativeHubConfig creativeHubConfig `json:"creative_hub_config"`
}

type getFirstLabelOptionRsp struct {
	Ret  int                     `json:"ret"`
	Data getFirstLabelOptionData `json:"data"`
}

// GetAllFirstLabels 拉取game下的标签系统全部一级标签
func GetAllFirstLabels(ctx context.Context, gameCode string) ([]string, error) {
	url := conf.GetBizConf().GetFirstLabelOptionURL
	req := newRequest(ctx)
	rsp := &getFirstLabelOptionRsp{}
	_, err := req.SetResult(rsp).Get(url)
	if err != nil {
		return nil, err
	}

	if rsp.Ret != 0 {
		return nil, fmt.Errorf("GetAllFirstLabels err rsp.Ret:%v", rsp.Ret)
	}

	var firstLabels []string
	// 先取game_code下的配置，没有就取 default
	labelOptions, ok := rsp.Data.CreativeHubConfig.ManualLabel[gameCode]
	if !ok {
		labelOptions = rsp.Data.CreativeHubConfig.ManualLabel["default"]
	}
	for _, labelOption := range labelOptions {
		firstLabels = append(firstLabels, labelOption.Name)
	}
	return funk.UniqString(firstLabels), nil
}
