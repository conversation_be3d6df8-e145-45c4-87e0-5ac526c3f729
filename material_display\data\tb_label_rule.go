package data

import (
	"context"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/go-pg/pg/v10"
)

// UpdateLabelRuleByFields 更新标签规则指定字段
func UpdateLabelRuleByFields(ctx context.Context, r *pb.LabelRule, fields []string) error {
	t := PBLabelRuleToPG(r)
	t.ID = r.GetId()
	query := postgresql.GetDBWithContext(ctx).Model(t)
	query.Column(fields...)
	_, err := query.WherePK().Where("game_code = ?", t.GameCode).Update()
	return err
}

// UpsertLabelRule 插入更新标签规则
func UpsertLabelRule(ctx context.Context, r *pb.LabelRule) error {
	t := PBLabelRuleToPG(r)
	err := repo.UpsertLabelRule(ctx, t, true)
	if err != nil {
		return err
	}

	// 赋值生成的自增id
	r.Id = t.ID
	return nil
}

// DeleteLabelRule 删除标签规则
func DeleteLabelRule(ctx context.Context, r *model.TbAssetLabelRule) error {
	query := postgresql.GetDBWithContext(ctx).Model(r)
	_, err := query.WherePK().Delete()
	return err
}

// GetAllLabelRules 拉取game_code下的所有标签规则
func GetAllLabelRules(ctx context.Context, gameCode string) ([]*pb.LabelRule, error) {
	rules, err := repo.GetAllLabelRulesByGameCode(ctx, gameCode)
	if err != nil {
		return nil, err
	}

	var rlt []*pb.LabelRule
	for _, rule := range rules {
		rlt = append(rlt, PGLabelRuleToPB(rule))
	}
	return rlt, nil
}

// GetPageLabelRules 拉取game_code下的标签规则, 分页
func GetPageLabelRules(ctx context.Context, gameCode string, offset, limit int) ([]*pb.LabelRule, int, error) {
	rules, total, err := repo.GetPageLabelRulesByGameCode(ctx, gameCode, offset, limit)
	if err != nil {
		return nil, 0, err
	}

	var rlt []*pb.LabelRule
	for _, rule := range rules {
		rlt = append(rlt, PGLabelRuleToPB(rule))
	}
	return rlt, total, nil
}

// GetLabelRule 查某个标签规则
func GetLabelRule(ctx context.Context, gameCode string, id int64) (*pb.LabelRule, error) {
	t, err := repo.GetLabelRuleByGameCodeAndID(ctx, gameCode, id)
	if err != nil {
		return nil, err
	}

	r := PGLabelRuleToPB(t)
	return r, nil
}

// GetLabelRuleByName 查某个标签规则, 没查到返回nil
func GetLabelRuleByName(ctx context.Context, gameCode string, ruleName string, ruleType int) (*pb.LabelRule, error) {
	t := &model.TbAssetLabelRule{}
	query := postgresql.GetDBWithContext(ctx).Model(t)
	query.Where("game_code = ?", gameCode)
	query.Where("rule = ?", ruleName)
	query.Where("type = ?", ruleType)
	err := query.First()
	if err != nil {
		if err == pg.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	r := PGLabelRuleToPB(t)
	return r, nil
}

// PBLabelRuleToPG pb标签规则转pg标签规则
func PBLabelRuleToPG(r *pb.LabelRule) *model.TbAssetLabelRule {
	t := &model.TbAssetLabelRule{
		GameCode:    r.GetGameCode(),
		Rule:        r.GetRule(),
		Type:        r.GetType(),
		CreateUser:  r.GetCreater(),
		UpdateUser:  r.GetUpdater(),
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
		OfflineDate: r.GetOfflineDate(),
	}
	for _, label := range r.GetLabels() {
		t.Labels = append(t.Labels, model.AssetLabel{
			LabelName:   label.GetLabelName(),
			FirstLabel:  label.GetFirstLabel(),
			SecondLabel: label.GetSecondLabel(),
		})
	}
	for _, op := range r.GetLabelOptions() {
		t.LabelOptions = append(t.LabelOptions, model.LabelOption{
			LabelName: op.GetLabelName(),
			Options:   op.GetOptions(),
		})
	}

	return t
}

// PGLabelRuleToPB pg标签规则转pb标签规则
func PGLabelRuleToPB(t *model.TbAssetLabelRule) *pb.LabelRule {
	r := &pb.LabelRule{
		Id:             t.ID,
		GameCode:       t.GameCode,
		Rule:           t.Rule,
		Type:           t.Type,
		Creater:        t.CreateUser,
		Updater:        t.UpdateUser,
		CreateTime:     t.CreateTime.Format(time.RFC3339),
		UpdateTime:     t.UpdateTime.Format(time.RFC3339),
		ImpressionDate: t.ImpressionDate,
		OfflineDate:    t.OfflineDate,
	}
	for _, label := range t.Labels {
		r.Labels = append(r.Labels, &pb.AssetLabel{
			LabelName:   label.LabelName,
			FirstLabel:  label.FirstLabel,
			SecondLabel: label.SecondLabel,
		})
	}
	for _, op := range t.LabelOptions {
		r.LabelOptions = append(r.LabelOptions, &pb.LabelOption{
			LabelName: op.LabelName,
			Options:   op.Options,
		})
	}

	return r
}
