package service

import (
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// SayHi 设置素材机器标签及封面信息
func SayHi(ctx *gin.Context, _ *pb.SayHiReq, _ *pb.SayHiRsp) error {
	game_code := ctx.Request.Header.Get("game")

	log.DebugContextf(ctx, "get game code: %s", game_code)
	log.InfoContextf(ctx, "get game code: %s", game_code)
	log.WarningContextf(ctx, "get game code: %s", game_code)
	log.ErrorContextf(ctx, "get game code: %s", game_code)

	return nil
}
