package cron

import (
	"context"

	"e.coding.intlgame.com/ptc/aix-backend/common/metrics/ginmetrics"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/metrics"
)

// 定时统计素材指标
func materialMetrics(ctx context.Context) {
	log.DebugContextf(ctx, "[info] materialMetrics start")

	gameCode2Depot, err := data.GetDepotCfg()
	if err != nil {
		return
	}

	for gameCode := range gameCode2Depot {
		metricCreativeTotalVideoImage(ctx, gameCode)
		metricCreativeMissCover(ctx, gameCode)
		metricCreativeMissMeta(ctx, gameCode)
	}
}

func metricCreativeTotalVideoImage(ctx context.Context, gameCode string) {
	metric := ginmetrics.GetMonitor().GetMetric(metrics.MetricCreativeTotalVideoImage)

	count := data.StatTotalVideo(ctx, gameCode)
	metric.SetGaugeValueWithLabels(map[string]string{"game": gameCode, "format": "video"}, float64(count))

	count = data.StatTotalImage(ctx, gameCode)
	metric.SetGaugeValueWithLabels(map[string]string{"game": gameCode, "format": "image"}, float64(count))
}

func metricCreativeMissCover(ctx context.Context, gameCode string) {
	metric := ginmetrics.GetMonitor().GetMetric(metrics.MetricCreativeMissCover)

	count := data.StatMissCoverVideo(ctx, gameCode)
	metric.SetGaugeValueWithLabels(map[string]string{"game": gameCode}, float64(count))
}

func metricCreativeMissMeta(ctx context.Context, gameCode string) {
	metric := ginmetrics.GetMonitor().GetMetric(metrics.MetricCreativeMissMeta)

	count := data.StatMissMetaVideo(ctx, gameCode)
	metric.SetGaugeValueWithLabels(map[string]string{"game": gameCode, "format": "video"}, float64(count))

	count = data.StatMissMetaImage(ctx, gameCode)
	metric.SetGaugeValueWithLabels(map[string]string{"game": gameCode, "format": "image"}, float64(count))
}
