package service

import (
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// SayHi 设置素材机器标签及封面信息
func SyncCreativeRecommendManul(ctx *gin.Context, _ *pb.SyncCreativeRecommendManualReq, _ *pb.SyncCreativeRecommendManualRsp) error {
	game_code := ctx.Request.Header.Get("game")

	syncCreativeRecommendCandidateForGameCode(game_code)
	SyncOnlineInfoTotalLoopForGameCode(game_code)

	return nil
}
