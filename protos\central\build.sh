#!/bin/bash

# 当前脚本绝对路径
current_path=$(cd "$(dirname "$0")";pwd)

cd ${current_path}/../

echo -e "【1】 build central.proto"
sh build_proto.sh ./central/central.proto
if [ $? -ne 0 ]; then
    echo -e "【ERROR】central proto build error"
    exit 1
fi

echo -e "【2】 build central jsonschema"
proto-to-jsonschema ./central/central.proto
if [ $? -ne 0 ]; then
    echo -e "【ERROR】central generate jsonschema error"
    exit 1
fi


