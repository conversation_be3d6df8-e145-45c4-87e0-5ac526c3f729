-- 标签规则结构化ck外表(
CREATE TABLE ieg_ads_bi_test.tb_asset_rule_labels on cluster default_cluster
(
    `game_code` String,
    `rule` String,
    `type` Int32,
    `label_name` String,
    `first_label` String,
    `second_label` String,
    `create_time` String,
    `update_time` String,
    `create_by` String,
    `update_by` String
)
ENGINE = PostgreSQL('10.190.24.16:5432', 'UA-Intelli-TEST', 'tb_asset_rule_labels', 'dic', 'V82VjKCE9', 'arthub_sync');