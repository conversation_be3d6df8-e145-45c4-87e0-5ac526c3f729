package data

import (
	"context"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"github.com/go-pg/pg/v10"
)

// 查询素材asset_id列表
func QueryCreativeMediaUploadAssetIds(ctx context.Context, accountId string,
	channel, formatType uint32, gameCode string, resourceNames []string) ([]*model.CreativeMediaUpload, error) {
	if len(resourceNames) == 0 {
		return nil, nil
	}
	db := postgresql.GetDBWithContext(ctx)
	var founds []*model.CreativeMediaUpload
	query := db.Model(&model.CreativeMediaUpload{}).
		Where("account_id=?", accountId).
		Where("channel=?", channel).
		Where("game_code=?", gameCode)
	if formatType > 0 {
		query = query.Where("format_type=?", formatType)
	}
	if len(resourceNames) > 0 {
		query = query.WhereIn("resource_name in (?)", resourceNames)
	}
	query.Order("update_time desc nulls last")
	err := query.Select(&founds)
	if err != nil {
		return nil, err
	}
	return founds, nil
}

// 根据素材id列表查询素材上传信息
func QueryCreativeMediaUploadByAssetId(ctx context.Context, accountId string,
	channel, formatType uint32, gameCode string, imgRatios, assetIds, assetRatios []string) ([]*model.CreativeMediaUpload, error) {
	if len(assetIds) == 0 {
		return nil, nil
	}
	db := postgresql.GetDBWithContext(ctx)
	var founds []*model.CreativeMediaUpload
	query := db.Model(&model.CreativeMediaUpload{}).
		Where("account_id=?", accountId).
		Where("channel=?", channel).
		Where("game_code=?", gameCode)
	if formatType > 0 {
		query = query.Where("format_type=?", formatType)
	}
	if len(assetIds) > 0 {
		query = query.WhereIn("asset_id in (?)", assetIds)
	}
	if len(imgRatios) > 0 {
		query = query.WhereIn("img_ratio in (?)", imgRatios)
	}

	if channel == constant.MediaGoogle && formatType == 0 && len(assetRatios) > 0 {
		// google渠道要特殊处理, 查所有素材类型时，需要过滤指定图片asset_ratios
		query.WhereGroup(func(subQeury *pg.Query) (*pg.Query, error) {
			// 要么是图片，满足ratio
			subQeury.WhereIn("asset_ratio IN (?)", assetRatios)
			// 要么是视频
			subQeury.WhereOr("format_type = ?", constant.AssetVideoType)

			return subQeury, nil
		})
	} else if len(assetRatios) > 0 {
		query = query.WhereIn("asset_ratio in (?)", assetRatios)
	}

	query.Order("update_time desc nulls last")
	err := query.Select(&founds)
	if err != nil {
		return nil, err
	}
	return founds, nil
}

// BatchGroupCreativeMediaUploadByAssetIds 根据素材id列表查询上传了哪些渠道
func BatchGroupCreativeMediaUploadByAssetIds(ctx context.Context, gameCode string, assetIds []string) ([]*model.CreativeMediaUpload, error) {
	if len(assetIds) == 0 {
		return nil, nil
	}
	db := postgresql.GetDBWithContext(ctx)
	var founds []*model.CreativeMediaUpload
	query := db.Model(&model.CreativeMediaUpload{})
	query.Column("asset_id", "channel")
	query.Where("game_code=?", gameCode)
	query.WhereIn("asset_id in (?)", assetIds)
	query.Group("asset_id", "channel")

	err := query.Select(&founds)
	if err != nil {
		return nil, err
	}
	return founds, nil
}
