// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.1
// source: audience_overview/audience_overview.proto

package audience_overview

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取audience折线图数据, POST, /api/v1/audience_overview/audience_plots
type AudiencePlotsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters     []*AudienceFilter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`                            // 客户端选中的筛选器
	MetricField string            `protobuf:"bytes,2,opt,name=metric_field,json=metricField,proto3" json:"metric_field,omitempty"` // 需要返回的metric字段信息
}

func (x *AudiencePlotsReq) Reset() {
	*x = AudiencePlotsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudiencePlotsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudiencePlotsReq) ProtoMessage() {}

func (x *AudiencePlotsReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudiencePlotsReq.ProtoReflect.Descriptor instead.
func (*AudiencePlotsReq) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{0}
}

func (x *AudiencePlotsReq) GetFilters() []*AudienceFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *AudiencePlotsReq) GetMetricField() string {
	if x != nil {
		return x.MetricField
	}
	return ""
}

type Campaign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CampaignId   string `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`       // campaign_id
	CampaignName string `protobuf:"bytes,2,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"` // campaign_name
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{1}
}

func (x *Campaign) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *Campaign) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

type Metric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateTime                     string  `protobuf:"bytes,5,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`                                                                      // date time
	Ipm                          float64 `protobuf:"fixed64,12,opt,name=ipm,proto3" json:"ipm,omitempty"`                                                                                             // ipm
	Ctr                          float64 `protobuf:"fixed64,13,opt,name=ctr,proto3" json:"ctr,omitempty"`                                                                                             // ctr
	Cvr                          float64 `protobuf:"fixed64,14,opt,name=cvr,proto3" json:"cvr,omitempty"`                                                                                             // cvr
	Cpc                          float64 `protobuf:"fixed64,15,opt,name=cpc,proto3" json:"cpc,omitempty"`                                                                                             // cpc
	Cpm                          float64 `protobuf:"fixed64,16,opt,name=cpm,proto3" json:"cpm,omitempty"`                                                                                             // cpm
	Cpi                          float64 `protobuf:"fixed64,17,opt,name=cpi,proto3" json:"cpi,omitempty"`                                                                                             // cpi
	Cpa                          float64 `protobuf:"fixed64,18,opt,name=cpa,proto3" json:"cpa,omitempty"`                                                                                             // cpa
	RealtimeCohortRegisterRate   float64 `protobuf:"fixed64,19,opt,name=realtime_cohort_register_rate,json=realtimeCohortRegisterRate,proto3" json:"realtime_cohort_register_rate,omitempty"`         // realtime_cohort_register_rate
	RealtimeCpi                  float64 `protobuf:"fixed64,20,opt,name=realtime_cpi,json=realtimeCpi,proto3" json:"realtime_cpi,omitempty"`                                                          // realtime_cpi
	RealtimeD1Roas               float64 `protobuf:"fixed64,21,opt,name=realtime_d1_roas,json=realtimeD1Roas,proto3" json:"realtime_d1_roas,omitempty"`                                               // realtime_d1_roas
	OfflineD2CohortRegisterRate  float64 `protobuf:"fixed64,22,opt,name=offline_d2_cohort_register_rate,json=offlineD2CohortRegisterRate,proto3" json:"offline_d2_cohort_register_rate,omitempty"`    // offline_d2_cohort_register_rate
	OfflineD3CohortRegisterRate  float64 `protobuf:"fixed64,23,opt,name=offline_d3_cohort_register_rate,json=offlineD3CohortRegisterRate,proto3" json:"offline_d3_cohort_register_rate,omitempty"`    // offline_d3_cohort_register_rate
	OfflineD7CohortRegisterRate  float64 `protobuf:"fixed64,24,opt,name=offline_d7_cohort_register_rate,json=offlineD7CohortRegisterRate,proto3" json:"offline_d7_cohort_register_rate,omitempty"`    // offline_d7_cohort_register_rate
	OfflineD14CohortRegisterRate float64 `protobuf:"fixed64,25,opt,name=offline_d14_cohort_register_rate,json=offlineD14CohortRegisterRate,proto3" json:"offline_d14_cohort_register_rate,omitempty"` // offline_d14_cohort_register_rate
	OfflineD2RetentionRate       float64 `protobuf:"fixed64,26,opt,name=offline_d2_retention_rate,json=offlineD2RetentionRate,proto3" json:"offline_d2_retention_rate,omitempty"`                     // offline_d2_retention_rate
	OfflineD3RetentionRate       float64 `protobuf:"fixed64,27,opt,name=offline_d3_retention_rate,json=offlineD3RetentionRate,proto3" json:"offline_d3_retention_rate,omitempty"`                     // offline_d3_retention_rate
	OfflineD7RetentionRate       float64 `protobuf:"fixed64,28,opt,name=offline_d7_retention_rate,json=offlineD7RetentionRate,proto3" json:"offline_d7_retention_rate,omitempty"`                     // offline_d7_retention_rate
	OfflineD14RetentionRate      float64 `protobuf:"fixed64,29,opt,name=offline_d14_retention_rate,json=offlineD14RetentionRate,proto3" json:"offline_d14_retention_rate,omitempty"`                  // offline_d14_retention_rate
	OfflineD2Roas                float64 `protobuf:"fixed64,30,opt,name=offline_d2_roas,json=offlineD2Roas,proto3" json:"offline_d2_roas,omitempty"`                                                  // offline_d2_roas
	OfflineD3Roas                float64 `protobuf:"fixed64,31,opt,name=offline_d3_roas,json=offlineD3Roas,proto3" json:"offline_d3_roas,omitempty"`                                                  // offline_d3_roas
	OfflineD7Roas                float64 `protobuf:"fixed64,32,opt,name=offline_d7_roas,json=offlineD7Roas,proto3" json:"offline_d7_roas,omitempty"`                                                  // offline_d7_roas
	OfflineD14Roas               float64 `protobuf:"fixed64,33,opt,name=offline_d14_roas,json=offlineD14Roas,proto3" json:"offline_d14_roas,omitempty"`                                               // offline_d14_roas
	Impressions                  float64 `protobuf:"fixed64,64,opt,name=impressions,proto3" json:"impressions,omitempty"`                                                                             //  impressions
	Clicks                       float64 `protobuf:"fixed64,65,opt,name=clicks,proto3" json:"clicks,omitempty"`                                                                                       //  clicks
	Spend                        float64 `protobuf:"fixed64,66,opt,name=spend,proto3" json:"spend,omitempty"`                                                                                         //  spend
	Conversions                  float64 `protobuf:"fixed64,67,opt,name=conversions,proto3" json:"conversions,omitempty"`                                                                             //  conversions
	Interactions                 float64 `protobuf:"fixed64,68,opt,name=interactions,proto3" json:"interactions,omitempty"`                                                                           //  interactions
	Installs                     float64 `protobuf:"fixed64,69,opt,name=installs,proto3" json:"installs,omitempty"`                                                                                   //  installs
	InAppAction                  float64 `protobuf:"fixed64,70,opt,name=in_app_action,json=inAppAction,proto3" json:"in_app_action,omitempty"`                                                        //  in_app_action
	CampaignDailyBudget          float64 `protobuf:"fixed64,71,opt,name=campaign_daily_budget,json=campaignDailyBudget,proto3" json:"campaign_daily_budget,omitempty"`                                //  campaign_daily_budget
	StrategyTotalBudget          float64 `protobuf:"fixed64,72,opt,name=strategy_total_budget,json=strategyTotalBudget,proto3" json:"strategy_total_budget,omitempty"`                                //  strategy_total_budget
	RealtimeCohortRegister       float64 `protobuf:"fixed64,73,opt,name=realtime_cohort_register,json=realtimeCohortRegister,proto3" json:"realtime_cohort_register,omitempty"`                       //  realtime_cohort_register
	RealtimeInstall              float64 `protobuf:"fixed64,74,opt,name=realtime_install,json=realtimeInstall,proto3" json:"realtime_install,omitempty"`                                              //  realtime_install
	RealtimeSpend                float64 `protobuf:"fixed64,75,opt,name=realtime_spend,json=realtimeSpend,proto3" json:"realtime_spend,omitempty"`                                                    //  realtime_spend
	RealtimeCohortRevenue        float64 `protobuf:"fixed64,76,opt,name=realtime_cohort_revenue,json=realtimeCohortRevenue,proto3" json:"realtime_cohort_revenue,omitempty"`                          //  realtime_cohort_revenue
	RealtimeD1CohortRevenue      float64 `protobuf:"fixed64,77,opt,name=realtime_d1_cohort_revenue,json=realtimeD1CohortRevenue,proto3" json:"realtime_d1_cohort_revenue,omitempty"`                  //  realtime_d1_cohort_revenue
	RealtimeDau                  float64 `protobuf:"fixed64,78,opt,name=realtime_dau,json=realtimeDau,proto3" json:"realtime_dau,omitempty"`                                                          //  realtime_dau
	MediaDailyBudget             float64 `protobuf:"fixed64,79,opt,name=media_daily_budget,json=mediaDailyBudget,proto3" json:"media_daily_budget,omitempty"`                                         //  media_daily_budget
	MediaBidAmount               float64 `protobuf:"fixed64,80,opt,name=media_bid_amount,json=mediaBidAmount,proto3" json:"media_bid_amount,omitempty"`                                               //  media_bid_amount
	OfflineInstall               float64 `protobuf:"fixed64,81,opt,name=offline_install,json=offlineInstall,proto3" json:"offline_install,omitempty"`                                                 //  offline_install
	OfflineD1CohortRegister      float64 `protobuf:"fixed64,82,opt,name=offline_d1_cohort_register,json=offlineD1CohortRegister,proto3" json:"offline_d1_cohort_register,omitempty"`                  //  offline_d1_cohort_register
	OfflineD2CohortRegister      float64 `protobuf:"fixed64,83,opt,name=offline_d2_cohort_register,json=offlineD2CohortRegister,proto3" json:"offline_d2_cohort_register,omitempty"`                  //  offline_d2_cohort_register
	OfflineD3CohortRegister      float64 `protobuf:"fixed64,84,opt,name=offline_d3_cohort_register,json=offlineD3CohortRegister,proto3" json:"offline_d3_cohort_register,omitempty"`                  //  offline_d3_cohort_register
	OfflineD7CohortRegister      float64 `protobuf:"fixed64,85,opt,name=offline_d7_cohort_register,json=offlineD7CohortRegister,proto3" json:"offline_d7_cohort_register,omitempty"`                  //  offline_d7_cohort_register
	OfflineD14CohortRegister     float64 `protobuf:"fixed64,86,opt,name=offline_d14_cohort_register,json=offlineD14CohortRegister,proto3" json:"offline_d14_cohort_register,omitempty"`               //  offline_d14_cohort_register
	OfflineD30CohortRegister     float64 `protobuf:"fixed64,87,opt,name=offline_d30_cohort_register,json=offlineD30CohortRegister,proto3" json:"offline_d30_cohort_register,omitempty"`               //  offline_d30_cohort_register
	OfflineD60CohortRegister     float64 `protobuf:"fixed64,88,opt,name=offline_d60_cohort_register,json=offlineD60CohortRegister,proto3" json:"offline_d60_cohort_register,omitempty"`               //  offline_d60_cohort_register
	OfflineD90CohortRegister     float64 `protobuf:"fixed64,89,opt,name=offline_d90_cohort_register,json=offlineD90CohortRegister,proto3" json:"offline_d90_cohort_register,omitempty"`               //  offline_d90_cohort_register
	OfflineD120CohortRegister    float64 `protobuf:"fixed64,90,opt,name=offline_d120_cohort_register,json=offlineD120CohortRegister,proto3" json:"offline_d120_cohort_register,omitempty"`            //  offline_d120_cohort_register
	OfflineD150CohortRegister    float64 `protobuf:"fixed64,91,opt,name=offline_d150_cohort_register,json=offlineD150CohortRegister,proto3" json:"offline_d150_cohort_register,omitempty"`            //  offline_d150_cohort_register
	OfflineD180CohortRegister    float64 `protobuf:"fixed64,92,opt,name=offline_d180_cohort_register,json=offlineD180CohortRegister,proto3" json:"offline_d180_cohort_register,omitempty"`            //  offline_d180_cohort_register
	OfflineD1Retention           float64 `protobuf:"fixed64,93,opt,name=offline_d1_retention,json=offlineD1Retention,proto3" json:"offline_d1_retention,omitempty"`                                   //  offline_d1_retention
	OfflineD2Retention           float64 `protobuf:"fixed64,94,opt,name=offline_d2_retention,json=offlineD2Retention,proto3" json:"offline_d2_retention,omitempty"`                                   //  offline_d2_retention
	OfflineD3Retention           float64 `protobuf:"fixed64,95,opt,name=offline_d3_retention,json=offlineD3Retention,proto3" json:"offline_d3_retention,omitempty"`                                   //  offline_d3_retention
	OfflineD7Retention           float64 `protobuf:"fixed64,96,opt,name=offline_d7_retention,json=offlineD7Retention,proto3" json:"offline_d7_retention,omitempty"`                                   //  offline_d7_retention
	OfflineD14Retention          float64 `protobuf:"fixed64,97,opt,name=offline_d14_retention,json=offlineD14Retention,proto3" json:"offline_d14_retention,omitempty"`                                //  offline_d14_retention
	OfflineD30Retention          float64 `protobuf:"fixed64,98,opt,name=offline_d30_retention,json=offlineD30Retention,proto3" json:"offline_d30_retention,omitempty"`                                //  offline_d30_retention
	OfflineD60Retention          float64 `protobuf:"fixed64,99,opt,name=offline_d60_retention,json=offlineD60Retention,proto3" json:"offline_d60_retention,omitempty"`                                //  offline_d60_retention
	OfflineD90Retention          float64 `protobuf:"fixed64,100,opt,name=offline_d90_retention,json=offlineD90Retention,proto3" json:"offline_d90_retention,omitempty"`                               //  offline_d90_retention
	OfflineD120Retention         float64 `protobuf:"fixed64,101,opt,name=offline_d120_retention,json=offlineD120Retention,proto3" json:"offline_d120_retention,omitempty"`                            //  offline_d120_retention
	OfflineD150Retention         float64 `protobuf:"fixed64,102,opt,name=offline_d150_retention,json=offlineD150Retention,proto3" json:"offline_d150_retention,omitempty"`                            //  offline_d150_retention
	OfflineD180Retention         float64 `protobuf:"fixed64,103,opt,name=offline_d180_retention,json=offlineD180Retention,proto3" json:"offline_d180_retention,omitempty"`                            //  offline_d180_retention
	OfflineD1CohortRevenue       float64 `protobuf:"fixed64,104,opt,name=offline_d1_cohort_revenue,json=offlineD1CohortRevenue,proto3" json:"offline_d1_cohort_revenue,omitempty"`                    //  offline_d1_cohort_revenue
	OfflineD2CohortRevenue       float64 `protobuf:"fixed64,105,opt,name=offline_d2_cohort_revenue,json=offlineD2CohortRevenue,proto3" json:"offline_d2_cohort_revenue,omitempty"`                    //  offline_d2_cohort_revenue
	OfflineD3CohortRevenue       float64 `protobuf:"fixed64,106,opt,name=offline_d3_cohort_revenue,json=offlineD3CohortRevenue,proto3" json:"offline_d3_cohort_revenue,omitempty"`                    //  offline_d3_cohort_revenue
	OfflineD7CohortRevenue       float64 `protobuf:"fixed64,107,opt,name=offline_d7_cohort_revenue,json=offlineD7CohortRevenue,proto3" json:"offline_d7_cohort_revenue,omitempty"`                    //  offline_d7_cohort_revenue
	OfflineD14CohortRevenue      float64 `protobuf:"fixed64,108,opt,name=offline_d14_cohort_revenue,json=offlineD14CohortRevenue,proto3" json:"offline_d14_cohort_revenue,omitempty"`                 //  offline_d14_cohort_revenue
	OfflineD30CohortRevenue      float64 `protobuf:"fixed64,109,opt,name=offline_d30_cohort_revenue,json=offlineD30CohortRevenue,proto3" json:"offline_d30_cohort_revenue,omitempty"`                 //  offline_d30_cohort_revenue
	OfflineD60CohortRevenue      float64 `protobuf:"fixed64,110,opt,name=offline_d60_cohort_revenue,json=offlineD60CohortRevenue,proto3" json:"offline_d60_cohort_revenue,omitempty"`                 //  offline_d60_cohort_revenue
	OfflineD90CohortRevenue      float64 `protobuf:"fixed64,111,opt,name=offline_d90_cohort_revenue,json=offlineD90CohortRevenue,proto3" json:"offline_d90_cohort_revenue,omitempty"`                 //  offline_d90_cohort_revenue
	OfflineD120CohortRevenue     float64 `protobuf:"fixed64,112,opt,name=offline_d120_cohort_revenue,json=offlineD120CohortRevenue,proto3" json:"offline_d120_cohort_revenue,omitempty"`              //  offline_d120_cohort_revenue
	OfflineD150CohortRevenue     float64 `protobuf:"fixed64,113,opt,name=offline_d150_cohort_revenue,json=offlineD150CohortRevenue,proto3" json:"offline_d150_cohort_revenue,omitempty"`              //  offline_d150_cohort_revenue
	OfflineD180CohortRevenue     float64 `protobuf:"fixed64,114,opt,name=offline_d180_cohort_revenue,json=offlineD180CohortRevenue,proto3" json:"offline_d180_cohort_revenue,omitempty"`              //  offline_d180_cohort_revenue
	OfflineSpend                 float64 `protobuf:"fixed64,115,opt,name=offline_spend,json=offlineSpend,proto3" json:"offline_spend,omitempty"`                                                      //  offline_spend
}

func (x *Metric) Reset() {
	*x = Metric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metric) ProtoMessage() {}

func (x *Metric) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metric.ProtoReflect.Descriptor instead.
func (*Metric) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{2}
}

func (x *Metric) GetDateTime() string {
	if x != nil {
		return x.DateTime
	}
	return ""
}

func (x *Metric) GetIpm() float64 {
	if x != nil {
		return x.Ipm
	}
	return 0
}

func (x *Metric) GetCtr() float64 {
	if x != nil {
		return x.Ctr
	}
	return 0
}

func (x *Metric) GetCvr() float64 {
	if x != nil {
		return x.Cvr
	}
	return 0
}

func (x *Metric) GetCpc() float64 {
	if x != nil {
		return x.Cpc
	}
	return 0
}

func (x *Metric) GetCpm() float64 {
	if x != nil {
		return x.Cpm
	}
	return 0
}

func (x *Metric) GetCpi() float64 {
	if x != nil {
		return x.Cpi
	}
	return 0
}

func (x *Metric) GetCpa() float64 {
	if x != nil {
		return x.Cpa
	}
	return 0
}

func (x *Metric) GetRealtimeCohortRegisterRate() float64 {
	if x != nil {
		return x.RealtimeCohortRegisterRate
	}
	return 0
}

func (x *Metric) GetRealtimeCpi() float64 {
	if x != nil {
		return x.RealtimeCpi
	}
	return 0
}

func (x *Metric) GetRealtimeD1Roas() float64 {
	if x != nil {
		return x.RealtimeD1Roas
	}
	return 0
}

func (x *Metric) GetOfflineD2CohortRegisterRate() float64 {
	if x != nil {
		return x.OfflineD2CohortRegisterRate
	}
	return 0
}

func (x *Metric) GetOfflineD3CohortRegisterRate() float64 {
	if x != nil {
		return x.OfflineD3CohortRegisterRate
	}
	return 0
}

func (x *Metric) GetOfflineD7CohortRegisterRate() float64 {
	if x != nil {
		return x.OfflineD7CohortRegisterRate
	}
	return 0
}

func (x *Metric) GetOfflineD14CohortRegisterRate() float64 {
	if x != nil {
		return x.OfflineD14CohortRegisterRate
	}
	return 0
}

func (x *Metric) GetOfflineD2RetentionRate() float64 {
	if x != nil {
		return x.OfflineD2RetentionRate
	}
	return 0
}

func (x *Metric) GetOfflineD3RetentionRate() float64 {
	if x != nil {
		return x.OfflineD3RetentionRate
	}
	return 0
}

func (x *Metric) GetOfflineD7RetentionRate() float64 {
	if x != nil {
		return x.OfflineD7RetentionRate
	}
	return 0
}

func (x *Metric) GetOfflineD14RetentionRate() float64 {
	if x != nil {
		return x.OfflineD14RetentionRate
	}
	return 0
}

func (x *Metric) GetOfflineD2Roas() float64 {
	if x != nil {
		return x.OfflineD2Roas
	}
	return 0
}

func (x *Metric) GetOfflineD3Roas() float64 {
	if x != nil {
		return x.OfflineD3Roas
	}
	return 0
}

func (x *Metric) GetOfflineD7Roas() float64 {
	if x != nil {
		return x.OfflineD7Roas
	}
	return 0
}

func (x *Metric) GetOfflineD14Roas() float64 {
	if x != nil {
		return x.OfflineD14Roas
	}
	return 0
}

func (x *Metric) GetImpressions() float64 {
	if x != nil {
		return x.Impressions
	}
	return 0
}

func (x *Metric) GetClicks() float64 {
	if x != nil {
		return x.Clicks
	}
	return 0
}

func (x *Metric) GetSpend() float64 {
	if x != nil {
		return x.Spend
	}
	return 0
}

func (x *Metric) GetConversions() float64 {
	if x != nil {
		return x.Conversions
	}
	return 0
}

func (x *Metric) GetInteractions() float64 {
	if x != nil {
		return x.Interactions
	}
	return 0
}

func (x *Metric) GetInstalls() float64 {
	if x != nil {
		return x.Installs
	}
	return 0
}

func (x *Metric) GetInAppAction() float64 {
	if x != nil {
		return x.InAppAction
	}
	return 0
}

func (x *Metric) GetCampaignDailyBudget() float64 {
	if x != nil {
		return x.CampaignDailyBudget
	}
	return 0
}

func (x *Metric) GetStrategyTotalBudget() float64 {
	if x != nil {
		return x.StrategyTotalBudget
	}
	return 0
}

func (x *Metric) GetRealtimeCohortRegister() float64 {
	if x != nil {
		return x.RealtimeCohortRegister
	}
	return 0
}

func (x *Metric) GetRealtimeInstall() float64 {
	if x != nil {
		return x.RealtimeInstall
	}
	return 0
}

func (x *Metric) GetRealtimeSpend() float64 {
	if x != nil {
		return x.RealtimeSpend
	}
	return 0
}

func (x *Metric) GetRealtimeCohortRevenue() float64 {
	if x != nil {
		return x.RealtimeCohortRevenue
	}
	return 0
}

func (x *Metric) GetRealtimeD1CohortRevenue() float64 {
	if x != nil {
		return x.RealtimeD1CohortRevenue
	}
	return 0
}

func (x *Metric) GetRealtimeDau() float64 {
	if x != nil {
		return x.RealtimeDau
	}
	return 0
}

func (x *Metric) GetMediaDailyBudget() float64 {
	if x != nil {
		return x.MediaDailyBudget
	}
	return 0
}

func (x *Metric) GetMediaBidAmount() float64 {
	if x != nil {
		return x.MediaBidAmount
	}
	return 0
}

func (x *Metric) GetOfflineInstall() float64 {
	if x != nil {
		return x.OfflineInstall
	}
	return 0
}

func (x *Metric) GetOfflineD1CohortRegister() float64 {
	if x != nil {
		return x.OfflineD1CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD2CohortRegister() float64 {
	if x != nil {
		return x.OfflineD2CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD3CohortRegister() float64 {
	if x != nil {
		return x.OfflineD3CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD7CohortRegister() float64 {
	if x != nil {
		return x.OfflineD7CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD14CohortRegister() float64 {
	if x != nil {
		return x.OfflineD14CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD30CohortRegister() float64 {
	if x != nil {
		return x.OfflineD30CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD60CohortRegister() float64 {
	if x != nil {
		return x.OfflineD60CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD90CohortRegister() float64 {
	if x != nil {
		return x.OfflineD90CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD120CohortRegister() float64 {
	if x != nil {
		return x.OfflineD120CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD150CohortRegister() float64 {
	if x != nil {
		return x.OfflineD150CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD180CohortRegister() float64 {
	if x != nil {
		return x.OfflineD180CohortRegister
	}
	return 0
}

func (x *Metric) GetOfflineD1Retention() float64 {
	if x != nil {
		return x.OfflineD1Retention
	}
	return 0
}

func (x *Metric) GetOfflineD2Retention() float64 {
	if x != nil {
		return x.OfflineD2Retention
	}
	return 0
}

func (x *Metric) GetOfflineD3Retention() float64 {
	if x != nil {
		return x.OfflineD3Retention
	}
	return 0
}

func (x *Metric) GetOfflineD7Retention() float64 {
	if x != nil {
		return x.OfflineD7Retention
	}
	return 0
}

func (x *Metric) GetOfflineD14Retention() float64 {
	if x != nil {
		return x.OfflineD14Retention
	}
	return 0
}

func (x *Metric) GetOfflineD30Retention() float64 {
	if x != nil {
		return x.OfflineD30Retention
	}
	return 0
}

func (x *Metric) GetOfflineD60Retention() float64 {
	if x != nil {
		return x.OfflineD60Retention
	}
	return 0
}

func (x *Metric) GetOfflineD90Retention() float64 {
	if x != nil {
		return x.OfflineD90Retention
	}
	return 0
}

func (x *Metric) GetOfflineD120Retention() float64 {
	if x != nil {
		return x.OfflineD120Retention
	}
	return 0
}

func (x *Metric) GetOfflineD150Retention() float64 {
	if x != nil {
		return x.OfflineD150Retention
	}
	return 0
}

func (x *Metric) GetOfflineD180Retention() float64 {
	if x != nil {
		return x.OfflineD180Retention
	}
	return 0
}

func (x *Metric) GetOfflineD1CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD1CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD2CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD2CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD3CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD3CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD7CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD7CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD14CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD14CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD30CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD30CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD60CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD60CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD90CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD90CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD120CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD120CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD150CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD150CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineD180CohortRevenue() float64 {
	if x != nil {
		return x.OfflineD180CohortRevenue
	}
	return 0
}

func (x *Metric) GetOfflineSpend() float64 {
	if x != nil {
		return x.OfflineSpend
	}
	return 0
}

type AudiencePlot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudienceName string      `protobuf:"bytes,1,opt,name=audience_name,json=audienceName,proto3" json:"audience_name,omitempty"` // audience name
	Campaigns    []*Campaign `protobuf:"bytes,2,rep,name=campaigns,proto3" json:"campaigns,omitempty"`                           // campaign list
	Metrics      []*Metric   `protobuf:"bytes,3,rep,name=metrics,proto3" json:"metrics,omitempty"`                               // metric list
}

func (x *AudiencePlot) Reset() {
	*x = AudiencePlot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudiencePlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudiencePlot) ProtoMessage() {}

func (x *AudiencePlot) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudiencePlot.ProtoReflect.Descriptor instead.
func (*AudiencePlot) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{3}
}

func (x *AudiencePlot) GetAudienceName() string {
	if x != nil {
		return x.AudienceName
	}
	return ""
}

func (x *AudiencePlot) GetCampaigns() []*Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

func (x *AudiencePlot) GetMetrics() []*Metric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type AudiencePlotsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Audiences []*AudiencePlot `protobuf:"bytes,2,rep,name=audiences,proto3" json:"audiences,omitempty"` // audience列表
}

func (x *AudiencePlotsRsp) Reset() {
	*x = AudiencePlotsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudiencePlotsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudiencePlotsRsp) ProtoMessage() {}

func (x *AudiencePlotsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudiencePlotsRsp.ProtoReflect.Descriptor instead.
func (*AudiencePlotsRsp) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{4}
}

func (x *AudiencePlotsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AudiencePlotsRsp) GetAudiences() []*AudiencePlot {
	if x != nil {
		return x.Audiences
	}
	return nil
}

// 获取audience表格数据, POST, /api/v1/audience_overview/audience_tables
type AudienceTablesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters []*AudienceFilter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"` // 客户端选中的筛选器
}

func (x *AudienceTablesReq) Reset() {
	*x = AudienceTablesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceTablesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceTablesReq) ProtoMessage() {}

func (x *AudienceTablesReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceTablesReq.ProtoReflect.Descriptor instead.
func (*AudienceTablesReq) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{5}
}

func (x *AudienceTablesReq) GetFilters() []*AudienceFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

type AudienceTable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudienceName string      `protobuf:"bytes,1,opt,name=audience_name,json=audienceName,proto3" json:"audience_name,omitempty"` // audience name
	Campaigns    []*Campaign `protobuf:"bytes,2,rep,name=campaigns,proto3" json:"campaigns,omitempty"`                           // campaign list
	Metric       *Metric     `protobuf:"bytes,3,opt,name=metric,proto3" json:"metric,omitempty"`                                 // metric info
}

func (x *AudienceTable) Reset() {
	*x = AudienceTable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceTable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceTable) ProtoMessage() {}

func (x *AudienceTable) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceTable.ProtoReflect.Descriptor instead.
func (*AudienceTable) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{6}
}

func (x *AudienceTable) GetAudienceName() string {
	if x != nil {
		return x.AudienceName
	}
	return ""
}

func (x *AudienceTable) GetCampaigns() []*Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

func (x *AudienceTable) GetMetric() *Metric {
	if x != nil {
		return x.Metric
	}
	return nil
}

type AudienceTablesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result      `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Audiences []*AudienceTable `protobuf:"bytes,2,rep,name=audiences,proto3" json:"audiences,omitempty"` // audience列表
}

func (x *AudienceTablesRsp) Reset() {
	*x = AudienceTablesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceTablesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceTablesRsp) ProtoMessage() {}

func (x *AudienceTablesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceTablesRsp.ProtoReflect.Descriptor instead.
func (*AudienceTablesRsp) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{7}
}

func (x *AudienceTablesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AudienceTablesRsp) GetAudiences() []*AudienceTable {
	if x != nil {
		return x.Audiences
	}
	return nil
}

// 获取audience汇总面板数据, POST, /api/v1/audience_overview/audience_sum_panel
type AudienceSumPanelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters []*AudienceFilter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"` // 客户端选中的筛选器
}

func (x *AudienceSumPanelReq) Reset() {
	*x = AudienceSumPanelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceSumPanelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceSumPanelReq) ProtoMessage() {}

func (x *AudienceSumPanelReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceSumPanelReq.ProtoReflect.Descriptor instead.
func (*AudienceSumPanelReq) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{8}
}

func (x *AudienceSumPanelReq) GetFilters() []*AudienceFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

type AudienceSumPanel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AudienceName string      `protobuf:"bytes,1,opt,name=audience_name,json=audienceName,proto3" json:"audience_name,omitempty"` // audience name
	Campaigns    []*Campaign `protobuf:"bytes,2,rep,name=campaigns,proto3" json:"campaigns,omitempty"`                           // campaign list
	Spend        float64     `protobuf:"fixed64,3,opt,name=spend,proto3" json:"spend,omitempty"`                                 // spend
}

func (x *AudienceSumPanel) Reset() {
	*x = AudienceSumPanel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceSumPanel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceSumPanel) ProtoMessage() {}

func (x *AudienceSumPanel) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceSumPanel.ProtoReflect.Descriptor instead.
func (*AudienceSumPanel) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{9}
}

func (x *AudienceSumPanel) GetAudienceName() string {
	if x != nil {
		return x.AudienceName
	}
	return ""
}

func (x *AudienceSumPanel) GetCampaigns() []*Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

func (x *AudienceSumPanel) GetSpend() float64 {
	if x != nil {
		return x.Spend
	}
	return 0
}

type AudienceSumPanelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result         `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                       // 返回结果
	Audiences []*AudienceSumPanel `protobuf:"bytes,2,rep,name=audiences,proto3" json:"audiences,omitempty"`                 // audience列表
	SumSpend  float64             `protobuf:"fixed64,3,opt,name=sum_spend,json=sumSpend,proto3" json:"sum_spend,omitempty"` // spend汇总数据
}

func (x *AudienceSumPanelRsp) Reset() {
	*x = AudienceSumPanelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceSumPanelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceSumPanelRsp) ProtoMessage() {}

func (x *AudienceSumPanelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceSumPanelRsp.ProtoReflect.Descriptor instead.
func (*AudienceSumPanelRsp) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{10}
}

func (x *AudienceSumPanelRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AudienceSumPanelRsp) GetAudiences() []*AudienceSumPanel {
	if x != nil {
		return x.Audiences
	}
	return nil
}

func (x *AudienceSumPanelRsp) GetSumSpend() float64 {
	if x != nil {
		return x.SumSpend
	}
	return 0
}

// 获取audience filter列表, POST, /api/v1/audience_overview/audience_filter_list
type AudienceFilterListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AudienceFilterListReq) Reset() {
	*x = AudienceFilterListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceFilterListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceFilterListReq) ProtoMessage() {}

func (x *AudienceFilterListReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceFilterListReq.ProtoReflect.Descriptor instead.
func (*AudienceFilterListReq) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{11}
}

type AudienceFilterTimeSpan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime string `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 开始时间
	EndTime   string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 结束时间
}

func (x *AudienceFilterTimeSpan) Reset() {
	*x = AudienceFilterTimeSpan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceFilterTimeSpan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceFilterTimeSpan) ProtoMessage() {}

func (x *AudienceFilterTimeSpan) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceFilterTimeSpan.ProtoReflect.Descriptor instead.
func (*AudienceFilterTimeSpan) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{12}
}

func (x *AudienceFilterTimeSpan) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *AudienceFilterTimeSpan) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type AudienceFilterEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                   // 枚举名
	EnumId    string `protobuf:"bytes,2,opt,name=enum_id,json=enumId,proto3" json:"enum_id,omitempty"` // 枚举ID
	Addition1 string `protobuf:"bytes,3,opt,name=addition1,proto3" json:"addition1,omitempty"`         // 附加字段1，当filter_id为country时表示region
	Addition2 string `protobuf:"bytes,4,opt,name=addition2,proto3" json:"addition2,omitempty"`         // 附加字段2，当filter_id为country时表示region_name
}

func (x *AudienceFilterEnum) Reset() {
	*x = AudienceFilterEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceFilterEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceFilterEnum) ProtoMessage() {}

func (x *AudienceFilterEnum) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceFilterEnum.ProtoReflect.Descriptor instead.
func (*AudienceFilterEnum) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{13}
}

func (x *AudienceFilterEnum) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AudienceFilterEnum) GetEnumId() string {
	if x != nil {
		return x.EnumId
	}
	return ""
}

func (x *AudienceFilterEnum) GetAddition1() string {
	if x != nil {
		return x.Addition1
	}
	return ""
}

func (x *AudienceFilterEnum) GetAddition2() string {
	if x != nil {
		return x.Addition2
	}
	return ""
}

type AudienceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string                  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                // 名称
	FilterId   string                  `protobuf:"bytes,2,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`        // 过滤器ID
	FilterType uint32                  `protobuf:"varint,3,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"` // 过滤器类型，1-时间跨度；2-枚举值
	TimeSpan   *AudienceFilterTimeSpan `protobuf:"bytes,4,opt,name=time_span,json=timeSpan,proto3" json:"time_span,omitempty"`        // 时间跨度
	Enums      []*AudienceFilterEnum   `protobuf:"bytes,5,rep,name=enums,proto3" json:"enums,omitempty"`                              // 枚举列表
}

func (x *AudienceFilter) Reset() {
	*x = AudienceFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceFilter) ProtoMessage() {}

func (x *AudienceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceFilter.ProtoReflect.Descriptor instead.
func (*AudienceFilter) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{14}
}

func (x *AudienceFilter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AudienceFilter) GetFilterId() string {
	if x != nil {
		return x.FilterId
	}
	return ""
}

func (x *AudienceFilter) GetFilterType() uint32 {
	if x != nil {
		return x.FilterType
	}
	return 0
}

func (x *AudienceFilter) GetTimeSpan() *AudienceFilterTimeSpan {
	if x != nil {
		return x.TimeSpan
	}
	return nil
}

func (x *AudienceFilter) GetEnums() []*AudienceFilterEnum {
	if x != nil {
		return x.Enums
	}
	return nil
}

type AudienceFilterListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result       `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`   // 返回结果
	Filters []*AudienceFilter `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"` // 过滤器列表
}

func (x *AudienceFilterListRsp) Reset() {
	*x = AudienceFilterListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceFilterListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceFilterListRsp) ProtoMessage() {}

func (x *AudienceFilterListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceFilterListRsp.ProtoReflect.Descriptor instead.
func (*AudienceFilterListRsp) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{15}
}

func (x *AudienceFilterListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AudienceFilterListRsp) GetFilters() []*AudienceFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

// 自测接口, POST, /api/v1/audience_overview/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset        int32  `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit         int32  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	UpdateOnCycle int32  `protobuf:"varint,3,opt,name=update_on_cycle,json=updateOnCycle,proto3" json:"update_on_cycle,omitempty"`
	AccountId     string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	CampaignId    string `protobuf:"bytes,5,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{16}
}

func (x *SayHiReq) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *SayHiReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SayHiReq) GetUpdateOnCycle() int32 {
	if x != nil {
		return x.UpdateOnCycle
	}
	return 0
}

func (x *SayHiReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *SayHiReq) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{17}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type UpdateAudienceListManulReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime string `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 开始时间
	EndTime   string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 结束时间
	Platform  string `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`                    // 平台名称: Google, Facebook
}

func (x *UpdateAudienceListManulReq) Reset() {
	*x = UpdateAudienceListManulReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAudienceListManulReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAudienceListManulReq) ProtoMessage() {}

func (x *UpdateAudienceListManulReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAudienceListManulReq.ProtoReflect.Descriptor instead.
func (*UpdateAudienceListManulReq) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateAudienceListManulReq) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *UpdateAudienceListManulReq) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *UpdateAudienceListManulReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type UpdateAudienceListManulRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *UpdateAudienceListManulRsp) Reset() {
	*x = UpdateAudienceListManulRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAudienceListManulRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAudienceListManulRsp) ProtoMessage() {}

func (x *UpdateAudienceListManulRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAudienceListManulRsp.ProtoReflect.Descriptor instead.
func (*UpdateAudienceListManulRsp) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateAudienceListManulRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 获取audience_metric列表, POST, /api/v1/audience_overview/audience_metrics
type AudienceMetricsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AudienceMetricsReq) Reset() {
	*x = AudienceMetricsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceMetricsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceMetricsReq) ProtoMessage() {}

func (x *AudienceMetricsReq) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceMetricsReq.ProtoReflect.Descriptor instead.
func (*AudienceMetricsReq) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{20}
}

type AudienceMetric struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key     string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`                         // Audience中的字段名
	Title   string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                     // 前端显示名称
	Format  string `protobuf:"bytes,3,opt,name=format,proto3" json:"format,omitempty"`                   // 显示格式
	Type    string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`                       // metric类型
	AggType int32  `protobuf:"varint,5,opt,name=agg_type,json=aggType,proto3" json:"agg_type,omitempty"` // 聚合类型，1-表示可以累加；2-表示比率类型，不可以累加
}

func (x *AudienceMetric) Reset() {
	*x = AudienceMetric{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceMetric) ProtoMessage() {}

func (x *AudienceMetric) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceMetric.ProtoReflect.Descriptor instead.
func (*AudienceMetric) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{21}
}

func (x *AudienceMetric) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AudienceMetric) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AudienceMetric) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *AudienceMetric) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AudienceMetric) GetAggType() int32 {
	if x != nil {
		return x.AggType
	}
	return 0
}

type AudienceMetricsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result       `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`   // 返回结果
	Metrics []*AudienceMetric `protobuf:"bytes,2,rep,name=metrics,proto3" json:"metrics,omitempty"` // audience列表
}

func (x *AudienceMetricsRsp) Reset() {
	*x = AudienceMetricsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_audience_overview_audience_overview_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AudienceMetricsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AudienceMetricsRsp) ProtoMessage() {}

func (x *AudienceMetricsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_audience_overview_audience_overview_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AudienceMetricsRsp.ProtoReflect.Descriptor instead.
func (*AudienceMetricsRsp) Descriptor() ([]byte, []int) {
	return file_audience_overview_audience_overview_proto_rawDescGZIP(), []int{22}
}

func (x *AudienceMetricsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AudienceMetricsRsp) GetMetrics() []*AudienceMetric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

var File_audience_overview_audience_overview_proto protoreflect.FileDescriptor

var file_audience_overview_audience_overview_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x1a, 0x1c,
	0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x72, 0x0a, 0x10,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x6c, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x3b, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x22, 0x50, 0x0a, 0x08, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xef, 0x1c, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70,
	0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x69, 0x70, 0x6d, 0x12, 0x10, 0x0a, 0x03,
	0x63, 0x74, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x74, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x76, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x76, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63,
	0x70, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x63, 0x70, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x69, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x03, 0x63, 0x70, 0x69, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x61, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x70, 0x61, 0x12, 0x41, 0x0a, 0x1d, 0x72, 0x65, 0x61, 0x6c,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x1a, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x70, 0x69, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x70, 0x69, 0x12, 0x28,
	0x0a, 0x10, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x31, 0x5f, 0x72, 0x6f,
	0x61, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x44, 0x31, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x44, 0x0a, 0x1f, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x32, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x1b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x32, 0x43, 0x6f, 0x68, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x44,
	0x0a, 0x1f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x5f, 0x63, 0x6f, 0x68,
	0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x33, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x44, 0x0a, 0x1f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x64, 0x37, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x01, 0x52, 0x1b, 0x6f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x37, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x46, 0x0a, 0x20, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x34, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74,
	0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x1c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x34,
	0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x32,
	0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x32,
	0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a,
	0x19, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x5f, 0x72, 0x65, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x33, 0x52, 0x65, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x37, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x37, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x31, 0x34, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x31, 0x34, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x32, 0x5f, 0x72,
	0x6f, 0x61, 0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x32, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0d, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x33, 0x52, 0x6f, 0x61, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x37, 0x5f, 0x72,
	0x6f, 0x61, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x37, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x34, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x34, 0x52, 0x6f,
	0x61, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x40, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x41,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x70, 0x65, 0x6e, 0x64, 0x18, 0x42, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x70, 0x65,
	0x6e, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x43, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x44, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x73, 0x18, 0x45, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x46, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x69, 0x6e, 0x41,
	0x70, 0x70, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65,
	0x74, 0x18, 0x47, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x32, 0x0a, 0x15,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62,
	0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x48, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74,
	0x12, 0x38, 0x0a, 0x18, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x68,
	0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x49, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x16, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x68, 0x6f,
	0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x18, 0x4a,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x12, 0x36, 0x0a, 0x17,
	0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f,
	0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x4c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76,
	0x65, 0x6e, 0x75, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x64, 0x31, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e,
	0x75, 0x65, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69,
	0x6d, 0x65, 0x44, 0x31, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x61,
	0x75, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x44, 0x61, 0x75, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x64, 0x61,
	0x69, 0x6c, 0x79, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x4f, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x10, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x42, 0x75, 0x64, 0x67,
	0x65, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62, 0x69, 0x64, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x50, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x42, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x18,
	0x51, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x12, 0x3b, 0x0a, 0x1a, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x64, 0x31, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x52, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x31, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x3b, 0x0a, 0x1a, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x32,
	0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x18, 0x53, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44,
	0x32, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12,
	0x3b, 0x0a, 0x1a, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x5f, 0x63, 0x6f,
	0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x54, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x33, 0x43, 0x6f,
	0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x1a,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x37, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72,
	0x74, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x55, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x37, 0x43, 0x6f, 0x68, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x34, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x56, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x34, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x57, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x33, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x64, 0x36, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x58, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x36, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x64, 0x39, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x59, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x39, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x1c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x64, 0x31, 0x32, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x19, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x32, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x1c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x64, 0x31, 0x35, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x19, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x35, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x1c, 0x6f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x64, 0x31, 0x38, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x19, 0x6f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x38, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x5d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44,
	0x31, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x32, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x44, 0x32, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x44, 0x33, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30,
	0x0a, 0x14, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x37, 0x5f, 0x72, 0x65, 0x74,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x60, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x37, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x32, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x34, 0x5f,
	0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x61, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x13, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x34, 0x52, 0x65, 0x74, 0x65, 0x6e,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x64, 0x33, 0x30, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x62, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x13, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x33, 0x30, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x36, 0x30, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x63, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x36, 0x30, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x15,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x39, 0x30, 0x5f, 0x72, 0x65, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x64, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x39, 0x30, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x34, 0x0a, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x32, 0x30,
	0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x65, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x14, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x32, 0x30, 0x52, 0x65, 0x74,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x64, 0x31, 0x35, 0x30, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x66, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44,
	0x31, 0x35, 0x30, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x16,
	0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x38, 0x30, 0x5f, 0x72, 0x65, 0x74,
	0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x67, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x38, 0x30, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x19, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31,
	0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18,
	0x68, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31,
	0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x39, 0x0a,
	0x19, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x32, 0x5f, 0x63, 0x6f, 0x68, 0x6f,
	0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x69, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x32, 0x43, 0x6f, 0x68, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x44, 0x33, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65,
	0x6e, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x37, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x18, 0x6b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x16, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44,
	0x37, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x3b,
	0x0a, 0x1a, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x34, 0x5f, 0x63, 0x6f,
	0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x6c, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31, 0x34, 0x43, 0x6f,
	0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x6f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x33, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72,
	0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x6d, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x33, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x36, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72,
	0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x6e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x36, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x3b, 0x0a, 0x1a, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x64, 0x39, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65,
	0x6e, 0x75, 0x65, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x6f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x39, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e,
	0x75, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31,
	0x32, 0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75,
	0x65, 0x18, 0x70, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x44, 0x31, 0x32, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75,
	0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x35,
	0x30, 0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x18, 0x71, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44,
	0x31, 0x35, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65,
	0x12, 0x3d, 0x0a, 0x1b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x31, 0x38, 0x30,
	0x5f, 0x63, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18,
	0x72, 0x20, 0x01, 0x28, 0x01, 0x52, 0x18, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x31,
	0x38, 0x30, 0x43, 0x6f, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x6e, 0x64,
	0x18, 0x73, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x53,
	0x70, 0x65, 0x6e, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x0c, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x50, 0x6c, 0x6f, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x75,
	0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0x76, 0x0a, 0x10, 0x41, 0x75,
	0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x6c, 0x6f, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x50, 0x6c, 0x6f, 0x74, 0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x73, 0x22, 0x50, 0x0a, 0x11, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x3b, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x0d, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x09, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x12, 0x31, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x22, 0x78, 0x0a, 0x11, 0x41, 0x75, 0x64,
	0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x73, 0x22, 0x52, 0x0a, 0x13, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x75, 0x6d, 0x50, 0x61, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x3b, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x75,
	0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x88, 0x01, 0x0a, 0x10, 0x41, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x75, 0x6d, 0x50, 0x61, 0x6e, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x70, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x70, 0x65,
	0x6e, 0x64, 0x22, 0x9a, 0x01, 0x0a, 0x13, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x75, 0x6d, 0x50, 0x61, 0x6e, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x41, 0x0a, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x75, 0x6d, 0x50, 0x61, 0x6e, 0x65, 0x6c, 0x52, 0x09, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x6d, 0x5f, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x73, 0x75, 0x6d, 0x53, 0x70, 0x65, 0x6e, 0x64, 0x22,
	0x17, 0x0a, 0x15, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x52, 0x0a, 0x16, 0x41, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x70,
	0x61, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x7d, 0x0a, 0x12,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x75, 0x6d, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x31, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x31, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x22, 0xe7, 0x01, 0x0a, 0x0e,
	0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x46, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x6e, 0x52, 0x08,
	0x74, 0x69, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x6e, 0x12, 0x3b, 0x0a, 0x05, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69,
	0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x22, 0x79, 0x0a, 0x15, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x22, 0xa0, 0x01, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x6e, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e, 0x43, 0x79,
	0x63, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x49, 0x64, 0x22, 0x2f, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x72, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x75,
	0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x6c, 0x52,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0x41, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61,
	0x6e, 0x75, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x14, 0x0a, 0x12, 0x41,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65,
	0x71, 0x22, 0x7f, 0x0a, 0x0e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61, 0x67, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x76, 0x0a, 0x12, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a,
	0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69,
	0x65, 0x77, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x40, 0x5a, 0x3e, 0x65, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_audience_overview_audience_overview_proto_rawDescOnce sync.Once
	file_audience_overview_audience_overview_proto_rawDescData = file_audience_overview_audience_overview_proto_rawDesc
)

func file_audience_overview_audience_overview_proto_rawDescGZIP() []byte {
	file_audience_overview_audience_overview_proto_rawDescOnce.Do(func() {
		file_audience_overview_audience_overview_proto_rawDescData = protoimpl.X.CompressGZIP(file_audience_overview_audience_overview_proto_rawDescData)
	})
	return file_audience_overview_audience_overview_proto_rawDescData
}

var file_audience_overview_audience_overview_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_audience_overview_audience_overview_proto_goTypes = []interface{}{
	(*AudiencePlotsReq)(nil),           // 0: audience_overview.AudiencePlotsReq
	(*Campaign)(nil),                   // 1: audience_overview.Campaign
	(*Metric)(nil),                     // 2: audience_overview.Metric
	(*AudiencePlot)(nil),               // 3: audience_overview.AudiencePlot
	(*AudiencePlotsRsp)(nil),           // 4: audience_overview.AudiencePlotsRsp
	(*AudienceTablesReq)(nil),          // 5: audience_overview.AudienceTablesReq
	(*AudienceTable)(nil),              // 6: audience_overview.AudienceTable
	(*AudienceTablesRsp)(nil),          // 7: audience_overview.AudienceTablesRsp
	(*AudienceSumPanelReq)(nil),        // 8: audience_overview.AudienceSumPanelReq
	(*AudienceSumPanel)(nil),           // 9: audience_overview.AudienceSumPanel
	(*AudienceSumPanelRsp)(nil),        // 10: audience_overview.AudienceSumPanelRsp
	(*AudienceFilterListReq)(nil),      // 11: audience_overview.AudienceFilterListReq
	(*AudienceFilterTimeSpan)(nil),     // 12: audience_overview.AudienceFilterTimeSpan
	(*AudienceFilterEnum)(nil),         // 13: audience_overview.AudienceFilterEnum
	(*AudienceFilter)(nil),             // 14: audience_overview.AudienceFilter
	(*AudienceFilterListRsp)(nil),      // 15: audience_overview.AudienceFilterListRsp
	(*SayHiReq)(nil),                   // 16: audience_overview.SayHiReq
	(*SayHiRsp)(nil),                   // 17: audience_overview.SayHiRsp
	(*UpdateAudienceListManulReq)(nil), // 18: audience_overview.UpdateAudienceListManulReq
	(*UpdateAudienceListManulRsp)(nil), // 19: audience_overview.UpdateAudienceListManulRsp
	(*AudienceMetricsReq)(nil),         // 20: audience_overview.AudienceMetricsReq
	(*AudienceMetric)(nil),             // 21: audience_overview.AudienceMetric
	(*AudienceMetricsRsp)(nil),         // 22: audience_overview.AudienceMetricsRsp
	(*aix.Result)(nil),                 // 23: aix.Result
}
var file_audience_overview_audience_overview_proto_depIdxs = []int32{
	14, // 0: audience_overview.AudiencePlotsReq.filters:type_name -> audience_overview.AudienceFilter
	1,  // 1: audience_overview.AudiencePlot.campaigns:type_name -> audience_overview.Campaign
	2,  // 2: audience_overview.AudiencePlot.metrics:type_name -> audience_overview.Metric
	23, // 3: audience_overview.AudiencePlotsRsp.result:type_name -> aix.Result
	3,  // 4: audience_overview.AudiencePlotsRsp.audiences:type_name -> audience_overview.AudiencePlot
	14, // 5: audience_overview.AudienceTablesReq.filters:type_name -> audience_overview.AudienceFilter
	1,  // 6: audience_overview.AudienceTable.campaigns:type_name -> audience_overview.Campaign
	2,  // 7: audience_overview.AudienceTable.metric:type_name -> audience_overview.Metric
	23, // 8: audience_overview.AudienceTablesRsp.result:type_name -> aix.Result
	6,  // 9: audience_overview.AudienceTablesRsp.audiences:type_name -> audience_overview.AudienceTable
	14, // 10: audience_overview.AudienceSumPanelReq.filters:type_name -> audience_overview.AudienceFilter
	1,  // 11: audience_overview.AudienceSumPanel.campaigns:type_name -> audience_overview.Campaign
	23, // 12: audience_overview.AudienceSumPanelRsp.result:type_name -> aix.Result
	9,  // 13: audience_overview.AudienceSumPanelRsp.audiences:type_name -> audience_overview.AudienceSumPanel
	12, // 14: audience_overview.AudienceFilter.time_span:type_name -> audience_overview.AudienceFilterTimeSpan
	13, // 15: audience_overview.AudienceFilter.enums:type_name -> audience_overview.AudienceFilterEnum
	23, // 16: audience_overview.AudienceFilterListRsp.result:type_name -> aix.Result
	14, // 17: audience_overview.AudienceFilterListRsp.filters:type_name -> audience_overview.AudienceFilter
	23, // 18: audience_overview.SayHiRsp.result:type_name -> aix.Result
	23, // 19: audience_overview.UpdateAudienceListManulRsp.result:type_name -> aix.Result
	23, // 20: audience_overview.AudienceMetricsRsp.result:type_name -> aix.Result
	21, // 21: audience_overview.AudienceMetricsRsp.metrics:type_name -> audience_overview.AudienceMetric
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_audience_overview_audience_overview_proto_init() }
func file_audience_overview_audience_overview_proto_init() {
	if File_audience_overview_audience_overview_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_audience_overview_audience_overview_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudiencePlotsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Campaign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudiencePlot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudiencePlotsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceTablesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceTable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceTablesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceSumPanelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceSumPanel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceSumPanelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceFilterListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceFilterTimeSpan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceFilterEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceFilterListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAudienceListManulReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAudienceListManulRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceMetricsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceMetric); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_audience_overview_audience_overview_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AudienceMetricsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_audience_overview_audience_overview_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_audience_overview_audience_overview_proto_goTypes,
		DependencyIndexes: file_audience_overview_audience_overview_proto_depIdxs,
		MessageInfos:      file_audience_overview_audience_overview_proto_msgTypes,
	}.Build()
	File_audience_overview_audience_overview_proto = out.File
	file_audience_overview_audience_overview_proto_rawDesc = nil
	file_audience_overview_audience_overview_proto_goTypes = nil
	file_audience_overview_audience_overview_proto_depIdxs = nil
}
