package service

import (
	"context"
	"fmt"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"

	ckmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	ckdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"github.com/go-pg/pg/v10"
	"github.com/jinzhu/copier"
	"github.com/thoas/go-funk"
)

// 补充历史数据的素材名称
func WriteName2label(gameCode string) {
	ctx := log.NewSessionIDContext()

	timeStr := "2022-02-27 00:00:00"
	utcTime, _ := time.Parse("2006-1-02 15:04:05", timeStr)

	pgsql := pgdb.GetDBWithContext(ctx)

	for i := 0; i < 15; i++ {
		datestr := fmt.Sprintf("%v%02d%02d", utcTime.Year(), utcTime.Month(), utcTime.Day())
		utcTime = utcTime.Add(-24 * time.Hour)
		goorealname := fmt.Sprintf("%s_%s", pgmodel.GetGoogleRealtimeAssetInfoTableName(gameCode), datestr)
		log.ErrorContextf(ctx, "date %v", goorealname)

		limit := 1000
		maxLimit := ********

		for offset := 0; offset < maxLimit; offset = offset + limit {
			var labels []*pgmodel.ChannelAssetLabel
			pgQuery := pgsql.Model(&labels).Table(pgmodel.GetChannelAssetLabelTableName(gameCode))
			pgQuery.Column("channel_asset_id")

			pgQuery.Column("channel_asset_id")
			pgQuery.Group("channel_asset_id")
			pgQuery.Where("channel_type = ?", 1)
			pgQuery.Where("asset_name is null")
			pgQuery.Order("channel_asset_id")
			pgQuery.Offset(offset)
			pgQuery.Limit(limit)

			err := pgQuery.Select()
			if err != nil {
				log.ErrorContextf(ctx, "getPgChannelAssetLabels failed: %s", err)
				return
			}

			log.ErrorContextf(ctx, "len %v", len(labels))
			if len(labels) == 0 {
				break
			}

			updateLabel := []*pgmodel.ChannelAssetLabel{}

			assid := []string{}
			for _, v := range labels {
				assid = append(assid, v.ChannelAssetId)
			}
			real := []*pgmodel.GoogleRealtimeAssetInfo{}

			subQuery := pgsql.Model(&real).Table(goorealname)

			subQuery.Column("asset_id", "asset_name")
			subQuery.WhereIn("asset_id in (?)", assid)
			subQuery.Group("asset_id")
			subQuery.Group("asset_name")

			err = subQuery.Select()
			if err != nil {
				log.ErrorContextf(ctx, "getgooglereal failed: %s", err)
				continue
			}

			// log.ErrorContextf(ctx, "real len %v", len(real))
			for _, asset := range real {
				if len(asset.AssetName) != 0 {
					inselabel := &pgmodel.ChannelAssetLabel{
						AssetName:      asset.AssetName,
						ChannelAssetId: asset.AssetId,
					}

					updateLabel = append(updateLabel, inselabel)
				}
			}

			log.ErrorContextf(ctx, "up len %v", len(updateLabel))
			if len(updateLabel) > 0 {
				pgQuery1 := pgsql.Model(&updateLabel).Table(pgmodel.GetChannelAssetLabelTableName(gameCode))
				pgQuery1.Column("asset_name")
				pgQuery1.Where("channel_asset_label.channel_asset_id = ?channel_asset_id")
				_, err = pgQuery1.Update()
				if err != nil {
					log.ErrorContextf(ctx, "getgooglereal failed: %s", err)
					continue
				}
			}

		}
	}

}

// SyncChannelAssetLabelToCk 同步广告素材标签到CK表
func SyncChannelAssetLabelToCk(ctx context.Context) {
	gameCodes, err := getGameCodes(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getGameCodes failed: %s", err)
		return
	}

	for _, gameCode := range gameCodes {
		// 新标签体系：将渠道素材标签同步到ck外表 channel_asset.channel_asset_label_all_aix
		SyncChannelAssetLabelToCKExternal(ctx, gameCode)

		// 目前只有PUBGm 需要做去重
		if gameCode == "pubgm" {
			SyncChannelAssetLabelToCkDailyRemoveDup(gameCode)
		} else {
			SyncChannelAssetLabelToCkDaily(gameCode)
		}
	}
}

// SyncChannelAssetLabelToCkDaily 将广告标签数据同步到CK表中(一天)  去重
func SyncChannelAssetLabelToCkDailyRemoveDup(gameCode string) {
	ctx := log.NewSessionIDContext()
	err := DropCkChannelAssetLablePartition(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "DropCkChannelAssetLablePartition failed: %s", err)
	}

	// 需要去重和不需要去重 分开处理
	err = DoSyncChannelAssetLabelToCkNotRemoveDup(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "DoSyncChannelAssetLabelToCkNotRemoveDup failed: %s", err)
		return
	}

	// 使用新的去重逻辑
	err = DoSyncChannelAssetLabelToCkRemoveDupNew(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "DoSyncChannelAssetLabelToCkRemoveDupNew failed: %s", err)
		return
	}

	return
}

var firstNameWheres = []string{"素材主题", "素材版本", "素材分类", "是否含有FPS第一视角", "有无文案"}

// 只有 first_label 为 '素材主题', '素材版本', '素材分类'才需要去重，其余的不需要去重
func DoSyncChannelAssetLabelToCkNotRemoveDup(ctx context.Context, gameCode string) error {
	selectColumns := []string{"channel_type", "channel_account_id", "channel_asset_id", "label_name", "first_label", "second_label"}

	limit := 10000
	maxLimit := ********
	for offset := 0; offset < maxLimit; offset = offset + limit {
		var labels []*pgmodel.ChannelAssetLabel
		pgQuery := pgdb.GetDBWithContext(ctx).Model(&labels).Table(pgmodel.GetChannelAssetLabelTableName(gameCode))
		pgQuery.Column(selectColumns...)
		pgQuery.WhereIn("first_label NOT IN (?)", firstNameWheres)
		pgQuery.Offset(offset)
		pgQuery.Limit(limit)

		err := pgQuery.Select()
		if err != nil {
			log.ErrorContextf(ctx, "getPgChannelAssetLabels failed: %s", err)
			return fmt.Errorf("getPgChannelAssetLabels failed: %s", err)
		}

		if len(labels) == 0 {
			break
		}

		log.DebugContextf(ctx, "len %v", len(labels))

		ckLabels := channelAssetLabelsPg2Ck(gameCode, labels)
		err = UpsertCkChannelAssetLabels(ctx, gameCode, ckLabels)
		if err != nil {
			log.ErrorContextf(ctx, "UpsertCkChannelAssetLabels failed: %s", err)
			return fmt.Errorf("UpsertCkChannelAssetLabels failed: %s", err)
		}

		if len(labels) < limit {
			break
		}
	}
	return nil
}

// DoSyncChannelAssetLabelToCkRemoveDupNew first_label 为 '素材主题', '素材版本', '素材分类'需要去重
func DoSyncChannelAssetLabelToCkRemoveDupNew(ctx context.Context, gameCode string) error {
	orderby := []string{"asset_name", "first_label", "create_by", "update_time desc", "create_time desc", "channel_asset_id"}

	limit := 10000
	maxLimit := ********

	// 保存所有的素材标签
	var allAssetLabels []*pgmodel.ChannelAssetLabel
	// 因为要按照 素材名+first_label 取最新的second_label，这里使用map来处理
	nameLableMap := make(map[string]*pgmodel.ChannelAssetLabel)
	for offset := 0; offset < maxLimit; offset = offset + limit {
		labels := []*pgmodel.ChannelAssetLabel{}
		pgQuery := pgdb.GetDBWithContext(ctx).Model(&labels).Table(pgmodel.GetChannelAssetLabelTableName(gameCode))
		pgQuery.Column("*")
		pgQuery.WhereIn("first_label IN (?)", firstNameWheres)
		pgQuery.Order(orderby...)
		pgQuery.Offset(offset)
		pgQuery.Limit(limit)
		err := pgQuery.Select()
		if err != nil {
			log.ErrorContextf(ctx, "DoSyncChannelAssetLabelToCkRemoveDupNew select err:%v", err)
			return err
		}

		log.DebugContextf(ctx, "DoSyncChannelAssetLabelToCkRemoveDupNew select len %v", len(labels))

		for _, label := range labels {
			allAssetLabels = append(allAssetLabels, label)

			// 按照 素材名+first_label 维度处理
			key := fmt.Sprintf("%v_%v", label.AssetName, label.FirstLabel)
			old, ok := nameLableMap[key]
			if !ok {
				// 深拷贝一下，以免后面处理把原始label数据污染了
				copy := &pgmodel.ChannelAssetLabel{}
				copier.CopyWithOption(copy, label, copier.Option{
					DeepCopy: true,
				})
				if err != nil {
					return fmt.Errorf("copier.CopyWithOption err:%v", err)
				}
				nameLableMap[key] = copy
			} else {
				// 比较分值，取最新的二级标签
				if labelCreateByScore(old.CreateBy) < labelCreateByScore(label.CreateBy) {
					// 深拷贝一下，以免后面处理把原始label数据污染了
					copy := &pgmodel.ChannelAssetLabel{}
					copier.CopyWithOption(copy, label, copier.Option{
						DeepCopy: true,
					})
					if err != nil {
						return fmt.Errorf("copier.CopyWithOption err:%v", err)
					}
					nameLableMap[key] = copy
				}
			}
		}

		// 没有多余数据了
		if len(labels) < limit {
			break
		}
	}

	// 分批处理
	chunks := funk.Chunk(allAssetLabels, 1000).([][]*pgmodel.ChannelAssetLabel)
	for _, chunk := range chunks {
		for _, label := range chunk {
			// 按照 素材名+first_label 维度处理， 取second_label
			key := fmt.Sprintf("%v_%v", label.AssetName, label.FirstLabel)
			if t, ok := nameLableMap[key]; ok {
				label.SecondLabel = t.SecondLabel
			}
		}

		ckLabels := channelAssetLabelsPg2Ck(gameCode, chunk)
		log.DebugContextf(ctx, "DoSyncChannelAssetLabelToCkRemoveDupNew insert len %v, labels:%v", len(ckLabels), utils.ToJson(ckLabels))
		err := UpsertCkChannelAssetLabels(ctx, gameCode, ckLabels)
		if err != nil {
			log.ErrorContextf(ctx, "DoSyncChannelAssetLabelToCkRemoveDupNew UpsertCkChannelAssetLabels err:%v", err)
			return err
		}
	}
	return nil
}

// DoSyncChannelAssetLabelToCkRemoveDup ...
// Deprecated 切换到 DoSyncChannelAssetLabelToCkRemoveDupNew
// first_label 为 '素材主题', '素材版本', '素材分类'需要去重
func DoSyncChannelAssetLabelToCkRemoveDup(ctx context.Context, gameCode string) error {
	firstNameWheres := []string{"素材主题", "素材版本", "素材分类"}
	orderby := []string{"asset_name", "first_label", "create_by", "update_time desc", "create_time desc", "channel_asset_id"}

	limit := 10000
	maxLimit := ********
	var curLabel *pgmodel.ChannelAssetLabel = nil
	for offset := 0; offset < maxLimit; offset = offset + limit {
		labels := []*pgmodel.ChannelAssetLabel{}
		pgQuery := pgdb.GetDBWithContext(ctx).Model(&labels).Table(pgmodel.GetChannelAssetLabelTableName(gameCode))
		pgQuery.Column("*")
		pgQuery.WhereIn("first_label IN (?)", firstNameWheres)
		pgQuery.Order(orderby...)
		pgQuery.Offset(offset)
		pgQuery.Limit(limit)
		err := pgQuery.Select()
		if err != nil {
			log.ErrorContextf(ctx, "SyncChannelAssetLabelToCkDailyBatch failed: %s", err)
			return err
		}

		log.ErrorContextf(ctx, "len %v", len(labels))

		insertLabes := []*pgmodel.ChannelAssetLabel{}
		for _, v := range labels {
			if curLabel == nil {
				curLabel = v
				continue
			}

			// 因为sql 直接是按照时间倒序排序，所以当前面四个条件相同时，那么前一个标签的更新时间必然晚于后一个标签的时间

			if v.AssetName == curLabel.AssetName && v.FirstLabel == curLabel.FirstLabel {
				if v.CreateBy != curLabel.CreateBy && labelCreateByScore(v.CreateBy) < labelCreateByScore(curLabel.CreateBy) {
					// 标签不相等，且v的标签等级高于cur 标签等级

					curLabel = v
				}
			} else {

				insertLabes = append(insertLabes, curLabel)
				curLabel = v
			}
		}

		// 已经全部遍历完，最后还剩下curLabel需要加入到队列中写入
		if len(labels) < limit {
			insertLabes = append(insertLabes, curLabel)
		}

		log.ErrorContextf(ctx, "insert len %v", len(insertLabes))
		ckLabels := channelAssetLabelsPg2Ck(gameCode, insertLabes)
		err = UpsertCkChannelAssetLabels(ctx, gameCode, ckLabels)
		if err != nil {
			return fmt.Errorf("UpsertCkChannelAssetLabels failed: %s", err)
		}

		if len(labels) < limit {
			break
		}

	}

	return nil
}

func labelCreateByScore(flag string) int {
	firstStr := []string{"material_sync.virtual_assets_prefix", "material_sync.virtual_assets_youtube_id"}
	if funk.ContainsString(firstStr, flag) {
		return 1
	}

	if flag == "material_sync.sync_channel_asset_label" {
		return 2
	}
	return 100
}

// SyncChannelAssetLabelToCkDaily 将广告标签数据同步到CK表中(一天)
func SyncChannelAssetLabelToCkDaily(gameCode string) {
	ctx := log.NewSessionIDContext()
	err := DropCkChannelAssetLablePartition(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "DropCkChannelAssetLablePartition failed: %s", err)
	}

	limit := 10000
	maxLimit := ********
	for offset := 0; offset < maxLimit; offset += limit {
		stop, err := SyncChannelAssetLabelToCkDailyBatch(ctx, gameCode, offset, limit)
		if err != nil {
			log.ErrorContextf(ctx, "SyncChannelAssetLabelToCkDailyBatch failed: %s", err)
			return
		}

		if stop {
			break
		}
	}
}

// SyncChannelAssetLabelToCkDailyBatch 分批同步某一天的数据
func SyncChannelAssetLabelToCkDailyBatch(ctx context.Context, gameCode string, offset, limit int) (bool, error) {
	pgLabels, err := getPgChannelAssetLabels(ctx, gameCode, offset, limit)
	if err != nil {
		return false, fmt.Errorf("getPgChannelAssetLabels failed: %s", err)
	}

	if len(pgLabels) == 0 {
		return true, nil
	}

	ckLabels := channelAssetLabelsPg2Ck(gameCode, pgLabels)
	err = UpsertCkChannelAssetLabels(ctx, gameCode, ckLabels)
	if err != nil {
		return false, fmt.Errorf("UpsertCkChannelAssetLabels failed: %s", err)
	}

	if len(pgLabels) < limit {
		return true, nil
	}

	return false, nil
}

// getPgChannelAssetLabels 拉取pg表中的广告素材标签
func getPgChannelAssetLabels(ctx context.Context, gameCode string, offset, limit int) ([]*pgmodel.ChannelAssetLabel, error) {
	var labels []*pgmodel.ChannelAssetLabel
	pgQuery := pgdb.GetDBWithContext(ctx).Model(&labels).Table(pgmodel.GetChannelAssetLabelTableName(gameCode))

	selectColumns := []string{"channel_type", "channel_account_id", "channel_asset_id", "label_name", "first_label", "second_label"}
	pgQuery.Column(selectColumns...)
	pgQuery.Order(selectColumns...)
	pgQuery.Offset(offset)
	pgQuery.Limit(limit)

	err := pgQuery.Select()
	if err != nil {
		return nil, err
	}

	return labels, nil
}

// channelAssetLabelsPg2Ck 将广告素材标签从pg转为ck
func channelAssetLabelsPg2Ck(gameCode string, pgLabels []*pgmodel.ChannelAssetLabel) []*ckmodel.ChannelAssetLabel {
	ckLabels := make([]*ckmodel.ChannelAssetLabel, 0, len(pgLabels))
	author := "material_sync.sync_channel_asset_label_to_ck"

	for _, pgLabel := range pgLabels {
		ckLabel := &ckmodel.ChannelAssetLabel{}
		ckLabel.GameCode = gameCode
		ckLabel.ChannelType = pgLabel.ChannelType
		ckLabel.ChannelAccountID = pgLabel.ChannelAccountID
		ckLabel.ChannelAssetId = pgLabel.ChannelAssetId
		ckLabel.LabelName = pgLabel.LabelName
		ckLabel.FirstLabel = pgLabel.FirstLabel
		ckLabel.SecondLabel = pgLabel.SecondLabel
		ckLabel.CreateBy = author
		ckLabel.UpdateBy = author

		ckLabels = append(ckLabels, ckLabel)
	}

	return ckLabels
}

// UpsertCkChannelAssetLabels 插入更新CK表中的广告素材标签
func UpsertCkChannelAssetLabels(ctx context.Context, gameCode string, labels []*ckmodel.ChannelAssetLabel) error {
	now := time.Now()
	time_now := now.Format("2006-01-02 15:04:05")
	// 切换到新标签体系后，因为ck比较难删除数据也可能会报错，这里纪录sdate为明天
	date_now := now.AddDate(0, 0, 1).Format("********")
	chunks := funk.Chunk(labels, 1000).([][]*ckmodel.ChannelAssetLabel)
	for _, chunk := range chunks {
		for _, label := range chunk {
			label.CreateTime = time_now
			label.UpdateTime = time_now
			label.Sdate = date_now
			log.DebugContextf(ctx, "UpsertCkChannelAssetLabels label:%+v", label)
		}

		ckQuery := ckdb.GetGORMDBWithGameCode(ctx, gameCode).Table(ckmodel.GetChannelAssetLabelTableName(conf.GetBizConf().ClickhouseEnv))
		result := ckQuery.Create(chunk)
		if result.Error != nil {
			return result.Error
		}
	}

	return nil
}

// DropCkChannelAssetLablePartition 根据game code删除partition
func DropCkChannelAssetLablePartition(ctx context.Context, gameCode string) error {
	tableName := ckmodel.GetChannelAssetLabelTableName(conf.GetBizConf().ClickhouseEnv)
	date := time.Now().AddDate(0, 0, -2).Format("********")
	sql := fmt.Sprintf("ALTER TABLE %s DROP PARTITION ('%s', '%s');", tableName, gameCode, date)
	ckQuery := ckdb.GetGORMDBWithGameCode(ctx, gameCode)
	result := ckQuery.Exec(sql)

	return result.Error
}

// SyncChannelAssetLabelToCKExternal 新标签体系：将渠道素材标签同步到ck外表
func SyncChannelAssetLabelToCKExternal(ctx context.Context, gameCode string) error {
	log.DebugContextf(ctx, "SyncChannelAssetLabelToCKExternal start gameCode:%v", gameCode)

	nowDate := time.Now().Format("********")
	err := postgresql.Transaction(ctx, func(tx *pg.Tx) error {
		// 先将ck外表的game数据删除
		deleteQuery := tx.Model(&pgmodel.ChannelAssetLabelAllAix{})
		deleteQuery.Where("game_code = ?", gameCode)
		_, err := deleteQuery.Delete()
		if err != nil {
			return err
		}

		// 再从 channel_asset.channel_asset_label_{game_code}表中导入
		sql := fmt.Sprintf(`
		INSERT INTO channel_asset.channel_asset_label_all_aix(
			game_code, sdate, channel_type, channel_account_id, channel_asset_id, label_name, first_label, second_label, create_by, create_time, update_by, update_time, asset_name)
			SELECT '%s', '%s', channel_type, channel_account_id, channel_asset_id, label_name, first_label, second_label, create_by, create_time, update_by, update_time, asset_name
			FROM %s`, gameCode, nowDate, pgmodel.GetChannelAssetLabelTableName(gameCode))
		importQuery := tx.Model(&pgmodel.ChannelAssetLabelAllAix{})
		_, err = importQuery.Exec(sql)
		return err
	})
	if err != nil {
		log.ErrorContextf(ctx, "SyncChannelAssetLabelToCKExternal gameCode:%v, err:%v", gameCode, err)
		return err
	}

	return nil
}
