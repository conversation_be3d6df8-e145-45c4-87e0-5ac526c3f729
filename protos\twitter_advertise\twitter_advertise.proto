syntax = "proto3";

package twitter_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/twitter_advertise";
import "protos/aix/aix_common_message.proto";
import "twitter_advertise/twitter_option.proto";
import "google/protobuf/struct.proto";

// ------- 【内部基础结构&常量定义】start ------------------------

// 基础结构 渠道账号信息
message MediaAccount {
  string account_id = 1;   // 渠道账号id
  string account_name = 2; // 渠道账号名称
  int32 account_status = 3; // 账号状态，0表示无效，1表示生效
}

message Campaign {
  string game_code = 1;               // 游戏id
  string account_id = 2;              // 渠道账户id
  string name = 3;                    // 广告系列名称
  string inner_campaign_id = 4;       // 广告系列内部id
  string media_campaign_id = 5;       // 广告系列渠道id
  string total_budget_amount = 6;     // 广告系列总预算 单位：美元 可不填
  string daily_budget_amount = 7;     // 广告系列每日预算金额 单位：美元
  string budget_optimization = 8;     // 广告系列优化类型 eg: CAMPAIGN, LINE_ITEM
  string funding_instrument_id = 9;   // 广告账号下对应的支付账号id
  bool standard_delivery = 10;        // 广告标准支付
  string objective = 11;              // 广告系列类型 eg: APP_INSTALLS, WEBSITE_CLICKS
  string status = 12;                 // 广告系列状态
  string creator = 13;                // 广告系列创建者 前端不关心
  string failed_reason = 14;          // 广告系列发布失败原因
  string create_time = 15;            // 广告创建时间，如果是已发布，则是广告在渠道端创建的时间
  string update_time = 16;            // 广告更新时间
  bool deleted = 17;                  // 广告是否被删除，eg true(已被删除)， false(未被删除)
  repeated AdGroup ad_groups = 18;    // 广告系列下的广告组
}

message AdGroup {
  string game_code = 1;             // 游戏id
  string account_id = 2;            // 渠道账户id
  string name = 3;                  // 广告组名称
  string inner_campaign_id = 4;     // 上层campaign内部id
  string media_campaign_id = 5;     // 上层campaign渠道id
  string inner_ad_group_id = 6;     // 广告组内部id
  string media_ad_group_id = 7;     // 广告组渠道id
  string android_app_store_identifier = 8;  // android app_id
  string ios_app_store_identifier = 9;      // ios app_id
  string objective = 10;                    // 广告类型，APP_INSTALLS, WEBSITE_CLICKS
  string product_goal = 11;                 // 下层广告类型 eg: promoted_tweet
  BudgetSchedule budget_schedule = 12;      // 发布时间
  Delivery delivery = 13;                   // 发布策略
  Demographics demographics = 14;           // 人口统计资料
  Devices devices = 15;           // 广告受众的设备
  Audience audiences = 16;        // 广告受众
  TargetingFeatures targeting_features = 17;  // 广告受众的特点
  repeated string placements = 18; // 广告投放的位置
  AudiencePlatform audience_platform = 19; // 广告组的受众平台
  string status = 20;                   // 广告组状态
  string creator = 21;                  // 广告组创建者 前端不关心
  string primary_web_event_tag = 22;    // 广告组 转换事件， conversion类型使用
  string failed_reason = 23;            // 广告组 发布失败原因
  string create_time = 24;              // 广告组创建时间，如果是已发布，则是广告在渠道端创建的时间
  string update_time = 25;              // 广告组更新时间
  bool deleted = 26;                    // 广告是否被删除，eg true(已被删除)， false(未被删除)
  repeated Ad ads = 27;                 // 广告组下的广告
}

message BudgetSchedule {
  string daily_budget_amount = 1; // 一天的费用上限 单位：美元
  string total_budget_amount = 2; // 总共的费用上线 单位：美元
  string start_time = 3;          // 开始时间
  string end_time = 4;            // 结束时间
}

message Delivery {
  string goal = 1;             // 优化目标 eg: WEBSITE_CONVERSIONS(conversions), APP_INSTALLS
  string bid_strategy = 2;     // 策略 eg: MAX, AUTO, TARGET
  string bid_amount = 3;       // 每次出价费用 单位：美元
  string pay_by = 4;           // 付费标准 eg: IMPRESSION,APP_CLICKS
  bool standard_delivery = 5; // 标准优化
}

message Demographics {
  string gender = 1;  // eg:为空表示所有，female, male
  uint32 min_age = 2; // 最小年龄
  uint32 max_age = 3; // 最大年龄
  repeated string locations = 4; // 受众位置
  repeated string languages = 5; // 受众语言
  bool age_range = 6; // 是否指定年龄段，当 age_range= true时，min_age和max_age才有意义
}

message Devices {
  string platform = 1;    // 设备系统 eg:0 (iOS), 1 (Android) 默认为0
  string version = 2;     // 系统版本 通过api从渠道拉取
  string wifi = 3;        // eg: 0 (所有) 1(wifi)
  uint32 active_type = 4; // eg: 0 (默认表示不勾选) 1(within the last) 2(more than)
  string months = 5;      // eg: 月份，1,2,3,4,5,6
}

message Audience {
  repeated string include = 1;    // 包含对象
  repeated string exclude = 2;    // 不包含对象
  bool look_alike = 3;            // 勾选了 Include look-alikes 为true 否则false
}

message TargetingFeatures {
  repeated string key_words_include = 1;   // 包含关键字
  repeated string key_words_exclude = 2;   // 排除关键字
  repeated string follower = 3;            // @对象
  repeated string interests = 4;           // 受众的兴趣
}

message Ad {
  string game_code = 1;           // 游戏id
  string account_id = 2;          // 渠道账户id
  string name = 3;                // 广告名称
  string inner_ad_group_id = 4;   // 上层ad_group内部id
  string media_ad_group_id = 5;   // 上层ad_group渠道id
  string inner_ad_id = 7;         // 广告组内部id
  string media_ad_id = 8;         // 广告组渠道id
  string status = 9;              // 广告状态
  string ad_type = 10;            // 广告类型，当前 promoted_tweet (后续会接入media_creatives类型)
  string creator = 11;            // 广告创建者, 前端不关心
  string objective = 12;          // 广告分类，在创建ad时必填，其他时候无用
  string failed_reason = 13;      // 广告 发布失败原因
  string create_time = 14;        // 广告创建时间，如果是已发布，则是广告在渠道端创建的时间
  string update_time = 15;        // 广告更新时间
  bool deleted = 16;              // 广告是否被删除，eg true(已被删除)， false(未被删除)
  string inner_status = 17;       // 广告资源状态（用于匹配资源发布任务状态）
  // tweet 类型
  Tweet tweet = 18; // tweet内容
}

message Destination {
  string type = 1;                // eg：APP
  string googleplay_app_id = 2;   // android app_id
  string ios_app_store_identifier = 3;  // ios app_id
}

message Follower {
  uint64 id = 1;                // id
  string id_str = 2;            // id str
  string name = 3;              // 名称
  string screen_name = 4;       // handle name
  string profile_image_url = 5; // 头像
}

message AudiencePlatform {
  repeated string format = 1;         // 默认全选：
  repeated string ad_categories = 2;  // 广告的分类
  string advertiser_domain = 3;       // website domain
  bool is_open = 4;                   // 是否勾选audiecePlatform
}

message User {
  string id = 1;                // id
  string name = 2;              // 名称
  string username = 3;         // handle name
  string screen_name = 4;       // handle name 和user_name 一个含义，两者不同时使用
  string profile_image_url = 5; // 头像
}

message Tweet {
  string id = 1;         // tweet id
  string name = 2;       // tweet name
  string tweet_type = 3; // tweet 类型
  string full_text = 4;  // tweet 文本
  User user = 5;         // 广告用户
  string call_to_action = 6; // eg:INSTALL, SHOP, CONNECT...
  string primary_app_store = 7; // 
  bool is_single = 8;      // eg: sigle(true) or carousel(false)
  Destination destination = 9;
  repeated MediaData media_datas = 10;  // 素材
  string card_id = 11;      // 后台使用，前端忽略
}

message MediaData {
  string media_key        = 1;  // media id
  string type             = 2;  // media type eg:IMAGE,VIDEO
  string url              = 3;  // video or image url
  string video_poster_url = 4;  // VIDEO 封面url
  string head_line        = 5;  // conversions 类型时使用
  string website_url      = 6;  // conversions 类型时使用
  uint32 width            = 7;  // 宽
  uint32 height           = 8;  // 高
}

message AccountCampaignInfo {
  string account_id = 1;        // 必填 渠道账号id
  string inner_campaign_id = 2;  // 必填 内部inner_campaign_id
  string media_campaign_id = 3; // 必填 渠道media_campaign_id 优先处理
}

message PromotableUser {
  string user_id = 1;     // 可发布广告的user账号
  string id = 2;          // 当前promotable user id
  string promotable_user_type = 3; // promotable user 的权限类型
}


// ------------ 【接口定义】start ------------------------

// 查询渠道账号列表, POST, /api/v1/twitter_advertise/get_media_accounts
message GetMediaAccountsReq {
  string game_code = 1; // 必填 游戏标识game_code
}

message GetMediaAccountsRsp {
  aix.Result result = 1;              // 返回结果
  string game_code = 2;               // 游戏标识game_code
  repeated MediaAccount accounts = 3; // 账号信息列表
}

// 获取指定的campaigns POST /api/v1/twitter_advertise/get_published_campaigns_summary
message GetPublishedCampaignsSummaryReq {
  string game_code = 1;  // 必填 游戏标识game_code
  string account_id = 2; // 必填 渠道账号id
  repeated string campaign_ids = 3; // 选填campaign_id列表，为空时拉account下的所有campaings(当前不可用，默认为空，拉所有的campaigns)
}

message GetPublishedCampaignsSummaryRsp {
  aix.Result result = 1;           // 返回结果
  repeated Campaign campaigns = 2; // Campaign列表信息
}

// 获取指定的ad_groups POST /api/v1/twitter_advertise/get_published_ad_groups_summary
message GetPublishedAdGroupsSummaryReq {
  string game_code = 1;  // 必填 游戏标识game_code
  string account_id = 2; // 必填 渠道账号id
  repeated string ad_group_ids = 3; // 选填 ad_group_id列表,不填时拉account下的所有ad_group(当前不可用，默认为空，拉所有的ad_groups)
}

message GetPublishedAdGroupsSummaryRsp {
  aix.Result result = 1;          // 返回结果
  repeated AdGroup ad_groups = 2; // AdGroup列表信息
}

// 获取指定的ads POST /api/v1/twitter_advertise/get_published_ads_summary
message GetPublishedAdsSummaryReq {
  string game_code = 1;  // 必填 游戏标识game_code
  string account_id = 2; // 必填 渠道账号id
  repeated string ad_ids = 3; // 选填 ad_id列表，不填时拉account下的所有ad(当前不可用，默认为空，拉所有的ad)
}

message GetPublishedAdsSummaryRsp {
  aix.Result result = 1; // 返回结果
  repeated Ad ads = 2;   // Ads列表信息
}

// 创建草稿Campaign POST /api/v1/twitter_advertise/create_campaign
message CreateCampaignReq {
  Campaign campaign = 1; // 必填 待创建campaign信息
}

message CreateCampaignRsp {
  aix.Result result = 1; // 返回结果
  Campaign campaign = 2; // 新campaign 信息
}

// 创建草稿ad_group POST /api/v1/twitter_advertise/create_ad_group
message CreateAdGroupReq {
  AdGroup ad_group = 1; // 必填 待创建ad_group信息
}

message CreateAdGroupRsp {
  aix.Result result = 1; // 返回结果
  AdGroup ad_group = 2;  // 新ad_group 信息
}

// 创建草稿ad POST /api/v1/twitter_advertise/create_ad
message CreateAdReq {
  Ad ad = 1; // 必填 待创建ad信息
}

message CreateAdRsp {
  aix.Result result = 1; // 返回结果
  Ad ad = 2;             // 新ad 信息
}

// 查询 campaign及其ad_group,ad树状列表, POST, /api/v1/twitter_advertise/get_campaign_trees
message GetCampaignTreesReq {
  string game_code = 1; // 必填 游戏标识
  string media = 2;     // 必填 渠道标识 eg:Twitter
  repeated AccountCampaignInfo account_campaigns = 3; // 账号+campaign_id列表
}

message GetCampaignTreesRsp {
  aix.Result result = 1;           // 返回结果
  repeated Campaign campaigns = 2; // 获取完整的广告树
}

// 更新 campaign(渠道或草稿)， POST, /api/v1/twitter_advertise/update_campaign
message UpdateCampaignReq {
  Campaign campaign = 1; // 更新的数据
  repeated string fields = 2; // 指定更新的字段，为空则全量更新所有字段 eg:account_id, name, total_budget_amount, daily_budget_amount, budget_optimization, status
}

message UpdateCampaignRsp {
  aix.Result result = 1; // 返回结果
  Campaign campaign = 2; // 更新后 campaign 信息 eg:account_id, name, android_app_store_identifier, ios_app_store_identifier, budget_schedule, delivery, demographics,devices, audiences, targeting_features, placements, status
}

// 更新 ad_group(渠道或草稿)， POST, /api/v1/twitter_advertise/update_ad_group
message UpdateAdGroupReq {
  AdGroup ad_group = 1; // 更新的数据
  repeated string fields = 2; // 指定更新的字段，为空则全量更新所有字段
}

message UpdateAdGroupRsp {
  aix.Result result = 1; // 返回结果
  AdGroup ad_group = 2;  // 更新后 ad_group 信息
}

// 更新 ad(渠道或草稿)， POST, /api/v1/twitter_advertise/update_ad
message UpdateAdReq {
  Ad ad = 1;                  // 更新的数据
  repeated string fields = 2; // 指定更新的字段，为空则全量更新所有字段 草稿 可更新字段 eg:"account_id", "name", "status", "full_text", "call_to_action","primary_app_store", "destination", "media_datas", "media_type", "user" 已发布广告可更新字段 eg: "name", "full_text", "call_to_action"
  }

message UpdateAdRsp {
  aix.Result result = 1; // 返回结果
  Ad ad = 2;             // 更新后 ad 信息
}

// 发布广告(包括所属上层adgroup, campaign发布),POST, /api/v1/twitter_advertise/publish_ad_link
message PublishAdLinkReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
  string inner_ad_id = 3; // 必填 待发布的ad id
}

message PublishAdLinkRsp {
  aix.Result result = 1;        // 返回结果
  string media_campaign_id = 2; // 渠道campaign_id
  string media_ad_group_id = 3; // 渠道ad_group_id
  string media_ad_id = 4;       // 渠道ad_id
}

message GetFundingSourcesReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
}

message GetFundingSourcesRsp {
  aix.Result result = 1; // 返回结果
  repeated FundingInstrument funding_sources = 2;
}

message GetAccountAppsReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
}

message GetAccountAppsRsp {
  aix.Result result = 1; // 返回结果
  repeated AppInfo apps = 2;
}

message GetLocationsReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
}

message GetLocationsRsp {
  aix.Result result = 1; // 返回结果
  repeated Location locations = 2;
}

message GetLanguagesReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
  string query = 3;      // 选填 查询关键字
}

message GetLanguagesRsp {
  aix.Result result = 1; // 返回结果
  repeated Language languages = 2;
}

message GetOsVersionsReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
}

message GetOsVersionsRsp {
  aix.Result result = 1; // 返回结果
  repeated OsVersion os_versions = 2;
}

message GetAudiencesReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
  string query = 3;      // 选填 查询关键字
}

message GetAudiencesRsp {
  aix.Result result = 1; // 返回结果
  repeated CustomAudience audiences = 2;
}

message GetInterestsReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
  string query = 3;      // 选填 查询关键字
}

message GetInterestsRsp {
  aix.Result result = 1; // 返回结果
  repeated Interest interests = 2;
}

message GetWebEventTagsReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
}

message GetWebEventTagsRsp {
  aix.Result result = 1;
  repeated WebEventTag web_event_tags = 2;
}

// 获取广告业务配置信息, POST, /api/v1/facebook_advertise/GetBizConfig
message GetBizConfigReq {
  string catelog = 1;
  string key = 2;
}

message GetBizConfigRsp {
  aix.Result result = 1;
  google.protobuf.Struct data = 2; // 返回的业务数据, 参数不同返回内容格式会有所不同
}

message ReloadRsp{
    aix.Result result = 1;
}

message DeleteDraftsReq {
  repeated string inner_ids = 1; // 想要删除的campaign,ad_group或者ad的inner id
  string advertise_type = 2;     // 类型：campaign, ad_group, ad
}

message DeleteDraftsRsp { 
  aix.Result result = 1; 
}

message GetPromotableUserReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
}

message GetPromotableUserRsp {
  aix.Result result = 1; 
  repeated User users = 2;
}

message GetFollowersReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
  string query = 3;      // 选填 查询关键字
}

message GetFollowersRsp {
  aix.Result result = 1;
  repeated Follower followers = 2;
}

message GetUsersReq {
  string game_code = 1;  // 必填 游戏标识
  string account_id = 2; // 必填 账户id
  repeated string ids = 3; // 必填，用户id
}

message GetUsersRsp {
  aix.Result result = 1;
  repeated User users = 2;
}

message StatusStatistic {
  string status = 1; // 状态
  int32 num = 2;     // 个数
}

message CampaignStatusStatistics {
  string id = 1;
  repeated StatusStatistic ad_group_statistics = 2;
  repeated StatusStatistic ad_statistics = 3;
}

// 获取campaign子级状态统计, POST, /api/v1/twitter_advertise/get_campaign_status_statistics
message GetCampaignStatusStatisticsReq {
  string game_code = 1;                         // 业务名
  repeated string inner_ids = 2;       // 内部campaign id
  repeated string media_ids = 3;       // 已发布campaign id
}

message GetCampaignStatusStatisticsRsp {
  aix.Result result = 1;
  repeated CampaignStatusStatistics inner_statistics = 2; // 状态统计结果列表, 结果与请求inner_ids顺序一致
  repeated CampaignStatusStatistics media_statistics = 3; // 状态统计结果列表, 结果与请求media_ids顺序一致
}

message AdGroupStatusStatistics {
  string id = 1;
  repeated StatusStatistic ad_statistics = 2;
}

// 获取ad_group子级状态统计, POST, /api/v1/twitter_advertise/get_ad_group_status_statistics
message GetAdGroupStatusStatisticsReq {
  string game_code = 1;                         // 业务名
  repeated string inner_ids = 2;       // 内部ad_group id
  repeated string media_ids = 3;       // 已发布ad_group id
}

message GetAdGroupStatusStatisticsRsp {
  aix.Result result = 1;
  repeated AdGroupStatusStatistics inner_statistics = 2; // 状态统计结果列表, 结果与请求inner_ids顺序一致
  repeated AdGroupStatusStatistics media_statistics = 3; // 状态统计结果列表, 结果与请求media_ids顺序一致
}


message SqlCondition {
  string column = 1;
  string operator = 2; // 目前只支持: IN, =, LIKE, OR_IN; 条件之间是 AND
  repeated string value = 3;
}

// 拉取草稿数据, POST, /api/v1/twitter_advertise/get_drafts
message GetDraftsReq {
  repeated SqlCondition conditions = 1;
  int32 offset = 2;          // 拉取偏移
  int32 limit = 3;           // 拉取条数
  string advertise_type = 4; // 类型：campaign, ad_group, ad
}

// 草稿数据
message DraftView {
  string game_code = 1;             // 业务名
  string account_id = 2;            // account账号
  string inner_campaign_id = 3;     // 内部 campaign_id
  string media_campaign_id = 4;     // 渠道 campaign_id
  string campaign_name = 5;         // campaign 名
  string region = 6;                // campaign 投放地区缩写
  string country = 7;               // campaign 投放国家缩写
  string platform = 8;              // Campaign 投放的系统, 比如ios, and, pc, all
  string campaign_status = 9;       // campaign 状态
  string inner_ad_group_id = 10;    // 内部 ad_group_id
  string media_ad_group_id = 11;    // 渠道 ad_group_id
  string ad_group_name = 12;        // ad_group 名
  string ad_group_status = 13;      // ad_group 状态
  string inner_ad_id = 14;          // 内部 ad_id
  string media_ad_id = 15;          // 渠道 ad_id
  string ad_name = 16;              // ad 名
  string ad_status = 17;            // ad 状态
  string aix_campaign_type = 18;    // aix campaign type(objective)
}

message GetDraftsRsp {
  aix.Result result = 1;
  repeated DraftView drafts = 2; // 草稿列表
}

message AdvertisingStatus {
  string inner_id = 1;
  string media_id = 2;
  string status = 3; // 参照枚举 Status
  string account_id = 4;
}

// 更新已发布广告状态, POST, /api/v1/twitter_advertise/change_published_advertising_status
message ChangePublishedAdvertisingStatusReq {
  string gameCode = 1;
  repeated AdvertisingStatus status = 2;
  string advertise_type = 3; // 类型：campaign, ad_group, ad
}

message ChangePublishedAdvertisingStatusRsp {
  aix.Result result = 1;
  repeated AdvertisingStatus status = 2; // 返回修改成功的campaign状态
}

// 创建campaign 树, POST, /api/v1/twitter_advertise/create_campaign_tree
message CreateCampaignTreeReq {
  Campaign campaign_tree = 1;
}

message CreateCampaignTreeRsp {
  aix.Result result = 1;
  Campaign campaign_tree = 2; // 创建成果的campaign树形结构
}

// 创建ad_group 树, POST, /api/v1/twitter_advertise/create_ad_group_tree
message CreateAdGroupTreeReq {
  AdGroup ad_group_tree = 1;
}

message CreateAdGroupTreeRsp {
  aix.Result result = 1;
  AdGroup ad_group_tree = 2;
}

message CancelPublishingReq {
  string game_code = 1; // 游戏业务
  string inner_id = 2;  // inner_id
  string advertise_type = 3;  // 广告类型
}

message CancelPublishingRsp {
  aix.Result result = 1;
}

message CopyCampaignReq {
  string                    game_code              = 1;  // 游戏业务
  string                    account_id             = 2;  // 渠道账号id
  string                    inner_campaign_id      = 3;  // 复制 内部 inner_campaign_id
  string                    campaign_id            = 4;  // 复制 渠道 campaign_id
  string                    copy_type              = 5;  // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyCampaignRsp {
  aix.Result                result                 = 1;
  string                    inner_campaign_id      = 2;  // 生成复制后的草稿 inner_campaign_id
}

message CopyAdgroupReq {
  string game_code = 1;      // 游戏业务
  string account_id = 2;     // 渠道账号id
  string inner_adgroup_id = 3; // 复制 内部 inner_adgroup_id
  string adgroup_id = 4;       // 复制 渠道 adgroup_id
  string copy_type = 5; // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyAdgroupRsp {
  aix.Result result = 1;
  string inner_adgroup_id = 2; // 生成复制后的草稿 inner_adgroup_id
}

message CopyAdReq {
  string game_code = 1;   // 游戏业务
  string account_id = 2;  // 渠道账号id
  string inner_ad_id = 3; // 复制 内部 inner_ad_id
  string ad_id = 4;       // 复制 渠道 ad_id
}

message CopyAdRsp {
  aix.Result result = 1;
  string inner_ad_id = 2; //  生成复制后的草稿 inner_ad_id
}

// 手动触发属性数据同步
message SyncStrategyDataReq {
  repeated string game_codes = 1;
  repeated string account_ids = 2;
}

message SyncStrategyDataRsp { 
  aix.Result result = 1;
}

// 拉取渠道图片视频素材信息 POST, /api/v1/twitter_advertise/get_media_image_video
message GetMediaImageVideoReq {
    string   game_code        = 1;  // 必填 游戏标识game_code
    string   account_id       = 2;  // 必填 渠道账号id
    repeated string image_ids = 3;  // 图片id列表
    repeated string video_ids = 4;  // 视频id列表
}

// 图片视频信息
message ImageVideo {
    string id            = 1;  // 图片视频id
    int32  type          = 2;  // 1-视频，2-图片
    string name          = 3;  // 名称
    int32  width         = 4;  // 宽
    int32  height        = 5;  // 高
    string thumbnail_url = 6;  // 缩略图
    string video_url     = 7;  // 视频预览地址
}

message GetMediaImageVideoRsp {
    aix.Result result            = 1;
    string     trace_id          = 2;  // 请求trace_id
    repeated   ImageVideo assets = 3;  // 素材信息
}