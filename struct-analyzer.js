const fs = require('fs');
const path = require('path');

/**
 * Go结构体依赖分析器
 * 分析Go结构体的依赖关系，递归查找所有相关的结构体定义
 */
class GoStructAnalyzer {
    constructor() {
        this.structCache = new Map(); // 缓存已解析的结构体
        this.visited = new Set(); // 防止循环依赖
        this.packagePaths = new Map(); // 包名到文件路径的映射
    }

    /**
     * 初始化包路径映射
     * @param {string} rootDir - 项目根目录
     */
    initializePackagePaths(rootDir) {
        // 添加已知的包路径
        this.packagePaths.set('aix', path.join(rootDir, 'protos/aix/aix_common_message.pb.go'));
        this.packagePaths.set('material_display', path.join(rootDir, 'protos/material_display/material_display.pb.go'));
    }

    /**
     * 解析Go文件中的所有结构体定义
     * @param {string} filePath - Go文件路径
     * @returns {Map} 结构体名称到定义的映射
     */
    parseGoFile(filePath) {
        if (!fs.existsSync(filePath)) {
            console.warn(`文件不存在: ${filePath}`);
            return new Map();
        }

        const content = fs.readFileSync(filePath, 'utf-8');
        const structs = new Map();
        const lines = content.split('\n');

        // 查找结构体定义，包括前面的注释
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // 匹配结构体定义开始
            const structMatch = line.match(/^type\s+(\w+)\s+struct\s*\{/);
            if (structMatch) {
                const structName = structMatch[1];

                // 收集结构体前的注释
                let structComment = '';
                let commentStartIndex = i - 1;
                while (commentStartIndex >= 0) {
                    const commentLine = lines[commentStartIndex].trim();
                    if (commentLine.startsWith('//')) {
                        structComment = commentLine + '\n' + structComment;
                        commentStartIndex--;
                    } else if (commentLine === '') {
                        commentStartIndex--;
                    } else {
                        break;
                    }
                }

                // 收集结构体内容直到结束
                let structBody = '';
                let braceCount = 1;
                let j = i + 1;

                while (j < lines.length && braceCount > 0) {
                    const bodyLine = lines[j];
                    structBody += bodyLine + '\n';

                    // 计算大括号数量
                    for (const char of bodyLine) {
                        if (char === '{') braceCount++;
                        if (char === '}') braceCount--;
                    }
                    j++;
                }

                // 构建完整的结构体定义
                const fullDefinition = structComment + line + '\n' + structBody;

                // 解析结构体字段
                const fields = this.parseStructFields(structBody);

                structs.set(structName, {
                    name: structName,
                    fields: fields,
                    comment: structComment.trim(),
                    rawDefinition: fullDefinition.trim()
                });

                // 跳过已处理的行
                i = j - 1;
            }
        }

        return structs;
    }

    /**
     * 解析结构体字段
     * @param {string} structBody - 结构体内容
     * @returns {Array} 字段信息数组
     */
    parseStructFields(structBody) {
        const fields = [];
        const lines = structBody.split('\n');

        for (let i = 0; i < lines.length; i++) {
            let line = lines[i].trim();
            if (!line || line.includes('protoimpl.') || line.includes('unknownFields')) {
                continue;
            }

            // 跳过单独的注释行，但保留字段后的注释
            if (line.startsWith('//') && !line.includes('protobuf:')) {
                continue;
            }

            // 匹配字段定义: fieldName type `tags` // comment
            const fieldMatch = line.match(/^(\w+)\s+([*\[\]]*\w+(?:\.\w+)?)\s*`([^`]*)`?\s*(\/\/.*)?/);
            if (fieldMatch) {
                const fieldName = fieldMatch[1];
                const fieldType = fieldMatch[2];
                const tags = fieldMatch[3] || '';
                const comment = fieldMatch[4] || '';

                fields.push({
                    name: fieldName,
                    type: fieldType,
                    tags: tags,
                    comment: comment.replace('//', '').trim(),
                    isPointer: fieldType.startsWith('*'),
                    isSlice: fieldType.startsWith('[]'),
                    baseType: this.extractBaseType(fieldType)
                });
            }
        }

        return fields;
    }

    /**
     * 提取字段的基础类型
     * @param {string} fieldType - 字段类型
     * @returns {string} 基础类型
     */
    extractBaseType(fieldType) {
        // 移除指针、切片等修饰符
        let baseType = fieldType.replace(/^[*\[\]]+/, '');
        
        // 处理包名.类型名的情况
        if (baseType.includes('.')) {
            const parts = baseType.split('.');
            return {
                package: parts[0],
                type: parts[1]
            };
        }
        
        return baseType;
    }

    /**
     * 检查是否为基础类型
     * @param {string} typeName - 类型名称
     * @returns {boolean} 是否为基础类型
     */
    isBasicType(typeName) {
        const basicTypes = [
            'string', 'int', 'int32', 'int64', 'uint', 'uint32', 'uint64',
            'float32', 'float64', 'bool', 'byte', 'rune'
        ];
        return basicTypes.includes(typeName);
    }

    /**
     * 分析结构体依赖关系
     * @param {string} structName - 结构体名称
     * @param {string} packageName - 包名（可选）
     * @param {string} rootDir - 项目根目录
     * @returns {Map} 所有相关结构体的定义
     */
    analyzeStructDependencies(structName, packageName = 'material_display', rootDir = '.') {
        this.initializePackagePaths(rootDir);
        this.structCache.clear();
        this.visited.clear();

        // 加载所有相关的Go文件
        this.loadAllStructs();

        const result = new Map();
        this.collectDependencies(structName, packageName, result);

        return result;
    }

    /**
     * 加载所有结构体定义到缓存
     */
    loadAllStructs() {
        for (const [pkg, filePath] of this.packagePaths) {
            const structs = this.parseGoFile(filePath);
            for (const [structName, structDef] of structs) {
                const key = `${pkg}.${structName}`;
                this.structCache.set(key, structDef);
            }
        }
    }

    /**
     * 递归收集依赖的结构体
     * @param {string} structName - 结构体名称
     * @param {string} packageName - 包名
     * @param {Map} result - 结果集合
     */
    collectDependencies(structName, packageName, result) {
        const key = `${packageName}.${structName}`;
        
        if (this.visited.has(key)) {
            return; // 防止循环依赖
        }
        
        this.visited.add(key);
        
        const structDef = this.structCache.get(key);
        if (!structDef) {
            console.warn(`未找到结构体定义: ${key}`);
            return;
        }

        // 添加当前结构体到结果
        result.set(structName, structDef);

        // 递归分析字段依赖
        for (const field of structDef.fields) {
            const baseType = field.baseType;
            
            if (typeof baseType === 'object' && baseType.package && baseType.type) {
                // 跨包引用
                if (!this.isBasicType(baseType.type)) {
                    this.collectDependencies(baseType.type, baseType.package, result);
                }
            } else if (typeof baseType === 'string' && !this.isBasicType(baseType)) {
                // 同包引用
                this.collectDependencies(baseType, packageName, result);
            }
        }
    }

    /**
     * 格式化输出结构体定义
     * @param {Map} structs - 结构体定义映射
     * @returns {string} 格式化的结构体定义
     */
    formatStructDefinitions(structs) {
        let output = '';

        for (const [structName, structDef] of structs) {
            // 添加结构体注释（如果有）
            if (structDef.comment) {
                output += structDef.comment + '\n';
            }

            // 添加结构体定义
            output += `type ${structName} struct {\n`;

            // 添加字段，保留注释和格式
            for (const field of structDef.fields) {
                // 跳过protobuf内部字段
                if (['state', 'sizeCache', 'unknownFields'].includes(field.name)) {
                    continue;
                }

                const fieldLine = `\t${field.name.padEnd(15)} ${field.type}`;
                const tagsLine = field.tags ? ` \`${field.tags}\`` : '';
                const commentLine = field.comment ? ` // ${field.comment}` : '';

                output += fieldLine + tagsLine + commentLine + '\n';
            }

            output += '}\n\n';
        }

        return output;
    }

    /**
     * 格式化输出原始结构体定义（保留完整格式）
     * @param {Map} structs - 结构体定义映射
     * @returns {string} 原始格式的结构体定义
     */
    formatRawStructDefinitions(structs) {
        let output = '';

        for (const [structName, structDef] of structs) {
            output += `// ${structName}\n`;
            output += structDef.rawDefinition + '\n\n';
        }

        return output;
    }
}

/**
 * 主函数 - 分析指定结构体的依赖关系
 * @param {string} structName - 要分析的结构体名称
 * @param {string} rootDir - 项目根目录
 * @param {boolean} preserveOriginal - 是否保留原始格式
 */
function analyzeStruct(structName, rootDir = '.', preserveOriginal = false) {
    const analyzer = new GoStructAnalyzer();

    console.log(`正在分析结构体: ${structName}`);
    console.log('='.repeat(50));

    const dependencies = analyzer.analyzeStructDependencies(structName, 'material_display', rootDir);

    if (dependencies.size === 0) {
        console.log(`未找到结构体 ${structName} 的定义`);
        return;
    }

    console.log(`找到 ${dependencies.size} 个相关结构体:`);
    for (const structName of dependencies.keys()) {
        console.log(`- ${structName}`);
    }

    console.log('\n' + '='.repeat(50));
    console.log('完整的结构体定义:');
    console.log('='.repeat(50));

    // 根据参数选择输出格式
    const formattedOutput = preserveOriginal ?
        analyzer.formatRawStructDefinitions(dependencies) :
        analyzer.formatStructDefinitions(dependencies);
    console.log(formattedOutput);

    return dependencies;
}

// 导出模块
module.exports = {
    GoStructAnalyzer,
    analyzeStruct
};

// 如果直接运行此脚本
if (require.main === module) {
    const args = process.argv.slice(2);
    const structName = args[0] || 'AddLabelRuleReq';
    const rootDir = args[1] || '.';
    const preserveOriginal = args.includes('--raw') || args.includes('-r');

    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
Go结构体依赖分析器

用法:
    node struct-analyzer.js <结构体名称> [项目根目录] [选项]

选项:
    --raw, -r     保留原始格式（包含所有protobuf字段）
    --help, -h    显示帮助信息

示例:
    node struct-analyzer.js GetMaterialInfoRsp
    node struct-analyzer.js GetMaterialInfoRsp . --raw
    node struct-analyzer.js MaterialExt /path/to/project
        `);
        process.exit(0);
    }

    analyzeStruct(structName, rootDir, preserveOriginal);
}
