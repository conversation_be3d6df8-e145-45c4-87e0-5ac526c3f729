package router

import (
	"net/http"
	_ "time"

	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"

	"e.coding.intlgame.com/ptc/aix-backend/common/metrics/ginmetrics"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pkgMiddleware "e.coding.intlgame.com/ptc/aix-backend/common/pkg/middleware"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/preprocess"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/service"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// 初始化路由
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(pkgMiddleware.GinLogger())
	r.Use(pkgMiddleware.GinRecovery(true))

	// prometheus metrics
	m := ginmetrics.GetMonitor()
	m.Use(r)

	// 素材同步
	serverApi := r.Group("api/v1/material_sync/")

	// 根据外部传参来改变系统日志级别
	serverApi.POST("/say_hi", sayHi)

	// 设置素材标签和封面
	serverApi.POST("/set_material_label", setMaterialLabel)

	// 校验素材信息
	serverApi.POST("/verify_asset", verifyAsset)

	// 手动同步素材推荐表
	serverApi.POST("/sync_creative_recommend_manual", syncCreativeRecommendManual)

	// 修正upload表中的信息
	serverApi.POST("/rectify_creative_upload_manual", rectifyCreativeUploadManual)

	// 上传无实体素材
	serverApi.POST("/upload_virtual_assets", upLoadVirtualAssets)

	// 设置素材映射
	serverApi.POST("/set_asset_map", setAssetMap)

	// 设置素材映射
	serverApi.POST("/start_media_content_map", startMediaContentMap)

	// 同步素材历史上线数据信息
	serverApi.POST("/sync_overview_online_status_history", syncOverviewOnlineStatusHistory)

	// 内部接口 触发定时任务  curl http://127.0.0.1:8080/api/v1/material_sync/cron_trigger
	serverApi.POST("/cron_trigger", func(ctx *gin.Context) {
		req, rsp := &material_sync.CronTriggerReq{}, &material_sync.CronTriggerRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		if err == nil {
			err = service.CronTrigger(ctx, req, rsp)
		}

		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 通知接口，将某个标签规则应用到aix library, POST, /api/v1/material_sync/notify_apply_label_rule_to_aix
	serverApi.POST("/notify_apply_label_rule_to_aix", func(ctx *gin.Context) {
		req, rsp := &material_sync.NotifyApplyLabelRuleToAixReq{}, &material_sync.NotifyApplyLabelRuleToAixRsp{}
		err := preprocess.BindAndValidReturnError(ctx, req)
		log.DebugContextf(ctx, "BindAndValidReturnError req:%+v, err:%v", req, err)
		if err == nil {
			err = service.NotifyApplyLabelRuleToAix(ctx, req, rsp)
		}

		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	return r
}

// setMaterialLabel 设置素材标签
func setMaterialLabel(ctx *gin.Context) {
	req, rsp := &material_sync.SetMaterialLabelReq{}, &material_sync.SetMaterialLabelRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "setMaterialLabel httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.SetMaterialLabel(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.setMaterialLabel failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// sayHi 测试接口
func sayHi(ctx *gin.Context) {
	req, rsp := &material_sync.SayHiReq{}, &material_sync.SayHiRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "say hi httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.SayHi(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.SayHi failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// verifyAsset 测试接口
func verifyAsset(ctx *gin.Context) {
	req, rsp := &material_sync.VerifyAssetReq{}, &material_sync.VerifyAssetRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "verify asset httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.VerifyAsset(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.SayHi failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// syncCreativeRecommendManul 手动同步素材推荐内容
func syncCreativeRecommendManual(ctx *gin.Context) {
	req, rsp := &material_sync.SyncCreativeRecommendManualReq{}, &material_sync.SyncCreativeRecommendManualRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "sync creative recommend manual httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.SyncCreativeRecommendManul(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.SyncCreativeRecommendManul failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// rectifyCreativeUploadManual 矫正素材上传广告数据
func rectifyCreativeUploadManual(ctx *gin.Context) {
	req, rsp := &material_sync.RectifyCreativeUploadManualReq{}, &material_sync.RectifyCreativeUploadManualRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "rectify creative upload manual httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.RectifyCreativeUploadManual(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.RectifyCreativeUploadManual failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// upLoadVirtualAssets 上传非实体素材
func upLoadVirtualAssets(ctx *gin.Context) {
	req, rsp := &material_sync.UploadVirtualAssetsReq{}, &material_sync.UploadVirtualAssetsRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "upload virtual assets httpCode: %d, code: %d, req: %+v", httpCode, code, req)

	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.UploadVirtualAssets(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.UploadVirtualAssets failed, err: %v", err)
	}

	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// setAssetMap 设置素材映射关系
func setAssetMap(ctx *gin.Context) {
	req, rsp := &material_sync.SetAssetMapReq{}, &material_sync.SetAssetMapRsq{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "set asset map httpCode: %d, code: %d, req: %+v", httpCode, code, req)

	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.SetAssetMap(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.SetAssetMap failed, err: %v", err)
	}

	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// startMediaContentMap 开启UA视频映射
func startMediaContentMap(ctx *gin.Context) {
	req, rsp := &material_sync.StartMediaContentMapReq{}, &material_sync.StartMediaContentMapRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.DebugContextf(ctx, "start media content map httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.StartMediaContentMap(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.SayHi failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}

// syncOverviewOnlineStatusHistory 同步素材历史上线状态信息
func syncOverviewOnlineStatusHistory(ctx *gin.Context) {
	req, rsp := &material_sync.SyncOverviewOnlineStatusHistoryReq{}, &material_sync.SyncOverviewOnlineStatusHistoryRsp{}
	httpCode, code := preprocess.BindAndValid(ctx, req)
	log.InfoContextf(ctx, "sync overview online status history httpCode: %d, code: %d, req: %+v", httpCode, code, req)
	if httpCode != http.StatusOK {
		log.ErrorContextf(ctx, "Fail to bind and valid body, httpCode: %v", httpCode)
		rsp.Result = &aix.Result{ErrorCode: uint32(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), ErrorMessage: "error params"}
		preprocess.Response(ctx, rsp)
		return
	}

	err := service.SyncOverviewOnlineStatusHistory(ctx, req, rsp)
	if err != nil {
		log.ErrorContextf(ctx, "service.SayHi failed, err: %v", err)
	}
	errs.FullRsp(rsp, err)
	preprocess.Response(ctx, rsp)
}
