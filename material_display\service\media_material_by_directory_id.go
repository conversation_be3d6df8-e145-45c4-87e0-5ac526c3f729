// Package service 服务接口实现代码
package service

import (
	"context"
	"fmt"
	"strconv"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

// MediaMaterialByDirectoryId 通过目录拉取素材列表
func MediaMaterialByDirectoryId(ctx *gin.Context, req *pb.MediaMaterialByDirectoryIdReq, rsp *pb.MediaMaterialByDirectoryIdRsp) error {
	log.InfoContextf(ctx, "MediaMaterialByDirectoryIdReq!!!: %+v", req)

	var overviews []*model.CreativeOverview
	var err error
	var total uint32
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)

	total, overviews, err = data.GetMediaMaterialByDirectoryId(ctx, req.GetOffset(), req.GetCount(), req.GetIsFilterStatus(), req.GetStatus(),
		req.GetOnlineStatus(), req.GetDirectoryId(), gameCode, arthub.ARTHUB_ASSET_STATUS_NORMAL, req.GetFilterOnlineStatus())
	if err != nil {
		log.ErrorContextf(ctx, "error data.GetMaterialList, err: %v", err)
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "system error")
	}

	//arthubCode := ""
	//depotCache, ok := cache.DepotTbArthubDepotCache[gameCode]
	//if ok {
	//	arthubCode = depotCache.ArthubCode
	//} else {
	//	depot, err := GetArthubToken(ctx, gameCode)
	//	if err != nil {
	//		log.ErrorContextf(ctx, "error data.GetMaterialList GetArthubToken, gameCode: %v, err: %v", gameCode, err)
	//		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR), "system error")
	//	}
	//	arthubCode = depot.ArthubCode
	//}

	rsp.Materials, err = MediaMaterialByDirectoryIdMetas(ctx, arthubOverViews2MediaMaterialByDirectoryIdMeta(ctx, overviews), gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "error MediaMaterialByDirectoryIdMetas, err: %v", err)
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR), "system error")
	}
	rsp.Total = total
	assetIDList := []string{}
	for _, asset := range rsp.GetMaterials() {
		assetIDList = append(assetIDList, cast.ToString(asset.GetAssetId()))
	}
	if req.GetWithDetail() > 0 {
		detailMap, err := data.BtGetMaterialDetail(ctx, gameCode, assetIDList)
		if err != nil {
			log.ErrorContextf(ctx, "error data.BtGetMaterialDetail, err: %v", err)
		}
		log.DebugContextf(ctx, "detailMap: %v", detailMap)
		for _, asset := range rsp.GetMaterials() {
			asset.MaterialExt = detail2proto(ctx, detailMap[cast.ToString(asset.AssetId)])
		}
	}
	return nil
}

// arthub数据列表转aix数据列表
func arthubOverViews2MediaMaterialByDirectoryIdMeta(ctx context.Context, overviews []*model.CreativeOverview) []*pb.MediaMaterialByDirectoryIdMeta {
	materials := make([]*pb.MediaMaterialByDirectoryIdMeta, 0, len(overviews))
	for _, overview := range overviews {
		materials = append(materials, arthubOverView2MediaMaterialByDirectoryIdMeta(ctx, overview))
	}
	return materials
}

// arthub数据转aix数据
func arthubOverView2MediaMaterialByDirectoryIdMeta(ctx context.Context, overview *model.CreativeOverview) *pb.MediaMaterialByDirectoryIdMeta {
	material := &pb.MediaMaterialByDirectoryIdMeta{}
	material.AssetId = overview.AssetID
	material.Name = overview.AssetName
	material.Status = overview.Status
	material.UplineDate = overview.UplineDate
	material.OfflineDate = overview.OfflineDate
	material.OnlineDays = getOnlineDays(ctx, overview.OnlineStatus, overview.OfflineDate, overview.OnlineDays)
	material.CreateDate = overview.CreateDate
	material.OnlineStatus = int32(overview.OnlineStatus)
	material.OnlineDate = overview.OnlineDate
	material.OfflineDate = overview.OfflineDate
	material.AssetStatus = int32(overview.AssetStatus)
	return material
}

// 获取媒体详情列表
func MediaMaterialDetails(ctx *gin.Context, assetIDs []string, gameCode string) (map[string]*model.CreativeAssetDetails, error) {
	var details []*model.CreativeAssetDetails
	//var assetIDsStr, splitor string
	//for _, assetID := range assetIDs {
	//	assetIDsStr += splitor + cast.ToString(assetID)
	//	splitor = ","
	//}
	//assetIDsStr = fmt.Sprintf("(%s)", assetIDsStr)
	//_, err := postgresql.GetDBWithContext(ctx).Query(&details, fmt.Sprintf("SELECT * FROM %s.tb_creative_details_%s WHERE asset_id in ", "arthub_sync", gameCode)+assetIDsStr)
	//
	err := postgresql.GetDBWithContext(ctx).Model(&details).
		Table(fmt.Sprintf("%s.tb_creative_details_%s", "arthub_sync", gameCode)).
		WhereIn("asset_id IN (?)", assetIDs).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "get material details failed, assetIDs: %v, err: %v", assetIDs, err.Error())
		return nil, err
	}

	assetID2Detail := make(map[string]*model.CreativeAssetDetails)
	for _, detail := range details {
		assetID2Detail[detail.AssetID] = detail
	}
	return assetID2Detail, nil
}

// 填充返回结果
func MediaMaterialByDirectoryIdMetas(ctx *gin.Context, metas []*pb.MediaMaterialByDirectoryIdMeta, gameCode string) ([]*pb.MediaMaterialByDirectoryIdMeta, error) {
	var assetIDs []string
	var assetPreviewUrlIDs []string
	for _, meta := range metas {
		assetIDs = append(assetIDs, meta.GetAssetId())
		if meta.AssetStatus == arthub.ARTHUB_ASSET_STATUS_NORMAL {
			assetPreviewUrlIDs = append(assetPreviewUrlIDs, meta.GetAssetId())
		}
	}

	if len(assetIDs) == 0 {
		return nil, nil
	}
	assetID2Detail, err := MediaMaterialDetails(ctx, assetIDs, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "materialDetails failed, err: %s", err.Error())
		return metas, err
	}

	depot, err := data.GetDepot(gameCode)
	if err != nil {
		log.ErrorContextf(ctx, " MediaMaterialByDirectoryIdMetas get depot error, gamecode: %v, err: %s", gameCode, err.Error())
		return metas, err
	}
	var new_metas []*pb.MediaMaterialByDirectoryIdMeta
	switch depot.Type {
	case utils.GAME_DEPOT_TYPE_ARTHUB:
		urlRsp, not_exist_ids, err := getAssetPreviewUrl(ctx, gameCode, depot.ArthubCode, assetPreviewUrlIDs)
		if err != nil {
			log.ErrorContextf(ctx, "getAssetPreviewUrl failed, err: %s", err.Error())
			return metas, nil
		}

		for _, meta := range metas {
			detail := assetID2Detail[meta.GetAssetId()]
			if detail == nil {
				continue
			}
			meta.Formate = detail.Format
			meta.Duration = detail.Duration
			previewUrl := ""
			for _, asset := range urlRsp {
				if strconv.FormatUint(asset.ID, 10) == meta.AssetId {
					previewUrl = asset.PreviewUrl
					break
				}
			}
			// 被删除的素材
			if funk.ContainsString(not_exist_ids, meta.AssetId) {
				meta.AssetStatus = 3
			}
			meta.PreviewUrl = previewUrl
			new_metas = append(new_metas, meta)
		}
	case utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER:
		srv, err := cache.GoogleServiceCache{}.Get(ctx, gameCode)
		if err != nil {
			log.ErrorContextf(ctx, "cache.GoogleServiceCache fail to get google service, gameCode: %v, err: %v", gameCode, err)
			return metas, err
		}
		for _, meta := range metas {
			detail := assetID2Detail[meta.GetAssetId()]
			if detail == nil {
				continue
			}
			previewUrl, err := srv.GetPreviewURL(meta.AssetId)
			if err != nil {
				log.ErrorContextf(ctx, "fail to get google asset preview URL, assetID: %v, err: %v", meta.AssetId, err)
				continue
			}

			meta.Formate = detail.Format
			meta.Duration = detail.Duration
			meta.PreviewUrl = previewUrl
			new_metas = append(new_metas, meta)
		}
	case utils.GAME_DEPOT_TYPE_DROPBOX:
		for _, meta := range metas {
			detail := assetID2Detail[meta.GetAssetId()]
			if detail == nil {
				continue
			}
			meta.Formate = detail.Format
			meta.Duration = detail.Duration
			new_metas = append(new_metas, meta)
		}
	default:
		log.ErrorContextf(ctx, "MediaMaterialByDirectoryIdMetas depot type not supported, depot.Type: %v", depot.Type)
		return metas, fmt.Errorf("depot type not supported")
	}

	return new_metas, nil
}
