package service

import (
	"context"
	"fmt"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data/bigquery"
	"github.com/thoas/go-funk"
)

// SyncRuleLabels 将标签规则结构化到ck外表(arthub_sync.tb_asset_rule_labels)
func SyncRuleLabels(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/SyncRuleLabels")

	// 这里加锁，避免同时去处理
	key := "cron/SyncRuleLabels"
	locked, _ := redis.GetMaterialSyncLock(ctx, key, 2*time.Hour)
	if !locked {
		log.WarningContextf(ctx, "SyncRuleLabels GetLock false")
		return
	}
	// 释放锁
	defer redis.DelMaterialSyncLock(ctx, key)

	start := time.Now()
	gameCodes, err := getGameCodes(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "SyncRuleLabels getGameCodes failed: %s", err)
		return
	}

	for _, gameCode := range gameCodes {
		// 新标签体系规则应用到aix library，这里会覆盖上面的标签，以新的标签规则的标签为准
		newCtx := log.NewSessionIDContext()
		syncGameRuleLabels(newCtx, gameCode)
	}
	log.InfoContextf(ctx, "SyncRuleLabels end, cost: %v", time.Since(start))
}

// syncGameRuleLabels 将标签规则结构化到ck外表(arthub_sync.tb_asset_rule_labels)
func syncGameRuleLabels(ctx context.Context, gameCode string) error {
	ctx = log.SetXAPIPath(ctx, "syncGameRuleLabels")

	// 从标签库查有哪些一二级标签
	labels, err := data.GetGameCreativeLabels(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "syncGameRuleLabels data.GetGameCreativeLabels err:%v", err)
		return err
	}
	existLabelMap := make(map[string]bool)
	for _, label := range labels {
		for _, opt := range label.Options {
			key := fmt.Sprintf("%v_%v", label.Name, opt)
			existLabelMap[key] = true
		}
	}
	firstLabelMap := make(map[string]*model.CreativeLabel)
	for _, label := range labels {
		firstLabelMap[label.Name] = label
	}

	rules, err := repo.GetAllLabelRulesByGameCode(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "syncGameRuleLabels repo.GetAllLabelRulesByGameCode err:%v", err)
		return err
	}

	// 分批处理
	chunks := funk.Chunk(rules, 100).([][]*model.TbAssetLabelRule)
	for _, rules := range chunks {
		// 待删除的行
		var deleteRows []*model.TbAssetRuleLabels
		// 待插入的行
		var insertRows []*model.TbAssetRuleLabels

		for _, rule := range rules {
			delete := &model.TbAssetRuleLabels{
				GameCode: rule.GameCode,
				Rule:     rule.Rule,
				Type:     rule.Type,
			}
			deleteRows = append(deleteRows, delete)

			for _, label := range rule.Labels {
				// 以标签库为基准，有的才同步到外表ck
				key := fmt.Sprintf("%v_%v", label.FirstLabel, label.SecondLabel)
				if existLabelMap[key] {
					insert := &model.TbAssetRuleLabels{
						GameCode:    rule.GameCode,
						Rule:        rule.Rule,
						Type:        rule.Type,
						LabelName:   label.LabelName,
						FirstLabel:  label.FirstLabel,
						SecondLabel: label.SecondLabel,
						CreateTime:  utils.GetNowStr(),
						UpdateTime:  utils.GetNowStr(),
						CreateBy:    "cron",
						UpdateBy:    "cron",
					}
					if first, ok := firstLabelMap[label.FirstLabel]; ok {
						insert.LabelType = first.LabelType
						insert.LabelMethod = first.LabelMethod
					}
					insertRows = append(insertRows, insert)
				}
			}
		}

		err := data.InsertRuleLabelsTransaction(ctx, deleteRows, insertRows)
		if err != nil {
			log.ErrorContextf(ctx, "syncGameRuleLabels data.InsertRuleLabelsTransaction err:%v", err)
			return err
		}

		err = bigquery.SyncRuleLabels(ctx, gameCode, deleteRows, insertRows)
		if err != nil {
			log.ErrorContextf(ctx, "bigquery.SyncRuleLabels err:%v", err)
			return err
		}
	}

	// 全部同步完成后，清理一下旧数据，因为如果标签规则删除了的话， pg表里的数据不会自动删除
	// 删除一天前的数据
	beforeTime := time.Now().AddDate(0, 0, -1)
	err = data.DeleteRuleLabelsBeforTime(ctx, gameCode, beforeTime)
	if err != nil {
		log.ErrorContextf(ctx, "syncGameRuleLabels data.DeleteRuleLabelsBeforTime err:%v", err)
		return err
	}

	// 删除bigquery 里面的旧数据
	err = bigquery.DeleteRuleLabelsBeforTime(ctx, gameCode, beforeTime)
	if err != nil {
		log.ErrorContextf(ctx, "syncGameRuleLabels bigquery.DeleteRuleLabelsBeforTime err:%v", err)
		return err
	}

	return nil
}
