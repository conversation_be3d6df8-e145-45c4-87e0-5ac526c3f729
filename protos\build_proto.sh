#!/bin/bash

if [[ "$1" == "-h" ]]; then
    echo "sh build_proto.sh will build all proto files; sh build_proto.sh file_path will build the target file."
    exit
fi

file_path=$1
if [[ -z "${file_path}" ]]; then
    file_paths=`find . -name *.proto`
else
    file_paths=${file_path}
fi

for file_path in $file_paths; do
    protoc --go_out=. --proto_path=. --proto_path=.. $file_path
    file_name=`basename $file_path`
    file_name_bare="${file_name%.*}"
    dir_name=`dirname $file_path`
    dir_name_bare=${dir_name#*/}
    cp e.coding.intlgame.com/ptc/aix-backend/protos/${dir_name_bare}/${file_name_bare}.pb.go ${dir_name_bare}
done
