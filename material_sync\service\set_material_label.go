package service

import (
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

var GAME_CODE_KEY = "game"

// SetMaterialLabel 设置素材机器标签及封面信息
func SetMaterialLabel(ctx *gin.Context, req *pb.SetMaterialLabelReq, rsp *pb.SetMaterialLabelRsp) error {
	ctx.Set("session_id", uuid.New().String())
	log.DebugContextf(ctx, "SetMaterialLabel req: %v", *req)
	arthubCode := req.GetDepotName()
	if arthubCode == "" {
		log.ErrorContextf(ctx, "error gameCode, gameCode: %v", arthubCode)
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	res := uint32(1)

	// 因为一个arthub_code可能对应多个game_code, 所以需要遍历
	depots, err := data.GetDepotCfg()
	if err != nil {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), err.Error())
	}

	for _, depot := range depots {
		if depot.ArthubCode != arthubCode {
			continue
		}

		gameCode := depot.GameCode
		err := data.SetMaterialLabel(ctx, gameCode, req.GetAlgorithmThumbnailUrl(), req.GetCosUrl(), req.GetFirstLevelLabels(), req.GetSecondLevelLabels(), req.GetAssetId())
		if err != nil {
			log.ErrorContextf(ctx, "error data.SetMaterialLabel assetId:%s, err: %v", req.GetAssetId(), err)
			res = 0
		}
		err = data.SetAssetLabel(ctx, gameCode, req.GetAssetId(), req.GetSecondLevelLabels())
		if err != nil {
			log.ErrorContextf(ctx, "error data.SetAssetLabel assetId:%s, err: %v", req.GetAssetId(), err)
			res = 0
		}
	}
	rsp.SetResList = &pb.SetRes{
		AssetId: req.GetAssetId(),
		SetRes:  res,
	}
	return nil
}
