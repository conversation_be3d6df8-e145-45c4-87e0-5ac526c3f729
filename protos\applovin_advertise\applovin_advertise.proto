syntax = "proto3";

package applovin_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise";

import "aix/aix_common_message.proto";

// POST /api/v1/applovin_advertise/say_hi
message SayHiReq {
    string msg = 1;
}

message SayHiRsp {
    aix.Result result = 1;
    string     msg    = 2;
}

message Campaign {
    string campaign_id   = 1;
    string campaign_name = 2;
    bool   status        = 3;  // campaign状态：true开启， false关闭
}

message CreativeSet {
    string   creative_set_id   = 1;
    string   campaign_id       = 2;  // 创建必填
    string   creative_set_name = 3;  // 创建必填
    repeated string languages  = 4;  // 创建必填
    repeated string countries  = 5;  // 创建选填
    string   product_page      = 6;
}

message Ad {
    int64  ad_id             = 1;
    string name              = 2;
    string creative_set_id   = 3;
    string creative_set_name = 4;
    string size              = 5;
    string template          = 6;
    bool   status            = 7;  // Whether the ad is active (true) or not (false)
}

// 基础结构 渠道账号信息
message MediaAccount {
    string   account_id            = 1;  // 渠道账号id
    string   account_name          = 2;  // 渠道账号名称
    Campaign upload_asset_campaign = 3;  // 不为null表示配置的素材上传的campaign
}

// POST /api/v1/applovin_advertise/get_media_accounts
message GetMediaAccountsReq {
    string game_code = 1;  // 必填 游戏标识game_code
}

message GetMediaAccountsRsp {
    aix.Result result = 1;
    repeated MediaAccount accounts = 3; // 账号信息列表
}

// POST /api/v1/applovin_advertise/get_campaigns
message GetCampaignsReq {
    string game_code   = 1;  // 必填 游戏标识game_code
    string account_id  = 2;  // 必填 渠道账号id
}

message GetCampaignsRsp {
    aix.Result result = 1;
    repeated Campaign campaigns = 2; // 广告campaign信息列表
}

// POST /api/v1/applovin_advertise/get_creative_set_by_campaign
message GetCreativeSetByCampaignReq {
    string game_code   = 1;  // 必填 游戏标识game_code
    string account_id  = 2;  // 必填 渠道账号id
    string campaign_id = 3;  // 必填 广告campaign id
}

message GetCreativeSetByCampaignRsp {
    aix.Result result = 1;
    repeated CreativeSet creative_sets = 2;
}

// POST /api/v1/applovin_advertise/create_creative_set
message CreateCreativeSetReq {
    string game_code   = 1;  // 必填 游戏标识game_code
    string account_id  = 2;  // 必填 渠道账号id
    CreativeSet creative_set = 3;
}

message CreateCreativeSetRsp {
    aix.Result result = 1;
    CreativeSet creative_set = 2;
}


// POST /api/v1/applovin_advertise/get_ads_by_creative_set
message GetAdsByCreativeSetReq {
    string game_code   = 1;  // 必填 游戏标识game_code
    string account_id  = 2;  // 必填 渠道账号id
    string creative_set_id = 3;
}

message GetAdsByCreativeSetRsp {
    aix.Result result = 1;
    repeated   Ad ads = 2;
}

// POST /api/v1/applovin_advertise/change_ad_status
message ChangeAdStatusReq {
    string game_code       = 1;  // 必填 游戏标识game_code
    string account_id      = 2;  // 必填 渠道账号id
    int64  ad_id           = 3;  // 必填 广告id
    string creative_set_id = 4;  // 必填 creative_set_id
    bool   status          = 5;  // 必填 广告状态 active (true) or not (false)
}

message ChangeAdStatusRsp {
    aix.Result result = 1;
}