package service

import "testing"

func TestGetCampaignType(t *testing.T) {
	samples := []struct {
		CampaignName string
		CampaignType string
	}{
		{"Google-RU-AND-211029-UAC1.0-textonly-newinstall-EUR", "other_types"},
		{"Google-DZ-AND-210321-UAC2.5-purchase-newinstall-MENA", "purchase"},
		{"Google-DZ-AND-210321-UAC2.5-install-newinstall-MENA", "install"},
		{"Google-DZ-AND-210321-UAC2.5-login-newinstall-MENA", "login"},
		{"Google-DZ-AND-210321-UAC2.5-testLogin123-newinstall-MENA", "login"},
		{"Google-PK-AND-210602-UAC2.5-high_act-newinstall-IN", "high_act"},
		{"Google-DZ-AND-210321-UAC2.5-high_test-newinstall-MENA", "high_act"},
	}
	for _, sample := range samples {
		campaign_type := getCampaignType(sample.CampaignName)
		if campaign_type != sample.CampaignType {
			t.<PERSON><PERSON><PERSON>("get wrong sample, campaign name: %s, expect campaign type: %s, get: %s", sample.CampaignName, sample.CampaignType, campaign_type)
		}
	}
}
