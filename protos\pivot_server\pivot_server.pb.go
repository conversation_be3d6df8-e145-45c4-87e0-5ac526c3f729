// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.19.1
// source: pivot_server/pivot_server.proto

package pivot_server

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// where
type AnalysisPivotWhere struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                        // 字段名
	Upper        float64  `protobuf:"fixed64,2,opt,name=upper,proto3" json:"upper,omitempty"`                                    // 最大值，type为2时适用
	Lower        float64  `protobuf:"fixed64,3,opt,name=lower,proto3" json:"lower,omitempty"`                                    // 最小值，type为2时适用
	InList       []string `protobuf:"bytes,4,rep,name=in_list,json=inList,proto3" json:"in_list,omitempty"`                      // 列表值，type为1时适用, string类型
	InListType   int32    `protobuf:"varint,5,opt,name=in_list_type,json=inListType,proto3" json:"in_list_type,omitempty"`       // 列表值类型, 1表示字符串, 2表示整型, 3表示浮点型
	Like         string   `protobuf:"bytes,6,opt,name=like,proto3" json:"like,omitempty"`                                        // 模糊匹配值，type为3时适用
	Type         int32    `protobuf:"varint,7,opt,name=type,proto3" json:"type,omitempty"`                                       // 1-in多个值任一匹配 2-where的range范围内查找 3-like模糊匹配 4-having的range范围查询,5-not in多个值任一匹配
	IsUpperEqual int32    `protobuf:"varint,8,opt,name=is_upper_equal,json=isUpperEqual,proto3" json:"is_upper_equal,omitempty"` // 最大值是否可等于，-1没有上限 0不等于 1等于，type为2时适用
	IsLowerEqual int32    `protobuf:"varint,9,opt,name=is_lower_equal,json=isLowerEqual,proto3" json:"is_lower_equal,omitempty"` // 最小值是否可等于，-1没有下限 0不等于 1等于，type为2时适用
	DataType     int32    `protobuf:"varint,10,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"`              // 筛选字段类型, 1表示字符串, 2表示整型, 3表示浮点型
}

func (x *AnalysisPivotWhere) Reset() {
	*x = AnalysisPivotWhere{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisPivotWhere) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisPivotWhere) ProtoMessage() {}

func (x *AnalysisPivotWhere) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisPivotWhere.ProtoReflect.Descriptor instead.
func (*AnalysisPivotWhere) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{0}
}

func (x *AnalysisPivotWhere) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AnalysisPivotWhere) GetUpper() float64 {
	if x != nil {
		return x.Upper
	}
	return 0
}

func (x *AnalysisPivotWhere) GetLower() float64 {
	if x != nil {
		return x.Lower
	}
	return 0
}

func (x *AnalysisPivotWhere) GetInList() []string {
	if x != nil {
		return x.InList
	}
	return nil
}

func (x *AnalysisPivotWhere) GetInListType() int32 {
	if x != nil {
		return x.InListType
	}
	return 0
}

func (x *AnalysisPivotWhere) GetLike() string {
	if x != nil {
		return x.Like
	}
	return ""
}

func (x *AnalysisPivotWhere) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AnalysisPivotWhere) GetIsUpperEqual() int32 {
	if x != nil {
		return x.IsUpperEqual
	}
	return 0
}

func (x *AnalysisPivotWhere) GetIsLowerEqual() int32 {
	if x != nil {
		return x.IsLowerEqual
	}
	return 0
}

func (x *AnalysisPivotWhere) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

// group
type AnalysisPivotGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CountryCode string `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"` //国家代码
	ShowNetwork string `protobuf:"bytes,2,opt,name=show_network,json=showNetwork,proto3" json:"show_network,omitempty"` //渠道
	AssetName   string `protobuf:"bytes,3,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`       //素材名称
	GameCode    string `protobuf:"bytes,4,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`          //游戏代码
}

func (x *AnalysisPivotGroup) Reset() {
	*x = AnalysisPivotGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisPivotGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisPivotGroup) ProtoMessage() {}

func (x *AnalysisPivotGroup) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisPivotGroup.ProtoReflect.Descriptor instead.
func (*AnalysisPivotGroup) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{1}
}

func (x *AnalysisPivotGroup) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *AnalysisPivotGroup) GetShowNetwork() string {
	if x != nil {
		return x.ShowNetwork
	}
	return ""
}

func (x *AnalysisPivotGroup) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *AnalysisPivotGroup) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

type OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	By    string `protobuf:"bytes,1,opt,name=by,proto3" json:"by,omitempty"`       // 字段名
	Order string `protobuf:"bytes,2,opt,name=order,proto3" json:"order,omitempty"` // 'desc' 'asc'
}

func (x *OrderBy) Reset() {
	*x = OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderBy) ProtoMessage() {}

func (x *OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderBy.ProtoReflect.Descriptor instead.
func (*OrderBy) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{2}
}

func (x *OrderBy) GetBy() string {
	if x != nil {
		return x.By
	}
	return ""
}

func (x *OrderBy) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

// analysis_pivot列表请求, POST, /api/v1/pivot_server/analysis_pivot_list
type AnalysisPivotListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group      []string              `protobuf:"bytes,1,rep,name=group,proto3" json:"group,omitempty"`                                // 聚合字段
	Where      []*AnalysisPivotWhere `protobuf:"bytes,2,rep,name=where,proto3" json:"where,omitempty"`                                // 筛选条件
	PageIndex  uint32                `protobuf:"varint,3,opt,name=pageIndex,proto3" json:"pageIndex,omitempty"`                       // 起始偏移
	PageSize   uint32                `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`                         // 拉取数量
	Metric     []string              `protobuf:"bytes,5,rep,name=metric,proto3" json:"metric,omitempty"`                              // 需要的字段
	Orderby    []*OrderBy            `protobuf:"bytes,6,rep,name=orderby,proto3" json:"orderby,omitempty"`                            // order by
	NeedAggNum int32                 `protobuf:"varint,7,opt,name=need_agg_num,json=needAggNum,proto3" json:"need_agg_num,omitempty"` // 是否需要聚合数量, 0-否, 1-是
}

func (x *AnalysisPivotListReq) Reset() {
	*x = AnalysisPivotListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisPivotListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisPivotListReq) ProtoMessage() {}

func (x *AnalysisPivotListReq) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisPivotListReq.ProtoReflect.Descriptor instead.
func (*AnalysisPivotListReq) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{3}
}

func (x *AnalysisPivotListReq) GetGroup() []string {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *AnalysisPivotListReq) GetWhere() []*AnalysisPivotWhere {
	if x != nil {
		return x.Where
	}
	return nil
}

func (x *AnalysisPivotListReq) GetPageIndex() uint32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *AnalysisPivotListReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AnalysisPivotListReq) GetMetric() []string {
	if x != nil {
		return x.Metric
	}
	return nil
}

func (x *AnalysisPivotListReq) GetOrderby() []*OrderBy {
	if x != nil {
		return x.Orderby
	}
	return nil
}

func (x *AnalysisPivotListReq) GetNeedAggNum() int32 {
	if x != nil {
		return x.NeedAggNum
	}
	return 0
}

// analysis_pivot元数据
type AnalysisPivotMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 需要重新计算的字段
	Ctr              float64 `protobuf:"fixed64,1,opt,name=ctr,proto3" json:"ctr,omitempty"`
	Cvr              float64 `protobuf:"fixed64,2,opt,name=cvr,proto3" json:"cvr,omitempty"`
	Ipm              float64 `protobuf:"fixed64,3,opt,name=ipm,proto3" json:"ipm,omitempty"`
	Cpi              float64 `protobuf:"fixed64,4,opt,name=cpi,proto3" json:"cpi,omitempty"`
	DailyImpressions float64 `protobuf:"fixed64,5,opt,name=daily_impressions,json=dailyImpressions,proto3" json:"daily_impressions,omitempty"`
	ImpressionChange float64 `protobuf:"fixed64,6,opt,name=impression_change,json=impressionChange,proto3" json:"impression_change,omitempty"`
	ClickChange      float64 `protobuf:"fixed64,7,opt,name=click_change,json=clickChange,proto3" json:"click_change,omitempty"`
	// clickhouse表中字段
	GameCode          string  `protobuf:"bytes,8,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Dtstatdate        int64   `protobuf:"varint,9,opt,name=dtstatdate,proto3" json:"dtstatdate,omitempty"`
	MccId             string  `protobuf:"bytes,10,opt,name=mcc_id,json=mccId,proto3" json:"mcc_id,omitempty"`
	AccountId         string  `protobuf:"bytes,11,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	AssetId           string  `protobuf:"bytes,12,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetName         string  `protobuf:"bytes,13,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	AssetStatus       string  `protobuf:"bytes,14,opt,name=asset_status,json=assetStatus,proto3" json:"asset_status,omitempty"`
	AssetSerialId     string  `protobuf:"bytes,15,opt,name=asset_serial_id,json=assetSerialId,proto3" json:"asset_serial_id,omitempty"`
	AssetProject      string  `protobuf:"bytes,16,opt,name=asset_project,json=assetProject,proto3" json:"asset_project,omitempty"`
	AssetFormat       string  `protobuf:"bytes,17,opt,name=asset_format,json=assetFormat,proto3" json:"asset_format,omitempty"`
	AssetSize         string  `protobuf:"bytes,18,opt,name=asset_size,json=assetSize,proto3" json:"asset_size,omitempty"`
	AssetLanguage     string  `protobuf:"bytes,19,opt,name=asset_language,json=assetLanguage,proto3" json:"asset_language,omitempty"`
	AssetOrganization string  `protobuf:"bytes,20,opt,name=asset_organization,json=assetOrganization,proto3" json:"asset_organization,omitempty"`
	AssetCustomName   string  `protobuf:"bytes,21,opt,name=asset_custom_name,json=assetCustomName,proto3" json:"asset_custom_name,omitempty"`
	AssetPlay         string  `protobuf:"bytes,22,opt,name=asset_play,json=assetPlay,proto3" json:"asset_play,omitempty"`
	AssetPerform      string  `protobuf:"bytes,23,opt,name=asset_perform,json=assetPerform,proto3" json:"asset_perform,omitempty"`
	AssetStage        string  `protobuf:"bytes,24,opt,name=asset_stage,json=assetStage,proto3" json:"asset_stage,omitempty"`
	AssetDeliveryDate string  `protobuf:"bytes,25,opt,name=asset_delivery_date,json=assetDeliveryDate,proto3" json:"asset_delivery_date,omitempty"`
	CreativeId        string  `protobuf:"bytes,26,opt,name=creative_id,json=creativeId,proto3" json:"creative_id,omitempty"`
	CreativeName      string  `protobuf:"bytes,27,opt,name=creative_name,json=creativeName,proto3" json:"creative_name,omitempty"`
	CreativeStatus    string  `protobuf:"bytes,28,opt,name=creative_status,json=creativeStatus,proto3" json:"creative_status,omitempty"`
	AdId              string  `protobuf:"bytes,29,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	AdName            string  `protobuf:"bytes,30,opt,name=ad_name,json=adName,proto3" json:"ad_name,omitempty"`
	AdStatus          string  `protobuf:"bytes,31,opt,name=ad_status,json=adStatus,proto3" json:"ad_status,omitempty"`
	AdgroupId         string  `protobuf:"bytes,32,opt,name=adgroup_id,json=adgroupId,proto3" json:"adgroup_id,omitempty"`
	AdgroupName       string  `protobuf:"bytes,33,opt,name=adgroup_name,json=adgroupName,proto3" json:"adgroup_name,omitempty"`
	AdgroupStatus     string  `protobuf:"bytes,34,opt,name=adgroup_status,json=adgroupStatus,proto3" json:"adgroup_status,omitempty"`
	CampaignId        string  `protobuf:"bytes,35,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	CampaignName      string  `protobuf:"bytes,36,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`
	CampaignStatus    string  `protobuf:"bytes,37,opt,name=campaign_status,json=campaignStatus,proto3" json:"campaign_status,omitempty"`
	CampaignType      string  `protobuf:"bytes,38,opt,name=campaign_type,json=campaignType,proto3" json:"campaign_type,omitempty"`
	ParseNetwork      string  `protobuf:"bytes,39,opt,name=parse_network,json=parseNetwork,proto3" json:"parse_network,omitempty"`
	ParseRegion       string  `protobuf:"bytes,40,opt,name=parse_region,json=parseRegion,proto3" json:"parse_region,omitempty"`
	ParsePlatform     string  `protobuf:"bytes,41,opt,name=parse_platform,json=parsePlatform,proto3" json:"parse_platform,omitempty"`
	ParseDate         string  `protobuf:"bytes,42,opt,name=parse_date,json=parseDate,proto3" json:"parse_date,omitempty"`
	ParseCampaignGoal string  `protobuf:"bytes,43,opt,name=parse_campaign_goal,json=parseCampaignGoal,proto3" json:"parse_campaign_goal,omitempty"`
	ParseCustomField  string  `protobuf:"bytes,44,opt,name=parse_custom_field,json=parseCustomField,proto3" json:"parse_custom_field,omitempty"`
	ParseCostType     string  `protobuf:"bytes,45,opt,name=parse_cost_type,json=parseCostType,proto3" json:"parse_cost_type,omitempty"`
	ParseCostRegion   string  `protobuf:"bytes,46,opt,name=parse_cost_region,json=parseCostRegion,proto3" json:"parse_cost_region,omitempty"`
	OnlineDate        string  `protobuf:"bytes,47,opt,name=online_date,json=onlineDate,proto3" json:"online_date,omitempty"`
	OfflineDate       string  `protobuf:"bytes,48,opt,name=offline_date,json=offlineDate,proto3" json:"offline_date,omitempty"`
	OnlineDays        int64   `protobuf:"varint,49,opt,name=online_days,json=onlineDays,proto3" json:"online_days,omitempty"`
	CountryCode       string  `protobuf:"bytes,50,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	CountryNameCh     string  `protobuf:"bytes,51,opt,name=country_name_ch,json=countryNameCh,proto3" json:"country_name_ch,omitempty"`
	CountryNameEn     string  `protobuf:"bytes,52,opt,name=country_name_en,json=countryNameEn,proto3" json:"country_name_en,omitempty"`
	UaRegion          string  `protobuf:"bytes,53,opt,name=ua_region,json=uaRegion,proto3" json:"ua_region,omitempty"`
	Language          string  `protobuf:"bytes,54,opt,name=language,proto3" json:"language,omitempty"`
	Network           string  `protobuf:"bytes,55,opt,name=network,proto3" json:"network,omitempty"`
	AssetUrl          string  `protobuf:"bytes,56,opt,name=asset_url,json=assetUrl,proto3" json:"asset_url,omitempty"`
	StartDate         string  `protobuf:"bytes,57,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate           string  `protobuf:"bytes,58,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Impressions       float64 `protobuf:"fixed64,59,opt,name=impressions,proto3" json:"impressions,omitempty"`
	Clicks            float64 `protobuf:"fixed64,60,opt,name=clicks,proto3" json:"clicks,omitempty"`
	Conversions       float64 `protobuf:"fixed64,61,opt,name=conversions,proto3" json:"conversions,omitempty"`
	Registers         float64 `protobuf:"fixed64,62,opt,name=registers,proto3" json:"registers,omitempty"`
	Installs          float64 `protobuf:"fixed64,63,opt,name=installs,proto3" json:"installs,omitempty"`
	Spend             float64 `protobuf:"fixed64,64,opt,name=spend,proto3" json:"spend,omitempty"`
	MachineLables     string  `protobuf:"bytes,65,opt,name=machine_lables,json=machineLables,proto3" json:"machine_lables,omitempty"`
	HumanLables       string  `protobuf:"bytes,66,opt,name=human_lables,json=humanLables,proto3" json:"human_lables,omitempty"`
	AssetType         string  `protobuf:"bytes,67,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	YoutubeId         string  `protobuf:"bytes,68,opt,name=youtube_id,json=youtubeId,proto3" json:"youtube_id,omitempty"`
	WatchTime         float64 `protobuf:"fixed64,69,opt,name=watch_time,json=watchTime,proto3" json:"watch_time,omitempty"`
	VideoPlayed_25    float64 `protobuf:"fixed64,70,opt,name=video_played_25,json=videoPlayed25,proto3" json:"video_played_25,omitempty"`
	VideoPlayed_50    float64 `protobuf:"fixed64,71,opt,name=video_played_50,json=videoPlayed50,proto3" json:"video_played_50,omitempty"`
	VideoPlayed_75    float64 `protobuf:"fixed64,72,opt,name=video_played_75,json=videoPlayed75,proto3" json:"video_played_75,omitempty"`
	VideoPlayed_100   float64 `protobuf:"fixed64,73,opt,name=video_played_100,json=videoPlayed100,proto3" json:"video_played_100,omitempty"`
	VideoDuration     float64 `protobuf:"fixed64,74,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration,omitempty"`
	NumOfAds          float64 `protobuf:"fixed64,75,opt,name=num_of_ads,json=numOfAds,proto3" json:"num_of_ads,omitempty"`
	D1PayUsers        float64 `protobuf:"fixed64,76,opt,name=d1_pay_users,json=d1PayUsers,proto3" json:"d1_pay_users,omitempty"`
	D2PayUsers        float64 `protobuf:"fixed64,77,opt,name=d2_pay_users,json=d2PayUsers,proto3" json:"d2_pay_users,omitempty"`
	D3PayUsers        float64 `protobuf:"fixed64,78,opt,name=d3_pay_users,json=d3PayUsers,proto3" json:"d3_pay_users,omitempty"`
	D7PayUsers        float64 `protobuf:"fixed64,79,opt,name=d7_pay_users,json=d7PayUsers,proto3" json:"d7_pay_users,omitempty"`
	D1PayRate         float64 `protobuf:"fixed64,80,opt,name=d1_pay_rate,json=d1PayRate,proto3" json:"d1_pay_rate,omitempty"`
	D2PayRate         float64 `protobuf:"fixed64,81,opt,name=d2_pay_rate,json=d2PayRate,proto3" json:"d2_pay_rate,omitempty"`
	D3PayRate         float64 `protobuf:"fixed64,82,opt,name=d3_pay_rate,json=d3PayRate,proto3" json:"d3_pay_rate,omitempty"`
	D7PayRate         float64 `protobuf:"fixed64,83,opt,name=d7_pay_rate,json=d7PayRate,proto3" json:"d7_pay_rate,omitempty"`
	D1PayAmount       float64 `protobuf:"fixed64,84,opt,name=d1_pay_amount,json=d1PayAmount,proto3" json:"d1_pay_amount,omitempty"`
	D2PayAmount       float64 `protobuf:"fixed64,85,opt,name=d2_pay_amount,json=d2PayAmount,proto3" json:"d2_pay_amount,omitempty"`
	D3PayAmount       float64 `protobuf:"fixed64,86,opt,name=d3_pay_amount,json=d3PayAmount,proto3" json:"d3_pay_amount,omitempty"`
	D7PayAmount       float64 `protobuf:"fixed64,87,opt,name=d7_pay_amount,json=d7PayAmount,proto3" json:"d7_pay_amount,omitempty"`
	D1Roas            float64 `protobuf:"fixed64,88,opt,name=d1_roas,json=d1Roas,proto3" json:"d1_roas,omitempty"`
	D2Roas            float64 `protobuf:"fixed64,89,opt,name=d2_roas,json=d2Roas,proto3" json:"d2_roas,omitempty"`
	D3Roas            float64 `protobuf:"fixed64,90,opt,name=d3_roas,json=d3Roas,proto3" json:"d3_roas,omitempty"`
	D7Roas            float64 `protobuf:"fixed64,91,opt,name=d7_roas,json=d7Roas,proto3" json:"d7_roas,omitempty"`
	D1Ltv             float64 `protobuf:"fixed64,92,opt,name=d1_ltv,json=d1Ltv,proto3" json:"d1_ltv,omitempty"`
	D2Ltv             float64 `protobuf:"fixed64,93,opt,name=d2_ltv,json=d2Ltv,proto3" json:"d2_ltv,omitempty"`
	D3Ltv             float64 `protobuf:"fixed64,94,opt,name=d3_ltv,json=d3Ltv,proto3" json:"d3_ltv,omitempty"`
	D7Ltv             float64 `protobuf:"fixed64,95,opt,name=d7_ltv,json=d7Ltv,proto3" json:"d7_ltv,omitempty"`
	R1                float64 `protobuf:"fixed64,96,opt,name=r1,proto3" json:"r1,omitempty"`
	R2                float64 `protobuf:"fixed64,97,opt,name=r2,proto3" json:"r2,omitempty"`
	R3                float64 `protobuf:"fixed64,98,opt,name=r3,proto3" json:"r3,omitempty"`
	R7                float64 `protobuf:"fixed64,99,opt,name=r7,proto3" json:"r7,omitempty"`
	AggNum            int64   `protobuf:"varint,100,opt,name=agg_num,json=aggNum,proto3" json:"agg_num,omitempty"` // 聚合数量
}

func (x *AnalysisPivotMeta) Reset() {
	*x = AnalysisPivotMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisPivotMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisPivotMeta) ProtoMessage() {}

func (x *AnalysisPivotMeta) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisPivotMeta.ProtoReflect.Descriptor instead.
func (*AnalysisPivotMeta) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{4}
}

func (x *AnalysisPivotMeta) GetCtr() float64 {
	if x != nil {
		return x.Ctr
	}
	return 0
}

func (x *AnalysisPivotMeta) GetCvr() float64 {
	if x != nil {
		return x.Cvr
	}
	return 0
}

func (x *AnalysisPivotMeta) GetIpm() float64 {
	if x != nil {
		return x.Ipm
	}
	return 0
}

func (x *AnalysisPivotMeta) GetCpi() float64 {
	if x != nil {
		return x.Cpi
	}
	return 0
}

func (x *AnalysisPivotMeta) GetDailyImpressions() float64 {
	if x != nil {
		return x.DailyImpressions
	}
	return 0
}

func (x *AnalysisPivotMeta) GetImpressionChange() float64 {
	if x != nil {
		return x.ImpressionChange
	}
	return 0
}

func (x *AnalysisPivotMeta) GetClickChange() float64 {
	if x != nil {
		return x.ClickChange
	}
	return 0
}

func (x *AnalysisPivotMeta) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *AnalysisPivotMeta) GetDtstatdate() int64 {
	if x != nil {
		return x.Dtstatdate
	}
	return 0
}

func (x *AnalysisPivotMeta) GetMccId() string {
	if x != nil {
		return x.MccId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetStatus() string {
	if x != nil {
		return x.AssetStatus
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetSerialId() string {
	if x != nil {
		return x.AssetSerialId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetProject() string {
	if x != nil {
		return x.AssetProject
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetFormat() string {
	if x != nil {
		return x.AssetFormat
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetSize() string {
	if x != nil {
		return x.AssetSize
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetLanguage() string {
	if x != nil {
		return x.AssetLanguage
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetOrganization() string {
	if x != nil {
		return x.AssetOrganization
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetCustomName() string {
	if x != nil {
		return x.AssetCustomName
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetPlay() string {
	if x != nil {
		return x.AssetPlay
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetPerform() string {
	if x != nil {
		return x.AssetPerform
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetStage() string {
	if x != nil {
		return x.AssetStage
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetDeliveryDate() string {
	if x != nil {
		return x.AssetDeliveryDate
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCreativeId() string {
	if x != nil {
		return x.CreativeId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCreativeName() string {
	if x != nil {
		return x.CreativeName
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCreativeStatus() string {
	if x != nil {
		return x.CreativeStatus
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAdName() string {
	if x != nil {
		return x.AdName
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAdStatus() string {
	if x != nil {
		return x.AdStatus
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAdgroupId() string {
	if x != nil {
		return x.AdgroupId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAdgroupName() string {
	if x != nil {
		return x.AdgroupName
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAdgroupStatus() string {
	if x != nil {
		return x.AdgroupStatus
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCampaignStatus() string {
	if x != nil {
		return x.CampaignStatus
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCampaignType() string {
	if x != nil {
		return x.CampaignType
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParseNetwork() string {
	if x != nil {
		return x.ParseNetwork
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParseRegion() string {
	if x != nil {
		return x.ParseRegion
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParsePlatform() string {
	if x != nil {
		return x.ParsePlatform
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParseDate() string {
	if x != nil {
		return x.ParseDate
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParseCampaignGoal() string {
	if x != nil {
		return x.ParseCampaignGoal
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParseCustomField() string {
	if x != nil {
		return x.ParseCustomField
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParseCostType() string {
	if x != nil {
		return x.ParseCostType
	}
	return ""
}

func (x *AnalysisPivotMeta) GetParseCostRegion() string {
	if x != nil {
		return x.ParseCostRegion
	}
	return ""
}

func (x *AnalysisPivotMeta) GetOnlineDate() string {
	if x != nil {
		return x.OnlineDate
	}
	return ""
}

func (x *AnalysisPivotMeta) GetOfflineDate() string {
	if x != nil {
		return x.OfflineDate
	}
	return ""
}

func (x *AnalysisPivotMeta) GetOnlineDays() int64 {
	if x != nil {
		return x.OnlineDays
	}
	return 0
}

func (x *AnalysisPivotMeta) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCountryNameCh() string {
	if x != nil {
		return x.CountryNameCh
	}
	return ""
}

func (x *AnalysisPivotMeta) GetCountryNameEn() string {
	if x != nil {
		return x.CountryNameEn
	}
	return ""
}

func (x *AnalysisPivotMeta) GetUaRegion() string {
	if x != nil {
		return x.UaRegion
	}
	return ""
}

func (x *AnalysisPivotMeta) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *AnalysisPivotMeta) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetUrl() string {
	if x != nil {
		return x.AssetUrl
	}
	return ""
}

func (x *AnalysisPivotMeta) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *AnalysisPivotMeta) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *AnalysisPivotMeta) GetImpressions() float64 {
	if x != nil {
		return x.Impressions
	}
	return 0
}

func (x *AnalysisPivotMeta) GetClicks() float64 {
	if x != nil {
		return x.Clicks
	}
	return 0
}

func (x *AnalysisPivotMeta) GetConversions() float64 {
	if x != nil {
		return x.Conversions
	}
	return 0
}

func (x *AnalysisPivotMeta) GetRegisters() float64 {
	if x != nil {
		return x.Registers
	}
	return 0
}

func (x *AnalysisPivotMeta) GetInstalls() float64 {
	if x != nil {
		return x.Installs
	}
	return 0
}

func (x *AnalysisPivotMeta) GetSpend() float64 {
	if x != nil {
		return x.Spend
	}
	return 0
}

func (x *AnalysisPivotMeta) GetMachineLables() string {
	if x != nil {
		return x.MachineLables
	}
	return ""
}

func (x *AnalysisPivotMeta) GetHumanLables() string {
	if x != nil {
		return x.HumanLables
	}
	return ""
}

func (x *AnalysisPivotMeta) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *AnalysisPivotMeta) GetYoutubeId() string {
	if x != nil {
		return x.YoutubeId
	}
	return ""
}

func (x *AnalysisPivotMeta) GetWatchTime() float64 {
	if x != nil {
		return x.WatchTime
	}
	return 0
}

func (x *AnalysisPivotMeta) GetVideoPlayed_25() float64 {
	if x != nil {
		return x.VideoPlayed_25
	}
	return 0
}

func (x *AnalysisPivotMeta) GetVideoPlayed_50() float64 {
	if x != nil {
		return x.VideoPlayed_50
	}
	return 0
}

func (x *AnalysisPivotMeta) GetVideoPlayed_75() float64 {
	if x != nil {
		return x.VideoPlayed_75
	}
	return 0
}

func (x *AnalysisPivotMeta) GetVideoPlayed_100() float64 {
	if x != nil {
		return x.VideoPlayed_100
	}
	return 0
}

func (x *AnalysisPivotMeta) GetVideoDuration() float64 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *AnalysisPivotMeta) GetNumOfAds() float64 {
	if x != nil {
		return x.NumOfAds
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD1PayUsers() float64 {
	if x != nil {
		return x.D1PayUsers
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD2PayUsers() float64 {
	if x != nil {
		return x.D2PayUsers
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD3PayUsers() float64 {
	if x != nil {
		return x.D3PayUsers
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD7PayUsers() float64 {
	if x != nil {
		return x.D7PayUsers
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD1PayRate() float64 {
	if x != nil {
		return x.D1PayRate
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD2PayRate() float64 {
	if x != nil {
		return x.D2PayRate
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD3PayRate() float64 {
	if x != nil {
		return x.D3PayRate
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD7PayRate() float64 {
	if x != nil {
		return x.D7PayRate
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD1PayAmount() float64 {
	if x != nil {
		return x.D1PayAmount
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD2PayAmount() float64 {
	if x != nil {
		return x.D2PayAmount
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD3PayAmount() float64 {
	if x != nil {
		return x.D3PayAmount
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD7PayAmount() float64 {
	if x != nil {
		return x.D7PayAmount
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD1Roas() float64 {
	if x != nil {
		return x.D1Roas
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD2Roas() float64 {
	if x != nil {
		return x.D2Roas
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD3Roas() float64 {
	if x != nil {
		return x.D3Roas
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD7Roas() float64 {
	if x != nil {
		return x.D7Roas
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD1Ltv() float64 {
	if x != nil {
		return x.D1Ltv
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD2Ltv() float64 {
	if x != nil {
		return x.D2Ltv
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD3Ltv() float64 {
	if x != nil {
		return x.D3Ltv
	}
	return 0
}

func (x *AnalysisPivotMeta) GetD7Ltv() float64 {
	if x != nil {
		return x.D7Ltv
	}
	return 0
}

func (x *AnalysisPivotMeta) GetR1() float64 {
	if x != nil {
		return x.R1
	}
	return 0
}

func (x *AnalysisPivotMeta) GetR2() float64 {
	if x != nil {
		return x.R2
	}
	return 0
}

func (x *AnalysisPivotMeta) GetR3() float64 {
	if x != nil {
		return x.R3
	}
	return 0
}

func (x *AnalysisPivotMeta) GetR7() float64 {
	if x != nil {
		return x.R7
	}
	return 0
}

func (x *AnalysisPivotMeta) GetAggNum() int64 {
	if x != nil {
		return x.AggNum
	}
	return 0
}

type AnalysisPivotListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result          `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Pivots []*AnalysisPivotMeta `protobuf:"bytes,2,rep,name=pivots,proto3" json:"pivots,omitempty"` // 列表
	Total  uint32               `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`  // 总数
}

func (x *AnalysisPivotListRsp) Reset() {
	*x = AnalysisPivotListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisPivotListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisPivotListRsp) ProtoMessage() {}

func (x *AnalysisPivotListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisPivotListRsp.ProtoReflect.Descriptor instead.
func (*AnalysisPivotListRsp) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{5}
}

func (x *AnalysisPivotListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AnalysisPivotListRsp) GetPivots() []*AnalysisPivotMeta {
	if x != nil {
		return x.Pivots
	}
	return nil
}

func (x *AnalysisPivotListRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取creative pivot表筛选项信息, POST, /api/v1/pivot_server/get_filter_info
type GetFilterInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetFilterInfoReq) Reset() {
	*x = GetFilterInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFilterInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFilterInfoReq) ProtoMessage() {}

func (x *GetFilterInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFilterInfoReq.ProtoReflect.Descriptor instead.
func (*GetFilterInfoReq) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{6}
}

type GetFilterInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result     *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`          // 返回结果
	Dtstatdate int64       `protobuf:"varint,2,opt,name=dtstatdate,proto3" json:"dtstatdate,omitempty"` // 最新数据时间
}

func (x *GetFilterInfoRsp) Reset() {
	*x = GetFilterInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFilterInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFilterInfoRsp) ProtoMessage() {}

func (x *GetFilterInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFilterInfoRsp.ProtoReflect.Descriptor instead.
func (*GetFilterInfoRsp) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{7}
}

func (x *GetFilterInfoRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetFilterInfoRsp) GetDtstatdate() int64 {
	if x != nil {
		return x.Dtstatdate
	}
	return 0
}

// 插入mock数据, POST, /api/v1/pivot_server/insert_mock
type InsertMockReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number int32 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"` // 插入mock数据数量
}

func (x *InsertMockReq) Reset() {
	*x = InsertMockReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsertMockReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertMockReq) ProtoMessage() {}

func (x *InsertMockReq) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertMockReq.ProtoReflect.Descriptor instead.
func (*InsertMockReq) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{8}
}

func (x *InsertMockReq) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

// 插入mock数据-返回结构
type InsertMockRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *InsertMockRsp) Reset() {
	*x = InsertMockRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsertMockRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertMockRsp) ProtoMessage() {}

func (x *InsertMockRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertMockRsp.ProtoReflect.Descriptor instead.
func (*InsertMockRsp) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{9}
}

func (x *InsertMockRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 测试语句
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{10}
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{11}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 请求google实时统计数据信息, POST, /api/v1/pivot_server/query_google_realtime_asset_info
type QueryGoogleRealtimeAssetInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group      []string              `protobuf:"bytes,1,rep,name=group,proto3" json:"group,omitempty"`                                // 聚合字段
	Where      []*AnalysisPivotWhere `protobuf:"bytes,2,rep,name=where,proto3" json:"where,omitempty"`                                // 筛选条件
	PageIndex  uint32                `protobuf:"varint,3,opt,name=pageIndex,proto3" json:"pageIndex,omitempty"`                       // 起始偏移
	PageSize   uint32                `protobuf:"varint,4,opt,name=pageSize,proto3" json:"pageSize,omitempty"`                         // 拉取数量
	Metric     []string              `protobuf:"bytes,5,rep,name=metric,proto3" json:"metric,omitempty"`                              // 需要的字段
	Orderby    []*OrderBy            `protobuf:"bytes,6,rep,name=orderby,proto3" json:"orderby,omitempty"`                            // order by
	NeedAggNum int32                 `protobuf:"varint,7,opt,name=need_agg_num,json=needAggNum,proto3" json:"need_agg_num,omitempty"` // 是否需要聚合数量, 0-否, 1-是
}

func (x *QueryGoogleRealtimeAssetInfoReq) Reset() {
	*x = QueryGoogleRealtimeAssetInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGoogleRealtimeAssetInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGoogleRealtimeAssetInfoReq) ProtoMessage() {}

func (x *QueryGoogleRealtimeAssetInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGoogleRealtimeAssetInfoReq.ProtoReflect.Descriptor instead.
func (*QueryGoogleRealtimeAssetInfoReq) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{12}
}

func (x *QueryGoogleRealtimeAssetInfoReq) GetGroup() []string {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *QueryGoogleRealtimeAssetInfoReq) GetWhere() []*AnalysisPivotWhere {
	if x != nil {
		return x.Where
	}
	return nil
}

func (x *QueryGoogleRealtimeAssetInfoReq) GetPageIndex() uint32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *QueryGoogleRealtimeAssetInfoReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *QueryGoogleRealtimeAssetInfoReq) GetMetric() []string {
	if x != nil {
		return x.Metric
	}
	return nil
}

func (x *QueryGoogleRealtimeAssetInfoReq) GetOrderby() []*OrderBy {
	if x != nil {
		return x.Orderby
	}
	return nil
}

func (x *QueryGoogleRealtimeAssetInfoReq) GetNeedAggNum() int32 {
	if x != nil {
		return x.NeedAggNum
	}
	return 0
}

// google实时统计数据结构体
type GoogleRealtimeAssetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AggNum int64 `protobuf:"varint,1,opt,name=agg_num,json=aggNum,proto3" json:"agg_num,omitempty"`
	// 需要重新计算的字段
	Ctr                               float64 `protobuf:"fixed64,2,opt,name=ctr,proto3" json:"ctr,omitempty"`
	Cvr                               float64 `protobuf:"fixed64,3,opt,name=cvr,proto3" json:"cvr,omitempty"`
	Ipm                               float64 `protobuf:"fixed64,4,opt,name=ipm,proto3" json:"ipm,omitempty"`
	Cpi                               float64 `protobuf:"fixed64,5,opt,name=cpi,proto3" json:"cpi,omitempty"`
	DailyImpressions                  float64 `protobuf:"fixed64,6,opt,name=daily_impressions,json=dailyImpressions,proto3" json:"daily_impressions,omitempty"`
	ImpressionChange                  float64 `protobuf:"fixed64,7,opt,name=impression_change,json=impressionChange,proto3" json:"impression_change,omitempty"`
	ClickChange                       float64 `protobuf:"fixed64,8,opt,name=click_change,json=clickChange,proto3" json:"click_change,omitempty"`
	Dtstatdate                        string  `protobuf:"bytes,9,opt,name=dtstatdate,proto3" json:"dtstatdate,omitempty"`
	Hour                              string  `protobuf:"bytes,10,opt,name=hour,proto3" json:"hour,omitempty"`
	GameCode                          string  `protobuf:"bytes,11,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	CustomerId                        string  `protobuf:"bytes,12,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	AssetId                           string  `protobuf:"bytes,13,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
	AssetName                         string  `protobuf:"bytes,14,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	AssetImageSize                    string  `protobuf:"bytes,15,opt,name=asset_image_size,json=assetImageSize,proto3" json:"asset_image_size,omitempty"`
	AssetImgHeight                    string  `protobuf:"bytes,16,opt,name=asset_img_height,json=assetImgHeight,proto3" json:"asset_img_height,omitempty"`
	AssetImgUrl                       string  `protobuf:"bytes,17,opt,name=asset_img_url,json=assetImgUrl,proto3" json:"asset_img_url,omitempty"`
	AssetImgWidth                     string  `protobuf:"bytes,18,opt,name=asset_img_width,json=assetImgWidth,proto3" json:"asset_img_width,omitempty"`
	AssetMimeType                     string  `protobuf:"bytes,19,opt,name=asset_mime_type,json=assetMimeType,proto3" json:"asset_mime_type,omitempty"`
	AssetResourceName                 string  `protobuf:"bytes,20,opt,name=asset_resource_name,json=assetResourceName,proto3" json:"asset_resource_name,omitempty"`
	AssetType                         string  `protobuf:"bytes,21,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	AssetVideoId                      string  `protobuf:"bytes,22,opt,name=asset_video_id,json=assetVideoId,proto3" json:"asset_video_id,omitempty"`
	AssetVideoTitle                   string  `protobuf:"bytes,23,opt,name=asset_video_title,json=assetVideoTitle,proto3" json:"asset_video_title,omitempty"`
	AdGroupId                         string  `protobuf:"bytes,24,opt,name=ad_group_id,json=adGroupId,proto3" json:"ad_group_id,omitempty"`
	AdGroupName                       string  `protobuf:"bytes,25,opt,name=ad_group_name,json=adGroupName,proto3" json:"ad_group_name,omitempty"`
	AdGroupResourceName               string  `protobuf:"bytes,26,opt,name=ad_group_resource_name,json=adGroupResourceName,proto3" json:"ad_group_resource_name,omitempty"`
	AdGroupStatus                     string  `protobuf:"bytes,27,opt,name=ad_group_status,json=adGroupStatus,proto3" json:"ad_group_status,omitempty"`
	AdStatus                          string  `protobuf:"bytes,28,opt,name=ad_status,json=adStatus,proto3" json:"ad_status,omitempty"`
	AdGroupAdResourceName             string  `protobuf:"bytes,29,opt,name=ad_group_ad_resource_name,json=adGroupAdResourceName,proto3" json:"ad_group_ad_resource_name,omitempty"`
	AdId                              string  `protobuf:"bytes,30,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	AdResourceName                    string  `protobuf:"bytes,31,opt,name=ad_resource_name,json=adResourceName,proto3" json:"ad_resource_name,omitempty"`
	HeadlineListStr                   string  `protobuf:"bytes,32,opt,name=headline_list_str,json=headlineListStr,proto3" json:"headline_list_str,omitempty"`
	DescriptionListStr                string  `protobuf:"bytes,33,opt,name=description_list_str,json=descriptionListStr,proto3" json:"description_list_str,omitempty"`
	ImageListStr                      string  `protobuf:"bytes,34,opt,name=image_list_str,json=imageListStr,proto3" json:"image_list_str,omitempty"`
	VideoListStr                      string  `protobuf:"bytes,35,opt,name=video_list_str,json=videoListStr,proto3" json:"video_list_str,omitempty"`
	CampaignId                        string  `protobuf:"bytes,36,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	CampaignName                      string  `protobuf:"bytes,37,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`
	CampaignResourceName              string  `protobuf:"bytes,38,opt,name=campaign_resource_name,json=campaignResourceName,proto3" json:"campaign_resource_name,omitempty"`
	CampaignBudgetResourceName        string  `protobuf:"bytes,39,opt,name=campaign_budget_resource_name,json=campaignBudgetResourceName,proto3" json:"campaign_budget_resource_name,omitempty"`
	CampaignStatus                    string  `protobuf:"bytes,40,opt,name=campaign_status,json=campaignStatus,proto3" json:"campaign_status,omitempty"`
	CampaignServingStatus             string  `protobuf:"bytes,41,opt,name=campaign_serving_status,json=campaignServingStatus,proto3" json:"campaign_serving_status,omitempty"`
	CampaignAdvertisingChannelType    string  `protobuf:"bytes,42,opt,name=campaign_advertising_channel_type,json=campaignAdvertisingChannelType,proto3" json:"campaign_advertising_channel_type,omitempty"`
	CampaignAdvertisingChannelSubType string  `protobuf:"bytes,43,opt,name=campaign_advertising_channel_sub_type,json=campaignAdvertisingChannelSubType,proto3" json:"campaign_advertising_channel_sub_type,omitempty"`
	CampaignCpa                       string  `protobuf:"bytes,44,opt,name=campaign_cpa,json=campaignCpa,proto3" json:"campaign_cpa,omitempty"`
	CampaignRoas                      string  `protobuf:"bytes,45,opt,name=campaign_roas,json=campaignRoas,proto3" json:"campaign_roas,omitempty"`
	CampaignAppId                     string  `protobuf:"bytes,46,opt,name=campaign_app_id,json=campaignAppId,proto3" json:"campaign_app_id,omitempty"`
	CampaignAppStore                  string  `protobuf:"bytes,47,opt,name=campaign_app_store,json=campaignAppStore,proto3" json:"campaign_app_store,omitempty"`
	CampaignBiddingStrategyGoalType   string  `protobuf:"bytes,48,opt,name=campaign_bidding_strategy_goal_type,json=campaignBiddingStrategyGoalType,proto3" json:"campaign_bidding_strategy_goal_type,omitempty"`
	CampaignStartDate                 string  `protobuf:"bytes,49,opt,name=campaign_start_date,json=campaignStartDate,proto3" json:"campaign_start_date,omitempty"`
	CampaignEndDate                   string  `protobuf:"bytes,50,opt,name=campaign_end_date,json=campaignEndDate,proto3" json:"campaign_end_date,omitempty"`
	CampaignConversionActions         string  `protobuf:"bytes,51,opt,name=campaign_conversion_actions,json=campaignConversionActions,proto3" json:"campaign_conversion_actions,omitempty"`
	Clicks                            float64 `protobuf:"fixed64,52,opt,name=clicks,proto3" json:"clicks,omitempty"`
	Conversions                       float64 `protobuf:"fixed64,53,opt,name=conversions,proto3" json:"conversions,omitempty"`
	CostMicros                        float64 `protobuf:"fixed64,54,opt,name=cost_micros,json=costMicros,proto3" json:"cost_micros,omitempty"`
	Impressions                       float64 `protobuf:"fixed64,55,opt,name=impressions,proto3" json:"impressions,omitempty"`
	CountryList                       string  `protobuf:"bytes,56,opt,name=country_list,json=countryList,proto3" json:"country_list,omitempty"`
	MainCountry                       string  `protobuf:"bytes,57,opt,name=main_country,json=mainCountry,proto3" json:"main_country,omitempty"`
	AccountId                         string  `protobuf:"bytes,58,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	AssetSerialId                     string  `protobuf:"bytes,59,opt,name=asset_serial_id,json=assetSerialId,proto3" json:"asset_serial_id,omitempty"`
	AssetProject                      string  `protobuf:"bytes,60,opt,name=asset_project,json=assetProject,proto3" json:"asset_project,omitempty"`
	AssetFormat                       string  `protobuf:"bytes,61,opt,name=asset_format,json=assetFormat,proto3" json:"asset_format,omitempty"`
	AssetSize                         string  `protobuf:"bytes,62,opt,name=asset_size,json=assetSize,proto3" json:"asset_size,omitempty"`
	AssetLanguage                     string  `protobuf:"bytes,63,opt,name=asset_language,json=assetLanguage,proto3" json:"asset_language,omitempty"`
	AssetOrganization                 string  `protobuf:"bytes,64,opt,name=asset_organization,json=assetOrganization,proto3" json:"asset_organization,omitempty"`
	AssetCustomName                   string  `protobuf:"bytes,65,opt,name=asset_custom_name,json=assetCustomName,proto3" json:"asset_custom_name,omitempty"`
	AssetPlay                         string  `protobuf:"bytes,66,opt,name=asset_play,json=assetPlay,proto3" json:"asset_play,omitempty"`
	AssetPerform                      string  `protobuf:"bytes,67,opt,name=asset_perform,json=assetPerform,proto3" json:"asset_perform,omitempty"`
	AssetStage                        string  `protobuf:"bytes,68,opt,name=asset_stage,json=assetStage,proto3" json:"asset_stage,omitempty"`
	AssetDeliveryDate                 string  `protobuf:"bytes,69,opt,name=asset_delivery_date,json=assetDeliveryDate,proto3" json:"asset_delivery_date,omitempty"`
	CampaignType                      string  `protobuf:"bytes,70,opt,name=campaign_type,json=campaignType,proto3" json:"campaign_type,omitempty"`
	ParseNetwork                      string  `protobuf:"bytes,71,opt,name=parse_network,json=parseNetwork,proto3" json:"parse_network,omitempty"`
	ParseRegion                       string  `protobuf:"bytes,72,opt,name=parse_region,json=parseRegion,proto3" json:"parse_region,omitempty"`
	ParsePlatform                     string  `protobuf:"bytes,73,opt,name=parse_platform,json=parsePlatform,proto3" json:"parse_platform,omitempty"`
	ParseDate                         string  `protobuf:"bytes,74,opt,name=parse_date,json=parseDate,proto3" json:"parse_date,omitempty"`
	ParseCampaignGoal                 string  `protobuf:"bytes,75,opt,name=parse_campaign_goal,json=parseCampaignGoal,proto3" json:"parse_campaign_goal,omitempty"`
	ParseCustomField                  string  `protobuf:"bytes,76,opt,name=parse_custom_field,json=parseCustomField,proto3" json:"parse_custom_field,omitempty"`
	ParseCostType                     string  `protobuf:"bytes,77,opt,name=parse_cost_type,json=parseCostType,proto3" json:"parse_cost_type,omitempty"`
	ParseCostRegion                   string  `protobuf:"bytes,78,opt,name=parse_cost_region,json=parseCostRegion,proto3" json:"parse_cost_region,omitempty"`
	CountryCode                       string  `protobuf:"bytes,79,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Language                          string  `protobuf:"bytes,80,opt,name=language,proto3" json:"language,omitempty"`
	Network                           string  `protobuf:"bytes,81,opt,name=network,proto3" json:"network,omitempty"`
	AssetUrl                          string  `protobuf:"bytes,82,opt,name=asset_url,json=assetUrl,proto3" json:"asset_url,omitempty"`
	Spend                             float64 `protobuf:"fixed64,83,opt,name=spend,proto3" json:"spend,omitempty"`
	YoutubeId                         string  `protobuf:"bytes,84,opt,name=youtube_id,json=youtubeId,proto3" json:"youtube_id,omitempty"`
	ConversionActionName              string  `protobuf:"bytes,85,opt,name=conversion_action_name,json=conversionActionName,proto3" json:"conversion_action_name,omitempty"`
	Installs                          float64 `protobuf:"fixed64,86,opt,name=installs,proto3" json:"installs,omitempty"`
	InAppActions                      float64 `protobuf:"fixed64,87,opt,name=in_app_actions,json=inAppActions,proto3" json:"in_app_actions,omitempty"`
	Dtstattime                        string  `protobuf:"bytes,88,opt,name=dtstattime,proto3" json:"dtstattime,omitempty"`
}

func (x *GoogleRealtimeAssetInfo) Reset() {
	*x = GoogleRealtimeAssetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoogleRealtimeAssetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleRealtimeAssetInfo) ProtoMessage() {}

func (x *GoogleRealtimeAssetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleRealtimeAssetInfo.ProtoReflect.Descriptor instead.
func (*GoogleRealtimeAssetInfo) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{13}
}

func (x *GoogleRealtimeAssetInfo) GetAggNum() int64 {
	if x != nil {
		return x.AggNum
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetCtr() float64 {
	if x != nil {
		return x.Ctr
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetCvr() float64 {
	if x != nil {
		return x.Cvr
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetIpm() float64 {
	if x != nil {
		return x.Ipm
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetCpi() float64 {
	if x != nil {
		return x.Cpi
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetDailyImpressions() float64 {
	if x != nil {
		return x.DailyImpressions
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetImpressionChange() float64 {
	if x != nil {
		return x.ImpressionChange
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetClickChange() float64 {
	if x != nil {
		return x.ClickChange
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetDtstatdate() string {
	if x != nil {
		return x.Dtstatdate
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetHour() string {
	if x != nil {
		return x.Hour
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetImageSize() string {
	if x != nil {
		return x.AssetImageSize
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetImgHeight() string {
	if x != nil {
		return x.AssetImgHeight
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetImgUrl() string {
	if x != nil {
		return x.AssetImgUrl
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetImgWidth() string {
	if x != nil {
		return x.AssetImgWidth
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetMimeType() string {
	if x != nil {
		return x.AssetMimeType
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetResourceName() string {
	if x != nil {
		return x.AssetResourceName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetVideoId() string {
	if x != nil {
		return x.AssetVideoId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetVideoTitle() string {
	if x != nil {
		return x.AssetVideoTitle
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdGroupId() string {
	if x != nil {
		return x.AdGroupId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdGroupName() string {
	if x != nil {
		return x.AdGroupName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdGroupResourceName() string {
	if x != nil {
		return x.AdGroupResourceName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdGroupStatus() string {
	if x != nil {
		return x.AdGroupStatus
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdStatus() string {
	if x != nil {
		return x.AdStatus
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdGroupAdResourceName() string {
	if x != nil {
		return x.AdGroupAdResourceName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAdResourceName() string {
	if x != nil {
		return x.AdResourceName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetHeadlineListStr() string {
	if x != nil {
		return x.HeadlineListStr
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetDescriptionListStr() string {
	if x != nil {
		return x.DescriptionListStr
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetImageListStr() string {
	if x != nil {
		return x.ImageListStr
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetVideoListStr() string {
	if x != nil {
		return x.VideoListStr
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignResourceName() string {
	if x != nil {
		return x.CampaignResourceName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignBudgetResourceName() string {
	if x != nil {
		return x.CampaignBudgetResourceName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignStatus() string {
	if x != nil {
		return x.CampaignStatus
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignServingStatus() string {
	if x != nil {
		return x.CampaignServingStatus
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignAdvertisingChannelType() string {
	if x != nil {
		return x.CampaignAdvertisingChannelType
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignAdvertisingChannelSubType() string {
	if x != nil {
		return x.CampaignAdvertisingChannelSubType
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignCpa() string {
	if x != nil {
		return x.CampaignCpa
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignRoas() string {
	if x != nil {
		return x.CampaignRoas
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignAppId() string {
	if x != nil {
		return x.CampaignAppId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignAppStore() string {
	if x != nil {
		return x.CampaignAppStore
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignBiddingStrategyGoalType() string {
	if x != nil {
		return x.CampaignBiddingStrategyGoalType
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignStartDate() string {
	if x != nil {
		return x.CampaignStartDate
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignEndDate() string {
	if x != nil {
		return x.CampaignEndDate
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignConversionActions() string {
	if x != nil {
		return x.CampaignConversionActions
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetClicks() float64 {
	if x != nil {
		return x.Clicks
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetConversions() float64 {
	if x != nil {
		return x.Conversions
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetCostMicros() float64 {
	if x != nil {
		return x.CostMicros
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetImpressions() float64 {
	if x != nil {
		return x.Impressions
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetCountryList() string {
	if x != nil {
		return x.CountryList
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetMainCountry() string {
	if x != nil {
		return x.MainCountry
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetSerialId() string {
	if x != nil {
		return x.AssetSerialId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetProject() string {
	if x != nil {
		return x.AssetProject
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetFormat() string {
	if x != nil {
		return x.AssetFormat
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetSize() string {
	if x != nil {
		return x.AssetSize
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetLanguage() string {
	if x != nil {
		return x.AssetLanguage
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetOrganization() string {
	if x != nil {
		return x.AssetOrganization
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetCustomName() string {
	if x != nil {
		return x.AssetCustomName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetPlay() string {
	if x != nil {
		return x.AssetPlay
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetPerform() string {
	if x != nil {
		return x.AssetPerform
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetStage() string {
	if x != nil {
		return x.AssetStage
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetDeliveryDate() string {
	if x != nil {
		return x.AssetDeliveryDate
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCampaignType() string {
	if x != nil {
		return x.CampaignType
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParseNetwork() string {
	if x != nil {
		return x.ParseNetwork
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParseRegion() string {
	if x != nil {
		return x.ParseRegion
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParsePlatform() string {
	if x != nil {
		return x.ParsePlatform
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParseDate() string {
	if x != nil {
		return x.ParseDate
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParseCampaignGoal() string {
	if x != nil {
		return x.ParseCampaignGoal
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParseCustomField() string {
	if x != nil {
		return x.ParseCustomField
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParseCostType() string {
	if x != nil {
		return x.ParseCostType
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetParseCostRegion() string {
	if x != nil {
		return x.ParseCostRegion
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetAssetUrl() string {
	if x != nil {
		return x.AssetUrl
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetSpend() float64 {
	if x != nil {
		return x.Spend
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetYoutubeId() string {
	if x != nil {
		return x.YoutubeId
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetConversionActionName() string {
	if x != nil {
		return x.ConversionActionName
	}
	return ""
}

func (x *GoogleRealtimeAssetInfo) GetInstalls() float64 {
	if x != nil {
		return x.Installs
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetInAppActions() float64 {
	if x != nil {
		return x.InAppActions
	}
	return 0
}

func (x *GoogleRealtimeAssetInfo) GetDtstattime() string {
	if x != nil {
		return x.Dtstattime
	}
	return ""
}

type QueryGoogleRealtimeAssetInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result                `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                     // 返回结果
	InfoList []*GoogleRealtimeAssetInfo `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"` // 返回结果
	Total    uint32                     `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`                      // 总数
	Sql      string                     `protobuf:"bytes,4,opt,name=sql,proto3" json:"sql,omitempty"`                           // 查询sql
}

func (x *QueryGoogleRealtimeAssetInfoRsp) Reset() {
	*x = QueryGoogleRealtimeAssetInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pivot_server_pivot_server_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryGoogleRealtimeAssetInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryGoogleRealtimeAssetInfoRsp) ProtoMessage() {}

func (x *QueryGoogleRealtimeAssetInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_pivot_server_pivot_server_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryGoogleRealtimeAssetInfoRsp.ProtoReflect.Descriptor instead.
func (*QueryGoogleRealtimeAssetInfoRsp) Descriptor() ([]byte, []int) {
	return file_pivot_server_pivot_server_proto_rawDescGZIP(), []int{14}
}

func (x *QueryGoogleRealtimeAssetInfoRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *QueryGoogleRealtimeAssetInfoRsp) GetInfoList() []*GoogleRealtimeAssetInfo {
	if x != nil {
		return x.InfoList
	}
	return nil
}

func (x *QueryGoogleRealtimeAssetInfoRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *QueryGoogleRealtimeAssetInfoRsp) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

var File_pivot_server_pivot_server_proto protoreflect.FileDescriptor

var file_pivot_server_pivot_server_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x69, 0x76, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70,
	0x69, 0x76, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0c, 0x70, 0x69, 0x76, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a,
	0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x02,
	0x0a, 0x12, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x57,
	0x68, 0x65, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x70, 0x70, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x75, 0x70, 0x70, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x6c,
	0x6f, 0x77, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x0c, 0x69, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x69, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6b, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c,
	0x69, 0x6b, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x75, 0x70,
	0x70, 0x65, 0x72, 0x5f, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x69, 0x73, 0x55, 0x70, 0x70, 0x65, 0x72, 0x45, 0x71, 0x75, 0x61, 0x6c, 0x12, 0x24, 0x0a,
	0x0e, 0x69, 0x73, 0x5f, 0x6c, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x73, 0x4c, 0x6f, 0x77, 0x65, 0x72, 0x45, 0x71,
	0x75, 0x61, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x96, 0x01, 0x0a, 0x12, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x50, 0x69, 0x76,
	0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x68, 0x6f, 0x77, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x2f, 0x0a, 0x07, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x62, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x62, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x89, 0x02, 0x0a, 0x14, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x36, 0x0a, 0x05, 0x77, 0x68, 0x65,
	0x72, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x69, 0x76, 0x6f, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x50, 0x69, 0x76, 0x6f, 0x74, 0x57, 0x68, 0x65, 0x72, 0x65, 0x52, 0x05, 0x77, 0x68, 0x65, 0x72,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x12, 0x2f, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62, 0x79, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x69, 0x76, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x62, 0x79, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x67, 0x67,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x65, 0x65, 0x64,
	0x41, 0x67, 0x67, 0x4e, 0x75, 0x6d, 0x22, 0xb2, 0x19, 0x0a, 0x11, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x63, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x74, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x76, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x76, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x69,
	0x70, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x69, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x63, 0x70, 0x69, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x10, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x74, 0x73, 0x74, 0x61, 0x74, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x64, 0x74, 0x73, 0x74, 0x61, 0x74, 0x64, 0x61, 0x74, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x6d, 0x63, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6d, 0x63, 0x63, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x6f,
	0x72, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x50,
	0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x73, 0x65, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x70, 0x61, 0x72, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13,
	0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x67,
	0x6f, 0x61, 0x6c, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x47, 0x6f, 0x61, 0x6c, 0x12, 0x2c, 0x0a, 0x12,
	0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x61, 0x72, 0x73, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x2d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x73, 0x65, 0x43, 0x6f, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x73, 0x74,
	0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70,
	0x61, 0x72, 0x73, 0x65, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x2f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x18, 0x31, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44,
	0x61, 0x79, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x68, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x12, 0x26,
	0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x65,
	0x6e, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x45, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x61, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x61, 0x52, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18,
	0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x3b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x3c, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x18,
	0x40, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x41,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4c, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x75, 0x6d, 0x61, 0x6e, 0x5f, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x75, 0x6d, 0x61, 0x6e,
	0x4c, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x43, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x79, 0x6f, 0x75, 0x74, 0x75,
	0x62, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x45, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x77, 0x61, 0x74, 0x63, 0x68, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x64, 0x5f, 0x32, 0x35, 0x18, 0x46, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x32, 0x35, 0x12, 0x26, 0x0a, 0x0f, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x5f, 0x35, 0x30, 0x18, 0x47,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x64, 0x35, 0x30, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x64, 0x5f, 0x37, 0x35, 0x18, 0x48, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x37, 0x35, 0x12, 0x28, 0x0a, 0x10, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x64, 0x5f, 0x31, 0x30, 0x30, 0x18,
	0x49, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x64, 0x31, 0x30, 0x30, 0x12, 0x25, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x0a,
	0x6e, 0x75, 0x6d, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x64, 0x73, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x6e, 0x75, 0x6d, 0x4f, 0x66, 0x41, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x64, 0x31,
	0x5f, 0x70, 0x61, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x4c, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x64, 0x31, 0x50, 0x61, 0x79, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x20, 0x0a, 0x0c,
	0x64, 0x32, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x4d, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x64, 0x32, 0x50, 0x61, 0x79, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x20,
	0x0a, 0x0c, 0x64, 0x33, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x4e,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x64, 0x33, 0x50, 0x61, 0x79, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x12, 0x20, 0x0a, 0x0c, 0x64, 0x37, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x18, 0x4f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x64, 0x37, 0x50, 0x61, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x64, 0x31, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x50, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x64, 0x31, 0x50, 0x61, 0x79, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x64, 0x32, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x51, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x64, 0x32, 0x50, 0x61, 0x79, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x64, 0x33, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x52, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x64, 0x33, 0x50, 0x61, 0x79, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x64, 0x37, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x53, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x64, 0x37, 0x50, 0x61, 0x79, 0x52, 0x61,
	0x74, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x64, 0x31, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x54, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x64, 0x31, 0x50, 0x61, 0x79,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x64, 0x32, 0x5f, 0x70, 0x61, 0x79,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x55, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x64,
	0x32, 0x50, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x64, 0x33,
	0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x56, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0b, 0x64, 0x33, 0x50, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22,
	0x0a, 0x0d, 0x64, 0x37, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x57, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x64, 0x37, 0x50, 0x61, 0x79, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x31, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x58, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x06, 0x64, 0x31, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x64,
	0x32, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x59, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x64, 0x32,
	0x52, 0x6f, 0x61, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x33, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18,
	0x5a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x64, 0x33, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x37, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x5b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06,
	0x64, 0x37, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x31, 0x5f, 0x6c, 0x74, 0x76,
	0x18, 0x5c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x64, 0x31, 0x4c, 0x74, 0x76, 0x12, 0x15, 0x0a,
	0x06, 0x64, 0x32, 0x5f, 0x6c, 0x74, 0x76, 0x18, 0x5d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x64,
	0x32, 0x4c, 0x74, 0x76, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x33, 0x5f, 0x6c, 0x74, 0x76, 0x18, 0x5e,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x64, 0x33, 0x4c, 0x74, 0x76, 0x12, 0x15, 0x0a, 0x06, 0x64,
	0x37, 0x5f, 0x6c, 0x74, 0x76, 0x18, 0x5f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x64, 0x37, 0x4c,
	0x74, 0x76, 0x12, 0x0e, 0x0a, 0x02, 0x72, 0x31, 0x18, 0x60, 0x20, 0x01, 0x28, 0x01, 0x52, 0x02,
	0x72, 0x31, 0x12, 0x0e, 0x0a, 0x02, 0x72, 0x32, 0x18, 0x61, 0x20, 0x01, 0x28, 0x01, 0x52, 0x02,
	0x72, 0x32, 0x12, 0x0e, 0x0a, 0x02, 0x72, 0x33, 0x18, 0x62, 0x20, 0x01, 0x28, 0x01, 0x52, 0x02,
	0x72, 0x33, 0x12, 0x0e, 0x0a, 0x02, 0x72, 0x37, 0x18, 0x63, 0x20, 0x01, 0x28, 0x01, 0x52, 0x02,
	0x72, 0x37, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x67, 0x67, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x64, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x67, 0x67, 0x4e, 0x75, 0x6d, 0x22, 0x8a, 0x01, 0x0a, 0x14,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x37, 0x0a, 0x06, 0x70, 0x69, 0x76,
	0x6f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70, 0x69, 0x76, 0x6f,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x06, 0x70, 0x69, 0x76, 0x6f,
	0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x12, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x57, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x74, 0x73, 0x74, 0x61, 0x74, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x74, 0x73, 0x74, 0x61,
	0x74, 0x64, 0x61, 0x74, 0x65, 0x22, 0x27, 0x0a, 0x0d, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x4d,
	0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x34,
	0x0a, 0x0d, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x4d, 0x6f, 0x63, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x0a, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71,
	0x22, 0x2f, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x94, 0x02, 0x0a, 0x1f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x36, 0x0a, 0x05, 0x77,
	0x68, 0x65, 0x72, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x70, 0x69, 0x76,
	0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x57, 0x68, 0x65, 0x72, 0x65, 0x52, 0x05, 0x77, 0x68,
	0x65, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x2f, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62, 0x79,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x69, 0x76, 0x6f, 0x74, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x62, 0x79, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x61,
	0x67, 0x67, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x65,
	0x65, 0x64, 0x41, 0x67, 0x67, 0x4e, 0x75, 0x6d, 0x22, 0xe2, 0x1a, 0x0a, 0x17, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x67, 0x67, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x67, 0x67, 0x4e, 0x75, 0x6d, 0x12, 0x10, 0x0a,
	0x03, 0x63, 0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x74, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x63, 0x76, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x63, 0x76,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03,
	0x69, 0x70, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x03, 0x63, 0x70, 0x69, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x69,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x10, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x69,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x74, 0x73, 0x74, 0x61, 0x74, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x74, 0x73, 0x74, 0x61, 0x74, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28,
	0x0a, 0x10, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x67, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x67, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x6d, 0x67, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x67, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x26,
	0x0a, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x69,
	0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x76,
	0x69, 0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x61, 0x64, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x64, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x61,
	0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x64, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x19, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x41, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x61, 0x64, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x61, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a,
	0x0a, 0x11, 0x68, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x73, 0x74, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x68, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73,
	0x74, 0x72, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x12, 0x24, 0x0a, 0x0e,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x73, 0x74, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x16, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x1d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x36, 0x0a, 0x17, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x21, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x2a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x1e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x25, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x2b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x21, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x75,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x63, 0x70, 0x61, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x43, 0x70, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x72, 0x6f, 0x61, 0x73, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x6f, 0x61, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x2f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x70, 0x70, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x12, 0x4c, 0x0a, 0x23, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x62, 0x69, 0x64, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x1f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x42, 0x69, 0x64, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x47, 0x6f, 0x61, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x0a,
	0x1b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x19, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x73, 0x18, 0x34, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x63,
	0x6c, 0x69, 0x63, 0x6b, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x35, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x73, 0x74, 0x5f,
	0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x18, 0x36, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x63, 0x6f,
	0x73, 0x74, 0x4d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x37, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x69,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x39, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x3a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x3d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x3e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x40, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x42,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x12,
	0x23, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x43, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x50, 0x65, 0x72,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x45, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x46, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61,
	0x72, 0x73, 0x65, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x47, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x70, 0x61, 0x72, 0x73, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x48, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x73, 0x65, 0x52, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x49, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x73,
	0x65, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72,
	0x73, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x72, 0x73, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x61, 0x72, 0x73,
	0x65, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x18,
	0x4b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x61, 0x72, 0x73, 0x65, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x47, 0x6f, 0x61, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x61, 0x72, 0x73,
	0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x4c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x61, 0x72, 0x73, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f,
	0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x70, 0x61, 0x72, 0x73, 0x65, 0x43, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a,
	0x0a, 0x11, 0x70, 0x61, 0x72, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x4f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x50, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x51, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x52, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x18, 0x53, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x05, 0x73, 0x70, 0x65, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x54, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x79, 0x6f, 0x75, 0x74,
	0x75, 0x62, 0x65, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x55, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x18, 0x56, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x6e, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x57, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0c, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x64, 0x74, 0x73, 0x74, 0x61, 0x74, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x58, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x74, 0x73, 0x74, 0x61, 0x74, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xb2, 0x01,
	0x0a, 0x1f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x61,
	0x6c, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x69, 0x76, 0x6f,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x71, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73,
	0x71, 0x6c, 0x42, 0x3b, 0x5a, 0x39, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69,
	0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f,
	0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x73, 0x2f, 0x70, 0x69, 0x76, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pivot_server_pivot_server_proto_rawDescOnce sync.Once
	file_pivot_server_pivot_server_proto_rawDescData = file_pivot_server_pivot_server_proto_rawDesc
)

func file_pivot_server_pivot_server_proto_rawDescGZIP() []byte {
	file_pivot_server_pivot_server_proto_rawDescOnce.Do(func() {
		file_pivot_server_pivot_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_pivot_server_pivot_server_proto_rawDescData)
	})
	return file_pivot_server_pivot_server_proto_rawDescData
}

var file_pivot_server_pivot_server_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_pivot_server_pivot_server_proto_goTypes = []interface{}{
	(*AnalysisPivotWhere)(nil),              // 0: pivot_server.AnalysisPivotWhere
	(*AnalysisPivotGroup)(nil),              // 1: pivot_server.AnalysisPivotGroup
	(*OrderBy)(nil),                         // 2: pivot_server.OrderBy
	(*AnalysisPivotListReq)(nil),            // 3: pivot_server.AnalysisPivotListReq
	(*AnalysisPivotMeta)(nil),               // 4: pivot_server.AnalysisPivotMeta
	(*AnalysisPivotListRsp)(nil),            // 5: pivot_server.AnalysisPivotListRsp
	(*GetFilterInfoReq)(nil),                // 6: pivot_server.GetFilterInfoReq
	(*GetFilterInfoRsp)(nil),                // 7: pivot_server.GetFilterInfoRsp
	(*InsertMockReq)(nil),                   // 8: pivot_server.InsertMockReq
	(*InsertMockRsp)(nil),                   // 9: pivot_server.InsertMockRsp
	(*SayHiReq)(nil),                        // 10: pivot_server.SayHiReq
	(*SayHiRsp)(nil),                        // 11: pivot_server.SayHiRsp
	(*QueryGoogleRealtimeAssetInfoReq)(nil), // 12: pivot_server.QueryGoogleRealtimeAssetInfoReq
	(*GoogleRealtimeAssetInfo)(nil),         // 13: pivot_server.GoogleRealtimeAssetInfo
	(*QueryGoogleRealtimeAssetInfoRsp)(nil), // 14: pivot_server.QueryGoogleRealtimeAssetInfoRsp
	(*aix.Result)(nil),                      // 15: aix.Result
}
var file_pivot_server_pivot_server_proto_depIdxs = []int32{
	0,  // 0: pivot_server.AnalysisPivotListReq.where:type_name -> pivot_server.AnalysisPivotWhere
	2,  // 1: pivot_server.AnalysisPivotListReq.orderby:type_name -> pivot_server.OrderBy
	15, // 2: pivot_server.AnalysisPivotListRsp.result:type_name -> aix.Result
	4,  // 3: pivot_server.AnalysisPivotListRsp.pivots:type_name -> pivot_server.AnalysisPivotMeta
	15, // 4: pivot_server.GetFilterInfoRsp.result:type_name -> aix.Result
	15, // 5: pivot_server.InsertMockRsp.result:type_name -> aix.Result
	15, // 6: pivot_server.SayHiRsp.result:type_name -> aix.Result
	0,  // 7: pivot_server.QueryGoogleRealtimeAssetInfoReq.where:type_name -> pivot_server.AnalysisPivotWhere
	2,  // 8: pivot_server.QueryGoogleRealtimeAssetInfoReq.orderby:type_name -> pivot_server.OrderBy
	15, // 9: pivot_server.QueryGoogleRealtimeAssetInfoRsp.result:type_name -> aix.Result
	13, // 10: pivot_server.QueryGoogleRealtimeAssetInfoRsp.info_list:type_name -> pivot_server.GoogleRealtimeAssetInfo
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_pivot_server_pivot_server_proto_init() }
func file_pivot_server_pivot_server_proto_init() {
	if File_pivot_server_pivot_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pivot_server_pivot_server_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisPivotWhere); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisPivotGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisPivotListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisPivotMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisPivotListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFilterInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFilterInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsertMockReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsertMockRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGoogleRealtimeAssetInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoogleRealtimeAssetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pivot_server_pivot_server_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryGoogleRealtimeAssetInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pivot_server_pivot_server_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pivot_server_pivot_server_proto_goTypes,
		DependencyIndexes: file_pivot_server_pivot_server_proto_depIdxs,
		MessageInfos:      file_pivot_server_pivot_server_proto_msgTypes,
	}.Build()
	File_pivot_server_pivot_server_proto = out.File
	file_pivot_server_pivot_server_proto_rawDesc = nil
	file_pivot_server_pivot_server_proto_goTypes = nil
	file_pivot_server_pivot_server_proto_depIdxs = nil
}
