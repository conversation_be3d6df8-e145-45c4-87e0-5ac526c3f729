package service

import (
	"context"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/cos"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	aixwebRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/aix_web"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	chRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/channel_asset"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/go-pg/pg/v10/orm"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

// AddLabelRule 增加/更新标签规则
func AddLabelRule(ctx *gin.Context, req *pb.AddLabelRuleReq, rsp *pb.AddLabelRuleRsp) error {
	user := utils.GetHeaderUser(ctx)

	rule := req.GetRule()
	if rule.GetGameCode() == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code error")
	}

	// 加载标签库
	creativeLabels, err := aixwebRepo.LoadCreativeLabels(ctx, rule.GetGameCode())
	if err != nil {
		return err
	}

	// 如果前端未指定startwith模式， 则根据配置来
	if rule.Type != constant.LabelRuleStartwith {
		ruleType, err := repo.GetGameLabelRuleType(ctx, rule.GetGameCode())
		if err != nil {
			return err
		}
		rule.Type = int32(ruleType)
	}

	normalizeRuleName, ok := model.CheckAndNormalizeLabelRuleName(rule.Type, rule.GetRule(), true)
	if !ok {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "ruleType:%v rule:%v not support", rule.Type, rule.GetRule())
	}
	rule.Rule = normalizeRuleName

	// 记录最终的标签
	var finalLabels []*pb.AssetLabel
	// 检查是否有重复的标签
	checkLableDum := make(map[string]bool)
	for _, label := range rule.GetLabels() {
		label.LabelName = "" // 标签规则的label_name固定为空，已不再使用
		key := fmt.Sprintf("%v,%v,%v", label.GetLabelName(), label.GetFirstLabel(), label.GetSecondLabel())
		if checkLableDum[key] {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "duplicate label:%v", key)
		}
		checkLableDum[key] = true

		// 只保存serial层级的标签
		if creativeLabels.IsFirstLabelSerialLevel(label.GetFirstLabel()) {
			finalLabels = append(finalLabels, label)
		}
	}
	rule.Labels = finalLabels

	// 判断下线日期格式
	if rule.GetOfflineDate() != "" {
		if !utils.IsNumericDate(rule.GetOfflineDate()) {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
				"offline_date err:%v", rule.GetOfflineDate())
		}
	}

	rule.Creater = user
	rule.Updater = user

	// 更新
	if rule.GetId() != 0 {
		// 这里更新标签规则的自身信息， 不能更新曝光数据字段
		fields := []string{
			"game_code", "rule", "type", "labels", "label_options",
			"update_time", "update_user", "offline_date"}
		err := data.UpdateLabelRuleByFields(ctx, rule, fields)
		if err != nil {
			return err
		}

		// 更新标签， 实时同步到ck外表
		dbRule := data.PBLabelRuleToPG(rule)
		err = data.InsertRuleLabelsByRule(ctx, dbRule)
		if err != nil {
			log.ErrorContextf(ctx, "data.InsertRuleLabelsByRule err:%v", err)
			return err
		}
	} else {
		// 新增
		err := data.UpsertLabelRule(ctx, rule)
		if err != nil {
			return err
		}
	}

	log.DebugContextf(ctx, "rule:%v", rule)

	// 20240425 这里标签不在绑定到具体的素材上，不应用下面逻辑了
	// // 更新成功后通知material_sync将规则应用到素材
	// err := rpc.NotifyApplyLabelRuleToAix(ctx, rule.GameCode, rule.Id)
	// if err != nil {
	// 	return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR),
	// 		"rpc.NotifyApplyLabelRuleToAix err:%v", err)
	// }

	return nil
}

// UpdateAssetLevelLabels 更新asset层级标签
func UpdateAssetLevelLabels(ctx *gin.Context, req *pb.UpdateAssetLevelLabelsReq, rsp *pb.UpdateAssetLevelLabelsRsp) error {
	user := utils.GetHeaderUser(ctx)

	if req.GetGameCode() == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code error")
	}
	if req.GetAssetName() == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "asset_name error")
	}

	// 加载标签库
	creativeLabels, err := aixwebRepo.LoadCreativeLabels(ctx, req.GetGameCode())
	if err != nil {
		return err
	}

	// 记录最终的标签
	var finalLabels []*pb.AssetLabel
	// 检查是否有重复的标签
	checkLableDum := make(map[string]bool)
	for _, label := range req.GetLabels() {
		label.LabelName = "" // 标签规则的label_name固定为空，已不再使用
		key := fmt.Sprintf("%v,%v,%v", label.GetLabelName(), label.GetFirstLabel(), label.GetSecondLabel())
		if checkLableDum[key] {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "duplicate label:%v", key)
		}
		checkLableDum[key] = true

		// 只保存asset层级的标签
		if creativeLabels.IsFirstLabelAssetLevel(label.GetFirstLabel()) {
			finalLabels = append(finalLabels, label)
		}
	}

	// 将asset层级标签记录到标签规则表， type=99 LabelRuleAssetLebel
	rule := &pb.LabelRule{
		GameCode:   req.GetGameCode(),
		Rule:       req.GetAssetName(),
		Type:       constant.LabelRuleAssetLebel,
		Labels:     finalLabels,
		CreateTime: utils.GetNowStr(),
		Creater:    user,
		UpdateTime: utils.GetNowStr(),
		Updater:    user,
	}

	err = data.UpsertLabelRule(ctx, rule)
	if err != nil {
		return err
	}

	// 更新标签， 实时同步到ck外表
	dbRule := data.PBLabelRuleToPG(rule)
	err = data.InsertRuleLabelsByRule(ctx, dbRule)
	if err != nil {
		log.ErrorContextf(ctx, "data.InsertRuleLabelsByRule err:%v", err)
		return err
	}

	return nil
}

// ModifyLabelRuleSecondLabel 修改标签规则二级标签
func ModifyLabelRuleSecondLabel(ctx *gin.Context, req *pb.ModifyLabelRuleSecondLabelReq, rsp *pb.ModifyLabelRuleSecondLabelRsp) error {
	user := utils.GetHeaderUser(ctx)

	if req.GetFirstLabel() == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "first_label error")
	}
	if !req.GetIsDelete() && req.GetNewSecondLabel() == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "new_second_label error")
	}

	ruleType, err := repo.GetGameLabelRuleType(ctx, req.GetGameCode())
	if err != nil {
		return err
	}
	// 拉取标签规则
	rule, err := data.GetLabelRuleByName(ctx, req.GetGameCode(), req.GetSerialId(), ruleType)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"repo.GetLabelRuleByGameCodeAndID err:%v", err)
	}
	if rule == nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
			"no such serial_id:%v", req.GetSerialId())
	}

	// 先将旧的二级标签删除
	var finalLabels []*pb.AssetLabel
	for _, label := range rule.Labels {
		if !(label.FirstLabel == req.GetFirstLabel() && label.SecondLabel == req.GetSecondLabel()) {
			finalLabels = append(finalLabels, label)
		}
	}
	if !req.GetIsDelete() {
		// 不是删除， 就增加新的二级标签
		finalLabels = append(finalLabels, &pb.AssetLabel{
			FirstLabel:  req.GetFirstLabel(),
			SecondLabel: req.GetNewSecondLabel(),
		})
	}
	rule.Labels = finalLabels
	rule.UpdateTime = utils.GetNowStr()
	rule.Updater = user
	// 去重标签
	removeRuleDuplicateLables(rule)

	fields := []string{
		"labels", "update_time", "update_user"}
	err = data.UpdateLabelRuleByFields(ctx, rule, fields)
	if err != nil {
		log.ErrorContextf(ctx, "data.UpdateLabelRuleByFields err:%v", err)
		return err
	}

	dbRule := data.PBLabelRuleToPG(rule)
	err = data.InsertRuleLabelsByRule(ctx, dbRule)
	if err != nil {
		log.ErrorContextf(ctx, "data.InsertRuleLabelsByRule err:%v", err)
		return err
	}
	return nil
}

// ModifyRuleContentLabels 修改标签规则机器内容标签
func ModifyRuleContentLabels(ctx *gin.Context, req *pb.ModifyRuleContentLabelsReq, rsp *pb.ModifyRuleContentLabelsRsp) error {
	gameCode := req.GetGameCode()
	ruleName := req.GetRule()
	if gameCode == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code error")
	}

	ruleType, err := repo.GetGameLabelRuleType(ctx, req.GetGameCode())
	if err != nil {
		return err
	}
	normalizeRuleName, ok := model.CheckAndNormalizeLabelRuleName(int32(ruleType), ruleName, true)
	if !ok {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "ruleType:%v rule:%v not support", ruleType, ruleName)
	}
	ruleName = normalizeRuleName

	rule, err := data.GetLabelRuleByName(ctx, gameCode, ruleName, ruleType)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.GetLabelRuleByName err:%v", err)
	}
	if rule == nil {
		rule = &pb.LabelRule{
			GameCode: gameCode,
			Rule:     ruleName,
			Type:     int32(ruleType),
		}
	}
	rule.Creater = "content_label"
	rule.Updater = "content_label"

	// 拉取标签集信息
	creativeLabels, err := aixwebRepo.GetGameCreativeLabels(ctx, gameCode)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "aixwebRepo.GetGameCreativeLabels err:%v", err)
	}

	// 保存标签体系，后面要特殊处理
	creativeLabelMap := make(map[string]*creativeLabelStatus)
	// 记录哪些是intelligent标签
	contentLabelMap := make(map[string]bool)
	for _, label := range creativeLabels {
		// intelligent标签
		if label.LabelMethod == constant.LabelMethodIntelligent {
			for _, opt := range label.Options {
				key := fmt.Sprintf("%v_%v", label.Name, opt)
				contentLabelMap[key] = true
			}
		}

		t := &creativeLabelStatus{
			label:   label,
			changed: false,
		}
		creativeLabelMap[label.Name] = t
	}

	// 增加的标签
	for _, label := range req.GetAdd() {
		label.LabelName = "" // 标签规则的label_name固定为空，已不再使用
		rule.Labels = append(rule.Labels, label)

		// 判断是否需要更新标签体系
		// 不在标签体系里面的自动添加
		if t, ok := creativeLabelMap[label.FirstLabel]; ok {
			if !funk.ContainsString(t.label.Options, label.SecondLabel) {
				t.label.UpdatedAt = time.Now()
				t.label.Updater = "content_label"
				t.label.Options = append(t.label.Options, label.SecondLabel)
				t.changed = true
			}
		} else {
			t = &creativeLabelStatus{
				label: &model.CreativeLabel{
					GameCode:    gameCode,
					Name:        label.FirstLabel,
					LabelMethod: constant.LabelMethodIntelligent,
					CreatedAt:   time.Now(),
					Creator:     "content_label",
					UpdatedAt:   time.Now(),
					Updater:     "content_label",
				},
				changed: true,
			}
			t.label.Options = append(t.label.Options, label.SecondLabel)
			creativeLabelMap[label.FirstLabel] = t
		}
	}
	// 去重标签
	removeRuleDuplicateLables(rule)

	// 记录需要删除的标签
	removeLabels := make(map[string]bool)
	for _, label := range req.GetRemove() {
		// 在intelligent标签集合中的，才删除
		key := fmt.Sprintf("%v_%v", label.GetFirstLabel(), label.GetSecondLabel())
		if !contentLabelMap[key] {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), `remove label %v not in content label`, key)
		}
		removeLabels[key] = true
	}

	var finalLabels []*pb.AssetLabel
	for _, label := range rule.GetLabels() {
		key := fmt.Sprintf("%v_%v", label.GetFirstLabel(), label.GetSecondLabel())
		if removeLabels[key] {
			continue
		}
		finalLabels = append(finalLabels, label)
	}
	rule.Labels = finalLabels

	// 保存更新
	if rule.GetId() != 0 {
		// 这里更新标签规则的自身信息， 不能更新曝光数据字段和下线日期字段
		fields := []string{
			"game_code", "rule", "type", "labels",
			"update_time", "update_user"}
		err := data.UpdateLabelRuleByFields(ctx, rule, fields)
		if err != nil {
			return err
		}
	} else {
		// 新增
		err := data.UpsertLabelRule(ctx, rule)
		if err != nil {
			return err
		}
	}

	// 有变化更新标签体系
	var saves []*model.CreativeLabel
	for _, t := range creativeLabelMap {
		if t.changed {
			saves = append(saves, t.label)
		}
	}
	if len(saves) > 0 {
		err = data.UpsertCreativeLabels(ctx, saves)
		if err != nil {
			log.ErrorContextf(ctx, "data.UpsertCreativeLabels err:%v", err)
		}
	}
	return nil
}

// DeleteLabelRule 删除标签规则
func DeleteLabelRule(ctx *gin.Context, req *pb.DeleteLabelRuleReq, rsp *pb.DeleteLabelRuleRsp) error {
	gameCode := req.GetRule().GetGameCode()
	ruleID := req.GetRule().GetId()
	// 拉取标签规则
	rule, err := repo.GetLabelRuleByGameCodeAndID(ctx, gameCode, ruleID)
	if err != nil {
		if err == pg.ErrNoRows {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
				"no such rule id:%v", ruleID)

		}
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"repo.GetLabelRuleByGameCodeAndID err:%v", err)
	}

	// 删除标签规则
	err = data.DeleteLabelRule(ctx, rule)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"data.DeleteLabelRule err:%v", err)
	}

	// 删除同步到ck外表的标签
	err = data.DeleteRuleLabelsByRule(ctx, rule)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"data.DeleteRuleLabelsByRUle err:%v", err)
	}

	return nil
}

// GetLabelRules 查询标签规则
func GetLabelRules(ctx *gin.Context, req *pb.GetLabelRulesReq, rsp *pb.GetLabelRulesRsp) error {
	if req.GetGameCode() == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code error")
	}

	_, fromInternal := ctx.Get("from-internal")
	page := req.GetPage()
	pageSize := req.GetPageSize()
	maxPageSize := int32(100)
	// 内部调用放宽page_size
	if _, ok := ctx.Get("from-internal"); ok {
		maxPageSize = 1000
	}
	if page < 0 || pageSize <= 0 || pageSize > maxPageSize {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "page or limit error")
	}
	offset := cast.ToInt(page * pageSize)
	limit := cast.ToInt(pageSize)

	// 支持复杂查询
	var rules []*model.TbAssetLabelRule
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(&rules)
	query.Where("game_code = ?", req.GetGameCode()) // 根据游戏过滤
	if !fromInternal {
		// 非内部调用，只返回serial层级标签规则
		query.Where("type != ?", constant.LabelRuleAssetLebel) // 过滤掉特殊asset层级标签规则
	}
	// 过滤标签
	if len(req.GetLabels()) > 0 {
		// 这里标签是json数组，所以用json_array_elements 平铺，再过滤
		// SELECT id, json_array_elements(labels)->>'FirstLabel' AS first_label,  json_array_elements(labels)->>'SecondLabel' AS second_label FROM arthub_sync.tb_asset_label_rule;
		labelVirtualTable := db.Model().TableExpr("arthub_sync.tb_asset_label_rule")
		labelVirtualTable.ColumnExpr("id, json_array_elements(labels)->>'FirstLabel' AS first_label, json_array_elements(labels)->>'SecondLabel' AS second_label")

		subQuery := db.Model().TableExpr("(?) t", labelVirtualTable)
		subQuery.Column("id")
		if req.GetLabelSearchType() == 2 {
			// 交集，一个标签是一行，所以先or查出来，看个数是否和期望的标签个数相等
			for _, label := range req.GetLabels() {
				subQuery.WhereOr("first_label = ? and second_label = ?", label.GetFirstLabel(), label.GetSecondLabel())
			}
			subQuery.Group("id").Having("count(id) = ?", len(req.GetLabels()))
		} else {
			// 并集
			for _, label := range req.GetLabels() {
				subQuery.WhereOr("first_label = ? and second_label = ?", label.GetFirstLabel(), label.GetSecondLabel())
			}
			subQuery.Group("id")
		}

		query.Where("id in (?)", subQuery)
	}
	// 过滤名字
	names := funk.UniqString(req.GetNames())
	// 去除空字符串
	names = funk.FilterString(names, func(s string) bool {
		return !utils.IsStringEmpty(s)
	})
	if len(names) > 0 {
		// 支持模糊查找素材，然后根据素材名字前缀反查标签
		rules, err := genRuleFromSearchAssets(ctx, req.GetGameCode(), names)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "genRuleFromSearchAssets err:%v", err)
		}
		rules = funk.UniqString(rules)

		query.WhereGroup(func(q *orm.Query) (*orm.Query, error) {
			q = ormQueryIlikeAny(q, "rule", names)
			if len(rules) > 0 {
				// 根据素材名字前缀反查标签, 要完全匹配
				q = q.WhereOr("rule = ANY(?)", pg.Array(rules))
			}

			return q, nil
		})

	}

	// 过滤曝光
	if req.GetImpressionStatus() == constant.ImpressionStatusPublished {
		query.Where("impression_date != ?", "") // 曝光日期非空
		if req.GetStartImpressionDate() != "" {
			query.Where("impression_date >= ?", req.GetStartImpressionDate())
		}
		if req.GetEndImpressionDate() != "" {
			query.Where("impression_date <= ?", req.GetEndImpressionDate())
		}
	} else if req.GetImpressionStatus() == constant.ImpressionStatusUnpublished {
		query.Where("impression_date = ?", "") // 曝光日期为空
	}

	// 过滤下线日期
	if req.GetStartOfflineDate() != "" || req.GetEndOfflineDate() != "" {
		query.Where("offline_date != ?", "") // 下线日期非空
		if req.GetStartOfflineDate() != "" {
			query.Where("offline_date >= ?", req.GetStartOfflineDate())
		}
		if req.GetEndOfflineDate() != "" {
			query.Where("offline_date <= ?", req.GetEndOfflineDate())
		}
	}

	query.Offset(offset).Limit(limit).Order("id desc") // 新创建的标签规则排在前面
	count, err := query.SelectAndCount()
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "query label SelectAndCount err:%v", err)
	}

	// 加载标签库
	creativeLabels, err := aixwebRepo.LoadCreativeLabels(ctx, req.GetGameCode())
	if err != nil {
		return err
	}

	rsp.Total = uint32(count)
	for _, r := range rules {
		// 只返回serial层级的标签
		var finalLabels []model.AssetLabel
		for _, label := range r.Labels {

			if fromInternal {
				// 内部调用，serial和asset层级标签都返回
				finalLabels = append(finalLabels, label)
			} else if creativeLabels.HasSerialLevelLabel(label.FirstLabel, label.SecondLabel) {
				// 非内部调用才只返回serial层级标签
				finalLabels = append(finalLabels, label)
			}
		}
		r.Labels = finalLabels

		tmp := data.PGLabelRuleToPB(r)
		tmp.ImpressionStatus = getImpressionStatusByImpressionDate(tmp.ImpressionDate)
		rsp.Rules = append(rsp.Rules, tmp)
	}
	// 非内部调用需要关联一个素材信息返回页面, 并发处理
	if !fromInternal {
		chunks := funk.Chunk(rsp.GetRules(), 5).([][]*pb.LabelRule)
		var handlers []func() error
		for _, chunk := range chunks {
			tmps := chunk
			handlers = append(handlers, func() error {
				for _, rule := range tmps {
					// 从缓存中先加载标签规则对应的素材信息
					cacheOverView, err := cache.GetLabelRuleMatchAsset(rule.GetGameCode(), int(rule.GetType()), rule.GetRule())
					if err == nil {
						rule.LinkAsset = arthubOverView2MaterialMeta(ctx, cacheOverView)
					} else {
						r := data.PBLabelRuleToPG(rule)
						overviews, err := repo.GetCreativeOverviewsByRuleNotCount(ctx, r, 0, 1)
						if err != nil {
							return err
						}
						if len(overviews) > 0 {
							rule.LinkAsset = arthubOverView2MaterialMeta(ctx, overviews[0])
							cache.SetLabelRuleMatchAsset(rule.GetGameCode(), int(rule.GetType()), rule.GetRule(), overviews[0])
						}
					}
				}
				return nil
			})
		}

		err := utils.GoAndWait(ctx, handlers...)

		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetCreativeOverviewsByRuleNotCount err:%v", err)
		}
	}
	return nil
}

// DownloadLabelRules 导出标签规则
func DownloadLabelRules(ctx *gin.Context, req *pb.DownloadLabelRulesReq, rsp *pb.DownloadLabelRulesRsp) error {
	ctx.Set("from-internal", true) // 标志是从内部调用的
	// 先查符合条件的全部规则
	var allRules []*pb.LabelRule
	for page := 0; page < 10000; page++ {
		getReq := &pb.GetLabelRulesReq{
			GameCode:            req.GetGameCode(),
			Names:               req.GetNames(),
			Labels:              req.GetLabels(),
			LabelSearchType:     req.GetLabelSearchType(),
			ImpressionStatus:    req.GetImpressionStatus(),
			StartImpressionDate: req.GetStartImpressionDate(),
			EndImpressionDate:   req.GetEndImpressionDate(),
			StartOfflineDate:    req.GetStartOfflineDate(),
			EndOfflineDate:      req.GetEndOfflineDate(),
			Page:                int32(page),
			PageSize:            500,
		}
		getRsp := &pb.GetLabelRulesRsp{}
		err := GetLabelRules(ctx, getReq, getRsp)
		if err != nil {
			return err
		}

		allRules = append(allRules, getRsp.Rules...)
		// 没数据了
		if len(getRsp.Rules) < 500 {
			break
		}
	}

	// 取标签系统的非机器打标的serial层级一级标签作为title
	title, err := getAllNoneIntelligentFirstLabels(ctx, req.GetGameCode())
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "getAllNoneIntelligentFirstLabels err:%v", err)
	}

	// serial和asset层级分两个sheet存储
	var datas [][]interface{}
	var assetDatas [][]interface{}
	for _, rule := range allRules {
		var row []interface{}

		cells := make(map[string]string)
		for _, label := range rule.GetLabels() {
			old := cells[label.FirstLabel]
			if old == "" {
				cells[label.FirstLabel] = label.SecondLabel
			} else {
				cells[label.FirstLabel] = old + "\n" + label.SecondLabel
			}
		}

		row = append(row, rule.Rule) // 第一列是名字
		for _, t := range title {
			row = append(row, cells[t])
		}

		if rule.GetType() == constant.LabelRuleAssetLebel {
			assetDatas = append(assetDatas, row)
		} else {
			datas = append(datas, row)
		}
	}

	var sheets []*Sheet
	serialTitle := append([]string{"Serial Name"}, title...)
	serialSheet := &Sheet{
		Name:       "Serial",
		Titles:     serialTitle,
		Datas:      datas,
		mergeCells: []mergeCell{},
	}
	sheets = append(sheets, serialSheet)
	assetTitle := append([]string{"Asset Name"}, title...)
	assetSheet := &Sheet{
		Name:       "Asset",
		Titles:     assetTitle,
		Datas:      assetDatas,
		mergeCells: []mergeCell{},
	}
	sheets = append(sheets, assetSheet)

	buffer, err := NewExcelBufferWithSheets(sheets)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "newExcelWithSheets err:%v", err)
	}

	// 保存本地文件
	fileName := fmt.Sprintf("%v_lable_rules_%v.xlsx", req.GetGameCode(), time.Now().UnixNano())
	log.DebugContextf(ctx, "save to file:%v", fileName)
	err = os.WriteFile(fileName, buffer.Bytes(), os.ModePerm)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "save file:%v err:%v", fileName, err)
	}
	defer os.Remove(fileName)

	// 上传到cos
	cosFile := fmt.Sprintf("/creative_label/%v", fileName)
	log.DebugContextf(ctx, "put to cos:%v", cosFile)
	err = cos.PutFromFile(ctx, cosFile, fileName)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "save file to cos err:%v", err)
	}

	rsp.CosFile = cosFile
	return nil
}

// GetAssetsByLabelRule 拉取标签规则下的素材
func GetAssetsByLabelRule(ctx *gin.Context, req *pb.GetAssetsByLabelRuleReq, rsp *pb.GetAssetsByLabelRuleRsp) error {
	// 拉取depot 后面要用
	depot, err := data.GetDepotWithContext(ctx, req.GetGameCode())
	if err != nil {
		log.ErrorContextf(ctx, "error data.GetDepot, err: %v", err)
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "system error")
	}

	page := req.GetPage()
	pageSize := req.GetPageSize()
	if page < 0 || pageSize <= 0 || pageSize > 100 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "page or limit error")
	}
	offset := cast.ToInt(page * pageSize)
	limit := cast.ToInt(pageSize)

	rule, err := repo.GetLabelRuleByGameCodeAndID(ctx, req.GetGameCode(), req.GetRuleId())
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetLabelRuleByGameCodeAndID err:%v", err)
	}
	overviews, total, err := repo.GetCreativeOverviewsByRule(ctx, rule, offset, limit)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetCreativeOverviewsByRule err:%v", err)
	}
	// 没有纪录，直接返回
	if len(overviews) == 0 {
		rsp.Total = uint32(total)
		return nil
	}

	creativeLabels, err := aixwebRepo.LoadCreativeLabels(ctx, req.GetGameCode())
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "aixwebRepo.LoadCreativeLabels err:%v", err)
	}

	pbRule := data.PGLabelRuleToPB(rule)
	// 过滤一下规则里面的标签，只保留serial层级
	var ruleLabels []*pb.AssetLabel
	for _, label := range pbRule.Labels {
		if creativeLabels.HasSerialLevelLabel(label.GetFirstLabel(), label.GetSecondLabel()) {
			ruleLabels = append(ruleLabels, label)
		}
	}

	var assetNames []string
	for _, view := range overviews {
		assetNames = append(assetNames, view.AssetName)
	}
	// 去重
	assetNames = funk.UniqString(assetNames)

	// 查asset层级的标签
	assetLabelMap, err := repo.GetAssetLevelLabelRules(ctx, req.GetGameCode(), assetNames)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetAssetLevelLabelRules err:%v", err)
	}

	for _, view := range overviews {
		view := arthubOverView2MaterialMeta(ctx, view)
		view.Labels = ruleLabels
		// 素材层级的标签，追加
		if assetLabel, ok := assetLabelMap[view.GetName()]; ok {
			for _, label := range assetLabel.Labels {
				if creativeLabels.HasAssetLevelLabel(label.FirstLabel, label.SecondLabel) {
					view.Labels = append(view.Labels, &pb.AssetLabel{
						LabelName:   "", // 固定为空
						FirstLabel:  label.FirstLabel,
						SecondLabel: label.SecondLabel,
					})
				}
			}
			// 去重
			view.Labels = uniqPBAssetLabels(view.Labels)
		}
		rsp.Materials = append(rsp.Materials, view)
	}
	rsp.Materials = fillMeterialMetas(ctx, "", rsp.GetMaterials(), depot.Type, depot.GameCode, depot.ArthubCode)

	rsp.Total = uint32(total)
	return nil
}

// GetChannelAssetsByLabelRule 拉取标签规则下的渠道素材
func GetChannelAssetsByLabelRule(ctx *gin.Context, req *pb.GetChannelAssetsByLabelRuleReq, rsp *pb.GetChannelAssetsByLabelRuleRsp) error {
	page := req.GetPage()
	pageSize := req.GetPageSize()
	if page < 0 || pageSize <= 0 || pageSize > 100 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "page or limit error")
	}
	offset := cast.ToInt(page * pageSize)
	limit := cast.ToInt(pageSize)

	rule, err := repo.GetLabelRuleByGameCodeAndID(ctx, req.GetGameCode(), req.GetRuleId())
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetLabelRuleByGameCodeAndID err:%v", err)
	}
	assets, total, err := chRepo.GetChannelAssetsByRule(ctx, rule, offset, limit)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetChannelAssetsByRule err:%v", err)
	}
	// 没有纪录，直接返回
	if len(assets) == 0 {
		rsp.Total = uint32(total)
		return nil
	}

	creativeLabels, err := aixwebRepo.LoadCreativeLabels(ctx, req.GetGameCode())
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "aixwebRepo.LoadCreativeLabels err:%v", err)
	}
	pbRule := data.PGLabelRuleToPB(rule)
	// 过滤一下规则里面的标签，只保留serial层级
	var ruleLabels []*pb.AssetLabel
	for _, label := range pbRule.Labels {
		if creativeLabels.HasSerialLevelLabel(label.GetFirstLabel(), label.GetSecondLabel()) {
			ruleLabels = append(ruleLabels, label)
		}
	}

	var assetNames []string
	for _, a := range assets {
		assetNames = append(assetNames, a.AssetName)
	}
	// 去重
	assetNames = funk.UniqString(assetNames)
	// 查asset层级的标签
	assetLabelMap, err := repo.GetAssetLevelLabelRules(ctx, req.GetGameCode(), assetNames)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.GetAssetLevelLabelRules err:%v", err)
	}

	for _, a := range assets {
		t := convertAllChannelAssetToPB(a)
		t.Labels = ruleLabels
		// 素材层级的标签，追加
		if assetLabel, ok := assetLabelMap[t.GetAssetName()]; ok {
			for _, label := range assetLabel.Labels {
				if creativeLabels.HasAssetLevelLabel(label.FirstLabel, label.SecondLabel) {
					t.Labels = append(t.Labels, &pb.AssetLabel{
						LabelName:   "", // 固定为空
						FirstLabel:  label.FirstLabel,
						SecondLabel: label.SecondLabel,
					})
				}
			}
			// 去重
			t.Labels = uniqPBAssetLabels(t.Labels)
		}

		rsp.Assets = append(rsp.Assets, t)
	}

	rsp.Total = uint32(total)
	return nil
}

// 一段式
var nameRuleOneReg = regexp.MustCompile("^([a-zA-Z0-9\u4e00-\u9fa5+]{1,})[-_\\s]")

// 两段式
var nameRuleTwoReg = regexp.MustCompile("^([a-zA-Z0-9\u4e00-\u9fa5+]{1,}[-_\\s][a-zA-Z0-9\u4e00-\u9fa5+]{1,})[-_\\s]")

// 模糊查询素材名，然后生成标签规则
func genRuleFromSearchAssets(ctx context.Context, gameCode string, names []string) ([]string, error) {
	var rlt []string
	// 查aix library overview表
	var overviews []*model.CreativeOverview
	query := postgresql.GetDBWithContext(ctx).Model(&overviews).Table(model.GetCreativeOverviewTableName(gameCode))
	query.Where("asset_status = ?", arthub.ARTHUB_ASSET_STATUS_NORMAL) // 过滤正常的素材
	query = ormQueryIlikeAny(query, "asset_name", names)
	query.Limit(1000)                      // 1000条够了
	query.Column("asset_id", "asset_name") // 只需要名字
	if err := query.Select(); err != nil {
		return nil, err
	}

	depot, err := data.GetDepotWithContext(ctx, gameCode)
	if err != nil {
		return nil, fmt.Errorf("GetDepotWithContext err:%v", err)
	}
	// 获取素材库的标签规则类型和分割符
	ruleType, _ := repo.GetDepotLabelRuleType(ctx, depot)
	splitReg, err := repo.GetDepotSerialSplitReg(ctx, depot)
	if err != nil {
		log.ErrorContextf(ctx, "genRuleFromSearchAssets repo.GetDepotSerialSplitReg err:%v", err)
		return nil, err
	}

	// 看是否符合标签前缀
	for _, o := range overviews {
		match := getRuleRegMatchName(gameCode, ruleType, splitReg, o.AssetName)
		if match != "" {
			rlt = append(rlt, match)
		}
	}

	// 查渠道素材表
	var assets []*model.AllChannelAssets
	query = postgresql.GetDBWithContext(ctx).Model(&assets).Table(model.GetAllChannelAssetsTableName(gameCode))
	query = ormQueryIlikeAny(query, "asset_name", names)
	query.Limit(1000)                // 1000条够了
	query.Column("id", "asset_name") // 只需要名字
	if err := query.Select(); err != nil {
		return nil, err
	}
	// 看是否符合标签前缀
	for _, a := range assets {
		match := getRuleRegMatchName(gameCode, ruleType, splitReg, a.AssetName)
		if match != "" {
			rlt = append(rlt, match)
		}
	}

	return rlt, nil
}

func ormQueryIlikeAny(q *orm.Query, column string, words []string) *orm.Query {
	var likeWorks []string
	for _, w := range words {
		likeWorks = append(likeWorks, fmt.Sprintf("%%%s%%", w))
	}

	q.Where(column+" ILIKE ANY(?)", pg.Array(likeWorks))
	return q
}

// 根据素材名获取符合标签规则, 包括unicode和前缀模式， 不符合返回空字符串
func getRuleRegMatchName(gameCode string, ruleType int, serialSplitReg *regexp.Regexp, name string) string {
	if ruleType == constant.LabelRuleNotSupport {
		return ""
	}

	// unicode 模式
	if ruleType == constant.LabelRuleUnicode {
		name = removeFileExtension(name)
		// 去掉一些特殊后缀
		for _, suffix := range conf.GetBizConf().SerialRemoveSuffix {
			if strings.HasSuffix(name, suffix) {
				name = strings.TrimSuffix(name, suffix)
				break
			}
		}
		// 分割
		parts := serialSplitReg.Split(name, -1)
		// 取最后一段
		name = utils.LastString(parts)
		// unicode模式检查和归一化
		normalizeName, ok := model.CheckAndNormalizeLabelRuleName(constant.LabelRuleUnicode, name, false)
		if !ok {
			return ""
		}
		return normalizeName
	}

	// pubgm是一段式
	if gameCode == "pubgm" {
		matches := nameRuleOneReg.FindStringSubmatch(name)
		if len(matches) >= 1 && matches[1] != "" {
			return normalizeRuleName(matches[1])
		}

		return ""
	}

	// 两段式
	matches := nameRuleTwoReg.FindStringSubmatch(name)
	if len(matches) >= 1 && matches[1] != "" {
		return normalizeRuleName(matches[1])
	}

	return ""
}

// 标签规则归一化处理， V-123 等价于 V_123 和 V 123
// 已经是处理后的 V-123
func normalizeRuleName(s string) string {
	s = strings.ReplaceAll(s, " ", "_")
	s = strings.ReplaceAll(s, "_", "-")

	return s
}

func convertAllChannelAssetToPB(a *model.AllChannelAssets) *pb.ChannelAsset {
	t := &pb.ChannelAsset{
		Id:               a.ID,
		ChannelType:      a.ChannelType,
		ChannelAccountId: a.ChannelAccountID,
		ChannelAssetId:   a.ChannelAssetID,
		AssetName:        a.AssetName,
		YoutubeId:        a.YoutubeID,
		CreateBy:         a.CreateBy,
		CreateTime:       a.CreateTime,
		UpdateBy:         a.UpdateBy,
		UpdateTime:       a.UpdateTime,
		Sdate:            a.SDate,
		ImpressionDate:   a.ImpressionDate,
		ImpressionStatus: getImpressionStatusByImpressionDate(a.ImpressionDate),
	}

	return t
}

// 拉取游戏下的所有非机器打标的一级标签(包括serial和asset层级)
func getAllNoneIntelligentFirstLabels(ctx context.Context, gameCode string) ([]string, error) {
	labels, err := aixwebRepo.GetGameCreativeLabels(ctx, gameCode)
	if err != nil {
		return nil, err
	}

	var firstLabels []string
	for _, l := range labels {
		if l.LabelMethod != constant.LabelMethodIntelligent {
			firstLabels = append(firstLabels, l.Name)
		}
	}
	return funk.UniqString(firstLabels), nil
}

// 删除重复的标签
func removeRuleDuplicateLables(rule *pb.LabelRule) {
	var labels []*pb.AssetLabel
	uniq := make(map[string]bool)
	for _, label := range rule.GetLabels() {
		key := fmt.Sprintf("%v,%v,%v", label.LabelName, label.FirstLabel, label.SecondLabel)
		if uniq[key] == false {
			labels = append(labels, label)
		}
		uniq[key] = true
	}

	rule.Labels = labels
}

func uniqPBAssetLabels(labels []*pb.AssetLabel) []*pb.AssetLabel {
	var final []*pb.AssetLabel
	uniq := make(map[string]bool)
	for _, label := range labels {
		key := fmt.Sprintf("%v,%v,%v", label.LabelName, label.FirstLabel, label.SecondLabel)
		if uniq[key] == false {
			final = append(final, label)
		}
		uniq[key] = true
	}

	return final
}
