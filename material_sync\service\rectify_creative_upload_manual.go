package service

import (
	"context"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// RectifyCreativeUploadManual ...
func RectifyCreativeUploadManual(ctx *gin.Context, _ *material_sync.RectifyCreativeUploadManualReq, _ *material_sync.RectifyCreativeUploadManualRsp) error {
	depots, err := getArthubDepotsForRectifyCreativeUploadManual(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getArthubDepots failed: %s", err)
		return err
	}

	for _, depot := range depots {
		RectifyCreativeUploadManualForDepot(depot)
	}

	return nil
}

// getArthubDepotsForRectifyCreativeUploadManual 获取depot配置信息
func getArthubDepotsForRectifyCreativeUploadManual(ctx context.Context) ([]pgmodel.ArthubDepot, error) {
	model := postgresql.GetDBWithContext(ctx).Model(&pgmodel.ArthubDepot{})
	model.Column("game_code")
	model.Column("depot_id")

	var depots []pgmodel.ArthubDepot
	err := model.Select(&depots)
	if err != nil {
		return nil, err
	}

	return depots, nil
}

// RectifyCreativeUploadManualForDepot ...
func RectifyCreativeUploadManualForDepot(depot pgmodel.ArthubDepot) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "RectifyCreativeUploadManualForGameCode start, game code: %s", depot.GameCode)
	st := time.Now()

	pg_query := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeMediaDirectory{})
	table_name := pgmodel.GetCreativeMediaDirectoryTableName(depot.GameCode)
	pg_query.Table(table_name)
	pg_query.Where("id = ?", depot.GameCode)
	pg_query.Set("id = ?", depot.GameCode)
	pg_query.Set("name=?", depot.GameCode)
	pg_query.Set("parent_id = ''")
	pg_query.Set("parent_name=''")
	pg_query.Set("full_path_id = ''")
	pg_query.Set("full_path_name=''")
	_, err := pg_query.Update()
	if err != nil {
		log.ErrorContextf(ctx, "update media directory root record failed: %s", err)
		return
	}

	cost := time.Since(st)
	log.InfoContextf(ctx, "RectifyCreativeUploadManualForGameCode end, game code: %s, cost: %s", depot.GameCode, cost)
}
