**Generated by protoc-gen-md. DO NOT EDIT.**
[TOC]
# 获取audience折线图数据, POST, /api/v1/audience_overview/audience_plots
test curl:
```shell
curl 'http://target/api/v1/audience_overview/audience_plots' -H 'Content-Type:application/json' -d '{"filters":[{"enums":[{"addition1":"","addition2":"","enum_id":"","name":""}],"filter_id":"","filter_type":0,"name":"","time_span":{"end_time":"","start_time":""}}],"metric_field":""}'
```
#### audience plots req
parameter explain:
```yaml
AudiencePlotsReq: # 获取audience折线图数据, POST, /api/v1/audience_overview/audience_plots
  filters: # 客户端选中的筛选器
  - name: "" # 名称
    filter_id: "" # 过滤器ID
    filter_type: 0 # 过滤器类型，1-时间跨度；2-枚举值
    time_span: # 时间跨度
      start_time: "" # 开始时间
      end_time: "" # 结束时间
    enums: # 枚举列表
    - name: "" # 枚举名
      enum_id: "" # 枚举ID
      addition1: "" # 附加字段1，当filter_id为country时表示region
      addition2: "" # 附加字段2，当filter_id为country时表示region_name
  metric_field: "" # 需要返回的metric字段信息

```
data example:
```json
{
    "filters": [
        {
            "enums": [
                {
                    "addition1": "",
                    "addition2": "",
                    "enum_id": "",
                    "name": ""
                }
            ],
            "filter_id": "",
            "filter_type": 0,
            "name": "",
            "time_span": {
                "end_time": "",
                "start_time": ""
            }
        }
    ],
    "metric_field": ""
}
```
#### audience plots rsp
parameter explain:
```yaml
AudiencePlotsRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  audiences: # audience列表
  - audience_name: "" # audience name
    campaigns: # campaign list
    - campaign_id: "" # campaign_id
      campaign_name: "" # campaign_name
    metrics: # metric list
    - date_time: "" # date time
      ipm: 0.0 # ipm
      ctr: 0.0 # ctr
      cvr: 0.0 # cvr
      cpc: 0.0 # cpc
      cpm: 0.0 # cpm
      cpi: 0.0 # cpi
      cpa: 0.0 # cpa
      realtime_cohort_register_rate: 0.0 # realtime_cohort_register_rate
      realtime_cpi: 0.0 # realtime_cpi
      realtime_d1_roas: 0.0 # realtime_d1_roas
      offline_d2_cohort_register_rate: 0.0 # offline_d2_cohort_register_rate
      offline_d3_cohort_register_rate: 0.0 # offline_d3_cohort_register_rate
      offline_d7_cohort_register_rate: 0.0 # offline_d7_cohort_register_rate
      offline_d14_cohort_register_rate: 0.0 # offline_d14_cohort_register_rate
      offline_d2_retention_rate: 0.0 # offline_d2_retention_rate
      offline_d3_retention_rate: 0.0 # offline_d3_retention_rate
      offline_d7_retention_rate: 0.0 # offline_d7_retention_rate
      offline_d14_retention_rate: 0.0 # offline_d14_retention_rate
      offline_d2_roas: 0.0 # offline_d2_roas
      offline_d3_roas: 0.0 # offline_d3_roas
      offline_d7_roas: 0.0 # offline_d7_roas
      offline_d14_roas: 0.0 # offline_d14_roas
      impressions: 0.0 # impressions
      clicks: 0.0 # clicks
      spend: 0.0 # spend
      conversions: 0.0 # conversions
      interactions: 0.0 # interactions
      installs: 0.0 # installs
      in_app_action: 0.0 # in_app_action
      campaign_daily_budget: 0.0 # campaign_daily_budget
      strategy_total_budget: 0.0 # strategy_total_budget
      realtime_cohort_register: 0.0 # realtime_cohort_register
      realtime_install: 0.0 # realtime_install
      realtime_spend: 0.0 # realtime_spend
      realtime_cohort_revenue: 0.0 # realtime_cohort_revenue
      realtime_d1_cohort_revenue: 0.0 # realtime_d1_cohort_revenue
      realtime_dau: 0.0 # realtime_dau
      media_daily_budget: 0.0 # media_daily_budget
      media_bid_amount: 0.0 # media_bid_amount
      offline_install: 0.0 # offline_install
      offline_d1_cohort_register: 0.0 # offline_d1_cohort_register
      offline_d2_cohort_register: 0.0 # offline_d2_cohort_register
      offline_d3_cohort_register: 0.0 # offline_d3_cohort_register
      offline_d7_cohort_register: 0.0 # offline_d7_cohort_register
      offline_d14_cohort_register: 0.0 # offline_d14_cohort_register
      offline_d30_cohort_register: 0.0 # offline_d30_cohort_register
      offline_d60_cohort_register: 0.0 # offline_d60_cohort_register
      offline_d90_cohort_register: 0.0 # offline_d90_cohort_register
      offline_d120_cohort_register: 0.0 # offline_d120_cohort_register
      offline_d150_cohort_register: 0.0 # offline_d150_cohort_register
      offline_d180_cohort_register: 0.0 # offline_d180_cohort_register
      offline_d1_retention: 0.0 # offline_d1_retention
      offline_d2_retention: 0.0 # offline_d2_retention
      offline_d3_retention: 0.0 # offline_d3_retention
      offline_d7_retention: 0.0 # offline_d7_retention
      offline_d14_retention: 0.0 # offline_d14_retention
      offline_d30_retention: 0.0 # offline_d30_retention
      offline_d60_retention: 0.0 # offline_d60_retention
      offline_d90_retention: 0.0 # offline_d90_retention
      offline_d120_retention: 0.0 # offline_d120_retention
      offline_d150_retention: 0.0 # offline_d150_retention
      offline_d180_retention: 0.0 # offline_d180_retention
      offline_d1_cohort_revenue: 0.0 # offline_d1_cohort_revenue
      offline_d2_cohort_revenue: 0.0 # offline_d2_cohort_revenue
      offline_d3_cohort_revenue: 0.0 # offline_d3_cohort_revenue
      offline_d7_cohort_revenue: 0.0 # offline_d7_cohort_revenue
      offline_d14_cohort_revenue: 0.0 # offline_d14_cohort_revenue
      offline_d30_cohort_revenue: 0.0 # offline_d30_cohort_revenue
      offline_d60_cohort_revenue: 0.0 # offline_d60_cohort_revenue
      offline_d90_cohort_revenue: 0.0 # offline_d90_cohort_revenue
      offline_d120_cohort_revenue: 0.0 # offline_d120_cohort_revenue
      offline_d150_cohort_revenue: 0.0 # offline_d150_cohort_revenue
      offline_d180_cohort_revenue: 0.0 # offline_d180_cohort_revenue
      offline_spend: 0.0 # offline_spend

```
data example:
```json
{
    "audiences": [
        {
            "audience_name": "",
            "campaigns": [
                {
                    "campaign_id": "",
                    "campaign_name": ""
                }
            ],
            "metrics": [
                {
                    "campaign_daily_budget": 0,
                    "clicks": 0,
                    "conversions": 0,
                    "cpa": 0,
                    "cpc": 0,
                    "cpi": 0,
                    "cpm": 0,
                    "ctr": 0,
                    "cvr": 0,
                    "date_time": "",
                    "impressions": 0,
                    "in_app_action": 0,
                    "installs": 0,
                    "interactions": 0,
                    "ipm": 0,
                    "media_bid_amount": 0,
                    "media_daily_budget": 0,
                    "offline_d120_cohort_register": 0,
                    "offline_d120_cohort_revenue": 0,
                    "offline_d120_retention": 0,
                    "offline_d14_cohort_register": 0,
                    "offline_d14_cohort_register_rate": 0,
                    "offline_d14_cohort_revenue": 0,
                    "offline_d14_retention": 0,
                    "offline_d14_retention_rate": 0,
                    "offline_d14_roas": 0,
                    "offline_d150_cohort_register": 0,
                    "offline_d150_cohort_revenue": 0,
                    "offline_d150_retention": 0,
                    "offline_d180_cohort_register": 0,
                    "offline_d180_cohort_revenue": 0,
                    "offline_d180_retention": 0,
                    "offline_d1_cohort_register": 0,
                    "offline_d1_cohort_revenue": 0,
                    "offline_d1_retention": 0,
                    "offline_d2_cohort_register": 0,
                    "offline_d2_cohort_register_rate": 0,
                    "offline_d2_cohort_revenue": 0,
                    "offline_d2_retention": 0,
                    "offline_d2_retention_rate": 0,
                    "offline_d2_roas": 0,
                    "offline_d30_cohort_register": 0,
                    "offline_d30_cohort_revenue": 0,
                    "offline_d30_retention": 0,
                    "offline_d3_cohort_register": 0,
                    "offline_d3_cohort_register_rate": 0,
                    "offline_d3_cohort_revenue": 0,
                    "offline_d3_retention": 0,
                    "offline_d3_retention_rate": 0,
                    "offline_d3_roas": 0,
                    "offline_d60_cohort_register": 0,
                    "offline_d60_cohort_revenue": 0,
                    "offline_d60_retention": 0,
                    "offline_d7_cohort_register": 0,
                    "offline_d7_cohort_register_rate": 0,
                    "offline_d7_cohort_revenue": 0,
                    "offline_d7_retention": 0,
                    "offline_d7_retention_rate": 0,
                    "offline_d7_roas": 0,
                    "offline_d90_cohort_register": 0,
                    "offline_d90_cohort_revenue": 0,
                    "offline_d90_retention": 0,
                    "offline_install": 0,
                    "offline_spend": 0,
                    "realtime_cohort_register": 0,
                    "realtime_cohort_register_rate": 0,
                    "realtime_cohort_revenue": 0,
                    "realtime_cpi": 0,
                    "realtime_d1_cohort_revenue": 0,
                    "realtime_d1_roas": 0,
                    "realtime_dau": 0,
                    "realtime_install": 0,
                    "realtime_spend": 0,
                    "spend": 0,
                    "strategy_total_budget": 0
                }
            ]
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 获取audience表格数据, POST, /api/v1/audience_overview/audience_tables
test curl:
```shell
curl 'http://target/api/v1/audience_overview/audience_tables' -H 'Content-Type:application/json' -d '{"filters":[{"enums":[{"addition1":"","addition2":"","enum_id":"","name":""}],"filter_id":"","filter_type":0,"name":"","time_span":{"end_time":"","start_time":""}}]}'
```
#### audience tables req
parameter explain:
```yaml
AudienceTablesReq: # 获取audience表格数据, POST, /api/v1/audience_overview/audience_tables
  filters: # 客户端选中的筛选器
  - name: "" # 名称
    filter_id: "" # 过滤器ID
    filter_type: 0 # 过滤器类型，1-时间跨度；2-枚举值
    time_span: # 时间跨度
      start_time: "" # 开始时间
      end_time: "" # 结束时间
    enums: # 枚举列表
    - name: "" # 枚举名
      enum_id: "" # 枚举ID
      addition1: "" # 附加字段1，当filter_id为country时表示region
      addition2: "" # 附加字段2，当filter_id为country时表示region_name

```
data example:
```json
{
    "filters": [
        {
            "enums": [
                {
                    "addition1": "",
                    "addition2": "",
                    "enum_id": "",
                    "name": ""
                }
            ],
            "filter_id": "",
            "filter_type": 0,
            "name": "",
            "time_span": {
                "end_time": "",
                "start_time": ""
            }
        }
    ]
}
```
#### audience tables rsp
parameter explain:
```yaml
AudienceTablesRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  audiences: # audience列表
  - audience_name: "" # audience name
    campaigns: # campaign list
    - campaign_id: "" # campaign_id
      campaign_name: "" # campaign_name
    metric: # metric info
      date_time: "" # date time
      ipm: 0.0 # ipm
      ctr: 0.0 # ctr
      cvr: 0.0 # cvr
      cpc: 0.0 # cpc
      cpm: 0.0 # cpm
      cpi: 0.0 # cpi
      cpa: 0.0 # cpa
      realtime_cohort_register_rate: 0.0 # realtime_cohort_register_rate
      realtime_cpi: 0.0 # realtime_cpi
      realtime_d1_roas: 0.0 # realtime_d1_roas
      offline_d2_cohort_register_rate: 0.0 # offline_d2_cohort_register_rate
      offline_d3_cohort_register_rate: 0.0 # offline_d3_cohort_register_rate
      offline_d7_cohort_register_rate: 0.0 # offline_d7_cohort_register_rate
      offline_d14_cohort_register_rate: 0.0 # offline_d14_cohort_register_rate
      offline_d2_retention_rate: 0.0 # offline_d2_retention_rate
      offline_d3_retention_rate: 0.0 # offline_d3_retention_rate
      offline_d7_retention_rate: 0.0 # offline_d7_retention_rate
      offline_d14_retention_rate: 0.0 # offline_d14_retention_rate
      offline_d2_roas: 0.0 # offline_d2_roas
      offline_d3_roas: 0.0 # offline_d3_roas
      offline_d7_roas: 0.0 # offline_d7_roas
      offline_d14_roas: 0.0 # offline_d14_roas
      impressions: 0.0 # impressions
      clicks: 0.0 # clicks
      spend: 0.0 # spend
      conversions: 0.0 # conversions
      interactions: 0.0 # interactions
      installs: 0.0 # installs
      in_app_action: 0.0 # in_app_action
      campaign_daily_budget: 0.0 # campaign_daily_budget
      strategy_total_budget: 0.0 # strategy_total_budget
      realtime_cohort_register: 0.0 # realtime_cohort_register
      realtime_install: 0.0 # realtime_install
      realtime_spend: 0.0 # realtime_spend
      realtime_cohort_revenue: 0.0 # realtime_cohort_revenue
      realtime_d1_cohort_revenue: 0.0 # realtime_d1_cohort_revenue
      realtime_dau: 0.0 # realtime_dau
      media_daily_budget: 0.0 # media_daily_budget
      media_bid_amount: 0.0 # media_bid_amount
      offline_install: 0.0 # offline_install
      offline_d1_cohort_register: 0.0 # offline_d1_cohort_register
      offline_d2_cohort_register: 0.0 # offline_d2_cohort_register
      offline_d3_cohort_register: 0.0 # offline_d3_cohort_register
      offline_d7_cohort_register: 0.0 # offline_d7_cohort_register
      offline_d14_cohort_register: 0.0 # offline_d14_cohort_register
      offline_d30_cohort_register: 0.0 # offline_d30_cohort_register
      offline_d60_cohort_register: 0.0 # offline_d60_cohort_register
      offline_d90_cohort_register: 0.0 # offline_d90_cohort_register
      offline_d120_cohort_register: 0.0 # offline_d120_cohort_register
      offline_d150_cohort_register: 0.0 # offline_d150_cohort_register
      offline_d180_cohort_register: 0.0 # offline_d180_cohort_register
      offline_d1_retention: 0.0 # offline_d1_retention
      offline_d2_retention: 0.0 # offline_d2_retention
      offline_d3_retention: 0.0 # offline_d3_retention
      offline_d7_retention: 0.0 # offline_d7_retention
      offline_d14_retention: 0.0 # offline_d14_retention
      offline_d30_retention: 0.0 # offline_d30_retention
      offline_d60_retention: 0.0 # offline_d60_retention
      offline_d90_retention: 0.0 # offline_d90_retention
      offline_d120_retention: 0.0 # offline_d120_retention
      offline_d150_retention: 0.0 # offline_d150_retention
      offline_d180_retention: 0.0 # offline_d180_retention
      offline_d1_cohort_revenue: 0.0 # offline_d1_cohort_revenue
      offline_d2_cohort_revenue: 0.0 # offline_d2_cohort_revenue
      offline_d3_cohort_revenue: 0.0 # offline_d3_cohort_revenue
      offline_d7_cohort_revenue: 0.0 # offline_d7_cohort_revenue
      offline_d14_cohort_revenue: 0.0 # offline_d14_cohort_revenue
      offline_d30_cohort_revenue: 0.0 # offline_d30_cohort_revenue
      offline_d60_cohort_revenue: 0.0 # offline_d60_cohort_revenue
      offline_d90_cohort_revenue: 0.0 # offline_d90_cohort_revenue
      offline_d120_cohort_revenue: 0.0 # offline_d120_cohort_revenue
      offline_d150_cohort_revenue: 0.0 # offline_d150_cohort_revenue
      offline_d180_cohort_revenue: 0.0 # offline_d180_cohort_revenue
      offline_spend: 0.0 # offline_spend

```
data example:
```json
{
    "audiences": [
        {
            "audience_name": "",
            "campaigns": [
                {
                    "campaign_id": "",
                    "campaign_name": ""
                }
            ],
            "metric": {
                "campaign_daily_budget": 0,
                "clicks": 0,
                "conversions": 0,
                "cpa": 0,
                "cpc": 0,
                "cpi": 0,
                "cpm": 0,
                "ctr": 0,
                "cvr": 0,
                "date_time": "",
                "impressions": 0,
                "in_app_action": 0,
                "installs": 0,
                "interactions": 0,
                "ipm": 0,
                "media_bid_amount": 0,
                "media_daily_budget": 0,
                "offline_d120_cohort_register": 0,
                "offline_d120_cohort_revenue": 0,
                "offline_d120_retention": 0,
                "offline_d14_cohort_register": 0,
                "offline_d14_cohort_register_rate": 0,
                "offline_d14_cohort_revenue": 0,
                "offline_d14_retention": 0,
                "offline_d14_retention_rate": 0,
                "offline_d14_roas": 0,
                "offline_d150_cohort_register": 0,
                "offline_d150_cohort_revenue": 0,
                "offline_d150_retention": 0,
                "offline_d180_cohort_register": 0,
                "offline_d180_cohort_revenue": 0,
                "offline_d180_retention": 0,
                "offline_d1_cohort_register": 0,
                "offline_d1_cohort_revenue": 0,
                "offline_d1_retention": 0,
                "offline_d2_cohort_register": 0,
                "offline_d2_cohort_register_rate": 0,
                "offline_d2_cohort_revenue": 0,
                "offline_d2_retention": 0,
                "offline_d2_retention_rate": 0,
                "offline_d2_roas": 0,
                "offline_d30_cohort_register": 0,
                "offline_d30_cohort_revenue": 0,
                "offline_d30_retention": 0,
                "offline_d3_cohort_register": 0,
                "offline_d3_cohort_register_rate": 0,
                "offline_d3_cohort_revenue": 0,
                "offline_d3_retention": 0,
                "offline_d3_retention_rate": 0,
                "offline_d3_roas": 0,
                "offline_d60_cohort_register": 0,
                "offline_d60_cohort_revenue": 0,
                "offline_d60_retention": 0,
                "offline_d7_cohort_register": 0,
                "offline_d7_cohort_register_rate": 0,
                "offline_d7_cohort_revenue": 0,
                "offline_d7_retention": 0,
                "offline_d7_retention_rate": 0,
                "offline_d7_roas": 0,
                "offline_d90_cohort_register": 0,
                "offline_d90_cohort_revenue": 0,
                "offline_d90_retention": 0,
                "offline_install": 0,
                "offline_spend": 0,
                "realtime_cohort_register": 0,
                "realtime_cohort_register_rate": 0,
                "realtime_cohort_revenue": 0,
                "realtime_cpi": 0,
                "realtime_d1_cohort_revenue": 0,
                "realtime_d1_roas": 0,
                "realtime_dau": 0,
                "realtime_install": 0,
                "realtime_spend": 0,
                "spend": 0,
                "strategy_total_budget": 0
            }
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 获取audience汇总面板数据, POST, /api/v1/audience_overview/audience_sum_panel
test curl:
```shell
curl 'http://target/api/v1/audience_overview/audience_sum_panel' -H 'Content-Type:application/json' -d '{"filters":[{"enums":[{"addition1":"","addition2":"","enum_id":"","name":""}],"filter_id":"","filter_type":0,"name":"","time_span":{"end_time":"","start_time":""}}]}'
```
#### audience sum panel req
parameter explain:
```yaml
AudienceSumPanelReq: # 获取audience汇总面板数据, POST, /api/v1/audience_overview/audience_sum_panel
  filters: # 客户端选中的筛选器
  - name: "" # 名称
    filter_id: "" # 过滤器ID
    filter_type: 0 # 过滤器类型，1-时间跨度；2-枚举值
    time_span: # 时间跨度
      start_time: "" # 开始时间
      end_time: "" # 结束时间
    enums: # 枚举列表
    - name: "" # 枚举名
      enum_id: "" # 枚举ID
      addition1: "" # 附加字段1，当filter_id为country时表示region
      addition2: "" # 附加字段2，当filter_id为country时表示region_name

```
data example:
```json
{
    "filters": [
        {
            "enums": [
                {
                    "addition1": "",
                    "addition2": "",
                    "enum_id": "",
                    "name": ""
                }
            ],
            "filter_id": "",
            "filter_type": 0,
            "name": "",
            "time_span": {
                "end_time": "",
                "start_time": ""
            }
        }
    ]
}
```
#### audience sum panel rsp
parameter explain:
```yaml
AudienceSumPanelRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  audiences: # audience列表
  - audience_name: "" # audience name
    campaigns: # campaign list
    - campaign_id: "" # campaign_id
      campaign_name: "" # campaign_name
    spend: 0.0 # spend
  sum_spend: 0.0 # spend汇总数据

```
data example:
```json
{
    "audiences": [
        {
            "audience_name": "",
            "campaigns": [
                {
                    "campaign_id": "",
                    "campaign_name": ""
                }
            ],
            "spend": 0
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "sum_spend": 0
}
```
# 获取audience filter列表, POST, /api/v1/audience_overview/audience_filter_list
test curl:
```shell
curl 'http://target/api/v1/audience_overview/audience_filter_list' -H 'Content-Type:application/json' -d '{}'
```
#### audience filter list req
parameter explain:
```yaml
AudienceFilterListReq: # 获取audience filter列表, POST, /api/v1/audience_overview/audience_filter_list

```
data example:
```json
{}
```
#### audience filter list rsp
parameter explain:
```yaml
AudienceFilterListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  filters: # 过滤器列表
  - name: "" # 名称
    filter_id: "" # 过滤器ID
    filter_type: 0 # 过滤器类型，1-时间跨度；2-枚举值
    time_span: # 时间跨度
      start_time: "" # 开始时间
      end_time: "" # 结束时间
    enums: # 枚举列表
    - name: "" # 枚举名
      enum_id: "" # 枚举ID
      addition1: "" # 附加字段1，当filter_id为country时表示region
      addition2: "" # 附加字段2，当filter_id为country时表示region_name

```
data example:
```json
{
    "filters": [
        {
            "enums": [
                {
                    "addition1": "",
                    "addition2": "",
                    "enum_id": "",
                    "name": ""
                }
            ],
            "filter_id": "",
            "filter_type": 0,
            "name": "",
            "time_span": {
                "end_time": "",
                "start_time": ""
            }
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 自测接口, POST, /api/v1/audience_overview/say_hi
test curl:
```shell
curl 'http://target/api/v1/audience_overview/say_hi' -H 'Content-Type:application/json' -d '{"account_id":"","campaign_id":"","limit":0,"offset":0,"update_on_cycle":0}'
```
#### say hi req
parameter explain:
```yaml
SayHiReq: # 自测接口, POST, /api/v1/audience_overview/say_hi
  offset: 0 # 
  limit: 0 # 
  update_on_cycle: 0 # 
  account_id: "" # 
  campaign_id: "" # 

```
data example:
```json
{
    "account_id": "",
    "campaign_id": "",
    "limit": 0,
    "offset": 0,
    "update_on_cycle": 0
}
```
#### say hi rsp
parameter explain:
```yaml
SayHiRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# Server Interface
test curl:
```shell
curl 'http://target' -H 'Content-Type:application/json' -d '{"end_time":"","platform":"","start_time":""}'
```
#### update audience list manul req
parameter explain:
```yaml
UpdateAudienceListManulReq: # 
  start_time: "" # 开始时间
  end_time: "" # 结束时间
  platform: "" # 平台名称: Google, Facebook

```
data example:
```json
{
    "end_time": "",
    "platform": "",
    "start_time": ""
}
```
#### update audience list manul rsp
parameter explain:
```yaml
UpdateAudienceListManulRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 获取audience_metric列表, POST, /api/v1/audience_overview/audience_metrics
test curl:
```shell
curl 'http://target/api/v1/audience_overview/audience_metrics' -H 'Content-Type:application/json' -d '{}'
```
#### audience metrics req
parameter explain:
```yaml
AudienceMetricsReq: # 获取audience_metric列表, POST, /api/v1/audience_overview/audience_metrics

```
data example:
```json
{}
```
#### audience metrics rsp
parameter explain:
```yaml
AudienceMetricsRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  metrics: # audience列表
  - key: "" # Audience中的字段名
    title: "" # 前端显示名称
    format: "" # 显示格式
    type: "" # metric类型
    agg_type: 0 # 聚合类型，1-表示可以累加；2-表示比率类型，不可以累加

```
data example:
```json
{
    "metrics": [
        {
            "agg_type": 0,
            "format": "",
            "key": "",
            "title": "",
            "type": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
