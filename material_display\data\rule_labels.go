package data

import (
	"context"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"github.com/go-pg/pg/v10"
)

// DeleteRuleLabelsByRule 删除标签规则结构化ck外表记录
func DeleteRuleLabelsByRule(ctx context.Context, rule *model.TbAssetLabelRule) error {
	query := postgresql.GetDBWithContext(ctx).Model(&model.TbAssetRuleLabels{})
	query.Where("game_code = ?", rule.GameCode)
	query.Where("rule = ?", rule.Rule)
	query.Where("type = ?", rule.Type)
	_, err := query.Delete()

	return err
}

// InsertRuleLabelsByRule 同步某个标签规则到结构化ck外表记录
func InsertRuleLabelsByRule(ctx context.Context, rule *model.TbAssetLabelRule) error {
	var rows []*model.TbAssetRuleLabels
	for _, label := range rule.Labels {
		row := &model.TbAssetRuleLabels{
			GameCode:    rule.GameCode,
			Rule:        rule.Rule,
			Type:        rule.Type,
			LabelName:   label.LabelName,
			FirstLabel:  label.FirstLabel,
			SecondLabel: label.SecondLabel,
			CreateTime:  utils.GetNowStr(),
			UpdateTime:  utils.GetNowStr(),
			CreateBy:    rule.CreateUser,
			UpdateBy:    rule.CreateUser,
		}

		rows = append(rows, row)
	}

	err := postgresql.Transaction(ctx, func(tx *pg.Tx) error {
		// 先删除之前的标签
		deleteQuery := tx.Model(&model.TbAssetRuleLabels{})
		deleteQuery.Where("game_code = ?", rule.GameCode)
		deleteQuery.Where("rule = ?", rule.Rule)
		deleteQuery.Where("type = ?", rule.Type)
		_, err := deleteQuery.Delete()
		if err != nil {
			return err
		}

		// 再插入
		if len(rows) > 0 {
			insertQuery := tx.Model(&rows)
			_, err = insertQuery.Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})

	return err
}
