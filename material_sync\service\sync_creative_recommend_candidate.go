package service

import (
	"context"
	"fmt"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/parser"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"github.com/go-pg/pg/v10"
	"github.com/go-pg/pg/v10/orm"
)

var language2countrylist map[string]string

func init() {
	language2countrylist = make(map[string]string)
	// 通过表解析获取
	language2countrylist["zh_cn"] = "cn,cx,sg"
	language2countrylist["ar"] = "dz,bh,td,km,dj,ps,iq,jo,kw,lb,ly,mr,ma,om,qa,sa,so,eh,sd,ae,tn,eg,ye"
	language2countrylist["en"] = "as,ag,au,bs,bb,bm,bw,bz,vg,bn,bi,cm,ca,ky,cx,ck,dm,fj,gh,gd,gp,gu,gy,hk,in,ie,jm,ke,ls,lr,mo,mw,mt,nan,nr,nz,ng,nu,nf,mh,pw,pk,pg,ph,rw,sh,kn,lc,pm,vc,sc,sl,sg,za,zw,sd,tk,to,tt,tv,ug,gb,tz,vi,ws,zm,us"
	language2countrylist["ca"] = "ad"
	language2countrylist["pt"] = "ao,br,cv,gq,mz,pt,gw,st"
	language2countrylist["es"] = "ar,bo,cl,co,cr,do,ec,sv,gq,gt,hn,mx,ni,pa,py,pe,pr,es,uy,ve"
	language2countrylist["de"] = "at,be,de,li,lu,ch"
	language2countrylist["bn"] = "bd"
	language2countrylist["nl"] = "be,nl"
	language2countrylist["fr"] = "be,bi,cm,ca,cf,td,km,cg,cd,bj,gq,fr,gf,pf,tf,dj,ga,gp,gn,ht,lu,mg,ml,mc,ne,rw,sh,pm,sn,sc,ch,tg"
	language2countrylist["hr"] = "ba,hr"
	language2countrylist["sr"] = "ba,rs"
	language2countrylist["ms"] = "bn,cx,my,sg"
	language2countrylist["bg"] = "bg"
	language2countrylist["ru"] = "by,kz,kg,ru,ua"
	language2countrylist["ta"] = "lk,sg"
	language2countrylist["zh_tw"] = "tw,hk,mo"
	language2countrylist["el"] = "cy,gr,mk"
	language2countrylist["tr"] = "cy,tr"
	language2countrylist["cs"] = "cz"
	language2countrylist["sk"] = "cz,sk"
	language2countrylist["da"] = "dk"
	language2countrylist["et"] = "ee"
	language2countrylist["fi"] = "fi"
	language2countrylist["sv"] = "fi,se"
	language2countrylist["it"] = "va,it,sm,ch"
	language2countrylist["hu"] = "hu"
	language2countrylist["is"] = "is"
	language2countrylist["hi"] = "in"
	language2countrylist["id"] = "id"
	language2countrylist["iw"] = "il"
	language2countrylist["ja"] = "jp"
	language2countrylist["ko"] = "kr"
	language2countrylist["lv"] = "lv"
	language2countrylist["ro"] = "md,ro"
	language2countrylist["no"] = "no"
	language2countrylist["ur"] = "pk"
	language2countrylist["tl"] = "ph"
	language2countrylist["pl"] = "pl"
	language2countrylist["vi"] = "vn"
	language2countrylist["sl"] = "si"
	language2countrylist["th"] = "th"
	language2countrylist["uk"] = "ua"

	// 补充特殊映射
	language2countrylist["tw"] = "tw,hk,mo" // 台湾使用中文繁体
	language2countrylist["zh"] = "cn,cx,sg" // 简体中文
	language2countrylist["kr"] = "kr"       // 韩语, 兼容业务的错误拼写
	language2countrylist["my"] = "my"       // 马来语
	language2countrylist["ch"] = "cn,cx,sg" // pubgm的简体中文错误书写兼容error
}

// SyncCreativeRecommendCandidate ...
func SyncCreativeRecommendCandidate(ctx context.Context) {
	syncCreativeRecommendCandidateLoop()
}

// syncCreativeRecommendCandidateLoop ...
func syncCreativeRecommendCandidateLoop() {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "syncCreativeRecommendCandidateLoop start")

	st := time.Now()
	game_codes, err := getGameCodes(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getGameCodes failed: %s", err)
		return
	}

	for _, game_code := range game_codes {
		syncCreativeRecommendCandidateForGameCode(game_code)
	}

	duration := time.Since(st)
	log.DebugContextf(ctx, "syncCreativeRecommendCandidateLoop end, cost: %s", duration)
}

// getGameCodes 获取game code列表
func getGameCodes(ctx context.Context) ([]string, error) {
	model := postgresql.GetDBWithContext(ctx).Model(&pgmodel.ArthubDepot{})
	model.Where("priority > 0")
	model.Order("priority desc")
	model.Column("game_code")

	var depots []pgmodel.ArthubDepot
	err := model.Select(&depots)
	if err != nil {
		return nil, err
	}

	var game_codes []string
	for _, depot := range depots {
		game_codes = append(game_codes, depot.GameCode)
	}

	return game_codes, nil
}

// syncCreativeRecommendCandidateForGameCode ...
func syncCreativeRecommendCandidateForGameCode(game_code string) {
	ctx := log.NewSessionIDContext()
	log.InfoContextf(ctx, "syncCreativeRecommendCandidateForGameCode start, game code: %s", game_code)

	st := time.Now()
	err := createCreativeRecommendCandidateForGameCode(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "createCreativeRecommendCandidateForGameCode failed: %s", err)
	}

	err = syncCreativeRecommendCandidateForGameCodeForVideo(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "syncCreativeRecommendCandidateForGameCodeForVideo failed: %s", err)
	}

	err = clearCreativeRecommendCandidateForGameCode(ctx, game_code, st)
	if err != nil {
		log.ErrorContextf(ctx, "clearCreativeRecommendCandidateForGameCode failed: %s", err)
		return
	}

	duration := time.Since(st)
	log.DebugContextf(ctx, "syncCreativeRecommendCandidateForGameCode end, game code: %s, cost: %s", game_code, duration)
}

// createCreativeRecommendCandidateForGameCode ...
func createCreativeRecommendCandidateForGameCode(ctx context.Context, game_code string) error {
	model := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendCandidate{})
	table_name := pgmodel.GetCreativeRecommendCandidateTableName(game_code)
	model.Table(table_name)

	err := model.CreateTable(&orm.CreateTableOptions{IfNotExists: true})
	if err != nil {
		return err
	}

	return nil
}

// syncCreativeRecommendCandidateForGameCodeForVideo ...
func syncCreativeRecommendCandidateForGameCodeForVideo(ctx context.Context, game_code string) error {
	// 获取视频素材
	model := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeOverview{})
	table_overview := pgmodel.GetCreativeOverviewTableName(game_code)
	table_details := pgmodel.GetCreativeAssetDetailsTableName(game_code)
	model.Table(table_overview)
	model.Join(fmt.Sprintf("left join %s as details on creative_overview.asset_id=details.asset_id", table_details))
	pg_in := pg.In([]string{"AVI", "MOV", "MP4", "MPEG", "MPG", "WMV", "OGG", "OGV", "avi", "mov", "mp4", "mpeg", "mpg", "wmv", "ogg", "ogv"})
	model.Where("details.format in (?)", pg_in)
	model.Where("creative_overview.asset_status=1") // 未删除素材
	model.Column("creative_overview.asset_id")
	model.Column("creative_overview.asset_name")
	model.ColumnExpr("details.format as file_format")
	model.ColumnExpr("details.update_date as updated_date")
	model.Column("creative_overview.create_date")
	var overviews []pgmodel.CreativeOverview
	err := model.Select(&overviews)
	if err != nil {
		log.ErrorContextf(ctx, "select overview failed: %s", err)
		return err
	}

	// 结构体转换
	log.DebugContextf(ctx, "get assets number: %d", len(overviews))
	author := "material_sync.sync_creative_recommend_candidate_insert"
	asset_name2candidate := make(map[string]pgmodel.CreativeRecommendCandidate)
	for _, overview := range overviews {
		exist_candidate, ok := asset_name2candidate[overview.AssetName]
		if ok && exist_candidate.AssetUpdatedTime > overview.UpdatedDate {
			continue
		}

		name_info, err := parser.ParseAssetName(game_code, overview.AssetName)
		if err != nil {
			log.WarningContextf(ctx, "parser.ParseAssetName failed: %s, asset name: %s", err, overview.AssetName)
			continue
		}
		if overview.UpdatedDate == "" {
			log.WarningContextf(ctx, "get update date empty: %+v", overview)
			continue
		}
		candidate := pgmodel.CreativeRecommendCandidate{}
		candidate.AssetId = overview.AssetID
		candidate.AssetName = overview.AssetName
		candidate.AssetCreatedTime = overview.CreateDate
		candidate.AssetUpdatedTime = overview.UpdatedDate
		candidate.AssetLanguage = name_info.Language
		candidate.AvailableCountryList = language2countrylist[name_info.Language]
		if candidate.AvailableCountryList == "" {
			log.WarningContextf(ctx, "get unknown language: %s", name_info.Language)
			continue
		}
		candidate.AvailableChannel = 3 // google and facebook
		candidate.FileFormat = overview.FileFormat

		candidate.CreateBy = author
		time_now := time.Now().Format("2006-01-02 15:04:05")
		candidate.CreateTime = time_now
		candidate.UpdateBy = author
		candidate.UpdateTime = time_now

		asset_name2candidate[overview.AssetName] = candidate
	}

	var candidates []pgmodel.CreativeRecommendCandidate
	for _, candidate := range asset_name2candidate {
		candidates = append(candidates, candidate)
	}

	// 插入候选集
	err = insertUpdateCreativeRecommendCandidateForGameCode(ctx, game_code, candidates)
	if err != nil {
		log.ErrorContextf(ctx, "insertUpdateCreativeRecommendCandidateForGameCode failed: %s", err)
	}

	return nil
}

// insertUpdateCreativeRecommendCandidateForGameCode ...
func insertUpdateCreativeRecommendCandidateForGameCode(ctx context.Context, game_code string, candidates []pgmodel.CreativeRecommendCandidate) error {
	if len(candidates) == 0 {
		return nil
	}

	log.DebugContextf(ctx, "get valid assets number: %d", len(candidates))
	author := "material_sync.sync_creative_recommend_candidate_update"
	model := postgresql.GetDBWithContext(ctx).Model(&candidates)
	table_name := pgmodel.GetCreativeRecommendCandidateTableName(game_code)
	model.Table(table_name)
	model.OnConflict("(asset_id) DO Update")
	model.Set("update_by=?", author)
	time_now := time.Now().Format("2006-01-02 15:04:05")
	model.Set("update_time=?", time_now)
	model.Set("available_channel=creative_recommend_candidate.available_channel|excluded.available_channel")
	model.Set("file_format=excluded.file_format")
	model.Set("asset_language=excluded.asset_language")
	model.Set("available_country_list=excluded.available_country_list")
	_, err := model.Insert()
	if err != nil {
		log.ErrorContextf(ctx, "insert failed: %s", err)
		return err
	}

	return nil
}

// clearCreativeRecommendCandidateForGameCode ...
func clearCreativeRecommendCandidateForGameCode(ctx context.Context, game_code string, st time.Time) error {
	pg_query := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendCandidate{})
	pg_query.Table(pgmodel.GetCreativeRecommendCandidateTableName(game_code))
	pg_query.Where("update_time<?", st.Format("2006-01-02 15:04:05"))
	_, err := pg_query.Delete()
	if err != nil {
		return fmt.Errorf("delete expired candidate failed: %s", err)
	}

	return nil
}
