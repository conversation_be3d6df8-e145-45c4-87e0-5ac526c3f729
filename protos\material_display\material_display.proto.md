**Generated by protoc-gen-md. DO NOT EDIT.**
[TOC]
# 素材列表请求, POST, /api/v1/material_display/material_list
test curl:
```shell
curl 'http://target/api/v1/material_display/material_list' -H 'Content-Type:application/json' -d '{"count":0,"directory_id":"","filter_online_status":false,"is_filter_status":0,"offset":0,"online_status":0,"status":0,"uid":0,"upload_state":0,"uuid":"","with_detail":0}'
```
#### material list req
parameter explain:
```yaml
MaterialListReq: # 素材列表请求, POST, /api/v1/material_display/material_list
  uid: 0 # 保留字段
  uuid: "" # 前端生成随机ID
  offset: 0 # 起始偏移
  count: 0 # 拉取数量
  is_filter_status: 0 # 是否按照状态过滤，0-否，1-是
  status: 0 # 过滤状态，is_filter_status=1生效 0-未上传；1-正在上传；2-上传成功；3-上传失败
  directory_id: "" # 目录ID
  upload_state: 0 # (已废弃) 是否上传至广告库，0-否，1-是
  with_detail: 0 # 是否需要拉取详情 0-不需要 1-需要
  online_status: 0 # 在线状态筛选, 0表示不需要筛选
  filter_online_status: false # 是否过滤在线状态

```
data example:
```json
{
    "count": 0,
    "directory_id": "",
    "filter_online_status": false,
    "is_filter_status": 0,
    "offset": 0,
    "online_status": 0,
    "status": 0,
    "uid": 0,
    "upload_state": 0,
    "uuid": "",
    "with_detail": 0
}
```
#### material list rsp
parameter explain:
```yaml
MaterialListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  materials: # 素材列表
  - asset_id: "" # 素材索引
    name: "" # 素材名称
    status: 0 # 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
    upline_date: "" # 上线日期
    offline_date: "" # 素材下线时间
    online_days: 0 # 在线天数
    formate: "" # 素材格式
    duration: "" # 素材时长
    preview_url: "" # 素材预览url
    create_date: "" # 创建时间
    material_ext: # 素材详情
      asset_id: "" # 素材索引
      universal: # 通用
        format: "" # 文件格式
        size: 0 # 文件大小
        updater: "" # 更新者
        update_time: "" # 更新时间
        creator: "" # 创建者
        cover: "" # 封面
      label: # 标签
        manual_first_label: [""] # 人工一级标签
        manual_second_label: [""] # 人工二级标签
        robot_first_label: [""] # 机器一级标签
        robot_second_label: [""] # 机器二级标签
      statistics: # 统计
        take_users: 0 # 取用人数
        take_count: 0 # 取用次数
      video: # 视频
        width: 0 # 宽
        high: 0 # 高
        duration: 0 # 时长
        frame_rate: "" # 帧率
        aspect_ratio: "" # 高宽比
        bit_rate: "" # 比特率
        compression_format: "" # 压缩格式
    asset_status: 0 # 素材在仓库状态, 1-正常, 2-回收站, 3-删除
    online_status: 0 # 素材曝光状态, 0-未上线, 1-上线, 2-下架
    online_date: "" # 素材第一次曝光时间
    full_path_id: "" # 素材路径ID
    full_path_name: "" # 素材路径名称
    aix_uploader: "" # aix平台的素材上传者
  total: 0 # 素材总数

```
data example:
```json
{
    "materials": [
        {
            "aix_uploader": "",
            "asset_id": "",
            "asset_status": 0,
            "create_date": "",
            "duration": "",
            "formate": "",
            "full_path_id": "",
            "full_path_name": "",
            "material_ext": {
                "asset_id": "",
                "label": {
                    "manual_first_label": [
                        ""
                    ],
                    "manual_second_label": [
                        ""
                    ],
                    "robot_first_label": [
                        ""
                    ],
                    "robot_second_label": [
                        ""
                    ]
                },
                "statistics": {
                    "take_count": 0,
                    "take_users": 0
                },
                "universal": {
                    "cover": "",
                    "creator": "",
                    "format": "",
                    "size": 0,
                    "update_time": "",
                    "updater": ""
                },
                "video": {
                    "aspect_ratio": "",
                    "bit_rate": "",
                    "compression_format": "",
                    "duration": 0,
                    "frame_rate": "",
                    "high": 0,
                    "width": 0
                }
            },
            "name": "",
            "offline_date": "",
            "online_date": "",
            "online_days": 0,
            "online_status": 0,
            "preview_url": "",
            "status": 0,
            "upline_date": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 获取素材详情, POST, /api/v1/material_display/get_material_info
test curl:
```shell
curl 'http://target/api/v1/material_display/get_material_info' -H 'Content-Type:application/json' -d '{"asset_id":"","uid":0,"uuid":""}'
```
#### get material info req
parameter explain:
```yaml
GetMaterialInfoReq: # 获取素材详情, POST, /api/v1/material_display/get_material_info
  uid: 0 # 
  uuid: "" # 
  asset_id: "" # 唯一索引

```
data example:
```json
{
    "asset_id": "",
    "uid": 0,
    "uuid": ""
}
```
#### get material info rsp
parameter explain:
```yaml
GetMaterialInfoRsp: # 
  result: # 
    error_code: 0 # 
    error_message: "" # 
  material_ext: # 
    asset_id: "" # 素材索引
    universal: # 通用
      format: "" # 文件格式
      size: 0 # 文件大小
      updater: "" # 更新者
      update_time: "" # 更新时间
      creator: "" # 创建者
      cover: "" # 封面
    label: # 标签
      manual_first_label: [""] # 人工一级标签
      manual_second_label: [""] # 人工二级标签
      robot_first_label: [""] # 机器一级标签
      robot_second_label: [""] # 机器二级标签
    statistics: # 统计
      take_users: 0 # 取用人数
      take_count: 0 # 取用次数
    video: # 视频
      width: 0 # 宽
      high: 0 # 高
      duration: 0 # 时长
      frame_rate: "" # 帧率
      aspect_ratio: "" # 高宽比
      bit_rate: "" # 比特率
      compression_format: "" # 压缩格式

```
data example:
```json
{
    "material_ext": {
        "asset_id": "",
        "label": {
            "manual_first_label": [
                ""
            ],
            "manual_second_label": [
                ""
            ],
            "robot_first_label": [
                ""
            ],
            "robot_second_label": [
                ""
            ]
        },
        "statistics": {
            "take_count": 0,
            "take_users": 0
        },
        "universal": {
            "cover": "",
            "creator": "",
            "format": "",
            "size": 0,
            "update_time": "",
            "updater": ""
        },
        "video": {
            "aspect_ratio": "",
            "bit_rate": "",
            "compression_format": "",
            "duration": 0,
            "frame_rate": "",
            "high": 0,
            "width": 0
        }
    },
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 批量获取素材详情, POST, /api/v1/material_display/bt_get_material_info
test curl:
```shell
curl 'http://target/api/v1/material_display/bt_get_material_info' -H 'Content-Type:application/json' -d '{"asset_id_list":[""],"uid":0,"uuid":""}'
```
#### bt get material info req
parameter explain:
```yaml
BtGetMaterialInfoReq: # 批量获取素材详情, POST, /api/v1/material_display/bt_get_material_info
  uid: 0 # 
  uuid: "" # 
  asset_id_list: [""] # 唯一索引

```
data example:
```json
{
    "asset_id_list": [
        ""
    ],
    "uid": 0,
    "uuid": ""
}
```
#### bt get material info rsp
parameter explain:
```yaml
BtGetMaterialInfoRsp: # 
  result: # 
    error_code: 0 # 
    error_message: "" # 
  material_ext_list: # 
  - asset_id: "" # 素材索引
    universal: # 通用
      format: "" # 文件格式
      size: 0 # 文件大小
      updater: "" # 更新者
      update_time: "" # 更新时间
      creator: "" # 创建者
      cover: "" # 封面
    label: # 标签
      manual_first_label: [""] # 人工一级标签
      manual_second_label: [""] # 人工二级标签
      robot_first_label: [""] # 机器一级标签
      robot_second_label: [""] # 机器二级标签
    statistics: # 统计
      take_users: 0 # 取用人数
      take_count: 0 # 取用次数
    video: # 视频
      width: 0 # 宽
      high: 0 # 高
      duration: 0 # 时长
      frame_rate: "" # 帧率
      aspect_ratio: "" # 高宽比
      bit_rate: "" # 比特率
      compression_format: "" # 压缩格式

```
data example:
```json
{
    "material_ext_list": [
        {
            "asset_id": "",
            "label": {
                "manual_first_label": [
                    ""
                ],
                "manual_second_label": [
                    ""
                ],
                "robot_first_label": [
                    ""
                ],
                "robot_second_label": [
                    ""
                ]
            },
            "statistics": {
                "take_count": 0,
                "take_users": 0
            },
            "universal": {
                "cover": "",
                "creator": "",
                "format": "",
                "size": 0,
                "update_time": "",
                "updater": ""
            },
            "video": {
                "aspect_ratio": "",
                "bit_rate": "",
                "compression_format": "",
                "duration": 0,
                "frame_rate": "",
                "high": 0,
                "width": 0
            }
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 素材搜索, POST, /api/v1/material_display/search_material
test curl:
```shell
curl 'http://target/api/v1/material_display/search_material' -H 'Content-Type:application/json' -d '{"aix_uploader":"","asset_ids":[""],"count":0,"directory_id":"","filter_online_status":false,"is_ad":false,"labels":[{"first_label":"","label_name":"","second_label":""}],"names":[""],"offset":0,"online_status":0,"search_type":0,"status":0,"text":"","uid":0,"upload_state":0,"uuid":"","with_detail":0}'
```
#### search materials req
parameter explain:
```yaml
SearchMaterialsReq: # 素材搜索, POST, /api/v1/material_display/search_material
  uid: 0 # 
  uuid: "" # 
  text: "" # 文本搜索, 为空表示搜索全部
  status: 0 # 状态搜索(已废弃)
  offset: 0 # 起始偏移
  count: 0 # 拉取数量
  directory_id: "" # 目录ID
  upload_state: 0 # 是否上传至广告库，0-否，1-是(已废弃)
  with_detail: 0 # 是否需要拉取详情 0-不需要 1-需要
  is_ad: false # 是否是搜索广告库; false: 搜索素材库，true: 搜索广告库
  online_status: 0 # 在线状态筛选, 0表示不需要筛选
  filter_online_status: false # 是否过滤在线状态
  search_type: 0 # 搜索类型, 1-搜索单个名称(模糊搜索); 2-搜索多个名称(精确搜索); 3-搜索多个素材ID
  names: [""] # 搜索名称, 为空表示搜索全部
  asset_ids: [""] # 搜索素材ID列表, 为空表示搜索全部
  aix_uploader: "" # aix平台的上传人, 空表示不进行过滤
  labels: # 需要搜索的标签列表, 为空表示不需要搜索
  - label_name: "" # 标签名称
    first_label: "" # 一级标签
    second_label: "" # 二级标签

```
data example:
```json
{
    "aix_uploader": "",
    "asset_ids": [
        ""
    ],
    "count": 0,
    "directory_id": "",
    "filter_online_status": false,
    "is_ad": false,
    "labels": [
        {
            "first_label": "",
            "label_name": "",
            "second_label": ""
        }
    ],
    "names": [
        ""
    ],
    "offset": 0,
    "online_status": 0,
    "search_type": 0,
    "status": 0,
    "text": "",
    "uid": 0,
    "upload_state": 0,
    "uuid": "",
    "with_detail": 0
}
```
#### search materials rsp
parameter explain:
```yaml
SearchMaterialsRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  materials: # 素材列表
  - asset_id: "" # 素材索引
    name: "" # 素材名称
    status: 0 # 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
    upline_date: "" # 上线日期
    offline_date: "" # 素材下线时间
    online_days: 0 # 在线天数
    formate: "" # 素材格式
    duration: "" # 素材时长
    preview_url: "" # 素材预览url
    create_date: "" # 创建时间
    material_ext: # 素材详情
      asset_id: "" # 素材索引
      universal: # 通用
        format: "" # 文件格式
        size: 0 # 文件大小
        updater: "" # 更新者
        update_time: "" # 更新时间
        creator: "" # 创建者
        cover: "" # 封面
      label: # 标签
        manual_first_label: [""] # 人工一级标签
        manual_second_label: [""] # 人工二级标签
        robot_first_label: [""] # 机器一级标签
        robot_second_label: [""] # 机器二级标签
      statistics: # 统计
        take_users: 0 # 取用人数
        take_count: 0 # 取用次数
      video: # 视频
        width: 0 # 宽
        high: 0 # 高
        duration: 0 # 时长
        frame_rate: "" # 帧率
        aspect_ratio: "" # 高宽比
        bit_rate: "" # 比特率
        compression_format: "" # 压缩格式
    asset_status: 0 # 素材在仓库状态, 1-正常, 2-回收站, 3-删除
    online_status: 0 # 素材曝光状态, 0-未上线, 1-上线, 2-下架
    online_date: "" # 素材第一次曝光时间
    full_path_id: "" # 素材路径ID
    full_path_name: "" # 素材路径名称
    aix_uploader: "" # aix平台的素材上传者
  total: 0 # 素材总数

```
data example:
```json
{
    "materials": [
        {
            "aix_uploader": "",
            "asset_id": "",
            "asset_status": 0,
            "create_date": "",
            "duration": "",
            "formate": "",
            "full_path_id": "",
            "full_path_name": "",
            "material_ext": {
                "asset_id": "",
                "label": {
                    "manual_first_label": [
                        ""
                    ],
                    "manual_second_label": [
                        ""
                    ],
                    "robot_first_label": [
                        ""
                    ],
                    "robot_second_label": [
                        ""
                    ]
                },
                "statistics": {
                    "take_count": 0,
                    "take_users": 0
                },
                "universal": {
                    "cover": "",
                    "creator": "",
                    "format": "",
                    "size": 0,
                    "update_time": "",
                    "updater": ""
                },
                "video": {
                    "aspect_ratio": "",
                    "bit_rate": "",
                    "compression_format": "",
                    "duration": 0,
                    "frame_rate": "",
                    "high": 0,
                    "width": 0
                }
            },
            "name": "",
            "offline_date": "",
            "online_date": "",
            "online_days": 0,
            "online_status": 0,
            "preview_url": "",
            "status": 0,
            "upline_date": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 拉取目录列表, POST, /api/v1/material_display/directory_list
test curl:
```shell
curl 'http://target/api/v1/material_display/directory_list' -H 'Content-Type:application/json' -d '{"filter_dir_name":"","get_child":0,"limit":0,"offset":0,"parent_id":""}'
```
#### directory list req
parameter explain:
```yaml
DirectoryListReq: # 拉取目录列表, POST, /api/v1/material_display/directory_list
  parent_id: "" # 父目录id
  offset: 0 # 分页偏移量
  limit: 0 # 最大获取数量
  filter_dir_name: "" # 过滤目录名称
  get_child: 0 # 是否包含子节点信息, 0-否, 1-是

```
data example:
```json
{
    "filter_dir_name": "",
    "get_child": 0,
    "limit": 0,
    "offset": 0,
    "parent_id": ""
}
```
#### directory list rsp
parameter explain:
```yaml
DirectoryListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  dirs: # 目录列表
  - id: "" # 目录id
    name: "" # 目录名称
    parent_id: "" # 父目录id
    parent_name: "" # 父目录名称
    create_date: "" # 创建日期
    update_date: "" # 更新日期（索引）
    direct_child_count: 0 # 当前目录包含子目录和元素数量（不递归）
    total_leaf_count: 0 # 当前目录下包含素材数量（递归）
    direct_directory_count: 0 # 当前目录下子目录数量（不递归）
    full_path_name: "" # 当前目录全路径名称
    full_path_id: "" # 当前目录全路径id
    media_directory_id: "" # 媒体素材id
    media_directory_name: "" # 媒体素材名称
    sync_time: "" # 最近同步时间
  total: 0 # 总数

```
data example:
```json
{
    "dirs": [
        {
            "create_date": "",
            "direct_child_count": 0,
            "direct_directory_count": 0,
            "full_path_id": "",
            "full_path_name": "",
            "id": "",
            "media_directory_id": "",
            "media_directory_name": "",
            "name": "",
            "parent_id": "",
            "parent_name": "",
            "sync_time": "",
            "total_leaf_count": 0,
            "update_date": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 获取指定目录的数据信息, POST, /api/v1/material_display/directory_get
test curl:
```shell
curl 'http://target/api/v1/material_display/directory_get' -H 'Content-Type:application/json' -d '{"dir_type":0,"id":""}'
```
#### directory get req
parameter explain:
```yaml
DirectoryGetReq: # 获取指定目录的数据信息, POST, /api/v1/material_display/directory_get
  id: "" # 目录id
  dir_type: 0 # 目录类型 0-普通目录 1-iegg媒体目录

```
data example:
```json
{
    "dir_type": 0,
    "id": ""
}
```
#### directory get rsp
parameter explain:
```yaml
DirectoryGetRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  dir: # 目录信息
    id: "" # 目录id
    name: "" # 目录名称
    parent_id: "" # 父目录id
    parent_name: "" # 父目录名称
    create_date: "" # 创建日期
    update_date: "" # 更新日期（索引）
    direct_child_count: 0 # 当前目录包含子目录和元素数量（不递归）
    total_leaf_count: 0 # 当前目录下包含素材数量（递归）
    direct_directory_count: 0 # 当前目录下子目录数量（不递归）
    full_path_name: "" # 当前目录全路径名称
    full_path_id: "" # 当前目录全路径id
    media_directory_id: "" # 媒体素材id
    media_directory_name: "" # 媒体素材名称
    sync_time: "" # 最近同步时间

```
data example:
```json
{
    "dir": {
        "create_date": "",
        "direct_child_count": 0,
        "direct_directory_count": 0,
        "full_path_id": "",
        "full_path_name": "",
        "id": "",
        "media_directory_id": "",
        "media_directory_name": "",
        "name": "",
        "parent_id": "",
        "parent_name": "",
        "sync_time": "",
        "total_leaf_count": 0,
        "update_date": ""
    },
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 新建目录, POST, /api/v1/material_display/directory_create
test curl:
```shell
curl 'http://target/api/v1/material_display/directory_create' -H 'Content-Type:application/json' -d '{"name":"","parent_id":"","rename_repeated_category":0}'
```
#### directory create req
parameter explain:
```yaml
DirectoryCreateReq: # 新建目录, POST, /api/v1/material_display/directory_create
  name: "" # 目录名称
  parent_id: "" # 父目录id
  rename_repeated_category: 0 # arthub接口参数，是否是新版本

```
data example:
```json
{
    "name": "",
    "parent_id": "",
    "rename_repeated_category": 0
}
```
#### directory create rsp
parameter explain:
```yaml
DirectoryCreateRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  dir: # 目录信息
    id: "" # 目录id
    name: "" # 目录名称
    parent_id: "" # 父目录id
    parent_name: "" # 父目录名称
    create_date: "" # 创建日期
    update_date: "" # 更新日期（索引）
    direct_child_count: 0 # 当前目录包含子目录和元素数量（不递归）
    total_leaf_count: 0 # 当前目录下包含素材数量（递归）
    direct_directory_count: 0 # 当前目录下子目录数量（不递归）
    full_path_name: "" # 当前目录全路径名称
    full_path_id: "" # 当前目录全路径id
    media_directory_id: "" # 媒体素材id
    media_directory_name: "" # 媒体素材名称
    sync_time: "" # 最近同步时间

```
data example:
```json
{
    "dir": {
        "create_date": "",
        "direct_child_count": 0,
        "direct_directory_count": 0,
        "full_path_id": "",
        "full_path_name": "",
        "id": "",
        "media_directory_id": "",
        "media_directory_name": "",
        "name": "",
        "parent_id": "",
        "parent_name": "",
        "sync_time": "",
        "total_leaf_count": 0,
        "update_date": ""
    },
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 素材拖拽到其他目录, POST, /api/v1/material_display/materials_move
test curl:
```shell
curl 'http://target/api/v1/material_display/materials_move' -H 'Content-Type:application/json' -d '{"ids":[""],"other_parent_id":""}'
```
#### material move req
parameter explain:
```yaml
MaterialMoveReq: # 素材拖拽到其他目录, POST, /api/v1/material_display/materials_move
  ids: [""] # 被移动的文件和文件夹ID
  other_parent_id: "" # 移动到的目标文件夹ID

```
data example:
```json
{
    "ids": [
        ""
    ],
    "other_parent_id": ""
}
```
#### material move rsp
parameter explain:
```yaml
MaterialMoveRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  material_items: # 素材移动到其他目录后的素材信息
  - node_id: "" # 当前素材所属目录id
    node_name: "" # 当前素材所属目录名称
    asset_id: "" # 当前素材id（主键）
    asset_name: "" # 当前素材名称
    status: 0 # 素材状态（索引）
    create_date: "" # 素材创建日期（索引）
    upline_date: "" # 素材上线日期（索引）
    offline_date: "" # 素材下线日期（索引）
    online_days: 0 # 素材上线天数
    full_path_name: "" # 当前素材全路径名称
    full_path_id: "" # 当前素材全路径id
  failed_material_ids: [""] # 移动失败的素材id列表

```
data example:
```json
{
    "failed_material_ids": [
        ""
    ],
    "material_items": [
        {
            "asset_id": "",
            "asset_name": "",
            "create_date": "",
            "full_path_id": "",
            "full_path_name": "",
            "node_id": "",
            "node_name": "",
            "offline_date": "",
            "online_days": 0,
            "status": 0,
            "upline_date": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 编辑素材信息(所有参数必须填写!), POST, /api/v1/material_display/set_material_info
test curl:
```shell
curl 'http://target/api/v1/material_display/set_material_info' -H 'Content-Type:application/json' -d '{"material_set_list":[{"asset_id":"","cover":"","label":{"manual_first_label":[""],"manual_second_label":[""],"robot_first_label":[""],"robot_second_label":[""]},"name":""}]}'
```
#### set material info req
parameter explain:
```yaml
SetMaterialInfoReq: # 编辑素材信息(所有参数必须填写!), POST, /api/v1/material_display/set_material_info
  material_set_list: # 待设置的素材
  - asset_id: "" # 素材id
    name: "" # 新名称
    cover: "" # 新封面
    label: # 新标签
      manual_first_label: [""] # 人工一级标签
      manual_second_label: [""] # 人工二级标签
      robot_first_label: [""] # 机器一级标签
      robot_second_label: [""] # 机器二级标签

```
data example:
```json
{
    "material_set_list": [
        {
            "asset_id": "",
            "cover": "",
            "label": {
                "manual_first_label": [
                    ""
                ],
                "manual_second_label": [
                    ""
                ],
                "robot_first_label": [
                    ""
                ],
                "robot_second_label": [
                    ""
                ]
            },
            "name": ""
        }
    ]
}
```
#### set material info rsp
parameter explain:
```yaml
SetMaterialInfoRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  set_res_list: # 设置结果列表
  - asset_id: "" # 素材id
    set_res: 0 # 设置结果 0-失败 1-成功

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "set_res_list": [
        {
            "asset_id": "",
            "set_res": 0
        }
    ]
}
```
# 上传素材至媒体上报, POST, /api/v1/material_display/upload_to_channel
test curl:
```shell
curl 'http://target/api/v1/material_display/upload_to_channel' -H 'Content-Type:application/json' -d '{"asset_media_path":"","assets":[{"assert_name":"","asset_id":"","asset_media_path":"","country":[""],"node_id":"","to_channel":[0]}]}'
```
#### upload to channel req
parameter explain:
```yaml
UploadToChannelReq: # 上传素材至媒体上报, POST, /api/v1/material_display/upload_to_channel
  assets: # 上传素材id
  - asset_id: "" # 素材id
    to_channel: [0] # 1-google 2-facebook
    country: [""] # 国家
    assert_name: "" # 素材名称
    node_id: "" # 所属目录id
    asset_media_path: "" # 素材media路径， 依'/'分割，不包括game_code根目录； 若为空，默认放在根目录
  asset_media_path: "" # 素材media路径， 依'/'分割，不包括game_code根目录； 若为空，默认放在根目录

```
data example:
```json
{
    "asset_media_path": "",
    "assets": [
        {
            "assert_name": "",
            "asset_id": "",
            "asset_media_path": "",
            "country": [
                ""
            ],
            "node_id": "",
            "to_channel": [
                0
            ]
        }
    ]
}
```
#### upload to channel rsp
parameter explain:
```yaml
UploadToChannelRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 拉取用户上传的媒体素材目录列表, POST, /api/v1/material_display/media_directory_list
test curl:
```shell
curl 'http://target/api/v1/material_display/media_directory_list' -H 'Content-Type:application/json' -d '{"limit":0,"offset":0,"parent_id":""}'
```
#### media directory list req
parameter explain:
```yaml
MediaDirectoryListReq: # 拉取用户上传的媒体素材目录列表, POST, /api/v1/material_display/media_directory_list
  parent_id: "" # 父目录id
  offset: 0 # 分页偏移量
  limit: 0 # 最大获取数量

```
data example:
```json
{
    "limit": 0,
    "offset": 0,
    "parent_id": ""
}
```
#### media directory list rsp
parameter explain:
```yaml
MediaDirectoryListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  dirs: # 目录列表
  - id: "" # 目录id
    name: "" # 目录名称
    parent_id: "" # 父目录id
    parent_name: "" # 父目录名称
    create_date: "" # 创建日期
    update_date: "" # 更新日期（索引）
    direct_child_count: 0 # 当前目录包含子目录和元素数量（不递归）
    total_leaf_count: 0 # 当前目录下包含素材数量（递归）
    direct_directory_count: 0 # 当前目录下子目录数量（不递归）
    full_path_name: "" # 当前目录全路径名称
    full_path_id: "" # 当前目录全路径id
    directory_id: "" # arthub目录id
    directory_name: "" # arthub目录名称
  total: 0 # 当前目录下目录总数

```
data example:
```json
{
    "dirs": [
        {
            "create_date": "",
            "direct_child_count": 0,
            "direct_directory_count": 0,
            "directory_id": "",
            "directory_name": "",
            "full_path_id": "",
            "full_path_name": "",
            "id": "",
            "name": "",
            "parent_id": "",
            "parent_name": "",
            "total_leaf_count": 0,
            "update_date": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 获取arthub token信息, POST, /api/v1/material_display/get_depot_token
test curl:
```shell
curl 'http://target/api/v1/material_display/get_depot_token' -H 'Content-Type:application/json' -d '{"game_code_list":[""]}'
```
#### get depot token req
parameter explain:
```yaml
GetDepotTokenReq: # 获取arthub token信息, POST, /api/v1/material_display/get_depot_token
  game_code_list: [""] # game_code

```
data example:
```json
{
    "game_code_list": [
        ""
    ]
}
```
#### get depot token rsp
parameter explain:
```yaml
GetDepotTokenRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  items: # depot信息列表
  - depot_id: "" # depot id
    depot_name: "" # depot名称
    public_token: "" # token信息
    game_code: "" # game_code
    game_name: "" # game_code名称
    arthub_code: "" # arthub_code名称
    type: 0 # 默认 1表示arthub，2表示google drive
    google_service_account: "" # google账户鉴权信息

```
data example:
```json
{
    "items": [
        {
            "arthub_code": "",
            "depot_id": "",
            "depot_name": "",
            "game_code": "",
            "game_name": "",
            "google_service_account": "",
            "public_token": "",
            "type": 0
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 获取arthub素材临时下载url, POST, /api/v1/material_display/generate_arthub_temp_download_url
test curl:
```shell
curl 'http://target/api/v1/material_display/generate_arthub_temp_download_url' -H 'Content-Type:application/json' -d '{"depot_code":"","items":[{"asset_id":"","download_name":""}]}'
```
#### generate arthub temp download url req
parameter explain:
```yaml
GenerateArthubTempDownloadUrlReq: # 获取arthub素材临时下载url, POST, /api/v1/material_display/generate_arthub_temp_download_url
  items: # 素材id列表
  - asset_id: "" # 素材id列表
    download_name: "" # 下载文件重命名，默认为存储文件名
  depot_code: "" # 对应指定game_code

```
data example:
```json
{
    "depot_code": "",
    "items": [
        {
            "asset_id": "",
            "download_name": ""
        }
    ]
}
```
#### generate arthub temp download url rsp
parameter explain:
```yaml
GenerateArthubTempDownloadUrlRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  succeed_items: # 成功生成的arthub临时下载url
  - asset_id: "" # 文件在S3上的路径
    signed_url: "" # 签名后的url
    expire: 0 # signed_url超时时间
  failed_items: # 生成arthub临时下载url失败的素材列表信息
  - asset_id: "" # 文件在S3上的路径
    message: "" # 失败原因

```
data example:
```json
{
    "failed_items": [
        {
            "asset_id": "",
            "message": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "succeed_items": [
        {
            "asset_id": "",
            "expire": 0,
            "signed_url": ""
        }
    ]
}
```
# 获取arthub gamecode信息, POST, /api/v1/material_display/list_depot
test curl:
```shell
curl 'http://target/api/v1/material_display/list_depot' -H 'Content-Type:application/json' -d '{"limit":0,"offset":0}'
```
#### list depot req
parameter explain:
```yaml
ListDepotReq: # 获取arthub gamecode信息, POST, /api/v1/material_display/list_depot
  offset: 0 # 偏移量
  limit: 0 # 每次请求最大数量, 默认返回10 条数据

```
data example:
```json
{
    "limit": 0,
    "offset": 0
}
```
#### list depot rsp
parameter explain:
```yaml
ListDepotRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  items: # depot信息列表
  - depot_id: "" # depot id
    depot_name: "" # depot名称
    game_code: "" # game_code
    game_name: "" # game_code名称
  total: 0 # gamecode总数

```
data example:
```json
{
    "items": [
        {
            "depot_id": "",
            "depot_name": "",
            "game_code": "",
            "game_name": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 上传素材上报, POST, /api/v1/material_display/upload_to_arthub
test curl:
```shell
curl 'http://target/api/v1/material_display/upload_to_arthub' -H 'Content-Type:application/json' -d '{"aix_uploader":"","asset_id_list":[""],"game_code":"","is_meta":false,"metas":[{"asset_id":"","duration":"","high":0,"width":0}]}'
```
#### upload to arthub req
parameter explain:
```yaml
UploadToArthubReq: # 上传素材上报, POST, /api/v1/material_display/upload_to_arthub
  asset_id_list: [""] # 上传素材id
  game_code: "" # 素材game_code，即素材来源
  is_meta: false # 是否提供素材详情信息，false: 无需提供素材详细信息；true: 提供素材详情信息
  metas: # 素材详情信息
  - asset_id: "" # 上传素材id
    width: 0 # 宽
    high: 0 # 高
    duration: "" # 时长（视频类）
  aix_uploader: "" # aix平台的上传人

```
data example:
```json
{
    "aix_uploader": "",
    "asset_id_list": [
        ""
    ],
    "game_code": "",
    "is_meta": false,
    "metas": [
        {
            "asset_id": "",
            "duration": "",
            "high": 0,
            "width": 0
        }
    ]
}
```
#### upload to arthub rsp
parameter explain:
```yaml
UploadToArthubRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  succeed_asset_ids: [""] # 成功上报asset信息的素材id
  failed_asset_ids: [""] # 成功上报asset信息的素材id

```
data example:
```json
{
    "failed_asset_ids": [
        ""
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "succeed_asset_ids": [
        ""
    ]
}
```
# 获取用户上传的素材列表请求, POST, /api/v1/material_display/media_material_list
test curl:
```shell
curl 'http://target/api/v1/material_display/media_material_list' -H 'Content-Type:application/json' -d '{"account_id":"","asset_names":[""],"asset_ratios":[""],"campaign_sub_type":0,"count":0,"format_type":0,"game_code":"","keyword":"","media":0,"offset":0}'
```
#### media material list req
parameter explain:
```yaml
MediaMaterialListReq: # 获取用户上传的素材列表请求, POST, /api/v1/material_display/media_material_list
  offset: 0 # 起始偏移
  count: 0 # 拉取数量
  format_type: 0 # 是否类型，0-全部，1-视频，2-图片
  media: 0 # 1-google 2-facebook
  game_code: "" # game_code
  account_id: "" # 上传账号
  campaign_sub_type: 0 # campaign sub_type: 具体值查看枚举: google_advertise.proto CampaignSubType
  keyword: "" # 素材名称模糊搜索关键字
  asset_names: [""] # 素材名称批量精确搜索，若指定asset_names，则不分页
  asset_ratios: [""] # 需要筛选的素材比例, 不填默认不进行筛选

```
data example:
```json
{
    "account_id": "",
    "asset_names": [
        ""
    ],
    "asset_ratios": [
        ""
    ],
    "campaign_sub_type": 0,
    "count": 0,
    "format_type": 0,
    "game_code": "",
    "keyword": "",
    "media": 0,
    "offset": 0
}
```
#### media material list rsp
parameter explain:
```yaml
MediaMaterialListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  materials: # 用户素材列表
  - asset_id: "" # 素材索引
    name: "" # 素材名称
    status: 0 # 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
    upline_date: "" # 上线日期
    offline_date: "" # 下线日期
    online_days: 0 # 在线天数
    format: "" # 素材格式
    duration: "" # 素材时长
    preview_url: "" # 素材预览url
    create_date: "" # 创建时间
    origin_url: "" # arthub的originUrl
    detail: # 素材详情
      size: 0 # 素材大小
      update_date: "" # 更新日期
      creator: "" # 素材创建者
      updater: "" # 素材更新者
      manual_first_label: "" # 人工一级标签
      manual_second_label: "" # 人工二级标签
      width: 0 # 宽
      high: 0 # 高
      frame_rate: "" # 帧率（视频类）
      aspect_ratio: "" # 比例（视频类）
      bit_rate: "" # 视频比特率
      compression_format: "" # 视频压缩格式
      robot_first_label: "" # 第1层机器标签
      robot_second_label: "" # 第2层机器标签
      cover: "" # 封面
    resource_name: "" # resource_name
    cover_hash: "" # 封面hash
    asset_status: 0 # 素材在仓库状态, 1-正常, 2-回收站, 3-删除
    online_status: 0 # 素材曝光状态
    online_date: "" # 素材第一次曝光时间
    asset_ratio: "" # 素材宽高比, 精确到小数点后2位, 如1.00
  total: 0 # 素材总数

```
data example:
```json
{
    "materials": [
        {
            "asset_id": "",
            "asset_ratio": "",
            "asset_status": 0,
            "cover_hash": "",
            "create_date": "",
            "detail": {
                "aspect_ratio": "",
                "bit_rate": "",
                "compression_format": "",
                "cover": "",
                "creator": "",
                "frame_rate": "",
                "high": 0,
                "manual_first_label": "",
                "manual_second_label": "",
                "robot_first_label": "",
                "robot_second_label": "",
                "size": 0,
                "update_date": "",
                "updater": "",
                "width": 0
            },
            "duration": "",
            "format": "",
            "name": "",
            "offline_date": "",
            "online_date": "",
            "online_days": 0,
            "online_status": 0,
            "origin_url": "",
            "preview_url": "",
            "resource_name": "",
            "status": 0,
            "upline_date": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 获取其他信息, POST, /api/v1/material_display/get_ext_info
test curl:
```shell
curl 'http://target/api/v1/material_display/get_ext_info' -H 'Content-Type:application/json' -d '{}'
```
#### get ext info req
parameter explain:
```yaml
GetExtInfoReq: # 获取其他信息, POST, /api/v1/material_display/get_ext_info

```
data example:
```json
{}
```
#### get ext info rsp
parameter explain:
```yaml
GetExtInfoRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  label_list: [""] # 一级标签下拉列表

```
data example:
```json
{
    "label_list": [
        ""
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 通过目录获取媒体列表请求, POST, /api/v1/material_display/media_material_by_directory_id
test curl:
```shell
curl 'http://target/api/v1/material_display/media_material_by_directory_id' -H 'Content-Type:application/json' -d '{"count":0,"directory_id":"","filter_online_status":false,"is_filter_status":0,"offset":0,"online_status":0,"status":0,"uid":0,"upload_state":0,"uuid":"","with_detail":0}'
```
#### media material by directory id req
parameter explain:
```yaml
MediaMaterialByDirectoryIdReq: # 通过目录获取媒体列表请求, POST, /api/v1/material_display/media_material_by_directory_id
  uid: 0 # 保留字段
  uuid: "" # 前端生成随机ID
  offset: 0 # 起始偏移
  count: 0 # 拉取数量
  is_filter_status: 0 # 是否按照状态过滤，0-否，1-是
  status: 0 # 过滤状态，is_filter_status=1生效 0-未上传；1-正在上传；2-上传成功；3-上传失败
  directory_id: "" # 目录ID
  upload_state: 0 # (已废弃) 是否上传至广告库，0-否，1-是
  with_detail: 0 # 是否需要拉取详情 0-不需要 1-需要
  online_status: 0 # 在线状态筛选
  filter_online_status: false # 是否过滤在线状态

```
data example:
```json
{
    "count": 0,
    "directory_id": "",
    "filter_online_status": false,
    "is_filter_status": 0,
    "offset": 0,
    "online_status": 0,
    "status": 0,
    "uid": 0,
    "upload_state": 0,
    "uuid": "",
    "with_detail": 0
}
```
#### media material by directory id rsp
parameter explain:
```yaml
MediaMaterialByDirectoryIdRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  materials: # 素材列表
  - asset_id: "" # 素材索引
    name: "" # 素材名称
    status: 0 # 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
    upline_date: "" # 上线日期
    offline_date: "" # 下线日期
    online_days: 0 # 在线天数
    formate: "" # 素材格式
    duration: "" # 素材时长
    preview_url: "" # 素材预览url
    create_date: "" # 创建时间
    material_ext: # 素材详情
      asset_id: "" # 素材索引
      universal: # 通用
        format: "" # 文件格式
        size: 0 # 文件大小
        updater: "" # 更新者
        update_time: "" # 更新时间
        creator: "" # 创建者
        cover: "" # 封面
      label: # 标签
        manual_first_label: [""] # 人工一级标签
        manual_second_label: [""] # 人工二级标签
        robot_first_label: [""] # 机器一级标签
        robot_second_label: [""] # 机器二级标签
      statistics: # 统计
        take_users: 0 # 取用人数
        take_count: 0 # 取用次数
      video: # 视频
        width: 0 # 宽
        high: 0 # 高
        duration: 0 # 时长
        frame_rate: "" # 帧率
        aspect_ratio: "" # 高宽比
        bit_rate: "" # 比特率
        compression_format: "" # 压缩格式
    asset_status: 0 # 素材在仓库状态, 1-正常, 2-回收站, 3-删除
    online_status: 0 # 素材曝光状态
    online_date: "" # 素材第一次曝光时间
  total: 0 # 素材总数

```
data example:
```json
{
    "materials": [
        {
            "asset_id": "",
            "asset_status": 0,
            "create_date": "",
            "duration": "",
            "formate": "",
            "material_ext": {
                "asset_id": "",
                "label": {
                    "manual_first_label": [
                        ""
                    ],
                    "manual_second_label": [
                        ""
                    ],
                    "robot_first_label": [
                        ""
                    ],
                    "robot_second_label": [
                        ""
                    ]
                },
                "statistics": {
                    "take_count": 0,
                    "take_users": 0
                },
                "universal": {
                    "cover": "",
                    "creator": "",
                    "format": "",
                    "size": 0,
                    "update_time": "",
                    "updater": ""
                },
                "video": {
                    "aspect_ratio": "",
                    "bit_rate": "",
                    "compression_format": "",
                    "duration": 0,
                    "frame_rate": "",
                    "high": 0,
                    "width": 0
                }
            },
            "name": "",
            "offline_date": "",
            "online_date": "",
            "online_days": 0,
            "online_status": 0,
            "preview_url": "",
            "status": 0,
            "upline_date": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 根据ID(resource_name)列表查询素材名称列表, POST, /api/v1/material_display/media_material_name_list
test curl:
```shell
curl 'http://target/api/v1/material_display/media_material_name_list' -H 'Content-Type:application/json' -d '{"account_id":"","campaign_sub_type":0,"format_type":0,"game_code":"","media":0,"resource_names":[""]}'
```
#### media material name list req
parameter explain:
```yaml
MediaMaterialNameListReq: # 根据ID(resource_name)列表查询素材名称列表, POST, /api/v1/material_display/media_material_name_list
  format_type: 0 # 是否类型，0-全部，1-视频，2-图片
  media: 0 # 1-google 2-facebook
  game_code: "" # game_code
  account_id: "" # 上传账号
  campaign_sub_type: 0 # campaign sub_type: 具体值查看枚举: google_advertise.proto CampaignSubType
  resource_names: [""] # 素材ID(resource_name)列表

```
data example:
```json
{
    "account_id": "",
    "campaign_sub_type": 0,
    "format_type": 0,
    "game_code": "",
    "media": 0,
    "resource_names": [
        ""
    ]
}
```
#### media material name list rsp
parameter explain:
```yaml
MediaMaterialNameListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  asset_name_infos: # 素材信息列表
  - resource_name: "" # 素材ID(resource_name)
    asset_id: "" # 素材唯一id
    asset_name: "" # 素材名称
    format: "" # 素材格式
    duration: "" # 素材时长
    preview_url: "" # 素材预览url
    detail: # 素材详情
      size: 0 # 素材大小
      update_date: "" # 更新日期
      creator: "" # 素材创建者
      updater: "" # 素材更新者
      manual_first_label: "" # 人工一级标签
      manual_second_label: "" # 人工二级标签
      width: 0 # 宽
      high: 0 # 高
      frame_rate: "" # 帧率（视频类）
      aspect_ratio: "" # 比例（视频类）
      bit_rate: "" # 视频比特率
      compression_format: "" # 视频压缩格式
      robot_first_label: "" # 第1层机器标签
      robot_second_label: "" # 第2层机器标签
      cover: "" # 封面
    cover_hash: "" # 素材封面hash, tiktok中即为封面image resource name

```
data example:
```json
{
    "asset_name_infos": [
        {
            "asset_id": "",
            "asset_name": "",
            "cover_hash": "",
            "detail": {
                "aspect_ratio": "",
                "bit_rate": "",
                "compression_format": "",
                "cover": "",
                "creator": "",
                "frame_rate": "",
                "high": 0,
                "manual_first_label": "",
                "manual_second_label": "",
                "robot_first_label": "",
                "robot_second_label": "",
                "size": 0,
                "update_date": "",
                "updater": "",
                "width": 0
            },
            "duration": "",
            "format": "",
            "preview_url": "",
            "resource_name": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 增加上传任务, POST, /api/v1/material_display/add_upload_to_channel_task
test curl:
```shell
curl 'http://target/api/v1/material_display/add_upload_to_channel_task' -H 'Content-Type:application/json' -d '{"accounts":"","asset_ids":[""],"country":"","creator":"","game_code":"","notify_days":0,"to_channels":[{"channel":0,"media_path":""}]}'
```
#### add upload to channel task req
parameter explain:
```yaml
AddUploadToChannelTaskReq: # 增加上传任务, POST, /api/v1/material_display/add_upload_to_channel_task
  game_code: "" # 
  country: "" # 
  to_channels: # 上传渠道目录信息
  - channel: 0 # 1-google 2-facebook 3-tiktok 4-twitter
    media_path: "" # 渠道目录名称
  asset_ids: [""] # 素材ID列表
  creator: "" # 创建人
  accounts: "" # 指定上传账号，用逗号给开，为空不指定
  notify_days: 0 # 提醒天数，为0不提醒

```
data example:
```json
{
    "accounts": "",
    "asset_ids": [
        ""
    ],
    "country": "",
    "creator": "",
    "game_code": "",
    "notify_days": 0,
    "to_channels": [
        {
            "channel": 0,
            "media_path": ""
        }
    ]
}
```
#### add upload to channel task rsp
parameter explain:
```yaml
AddUploadToChannelTaskRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 取消正在上传的任务, POST, /api/v1/material_display/cancel_upload_to_channel_task
test curl:
```shell
curl 'http://target/api/v1/material_display/cancel_upload_to_channel_task' -H 'Content-Type:application/json' -d '{"game_code":"","task_ids":[""]}'
```
#### cancel upload to channel task req
parameter explain:
```yaml
CancelUploadToChannelTaskReq: # 取消正在上传的任务, POST, /api/v1/material_display/cancel_upload_to_channel_task
  game_code: "" # 
  task_ids: [""] # 任务ID列表

```
data example:
```json
{
    "game_code": "",
    "task_ids": [
        ""
    ]
}
```
#### cancel upload to channel task rsp
parameter explain:
```yaml
CancelUploadToChannelTaskRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 重启上传失败的任务, POST, /api/v1/material_display/resume_upload_to_channel_task
test curl:
```shell
curl 'http://target/api/v1/material_display/resume_upload_to_channel_task' -H 'Content-Type:application/json' -d '{"game_code":"","task_ids":[""]}'
```
#### resume upload to channel task req
parameter explain:
```yaml
ResumeUploadToChannelTaskReq: # 重启上传失败的任务, POST, /api/v1/material_display/resume_upload_to_channel_task
  game_code: "" # 
  task_ids: [""] # 任务ID列表

```
data example:
```json
{
    "game_code": "",
    "task_ids": [
        ""
    ]
}
```
#### resume upload to channel task rsp
parameter explain:
```yaml
ResumeUploadToChannelTaskRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 获取提示关键词, POST, /api/v1/material_display/get_keyword_list
test curl:
```shell
curl 'http://target/api/v1/material_display/get_keyword_list' -H 'Content-Type:application/json' -d '{"limit":0,"prefix":""}'
```
#### get keyword list req
parameter explain:
```yaml
GetKeywordListReq: # 获取提示关键词, POST, /api/v1/material_display/get_keyword_list
  prefix: "" # 前缀
  limit: 0 # 限制数量

```
data example:
```json
{
    "limit": 0,
    "prefix": ""
}
```
#### get keyword list rsp
parameter explain:
```yaml
GetKeywordListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  Suggestions: # 关键词列表
  - keyword: "" # 
    frequency: 0 # 

```
data example:
```json
{
    "Suggestions": [
        {
            "frequency": 0,
            "keyword": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
