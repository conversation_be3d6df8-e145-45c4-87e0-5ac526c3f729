syntax = "proto3";

package asa_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/asa_advertise";

import "aix/aix_common_message.proto";

// 自测接口, POST, /api/v1/asa/say_hi
message SayHiReq {
}

message SayHiRsp {
    aix.Result result = 1;  // 返回结果
}

// 拉取账号列表, POST, /api/v1/asa_advertise/get_accounts
message GetAccountsReq {
}

message Account {
    string account_id   = 1;
    string account_name = 2;
}

message GetAccountsRsp {
    aix.Result result         = 1;
    repeated Account accounts = 2;
}

// 手动触发属性数据同步
message SyncStrategyDataReq {
  repeated string game_codes = 1;
  repeated string account_ids = 2;
}

message SyncStrategyDataRsp { aix.Result result = 1; }

// 拉取campaign树形结构简要信息(status,name), POST, /api/v1/asa_advertise/get_campaign_tree_summary
message GetCampaignTreeSummaryReq {
    string account_id  = 1;  // 前端填写
    string campaign_id = 2;  // campaign id信息
}

message GetCampaignTreeSummaryRsp {
    aix.Result result          = 1;
    CampaignTree campaign_tree = 2;  // 返回的campaign树形结构信息
}

message CampaignTree {
    Campaign campaign          = 1;  // campaign信息
    repeated Adgroup ad_groups = 2;  // 子级ad_group信息
}

message Campaign {
    string game_code                = 1;
    string media                    = 2;
    string account_id               = 3;
    string account_name             = 4;
    string campaign_id              = 5;
    string campaign_name            = 6;
    string campaign_status          = 7;
    string campaign_delivery_status = 8;
    string campaign_create_time     = 9;
    string campaign_modify_time     = 10;
    string aix_currency             = 15;
    double aix_daily_budget         = 16;
    double aix_total_budget         = 17;
    string ad_channel_type          = 19;
}

message Adgroup {
    string game_code               = 1;
    string media                   = 2;
    string account_id              = 3;
    string account_name            = 4;
    string campaign_id             = 5;
    string campaign_name           = 6;
    string adgroup_id              = 7;
    string adgroup_name            = 8;
    string adgroup_status          = 9;
    string adgroup_delivery_status = 10;
    string adgroup_create_time     = 11;
    string adgroup_modify_time     = 12;
    string aix_currency            = 17;
    double default_bid_amount      = 18;
    double cpa_goal                = 19;
}

// 拉取campaign列表, POST, /api/v1/asa_advertise/get_campaigns
message GetCampaignsReq {
    string account_id            = 1;  // 前端填写
    repeated string campaign_ids = 2;  // campaign id列表
}

message GetCampaignsRsp {
    aix.Result result           = 1;  // 结果
    repeated Campaign campaigns = 2;  // campaign列表
}

// 拉取adgroup列表, POST, /api/v1/asa_advertise/get_campaigns
message GetAdgroupsReq {
    string account_id           = 1;  // 前端填写
    string campaign_id          = 2;  // campaign id
    repeated string adgroup_ids = 3;  // adgroup列表
}

message GetAdgroupsRsp {
    aix.Result result         = 1;  // 结果
    repeated Adgroup adgroups = 2;  // adgroup列表
}

message CampaignAdgroupUnit {
    string campaign_id          = 1;  // campaign id
    repeated string adgroup_ids = 2;  // adgroup列表
}

message BatchGetAdgroupsReq {
    string account_id                  = 1;
    repeated CampaignAdgroupUnit units = 2;  // campaignadgroup集合列表
}

message BatchGetAdgroupsRsp {
    aix.Result result         = 1;  // 结果
    repeated Adgroup adgroups = 2;  // adgroup列表
}