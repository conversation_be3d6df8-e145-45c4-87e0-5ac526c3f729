package service

import (
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/conf"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
)

// GetExtInfo ...
func GetExtInfo(ctx *gin.Context, req *pb.GetExtInfoReq, rsp *pb.GetExtInfoRsp) error {
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if gameCode == "" {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	for _, label := range conf.GetBizConf().LabelList {
		if gameCode == label.GameCode {
			rsp.LabelList = label.LabelList
			break
		}
	}
	return nil
}
