package service

import (
	"context"

	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/data"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise"
)

// GetAdsByCreativeSet 拉取creative_set下的ads列表
func GetAdsByCreativeSet(ctx context.Context, req *pb.GetAdsByCreativeSetReq, rsp *pb.GetAdsByCreativeSetRsp) error {
	if req.GetAccountId() == "" || req.GetAccountId() == "" || req.GetCreativeSetId() == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code, account_id, creative_set_id is required")
	}

	account, err := data.GetOneMediaAccounts(ctx, req.GetGameCode(), req.GetAccountId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.GetOneMediaAccounts err:%v", err)
	}

	client := newApplovinAPIClient(account)
	ads, err := client.GetAdsByCreativeSet(ctx, req.GetCreativeSetId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "client.GetAdsByCreativeSet err:%v", err)
	}
	for _, ad := range ads {
		rsp.Ads = append(rsp.Ads, &pb.Ad{
			AdId:            ad.ID,
			Name:            ad.Name,
			CreativeSetId:   ad.CreativeSetID,
			CreativeSetName: ad.CreativeSetName,
			Size:            ad.Size,
			Template:        ad.Template,
			Status:          ad.Status,
		})
	}
	return nil
}

// ChangeAdStatus 更新广告ad状态
func ChangeAdStatus(ctx context.Context, req *pb.ChangeAdStatusReq, rsp *pb.ChangeAdStatusRsp) error {
	if req.GetGameCode() == "" ||
		req.GetAccountId() == "" ||
		req.GetCreativeSetId() == "" ||
		req.GetAdId() == 0 {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code, account_id, ad_id, creative_set_id is required")
	}

	account, err := data.GetOneMediaAccounts(ctx, req.GetGameCode(), req.GetAccountId())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.GetOneMediaAccounts err:%v", err)
	}

	client := newApplovinAPIClient(account)
	err = client.ChangeAdStatus(ctx, req.GetCreativeSetId(), req.GetAdId(), req.GetStatus())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR), "client.ChangeAdStatus err:%v", err)
	}

	return nil
}
