// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.6.1
// source: central/central.proto

package central

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取翻译信息, POST, /api/v1/central/get_translation
type GetTranslationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode     string              `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识 game_code
	Media        string              `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                          // 必填 渠道标识 eg: TikTok
	AccountId    string              `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 渠道账号id 不填表示账号无关
	Translations []*TranslationParam `protobuf:"bytes,4,rep,name=translations,proto3" json:"translations,omitempty"`            // 待翻译源内容列表
}

func (x *GetTranslationReq) Reset() {
	*x = GetTranslationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTranslationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTranslationReq) ProtoMessage() {}

func (x *GetTranslationReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTranslationReq.ProtoReflect.Descriptor instead.
func (*GetTranslationReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{0}
}

func (x *GetTranslationReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetTranslationReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *GetTranslationReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetTranslationReq) GetTranslations() []*TranslationParam {
	if x != nil {
		return x.Translations
	}
	return nil
}

// 翻译 参数
type TranslationParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TranslationType string `protobuf:"bytes,1,opt,name=translation_type,json=translationType,proto3" json:"translation_type,omitempty"` // 翻译类型. AIX_CAMPAIGN_TYPE
	SourceContent   string `protobuf:"bytes,2,opt,name=source_content,json=sourceContent,proto3" json:"source_content,omitempty"`       // 翻译源内容. APP_INSTALL. 为空则表示查询 translation_type 下所有配置翻译项列表
}

func (x *TranslationParam) Reset() {
	*x = TranslationParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslationParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslationParam) ProtoMessage() {}

func (x *TranslationParam) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslationParam.ProtoReflect.Descriptor instead.
func (*TranslationParam) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{1}
}

func (x *TranslationParam) GetTranslationType() string {
	if x != nil {
		return x.TranslationType
	}
	return ""
}

func (x *TranslationParam) GetSourceContent() string {
	if x != nil {
		return x.SourceContent
	}
	return ""
}

// 翻译项
type TranslationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceContent  string `protobuf:"bytes,1,opt,name=source_content,json=sourceContent,proto3" json:"source_content,omitempty"`    // 翻译源内容. 例如: APP_INSTALL.
	TargetContent  string `protobuf:"bytes,2,opt,name=target_content,json=targetContent,proto3" json:"target_content,omitempty"`    // 翻译目标内容. 例如: App Installs
	IsVisible      bool   `protobuf:"varint,3,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`               // 翻译【源内容】是否处于可见状态. (注：翻译源内容和目标内容仍然可用) true:可见  false:不可见，需要翻译内容，又不允许 source_content 可见
	SourceCategory string `protobuf:"bytes,4,opt,name=source_category,json=sourceCategory,proto3" json:"source_category,omitempty"` // source_content 所属类别. 空串表示无类别, 不为空则source_category与source_content其中一项一致
}

func (x *TranslationItem) Reset() {
	*x = TranslationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslationItem) ProtoMessage() {}

func (x *TranslationItem) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslationItem.ProtoReflect.Descriptor instead.
func (*TranslationItem) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{2}
}

func (x *TranslationItem) GetSourceContent() string {
	if x != nil {
		return x.SourceContent
	}
	return ""
}

func (x *TranslationItem) GetTargetContent() string {
	if x != nil {
		return x.TargetContent
	}
	return ""
}

func (x *TranslationItem) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

func (x *TranslationItem) GetSourceCategory() string {
	if x != nil {
		return x.SourceCategory
	}
	return ""
}

// 翻译类型 & 翻译item列表
type TranslationTypeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TranslationType string             `protobuf:"bytes,1,opt,name=translation_type,json=translationType,proto3" json:"translation_type,omitempty"` // 翻译类型. 例如: AIX_CAMPAIGN_TYPE
	Items           []*TranslationItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`                                            // 翻译项列表
}

func (x *TranslationTypeItem) Reset() {
	*x = TranslationTypeItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslationTypeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslationTypeItem) ProtoMessage() {}

func (x *TranslationTypeItem) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslationTypeItem.ProtoReflect.Descriptor instead.
func (*TranslationTypeItem) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{3}
}

func (x *TranslationTypeItem) GetTranslationType() string {
	if x != nil {
		return x.TranslationType
	}
	return ""
}

func (x *TranslationTypeItem) GetItems() []*TranslationItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetTranslationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result             *aix.Result            `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                                   // 返回结果
	TraceId            string                 `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`                                  // 请求trace_id
	TranslationResults []*TranslationTypeItem `protobuf:"bytes,3,rep,name=translation_results,json=translationResults,proto3" json:"translation_results,omitempty"` // 翻译结果列表
}

func (x *GetTranslationRsp) Reset() {
	*x = GetTranslationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTranslationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTranslationRsp) ProtoMessage() {}

func (x *GetTranslationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTranslationRsp.ProtoReflect.Descriptor instead.
func (*GetTranslationRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{4}
}

func (x *GetTranslationRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetTranslationRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *GetTranslationRsp) GetTranslationResults() []*TranslationTypeItem {
	if x != nil {
		return x.TranslationResults
	}
	return nil
}

// 批量创建或更新翻译信息, POST, /api/v1/central/save_translations
type SaveTranslationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode             string                 `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                       // 游戏标识 game_code. 默认为 AIX_ALL
	Media                string                 `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                                             // 必填 渠道标识 eg: TikTok
	AccountId            string                 `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                    // 渠道账号id. 默认为 AIX_ALL
	SaveType             string                 `protobuf:"bytes,4,opt,name=save_type,json=saveType,proto3" json:"save_type,omitempty"`                                       // 保存类型. 默认为 DEFAULT. 支持: "DELETE_OLD_TRANSLATION": 根据type删除旧translation列表，再进行覆盖
	TranslationTypeItems []*TranslationTypeItem `protobuf:"bytes,5,rep,name=translation_type_items,json=translationTypeItems,proto3" json:"translation_type_items,omitempty"` // 翻译类型 & 翻译item列表
}

func (x *SaveTranslationsReq) Reset() {
	*x = SaveTranslationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTranslationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTranslationsReq) ProtoMessage() {}

func (x *SaveTranslationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTranslationsReq.ProtoReflect.Descriptor instead.
func (*SaveTranslationsReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{5}
}

func (x *SaveTranslationsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *SaveTranslationsReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *SaveTranslationsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *SaveTranslationsReq) GetSaveType() string {
	if x != nil {
		return x.SaveType
	}
	return ""
}

func (x *SaveTranslationsReq) GetTranslationTypeItems() []*TranslationTypeItem {
	if x != nil {
		return x.TranslationTypeItems
	}
	return nil
}

type SaveTranslationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	TraceId string      `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
}

func (x *SaveTranslationsRsp) Reset() {
	*x = SaveTranslationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTranslationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTranslationsRsp) ProtoMessage() {}

func (x *SaveTranslationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTranslationsRsp.ProtoReflect.Descriptor instead.
func (*SaveTranslationsRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{6}
}

func (x *SaveTranslationsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SaveTranslationsRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

// 获取国家地区分类及其列表信息, POST, /api/v1/central/get_location
type GetLocationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识 game_code
	Media     string `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                          // 必填 渠道标识 eg: TikTok
	AccountId string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 渠道账号id 不填表示账号无关
}

func (x *GetLocationReq) Reset() {
	*x = GetLocationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationReq) ProtoMessage() {}

func (x *GetLocationReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationReq.ProtoReflect.Descriptor instead.
func (*GetLocationReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{7}
}

func (x *GetLocationReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetLocationReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *GetLocationReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type RegionCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryId   string    `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`       // 分类唯一ID
	CategoryName string    `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"` // 分类名称
	Regions      []*Region `protobuf:"bytes,3,rep,name=regions,proto3" json:"regions,omitempty"`                               // 分类下国家/地区列表
}

func (x *RegionCategory) Reset() {
	*x = RegionCategory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegionCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionCategory) ProtoMessage() {}

func (x *RegionCategory) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionCategory.ProtoReflect.Descriptor instead.
func (*RegionCategory) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{8}
}

func (x *RegionCategory) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *RegionCategory) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *RegionCategory) GetRegions() []*Region {
	if x != nil {
		return x.Regions
	}
	return nil
}

type Region struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId   string `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`       // 国家/地区 唯一ID
	RegionCode string `protobuf:"bytes,2,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"` // 国家/地区 二字码(大写)
	RegionName string `protobuf:"bytes,3,opt,name=region_name,json=regionName,proto3" json:"region_name,omitempty"` // 国家/地区 名称
}

func (x *Region) Reset() {
	*x = Region{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Region) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Region) ProtoMessage() {}

func (x *Region) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Region.ProtoReflect.Descriptor instead.
func (*Region) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{9}
}

func (x *Region) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *Region) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *Region) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

type GetLocationRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          *aix.Result       `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                          // 返回结果
	TraceId         string            `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`                         // 请求trace_id
	RegionCategorys []*RegionCategory `protobuf:"bytes,3,rep,name=region_categorys,json=regionCategorys,proto3" json:"region_categorys,omitempty"` // 国家地区分类列表
}

func (x *GetLocationRsp) Reset() {
	*x = GetLocationRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLocationRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLocationRsp) ProtoMessage() {}

func (x *GetLocationRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLocationRsp.ProtoReflect.Descriptor instead.
func (*GetLocationRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{10}
}

func (x *GetLocationRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetLocationRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *GetLocationRsp) GetRegionCategorys() []*RegionCategory {
	if x != nil {
		return x.RegionCategorys
	}
	return nil
}

// 批量创建或更新国家地区信息, POST, /api/v1/central/save_locations
type SaveLocationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode        string            `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                      // 游戏标识 game_code. 默认为 AIX_ALL
	Media           string            `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                            // 必填 渠道标识 eg: TikTok . 默认为 AIX_ALL
	AccountId       string            `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                   // 渠道账号id. 默认为 AIX_ALL
	SaveType        string            `protobuf:"bytes,4,opt,name=save_type,json=saveType,proto3" json:"save_type,omitempty"`                      // 保存类型. 默认为 DEFAULT. 支持: "DELETE_OLD_REGION": 删除旧region列表，再进行覆盖
	RegionCategorys []*RegionCategory `protobuf:"bytes,5,rep,name=region_categorys,json=regionCategorys,proto3" json:"region_categorys,omitempty"` // 国家地区分类列表
}

func (x *SaveLocationsReq) Reset() {
	*x = SaveLocationsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveLocationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveLocationsReq) ProtoMessage() {}

func (x *SaveLocationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveLocationsReq.ProtoReflect.Descriptor instead.
func (*SaveLocationsReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{11}
}

func (x *SaveLocationsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *SaveLocationsReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *SaveLocationsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *SaveLocationsReq) GetSaveType() string {
	if x != nil {
		return x.SaveType
	}
	return ""
}

func (x *SaveLocationsReq) GetRegionCategorys() []*RegionCategory {
	if x != nil {
		return x.RegionCategorys
	}
	return nil
}

type SaveLocationsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	TraceId string      `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
}

func (x *SaveLocationsRsp) Reset() {
	*x = SaveLocationsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveLocationsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveLocationsRsp) ProtoMessage() {}

func (x *SaveLocationsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveLocationsRsp.ProtoReflect.Descriptor instead.
func (*SaveLocationsRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{12}
}

func (x *SaveLocationsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SaveLocationsRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

// 查询模板列表, POST, /api/v1/central/get_templates
type GetTemplatesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode     string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`             // 必填 游戏标识game_code
	Media        string `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                   // 必填 渠道标识 eg: TikTok
	TemplateType string `protobuf:"bytes,3,opt,name=template_type,json=templateType,proto3" json:"template_type,omitempty"` // 必填 模板类型. 支持 TARGETING
}

func (x *GetTemplatesReq) Reset() {
	*x = GetTemplatesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplatesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplatesReq) ProtoMessage() {}

func (x *GetTemplatesReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplatesReq.ProtoReflect.Descriptor instead.
func (*GetTemplatesReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{13}
}

func (x *GetTemplatesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetTemplatesReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *GetTemplatesReq) GetTemplateType() string {
	if x != nil {
		return x.TemplateType
	}
	return ""
}

type Template struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId      string `protobuf:"bytes,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`                // 模板唯一ID
	TemplateType    string `protobuf:"bytes,2,opt,name=template_type,json=templateType,proto3" json:"template_type,omitempty"`          // 模板类型
	TemplateName    string `protobuf:"bytes,3,opt,name=template_name,json=templateName,proto3" json:"template_name,omitempty"`          // 模板名称
	TemplateContent string `protobuf:"bytes,4,opt,name=template_content,json=templateContent,proto3" json:"template_content,omitempty"` // 模板内容. 模板类型为TARGETING: 校验是否为json格式
	CreateUser      string `protobuf:"bytes,5,opt,name=create_user,json=createUser,proto3" json:"create_user,omitempty"`                // 模板创建者
	UpdateUser      string `protobuf:"bytes,6,opt,name=update_user,json=updateUser,proto3" json:"update_user,omitempty"`                // 模板更新者
	CreateTime      string `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                // 模板创建时间
	UpdateTime      string `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                // 模板更新时间
	TemplateStatus  string `protobuf:"bytes,9,opt,name=template_status,json=templateStatus,proto3" json:"template_status,omitempty"`    // 模板状态. AVAILABLE: 可用状态; UNAVAILABLE: 不可用状态
}

func (x *Template) Reset() {
	*x = Template{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Template) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Template) ProtoMessage() {}

func (x *Template) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Template.ProtoReflect.Descriptor instead.
func (*Template) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{14}
}

func (x *Template) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *Template) GetTemplateType() string {
	if x != nil {
		return x.TemplateType
	}
	return ""
}

func (x *Template) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

func (x *Template) GetTemplateContent() string {
	if x != nil {
		return x.TemplateContent
	}
	return ""
}

func (x *Template) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *Template) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

func (x *Template) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Template) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Template) GetTemplateStatus() string {
	if x != nil {
		return x.TemplateStatus
	}
	return ""
}

type GetTemplatesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	TraceId   string      `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
	Templates []*Template `protobuf:"bytes,3,rep,name=templates,proto3" json:"templates,omitempty"`            // 模板列表
}

func (x *GetTemplatesRsp) Reset() {
	*x = GetTemplatesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTemplatesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTemplatesRsp) ProtoMessage() {}

func (x *GetTemplatesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTemplatesRsp.ProtoReflect.Descriptor instead.
func (*GetTemplatesRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{15}
}

func (x *GetTemplatesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetTemplatesRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *GetTemplatesRsp) GetTemplates() []*Template {
	if x != nil {
		return x.Templates
	}
	return nil
}

// 新增或更新 模板, POST, /api/v1/central/save_template
type SaveTemplateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode     string    `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`             // 必填 游戏标识game_code
	Media        string    `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                   // 必填 渠道标识 eg: TikTok
	TemplateType string    `protobuf:"bytes,3,opt,name=template_type,json=templateType,proto3" json:"template_type,omitempty"` // 必填 模板类型. 支持 TARGETING
	Template     *Template `protobuf:"bytes,4,opt,name=template,proto3" json:"template,omitempty"`                             // 模板数据
}

func (x *SaveTemplateReq) Reset() {
	*x = SaveTemplateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTemplateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTemplateReq) ProtoMessage() {}

func (x *SaveTemplateReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTemplateReq.ProtoReflect.Descriptor instead.
func (*SaveTemplateReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{16}
}

func (x *SaveTemplateReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *SaveTemplateReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *SaveTemplateReq) GetTemplateType() string {
	if x != nil {
		return x.TemplateType
	}
	return ""
}

func (x *SaveTemplateReq) GetTemplate() *Template {
	if x != nil {
		return x.Template
	}
	return nil
}

type SaveTemplateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	TraceId  string      `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
	Template *Template   `protobuf:"bytes,3,opt,name=template,proto3" json:"template,omitempty"`              // 模板数据. 新增时会填充 template_id
}

func (x *SaveTemplateRsp) Reset() {
	*x = SaveTemplateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTemplateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTemplateRsp) ProtoMessage() {}

func (x *SaveTemplateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTemplateRsp.ProtoReflect.Descriptor instead.
func (*SaveTemplateRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{17}
}

func (x *SaveTemplateRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SaveTemplateRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *SaveTemplateRsp) GetTemplate() *Template {
	if x != nil {
		return x.Template
	}
	return nil
}

// 批量删除模板, POST, /api/v1/central/delete_templates
type DeleteTemplatesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode     string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`             // 必填 游戏标识game_code
	Media        string   `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                   // 必填 渠道标识 eg: TikTok
	TemplateType string   `protobuf:"bytes,3,opt,name=template_type,json=templateType,proto3" json:"template_type,omitempty"` // 必填 模板类型. 支持 TARGETING
	TemplateIds  []string `protobuf:"bytes,4,rep,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`    // 模板ID列表
}

func (x *DeleteTemplatesReq) Reset() {
	*x = DeleteTemplatesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTemplatesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTemplatesReq) ProtoMessage() {}

func (x *DeleteTemplatesReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTemplatesReq.ProtoReflect.Descriptor instead.
func (*DeleteTemplatesReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteTemplatesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *DeleteTemplatesReq) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *DeleteTemplatesReq) GetTemplateType() string {
	if x != nil {
		return x.TemplateType
	}
	return ""
}

func (x *DeleteTemplatesReq) GetTemplateIds() []string {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

type DeleteTemplatesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	TraceId string      `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
}

func (x *DeleteTemplatesRsp) Reset() {
	*x = DeleteTemplatesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTemplatesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTemplatesRsp) ProtoMessage() {}

func (x *DeleteTemplatesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTemplatesRsp.ProtoReflect.Descriptor instead.
func (*DeleteTemplatesRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteTemplatesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *DeleteTemplatesRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

// 上报监控数据, POST, /api/v1/central/report_monitor_data
type ReportMonitorDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MonitorType string `protobuf:"bytes,1,opt,name=monitor_type,json=monitorType,proto3" json:"monitor_type,omitempty"` // 监控类型.
	// support: [ACCOUNT_STATUS, CAMPAIGN_DELIVERY_STATUS,
	// ADGROUP_DELIVERY_STATUS, AD_DELIVERY_STATUS]
	//
	// Types that are assignable to MonitorData:
	//	*ReportMonitorDataReq_AccountStatusData
	//	*ReportMonitorDataReq_CampaignDeliveryStatusData
	//	*ReportMonitorDataReq_AdgroupDeliveryStatusData
	//	*ReportMonitorDataReq_AdDeliveryStatusData
	MonitorData isReportMonitorDataReq_MonitorData `protobuf_oneof:"monitor_data"`
}

func (x *ReportMonitorDataReq) Reset() {
	*x = ReportMonitorDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportMonitorDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportMonitorDataReq) ProtoMessage() {}

func (x *ReportMonitorDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportMonitorDataReq.ProtoReflect.Descriptor instead.
func (*ReportMonitorDataReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{20}
}

func (x *ReportMonitorDataReq) GetMonitorType() string {
	if x != nil {
		return x.MonitorType
	}
	return ""
}

func (m *ReportMonitorDataReq) GetMonitorData() isReportMonitorDataReq_MonitorData {
	if m != nil {
		return m.MonitorData
	}
	return nil
}

func (x *ReportMonitorDataReq) GetAccountStatusData() *MonitorAccountStatusList {
	if x, ok := x.GetMonitorData().(*ReportMonitorDataReq_AccountStatusData); ok {
		return x.AccountStatusData
	}
	return nil
}

func (x *ReportMonitorDataReq) GetCampaignDeliveryStatusData() *MonitorCampaignDeliveryStatusList {
	if x, ok := x.GetMonitorData().(*ReportMonitorDataReq_CampaignDeliveryStatusData); ok {
		return x.CampaignDeliveryStatusData
	}
	return nil
}

func (x *ReportMonitorDataReq) GetAdgroupDeliveryStatusData() *MonitorAdgroupDeliveryStatusList {
	if x, ok := x.GetMonitorData().(*ReportMonitorDataReq_AdgroupDeliveryStatusData); ok {
		return x.AdgroupDeliveryStatusData
	}
	return nil
}

func (x *ReportMonitorDataReq) GetAdDeliveryStatusData() *MonitorAdDeliveryStatusList {
	if x, ok := x.GetMonitorData().(*ReportMonitorDataReq_AdDeliveryStatusData); ok {
		return x.AdDeliveryStatusData
	}
	return nil
}

type isReportMonitorDataReq_MonitorData interface {
	isReportMonitorDataReq_MonitorData()
}

type ReportMonitorDataReq_AccountStatusData struct {
	AccountStatusData *MonitorAccountStatusList `protobuf:"bytes,2,opt,name=account_status_data,json=accountStatusData,proto3,oneof"`
}

type ReportMonitorDataReq_CampaignDeliveryStatusData struct {
	CampaignDeliveryStatusData *MonitorCampaignDeliveryStatusList `protobuf:"bytes,3,opt,name=campaign_delivery_status_data,json=campaignDeliveryStatusData,proto3,oneof"`
}

type ReportMonitorDataReq_AdgroupDeliveryStatusData struct {
	AdgroupDeliveryStatusData *MonitorAdgroupDeliveryStatusList `protobuf:"bytes,4,opt,name=adgroup_delivery_status_data,json=adgroupDeliveryStatusData,proto3,oneof"`
}

type ReportMonitorDataReq_AdDeliveryStatusData struct {
	AdDeliveryStatusData *MonitorAdDeliveryStatusList `protobuf:"bytes,5,opt,name=ad_delivery_status_data,json=adDeliveryStatusData,proto3,oneof"`
}

func (*ReportMonitorDataReq_AccountStatusData) isReportMonitorDataReq_MonitorData() {}

func (*ReportMonitorDataReq_CampaignDeliveryStatusData) isReportMonitorDataReq_MonitorData() {}

func (*ReportMonitorDataReq_AdgroupDeliveryStatusData) isReportMonitorDataReq_MonitorData() {}

func (*ReportMonitorDataReq_AdDeliveryStatusData) isReportMonitorDataReq_MonitorData() {}

type ReportMonitorDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	TraceId string      `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
}

func (x *ReportMonitorDataRsp) Reset() {
	*x = ReportMonitorDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportMonitorDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportMonitorDataRsp) ProtoMessage() {}

func (x *ReportMonitorDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportMonitorDataRsp.ProtoReflect.Descriptor instead.
func (*ReportMonitorDataRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{21}
}

func (x *ReportMonitorDataRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ReportMonitorDataRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

type MonitorAccountStatusList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountStatusList []*MonitorAccountStatus `protobuf:"bytes,1,rep,name=account_status_list,json=accountStatusList,proto3" json:"account_status_list,omitempty"`
}

func (x *MonitorAccountStatusList) Reset() {
	*x = MonitorAccountStatusList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorAccountStatusList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorAccountStatusList) ProtoMessage() {}

func (x *MonitorAccountStatusList) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorAccountStatusList.ProtoReflect.Descriptor instead.
func (*MonitorAccountStatusList) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{22}
}

func (x *MonitorAccountStatusList) GetAccountStatusList() []*MonitorAccountStatus {
	if x != nil {
		return x.AccountStatusList
	}
	return nil
}

type MonitorAccountStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode              string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                          // 游戏标识 game_code
	Media                 string `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                                                // 渠道标识 eg: TikTok
	AccountId             string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                       // 渠道账号id
	AccountName           string `protobuf:"bytes,4,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`                                 // 账号名称
	PreviousAccountStatus string `protobuf:"bytes,5,opt,name=previous_account_status,json=previousAccountStatus,proto3" json:"previous_account_status,omitempty"` // account 上一个 status
	CurrentAccountStatus  string `protobuf:"bytes,6,opt,name=current_account_status,json=currentAccountStatus,proto3" json:"current_account_status,omitempty"`    // account 当前 status
}

func (x *MonitorAccountStatus) Reset() {
	*x = MonitorAccountStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorAccountStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorAccountStatus) ProtoMessage() {}

func (x *MonitorAccountStatus) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorAccountStatus.ProtoReflect.Descriptor instead.
func (*MonitorAccountStatus) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{23}
}

func (x *MonitorAccountStatus) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *MonitorAccountStatus) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *MonitorAccountStatus) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MonitorAccountStatus) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *MonitorAccountStatus) GetPreviousAccountStatus() string {
	if x != nil {
		return x.PreviousAccountStatus
	}
	return ""
}

func (x *MonitorAccountStatus) GetCurrentAccountStatus() string {
	if x != nil {
		return x.CurrentAccountStatus
	}
	return ""
}

type MonitorCampaignDeliveryStatusList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CampaignDeliveryList []*MonitorCampaignDeliveryStatus `protobuf:"bytes,1,rep,name=campaign_delivery_list,json=campaignDeliveryList,proto3" json:"campaign_delivery_list,omitempty"`
}

func (x *MonitorCampaignDeliveryStatusList) Reset() {
	*x = MonitorCampaignDeliveryStatusList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorCampaignDeliveryStatusList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorCampaignDeliveryStatusList) ProtoMessage() {}

func (x *MonitorCampaignDeliveryStatusList) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorCampaignDeliveryStatusList.ProtoReflect.Descriptor instead.
func (*MonitorCampaignDeliveryStatusList) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{24}
}

func (x *MonitorCampaignDeliveryStatusList) GetCampaignDeliveryList() []*MonitorCampaignDeliveryStatus {
	if x != nil {
		return x.CampaignDeliveryList
	}
	return nil
}

type MonitorCampaignDeliveryStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode               string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                             // 游戏标识 game_code
	Media                  string `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                                                   // 渠道标识 eg: TikTok
	AccountId              string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                          // 渠道账号id
	AccountName            string `protobuf:"bytes,4,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`                                    // 账号名称
	CampaignId             string `protobuf:"bytes,5,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`                                       // campaign id
	CampaignName           string `protobuf:"bytes,6,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`                                 // campaign 名称
	PreviousDeliveryStatus string `protobuf:"bytes,7,opt,name=previous_delivery_status,json=previousDeliveryStatus,proto3" json:"previous_delivery_status,omitempty"` // campaign 上一个 delivery status
	CurrentDeliveryStatus  string `protobuf:"bytes,8,opt,name=current_delivery_status,json=currentDeliveryStatus,proto3" json:"current_delivery_status,omitempty"`    // campaign 当前 delivery status
}

func (x *MonitorCampaignDeliveryStatus) Reset() {
	*x = MonitorCampaignDeliveryStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorCampaignDeliveryStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorCampaignDeliveryStatus) ProtoMessage() {}

func (x *MonitorCampaignDeliveryStatus) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorCampaignDeliveryStatus.ProtoReflect.Descriptor instead.
func (*MonitorCampaignDeliveryStatus) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{25}
}

func (x *MonitorCampaignDeliveryStatus) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *MonitorCampaignDeliveryStatus) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *MonitorCampaignDeliveryStatus) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MonitorCampaignDeliveryStatus) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *MonitorCampaignDeliveryStatus) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *MonitorCampaignDeliveryStatus) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *MonitorCampaignDeliveryStatus) GetPreviousDeliveryStatus() string {
	if x != nil {
		return x.PreviousDeliveryStatus
	}
	return ""
}

func (x *MonitorCampaignDeliveryStatus) GetCurrentDeliveryStatus() string {
	if x != nil {
		return x.CurrentDeliveryStatus
	}
	return ""
}

type MonitorAdgroupDeliveryStatusList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdgroupDeliveryList []*MonitorAdgroupDeliveryStatus `protobuf:"bytes,1,rep,name=adgroup_delivery_list,json=adgroupDeliveryList,proto3" json:"adgroup_delivery_list,omitempty"`
}

func (x *MonitorAdgroupDeliveryStatusList) Reset() {
	*x = MonitorAdgroupDeliveryStatusList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorAdgroupDeliveryStatusList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorAdgroupDeliveryStatusList) ProtoMessage() {}

func (x *MonitorAdgroupDeliveryStatusList) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorAdgroupDeliveryStatusList.ProtoReflect.Descriptor instead.
func (*MonitorAdgroupDeliveryStatusList) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{26}
}

func (x *MonitorAdgroupDeliveryStatusList) GetAdgroupDeliveryList() []*MonitorAdgroupDeliveryStatus {
	if x != nil {
		return x.AdgroupDeliveryList
	}
	return nil
}

type MonitorAdgroupDeliveryStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode               string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                             // 游戏标识 game_code
	Media                  string `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                                                   // 渠道标识 eg: TikTok
	AccountId              string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                          // 渠道账号id
	AccountName            string `protobuf:"bytes,4,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`                                    // 渠道账号名称
	AdgroupId              string `protobuf:"bytes,5,opt,name=adgroup_id,json=adgroupId,proto3" json:"adgroup_id,omitempty"`                                          // adgroup id
	AdgroupName            string `protobuf:"bytes,6,opt,name=adgroup_name,json=adgroupName,proto3" json:"adgroup_name,omitempty"`                                    // adgroup 名称
	PreviousDeliveryStatus string `protobuf:"bytes,7,opt,name=previous_delivery_status,json=previousDeliveryStatus,proto3" json:"previous_delivery_status,omitempty"` // adgroup 上一个 delivery status
	CurrentDeliveryStatus  string `protobuf:"bytes,8,opt,name=current_delivery_status,json=currentDeliveryStatus,proto3" json:"current_delivery_status,omitempty"`    // adgroup 当前 delivery status
}

func (x *MonitorAdgroupDeliveryStatus) Reset() {
	*x = MonitorAdgroupDeliveryStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorAdgroupDeliveryStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorAdgroupDeliveryStatus) ProtoMessage() {}

func (x *MonitorAdgroupDeliveryStatus) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorAdgroupDeliveryStatus.ProtoReflect.Descriptor instead.
func (*MonitorAdgroupDeliveryStatus) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{27}
}

func (x *MonitorAdgroupDeliveryStatus) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *MonitorAdgroupDeliveryStatus) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *MonitorAdgroupDeliveryStatus) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MonitorAdgroupDeliveryStatus) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *MonitorAdgroupDeliveryStatus) GetAdgroupId() string {
	if x != nil {
		return x.AdgroupId
	}
	return ""
}

func (x *MonitorAdgroupDeliveryStatus) GetAdgroupName() string {
	if x != nil {
		return x.AdgroupName
	}
	return ""
}

func (x *MonitorAdgroupDeliveryStatus) GetPreviousDeliveryStatus() string {
	if x != nil {
		return x.PreviousDeliveryStatus
	}
	return ""
}

func (x *MonitorAdgroupDeliveryStatus) GetCurrentDeliveryStatus() string {
	if x != nil {
		return x.CurrentDeliveryStatus
	}
	return ""
}

type MonitorAdDeliveryStatusList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdDeliveryList []*MonitorAdDeliveryStatus `protobuf:"bytes,1,rep,name=ad_delivery_list,json=adDeliveryList,proto3" json:"ad_delivery_list,omitempty"`
}

func (x *MonitorAdDeliveryStatusList) Reset() {
	*x = MonitorAdDeliveryStatusList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorAdDeliveryStatusList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorAdDeliveryStatusList) ProtoMessage() {}

func (x *MonitorAdDeliveryStatusList) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorAdDeliveryStatusList.ProtoReflect.Descriptor instead.
func (*MonitorAdDeliveryStatusList) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{28}
}

func (x *MonitorAdDeliveryStatusList) GetAdDeliveryList() []*MonitorAdDeliveryStatus {
	if x != nil {
		return x.AdDeliveryList
	}
	return nil
}

type MonitorAdDeliveryStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode               string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                             // 游戏标识 game_code
	Media                  string `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`                                                                   // 渠道标识 eg: TikTok
	AccountId              string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                          // 渠道账号id
	AccountName            string `protobuf:"bytes,4,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`                                    // 渠道账号名称
	AdId                   string `protobuf:"bytes,5,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                                                         // ad id
	AdName                 string `protobuf:"bytes,6,opt,name=ad_name,json=adName,proto3" json:"ad_name,omitempty"`                                                   // ad 名称
	PreviousDeliveryStatus string `protobuf:"bytes,7,opt,name=previous_delivery_status,json=previousDeliveryStatus,proto3" json:"previous_delivery_status,omitempty"` // ad 上一个 delivery status
	CurrentDeliveryStatus  string `protobuf:"bytes,8,opt,name=current_delivery_status,json=currentDeliveryStatus,proto3" json:"current_delivery_status,omitempty"`    // ad 当前 delivery status
}

func (x *MonitorAdDeliveryStatus) Reset() {
	*x = MonitorAdDeliveryStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonitorAdDeliveryStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorAdDeliveryStatus) ProtoMessage() {}

func (x *MonitorAdDeliveryStatus) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorAdDeliveryStatus.ProtoReflect.Descriptor instead.
func (*MonitorAdDeliveryStatus) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{29}
}

func (x *MonitorAdDeliveryStatus) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *MonitorAdDeliveryStatus) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *MonitorAdDeliveryStatus) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MonitorAdDeliveryStatus) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *MonitorAdDeliveryStatus) GetAdId() string {
	if x != nil {
		return x.AdId
	}
	return ""
}

func (x *MonitorAdDeliveryStatus) GetAdName() string {
	if x != nil {
		return x.AdName
	}
	return ""
}

func (x *MonitorAdDeliveryStatus) GetPreviousDeliveryStatus() string {
	if x != nil {
		return x.PreviousDeliveryStatus
	}
	return ""
}

func (x *MonitorAdDeliveryStatus) GetCurrentDeliveryStatus() string {
	if x != nil {
		return x.CurrentDeliveryStatus
	}
	return ""
}

// 发起monitor检测任务, POST, /api/v1/central/launch_monitor_task
type LaunchMonitorTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetGameCodes   []string `protobuf:"bytes,1,rep,name=target_game_codes,json=targetGameCodes,proto3" json:"target_game_codes,omitempty"`       // 待检测目标 game_code 列表，不允许为空列表
	TargetMedias      []string `protobuf:"bytes,2,rep,name=target_medias,json=targetMedias,proto3" json:"target_medias,omitempty"`                  // 待检测目标 media 渠道列表，不允许为空列表
	TargetStatusTypes []string `protobuf:"bytes,3,rep,name=target_status_types,json=targetStatusTypes,proto3" json:"target_status_types,omitempty"` // 待检测目标 status_type 渠道列表，不允许为空列表
}

func (x *LaunchMonitorTaskReq) Reset() {
	*x = LaunchMonitorTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaunchMonitorTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaunchMonitorTaskReq) ProtoMessage() {}

func (x *LaunchMonitorTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaunchMonitorTaskReq.ProtoReflect.Descriptor instead.
func (*LaunchMonitorTaskReq) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{30}
}

func (x *LaunchMonitorTaskReq) GetTargetGameCodes() []string {
	if x != nil {
		return x.TargetGameCodes
	}
	return nil
}

func (x *LaunchMonitorTaskReq) GetTargetMedias() []string {
	if x != nil {
		return x.TargetMedias
	}
	return nil
}

func (x *LaunchMonitorTaskReq) GetTargetStatusTypes() []string {
	if x != nil {
		return x.TargetStatusTypes
	}
	return nil
}

type LaunchMonitorTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	TraceId string      `protobuf:"bytes,2,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"` // 请求trace_id
}

func (x *LaunchMonitorTaskRsp) Reset() {
	*x = LaunchMonitorTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_central_central_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LaunchMonitorTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LaunchMonitorTaskRsp) ProtoMessage() {}

func (x *LaunchMonitorTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_central_central_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LaunchMonitorTaskRsp.ProtoReflect.Descriptor instead.
func (*LaunchMonitorTaskRsp) Descriptor() ([]byte, []int) {
	return file_central_central_proto_rawDescGZIP(), []int{31}
}

func (x *LaunchMonitorTaskRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *LaunchMonitorTaskRsp) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

var File_central_central_proto protoreflect.FileDescriptor

var file_central_central_proto_rawDesc = []byte{
	0x0a, 0x15, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2f, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c,
	0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4,
	0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x64, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x0f,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x70, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x29, 0x0a, 0x10,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x4d, 0x0a,
	0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x65, 0x6e,
	0x74, 0x72, 0x61, 0x6c, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xd8, 0x01, 0x0a,
	0x13, 0x53, 0x61, 0x76, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x61, 0x76, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x55, 0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x62,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x07, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x67, 0x0a, 0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x94, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x42, 0x0a, 0x10, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x73, 0x22, 0xc5, 0x01, 0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x61, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x10, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x73, 0x22, 0x52,
	0x0a, 0x10, 0x53, 0x61, 0x76, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x49, 0x64, 0x22, 0x69, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xcd, 0x02,
	0x0a, 0x08, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x82, 0x01,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x0f, 0x53, 0x61, 0x76, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d,
	0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x80, 0x01,
	0x0a, 0x0f, 0x53, 0x61, 0x76, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x22, 0x8f, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x73, 0x22, 0x54, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0xdc, 0x03, 0x0a, 0x14, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6f, 0x0a, 0x1d, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x1a,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6c, 0x0a, 0x1c, 0x61, 0x64,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x19, 0x61,
	0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x5d, 0x0a, 0x17, 0x61, 0x64, 0x5f, 0x64,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x65, 0x6e, 0x74,
	0x72, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x64, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x48,
	0x00, 0x52, 0x14, 0x61, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x44, 0x61, 0x74, 0x61, 0x42, 0x0e, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x22, 0x56, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22,
	0x69, 0x0a, 0x18, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4d, 0x0a, 0x13, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x65, 0x6e, 0x74, 0x72,
	0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xf9, 0x01, 0x0a, 0x14, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x34, 0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x21, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5c, 0x0a, 0x16,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xcc, 0x02, 0x0a, 0x1d, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7d, 0x0a, 0x20, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x59, 0x0a,
	0x15, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x64,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x13, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc7, 0x02, 0x0a, 0x1c, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x38, 0x0a, 0x18, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x69, 0x0a, 0x1b, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x64, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x4a, 0x0a, 0x10, 0x61, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x65,
	0x6e, 0x74, 0x72, 0x61, 0x6c, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x64, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x61,
	0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xae, 0x02,
	0x0a, 0x17, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x41, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x13,
	0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61,
	0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x18,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x97,
	0x01, 0x0a, 0x14, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x56, 0x0a, 0x14, 0x4c, 0x61, 0x75, 0x6e,
	0x63, 0x68, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64,
	0x42, 0x36, 0x5a, 0x34, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74,
	0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69,
	0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73,
	0x2f, 0x63, 0x65, 0x6e, 0x74, 0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_central_central_proto_rawDescOnce sync.Once
	file_central_central_proto_rawDescData = file_central_central_proto_rawDesc
)

func file_central_central_proto_rawDescGZIP() []byte {
	file_central_central_proto_rawDescOnce.Do(func() {
		file_central_central_proto_rawDescData = protoimpl.X.CompressGZIP(file_central_central_proto_rawDescData)
	})
	return file_central_central_proto_rawDescData
}

var file_central_central_proto_msgTypes = make([]protoimpl.MessageInfo, 32)
var file_central_central_proto_goTypes = []interface{}{
	(*GetTranslationReq)(nil),                 // 0: central.GetTranslationReq
	(*TranslationParam)(nil),                  // 1: central.TranslationParam
	(*TranslationItem)(nil),                   // 2: central.TranslationItem
	(*TranslationTypeItem)(nil),               // 3: central.TranslationTypeItem
	(*GetTranslationRsp)(nil),                 // 4: central.GetTranslationRsp
	(*SaveTranslationsReq)(nil),               // 5: central.SaveTranslationsReq
	(*SaveTranslationsRsp)(nil),               // 6: central.SaveTranslationsRsp
	(*GetLocationReq)(nil),                    // 7: central.GetLocationReq
	(*RegionCategory)(nil),                    // 8: central.RegionCategory
	(*Region)(nil),                            // 9: central.Region
	(*GetLocationRsp)(nil),                    // 10: central.GetLocationRsp
	(*SaveLocationsReq)(nil),                  // 11: central.SaveLocationsReq
	(*SaveLocationsRsp)(nil),                  // 12: central.SaveLocationsRsp
	(*GetTemplatesReq)(nil),                   // 13: central.GetTemplatesReq
	(*Template)(nil),                          // 14: central.Template
	(*GetTemplatesRsp)(nil),                   // 15: central.GetTemplatesRsp
	(*SaveTemplateReq)(nil),                   // 16: central.SaveTemplateReq
	(*SaveTemplateRsp)(nil),                   // 17: central.SaveTemplateRsp
	(*DeleteTemplatesReq)(nil),                // 18: central.DeleteTemplatesReq
	(*DeleteTemplatesRsp)(nil),                // 19: central.DeleteTemplatesRsp
	(*ReportMonitorDataReq)(nil),              // 20: central.ReportMonitorDataReq
	(*ReportMonitorDataRsp)(nil),              // 21: central.ReportMonitorDataRsp
	(*MonitorAccountStatusList)(nil),          // 22: central.MonitorAccountStatusList
	(*MonitorAccountStatus)(nil),              // 23: central.MonitorAccountStatus
	(*MonitorCampaignDeliveryStatusList)(nil), // 24: central.MonitorCampaignDeliveryStatusList
	(*MonitorCampaignDeliveryStatus)(nil),     // 25: central.MonitorCampaignDeliveryStatus
	(*MonitorAdgroupDeliveryStatusList)(nil),  // 26: central.MonitorAdgroupDeliveryStatusList
	(*MonitorAdgroupDeliveryStatus)(nil),      // 27: central.MonitorAdgroupDeliveryStatus
	(*MonitorAdDeliveryStatusList)(nil),       // 28: central.MonitorAdDeliveryStatusList
	(*MonitorAdDeliveryStatus)(nil),           // 29: central.MonitorAdDeliveryStatus
	(*LaunchMonitorTaskReq)(nil),              // 30: central.LaunchMonitorTaskReq
	(*LaunchMonitorTaskRsp)(nil),              // 31: central.LaunchMonitorTaskRsp
	(*aix.Result)(nil),                        // 32: aix.Result
}
var file_central_central_proto_depIdxs = []int32{
	1,  // 0: central.GetTranslationReq.translations:type_name -> central.TranslationParam
	2,  // 1: central.TranslationTypeItem.items:type_name -> central.TranslationItem
	32, // 2: central.GetTranslationRsp.result:type_name -> aix.Result
	3,  // 3: central.GetTranslationRsp.translation_results:type_name -> central.TranslationTypeItem
	3,  // 4: central.SaveTranslationsReq.translation_type_items:type_name -> central.TranslationTypeItem
	32, // 5: central.SaveTranslationsRsp.result:type_name -> aix.Result
	9,  // 6: central.RegionCategory.regions:type_name -> central.Region
	32, // 7: central.GetLocationRsp.result:type_name -> aix.Result
	8,  // 8: central.GetLocationRsp.region_categorys:type_name -> central.RegionCategory
	8,  // 9: central.SaveLocationsReq.region_categorys:type_name -> central.RegionCategory
	32, // 10: central.SaveLocationsRsp.result:type_name -> aix.Result
	32, // 11: central.GetTemplatesRsp.result:type_name -> aix.Result
	14, // 12: central.GetTemplatesRsp.templates:type_name -> central.Template
	14, // 13: central.SaveTemplateReq.template:type_name -> central.Template
	32, // 14: central.SaveTemplateRsp.result:type_name -> aix.Result
	14, // 15: central.SaveTemplateRsp.template:type_name -> central.Template
	32, // 16: central.DeleteTemplatesRsp.result:type_name -> aix.Result
	22, // 17: central.ReportMonitorDataReq.account_status_data:type_name -> central.MonitorAccountStatusList
	24, // 18: central.ReportMonitorDataReq.campaign_delivery_status_data:type_name -> central.MonitorCampaignDeliveryStatusList
	26, // 19: central.ReportMonitorDataReq.adgroup_delivery_status_data:type_name -> central.MonitorAdgroupDeliveryStatusList
	28, // 20: central.ReportMonitorDataReq.ad_delivery_status_data:type_name -> central.MonitorAdDeliveryStatusList
	32, // 21: central.ReportMonitorDataRsp.result:type_name -> aix.Result
	23, // 22: central.MonitorAccountStatusList.account_status_list:type_name -> central.MonitorAccountStatus
	25, // 23: central.MonitorCampaignDeliveryStatusList.campaign_delivery_list:type_name -> central.MonitorCampaignDeliveryStatus
	27, // 24: central.MonitorAdgroupDeliveryStatusList.adgroup_delivery_list:type_name -> central.MonitorAdgroupDeliveryStatus
	29, // 25: central.MonitorAdDeliveryStatusList.ad_delivery_list:type_name -> central.MonitorAdDeliveryStatus
	32, // 26: central.LaunchMonitorTaskRsp.result:type_name -> aix.Result
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_central_central_proto_init() }
func file_central_central_proto_init() {
	if File_central_central_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_central_central_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTranslationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslationParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslationTypeItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTranslationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTranslationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTranslationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegionCategory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Region); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLocationRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveLocationsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveLocationsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplatesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Template); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTemplatesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTemplateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTemplateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTemplatesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTemplatesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportMonitorDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportMonitorDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorAccountStatusList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorAccountStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorCampaignDeliveryStatusList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorCampaignDeliveryStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorAdgroupDeliveryStatusList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorAdgroupDeliveryStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorAdDeliveryStatusList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonitorAdDeliveryStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaunchMonitorTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_central_central_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LaunchMonitorTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_central_central_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*ReportMonitorDataReq_AccountStatusData)(nil),
		(*ReportMonitorDataReq_CampaignDeliveryStatusData)(nil),
		(*ReportMonitorDataReq_AdgroupDeliveryStatusData)(nil),
		(*ReportMonitorDataReq_AdDeliveryStatusData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_central_central_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   32,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_central_central_proto_goTypes,
		DependencyIndexes: file_central_central_proto_depIdxs,
		MessageInfos:      file_central_central_proto_msgTypes,
	}.Build()
	File_central_central_proto = out.File
	file_central_central_proto_rawDesc = nil
	file_central_central_proto_goTypes = nil
	file_central_central_proto_depIdxs = nil
}
