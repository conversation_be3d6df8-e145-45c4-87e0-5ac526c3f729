package router

import (
	_ "time"

	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/service"
	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/middleware"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/preprocess"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise"
	"github.com/gin-gonic/gin"
)

// InitRouter 初始化路由
func InitRouter() *gin.Engine {
	r := gin.New()
	r.Use(middleware.GinLogger())
	r.Use(middleware.GinRecovery(true))

	// 更新操作类接口接入审计
	r.Use(middleware.GinOperationAudit(constant.MediaNameApplovin, []string{
		"create_creative_set", "change_ad_status",
	}))

	serverAPI := r.Group("api/v1/applovin_advertise/")

	// 测试接口
	serverAPI.POST("/say_hi", func(ctx *gin.Context) {
		req, rsp := &pb.SayHiReq{}, &pb.SayHiRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "say_hi req: %+v, err:%v", req, err)
		if err == nil {
			err = service.SayHi(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})
	serverAPI.POST("/cron_trigger", func(ctx *gin.Context) {
		req, rsp := &service.CronTriggerReq{}, &service.CronTriggerRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "cron_trigger req: %+v, err:%v", req, err)
		if err == nil {
			err = service.CronTrigger(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 获取媒体账号列表
	serverAPI.POST("/get_media_accounts", func(ctx *gin.Context) {
		req, rsp := &pb.GetMediaAccountsReq{}, &pb.GetMediaAccountsRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "get_media_accounts req: %+v, err:%v", req, err)
		if err == nil {
			err = service.GetMediaAccounts(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 拉取某个账号下的campaign列表
	serverAPI.POST("/get_campaigns", func(ctx *gin.Context) {
		req, rsp := &pb.GetCampaignsReq{}, &pb.GetCampaignsRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "get_campaigns req: %+v, err:%v", req, err)
		if err == nil {
			err = service.GetCampaigns(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 拉取campaign下的creative_set列表
	serverAPI.POST("/get_creative_set_by_campaign", func(ctx *gin.Context) {
		req, rsp := &pb.GetCreativeSetByCampaignReq{}, &pb.GetCreativeSetByCampaignRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "get_creative_set_by_campaign req: %+v, err:%v", req, err)
		if err == nil {
			err = service.GetCreativeSetByCampaign(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 创建creative_set
	serverAPI.POST("/create_creative_set", func(ctx *gin.Context) {
		req, rsp := &pb.CreateCreativeSetReq{}, &pb.CreateCreativeSetRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "get_creative_set_by_campaign req: %+v, err:%v", req, err)
		if err == nil {
			err = service.CreateCreativeSet(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 拉取creative_set下的ads列表
	serverAPI.POST("/get_ads_by_creative_set", func(ctx *gin.Context) {
		req, rsp := &pb.GetAdsByCreativeSetReq{}, &pb.GetAdsByCreativeSetRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "get_ads_by_creative_set req: %+v, err:%v", req, err)
		if err == nil {
			err = service.GetAdsByCreativeSet(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	// 更新广告ad状态
	serverAPI.POST("/change_ad_status", func(ctx *gin.Context) {
		req, rsp := &pb.ChangeAdStatusReq{}, &pb.ChangeAdStatusRsp{}
		err := preprocess.BindRequest(ctx, req)
		log.InfoContextf(ctx, "change_ad_status req: %+v, err:%v", req, err)
		if err == nil {
			err = service.ChangeAdStatus(ctx, req, rsp)
		}
		errs.FullRspContext(ctx, rsp, err)
		preprocess.Response(ctx, rsp)
	})

	return r
}
