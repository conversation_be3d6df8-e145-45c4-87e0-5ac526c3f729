// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: slack_bot/slack_bot.proto

package slack_bot

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 自测接口, POST, /api/v1/slack_bot/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slack_bot_slack_bot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_slack_bot_slack_bot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_slack_bot_slack_bot_proto_rawDescGZIP(), []int{0}
}

type Attachment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title       string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`                                // 必填 标题
	DownloadUrl string `protobuf:"bytes,2,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"` // 必填 下载链接
}

func (x *Attachment) Reset() {
	*x = Attachment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slack_bot_slack_bot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attachment) ProtoMessage() {}

func (x *Attachment) ProtoReflect() protoreflect.Message {
	mi := &file_slack_bot_slack_bot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attachment.ProtoReflect.Descriptor instead.
func (*Attachment) Descriptor() ([]byte, []int) {
	return file_slack_bot_slack_bot_proto_rawDescGZIP(), []int{1}
}

func (x *Attachment) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Attachment) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

// 发送bot消息, POST, /api/v1/slack_bot/send_message
type SendMessageReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Studio      string        `protobuf:"bytes,1,opt,name=studio,proto3" json:"studio,omitempty"`                        // 必填 studio
	ChannelId   string        `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"` // 必填 channel
	ThreadId    string        `protobuf:"bytes,3,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`    // 必填 thread
	Mrkdwn      string        `protobuf:"bytes,4,opt,name=mrkdwn,proto3" json:"mrkdwn,omitempty"`                        // 必填 正文, mrkdw格式: https://api.slack.com/reference/surfaces/formatting
	Attachments []*Attachment `protobuf:"bytes,5,rep,name=attachments,proto3" json:"attachments,omitempty"`              // 附件列表
}

func (x *SendMessageReq) Reset() {
	*x = SendMessageReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slack_bot_slack_bot_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageReq) ProtoMessage() {}

func (x *SendMessageReq) ProtoReflect() protoreflect.Message {
	mi := &file_slack_bot_slack_bot_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageReq.ProtoReflect.Descriptor instead.
func (*SendMessageReq) Descriptor() ([]byte, []int) {
	return file_slack_bot_slack_bot_proto_rawDescGZIP(), []int{2}
}

func (x *SendMessageReq) GetStudio() string {
	if x != nil {
		return x.Studio
	}
	return ""
}

func (x *SendMessageReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *SendMessageReq) GetThreadId() string {
	if x != nil {
		return x.ThreadId
	}
	return ""
}

func (x *SendMessageReq) GetMrkdwn() string {
	if x != nil {
		return x.Mrkdwn
	}
	return ""
}

func (x *SendMessageReq) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

type SendMessageRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SendMessageRsp) Reset() {
	*x = SendMessageRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_slack_bot_slack_bot_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendMessageRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRsp) ProtoMessage() {}

func (x *SendMessageRsp) ProtoReflect() protoreflect.Message {
	mi := &file_slack_bot_slack_bot_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRsp.ProtoReflect.Descriptor instead.
func (*SendMessageRsp) Descriptor() ([]byte, []int) {
	return file_slack_bot_slack_bot_proto_rawDescGZIP(), []int{3}
}

func (x *SendMessageRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_slack_bot_slack_bot_proto protoreflect.FileDescriptor

var file_slack_bot_slack_bot_proto_rawDesc = []byte{
	0x0a, 0x19, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2f, 0x73, 0x6c, 0x61, 0x63,
	0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x73, 0x6c, 0x61,
	0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x0a, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71,
	0x22, 0x45, 0x0a, 0x0a, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0xb5, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x75, 0x64, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x75, 0x64,
	0x69, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x72, 0x6b, 0x64, 0x77, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6d, 0x72, 0x6b, 0x64, 0x77, 0x6e, 0x12, 0x37, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x6c,
	0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22,
	0x35, 0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x38, 0x5a, 0x36, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x73, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_slack_bot_slack_bot_proto_rawDescOnce sync.Once
	file_slack_bot_slack_bot_proto_rawDescData = file_slack_bot_slack_bot_proto_rawDesc
)

func file_slack_bot_slack_bot_proto_rawDescGZIP() []byte {
	file_slack_bot_slack_bot_proto_rawDescOnce.Do(func() {
		file_slack_bot_slack_bot_proto_rawDescData = protoimpl.X.CompressGZIP(file_slack_bot_slack_bot_proto_rawDescData)
	})
	return file_slack_bot_slack_bot_proto_rawDescData
}

var file_slack_bot_slack_bot_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_slack_bot_slack_bot_proto_goTypes = []interface{}{
	(*SayHiReq)(nil),       // 0: slack_bot.SayHiReq
	(*Attachment)(nil),     // 1: slack_bot.Attachment
	(*SendMessageReq)(nil), // 2: slack_bot.SendMessageReq
	(*SendMessageRsp)(nil), // 3: slack_bot.SendMessageRsp
	(*aix.Result)(nil),     // 4: aix.Result
}
var file_slack_bot_slack_bot_proto_depIdxs = []int32{
	1, // 0: slack_bot.SendMessageReq.attachments:type_name -> slack_bot.Attachment
	4, // 1: slack_bot.SendMessageRsp.result:type_name -> aix.Result
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_slack_bot_slack_bot_proto_init() }
func file_slack_bot_slack_bot_proto_init() {
	if File_slack_bot_slack_bot_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_slack_bot_slack_bot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slack_bot_slack_bot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attachment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slack_bot_slack_bot_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_slack_bot_slack_bot_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendMessageRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_slack_bot_slack_bot_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_slack_bot_slack_bot_proto_goTypes,
		DependencyIndexes: file_slack_bot_slack_bot_proto_depIdxs,
		MessageInfos:      file_slack_bot_slack_bot_proto_msgTypes,
	}.Build()
	File_slack_bot_slack_bot_proto = out.File
	file_slack_bot_slack_bot_proto_rawDesc = nil
	file_slack_bot_slack_bot_proto_goTypes = nil
	file_slack_bot_slack_bot_proto_depIdxs = nil
}
