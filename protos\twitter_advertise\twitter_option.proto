syntax = "proto3";

package twitter_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/twitter_advertise";

message FundingInstrument {
  string id = 1;            // funding sources id
  string description = 2;   // 名称
}

message AppInfo {
  string app_store_identifier = 1;  // app id
  string os_type = 2;       // app 平台
}

message Location {
  string name = 1;            // 地区名称
  string country_code = 2;    // 地区所在国家
  string loaction_type = 3;   // 地区分类 city country
  string targeting_value = 4; // 值，在设置时，填入该值
  string targeting_type = 5;  // 类型
}

message Language {
  string name = 1;              // 名称
  string targeting_value = 2;   // 值，在设置时，填入该值
  string targeting_type = 3;    // 类型
}

message OsVersion {
  string name = 1;            // 名称
  string targeting_value = 2; // 值，在设置时，填入该值
  string targeting_type = 3;  // 类型
  string number = 4;
  string os_type = 5;
}

message AudienceMetaData {
  string app_id = 1;          // app id
  string os_type = 2;         // 系统类型，eg:ANDROID, IOS
  string event_type = 3;      // 事件类型，
  string lookback_window = 4; // 回顾周期，eg: "1", "7", "14", "30", 空表示all
}

message CustomAudience {
  string id = 1;                  // audience id，在设置时，填入该值
  string name = 2;                // 名称
  bool targetable = 3;            // 状态
  AudienceMetaData metadata = 4;  // 
  string audience_type = 5;       // 类型
  int64 audience_size = 6;        // 观众数量
}

message Interest {
  string name = 1;            // 名称
  string targeting_value = 2; // 值，在设置时，填入该值
  string targeting_type = 3;  // 类型
}

message WebEventTag {
  string name = 1;              // 名称
  string id = 2;                // id 值
  string type = 3;              // 类型 eg：CUSTOM
  string website_tag_id = 4;
  string status = 5;
}