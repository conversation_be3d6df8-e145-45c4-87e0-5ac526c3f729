# 接口说明

## 目录:

|接口|请求方法|路径|
|--|--|--|
|[批量获取卡片详情](#/api/v1/audience_launcher/bt_get_card_info)|`POST`|`/api/v1/audience_launcher/bt_get_card_info`|
|[获取卡片列表](#/api/v1/audience_launcher/get_card_info_list)|`POST`|`/api/v1/audience_launcher/get_card_info_list`|
## 结构说明:

### <span id="/api/v1/audience_launcher/bt_get_card_info">批量获取卡片详情</span>

|项目|值|
|--|--|
|请求方法|`POST`|
|请求路径|`/api/v1/audience_launcher/bt_get_card_info`|
|请求结构说明|[req](#/api/v1/audience_launcher/bt_get_card_info_req)|
|返回结构说明|[rsp](#/api/v1/audience_launcher/bt_get_card_info_rsp)|
#### <span id="/api/v1/audience_launcher/bt_get_card_info_req">请求数据 Req</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "launcher_id_list":{
            "items":{
                "type":"string",
                "description":"卡片id"
            },
            "type":"array",
            "description":"卡片id"
        }
    }
}

```

- 示例

```json
{
    "launcher_id_list":[
        ""
    ]
}

```

#### <span id="/api/v1/audience_launcher/bt_get_card_info_rsp">返回数据 Rsp</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "card_info":{
            "items":{
                "type":"object",
                "description":"卡片id",
                "properties":{
                    "card_type":{
                        "type":"integer",
                        "description":"卡片类型1-标准2-profile"
                    },
                    "base_info":{
                        "type":"object",
                        "description":"卡片基础信息",
                        "properties":{
                            "launcher_id":{
                                "type":"string",
                                "description":"卡片id"
                            },
                            "launcher_name":{
                                "type":"string",
                                "description":"卡片名称"
                            },
                            "launcher_intro":{
                                "type":"string",
                                "description":"卡片简介"
                            },
                            "label_type":{
                                "type":"integer",
                                "description":"标签类型LabelType:0-标准1-智能"
                            }
                        }
                    },
                    "ext_info":{
                        "type":"object",
                        "description":"卡片附加信息",
                        "properties":{
                            "live":{
                                "type":"integer",
                                "description":"live数据"
                            },
                            "optimize_goal":{
                                "type":"integer",
                                "description":"优化目标，位操作0-空1-活跃向优化2-付费向优化"
                            },
                            "optimize_retarget":{
                                "type":"integer",
                                "description":"1-newinstallretention%2-newinstallpurchase3-reattributionreattribution"
                            },
                            "media_type":{
                                "type":"integer",
                                "description":"1-GG"
                            },
                            "history_account":{
                                "type":"integer",
                                "description":"0-不能拉历史account1-能拉到历史account"
                            },
                            "created_by":{
                                "type":"integer",
                                "description":"创建者1-model2-rule-based"
                            },
                            "high_type":{
                                "type":"integer",
                                "description":"1-highact2-highmodel3-highattribution"
                            },
                            "high_percent":{
                                "type":"float",
                                "description":"百分比不超过1"
                            },
                            "audience_type":{
                                "type":"integer",
                                "description":"audience的类型1-event"
                            },
                            "frequency":{
                                "type":"integer",
                                "description":"频率1-daily"
                            },
                            "country":{
                                "items":{
                                    "type":"string",
                                    "description":"国家\"all\"代表所有"
                                },
                                "type":"array",
                                "description":"国家\"all\"代表所有"
                            },
                            "os":{
                                "type":"string",
                                "description":"系统\"all\"代表所有"
                            },
                            "install_range":{
                                "type":"integer",
                                "description":"<0负数代表前x天"
                            },
                            "install_register":{
                                "type":"integer",
                                "description":"<0负数代表前x天"
                            },
                            "install_active":{
                                "type":"integer",
                                "description":"<0负数代表前x天"
                            },
                            "install_ex_active":{
                                "type":"integer",
                                "description":"<0负数代表前x天"
                            }
                        }
                    }
                }
            },
            "type":"array",
            "description":"卡片id"
        }
    }
}

```

- 示例

```json
{
    "card_info":[
        {
            "card_type":0,
            "ext_info":{
                "high_type":0,
                "install_active":0,
                "country":[
                    ""
                ],
                "install_ex_active":0,
                "optimize_goal":0,
                "created_by":0,
                "live":0,
                "frequency":0,
                "audience_type":0,
                "optimize_retarget":0,
                "install_range":0,
                "media_type":0,
                "history_account":0,
                "os":"",
                "high_percent":0.0,
                "install_register":0
            },
            "base_info":{
                "label_type":0,
                "launcher_id":"",
                "launcher_intro":"",
                "launcher_name":""
            }
        }
    ]
}

```

### <span id="/api/v1/audience_launcher/get_card_info_list">获取卡片列表</span>

|项目|值|
|--|--|
|请求方法|`POST`|
|请求路径|`/api/v1/audience_launcher/get_card_info_list`|
|请求结构说明|[req](#/api/v1/audience_launcher/get_card_info_list_req)|
|返回结构说明|[rsp](#/api/v1/audience_launcher/get_card_info_list_rsp)|
#### <span id="/api/v1/audience_launcher/get_card_info_list_req">请求数据 Req</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{}
}

```

- 示例

```json
{}

```

#### <span id="/api/v1/audience_launcher/get_card_info_list_rsp">返回数据 Rsp</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "result":{
            "type":"object",
            "description":"",
            "properties":{
                "error_code":{
                    "type":"integer",
                    "description":""
                },
                "error_message":{
                    "type":"string",
                    "description":""
                }
            }
        },
        "card_group_list":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "group_name":{
                        "type":"string",
                        "description":"卡片分组名称"
                    },
                    "card_info_list":{
                        "items":{
                            "type":"object",
                            "description":"卡片详情列表",
                            "properties":{
                                "card_type":{
                                    "type":"integer",
                                    "description":"卡片类型1-标准2-profile"
                                },
                                "base_info":{
                                    "type":"object",
                                    "description":"卡片基础信息",
                                    "properties":{
                                        "launcher_id":{
                                            "type":"string",
                                            "description":"卡片id"
                                        },
                                        "launcher_name":{
                                            "type":"string",
                                            "description":"卡片名称"
                                        },
                                        "launcher_intro":{
                                            "type":"string",
                                            "description":"卡片简介"
                                        },
                                        "label_type":{
                                            "type":"integer",
                                            "description":"标签类型LabelType:0-标准1-智能"
                                        }
                                    }
                                },
                                "ext_info":{
                                    "type":"object",
                                    "description":"卡片附加信息",
                                    "properties":{
                                        "live":{
                                            "type":"integer",
                                            "description":"live数据"
                                        },
                                        "optimize_goal":{
                                            "type":"integer",
                                            "description":"优化目标，位操作0-空1-活跃向优化2-付费向优化"
                                        },
                                        "optimize_retarget":{
                                            "type":"integer",
                                            "description":"1-newinstallretention%2-newinstallpurchase3-reattributionreattribution"
                                        },
                                        "media_type":{
                                            "type":"integer",
                                            "description":"1-GG"
                                        },
                                        "history_account":{
                                            "type":"integer",
                                            "description":"0-不能拉历史account1-能拉到历史account"
                                        },
                                        "created_by":{
                                            "type":"integer",
                                            "description":"创建者1-model2-rule-based"
                                        },
                                        "high_type":{
                                            "type":"integer",
                                            "description":"1-highact2-highmodel3-highattribution"
                                        },
                                        "high_percent":{
                                            "type":"float",
                                            "description":"百分比不超过1"
                                        },
                                        "audience_type":{
                                            "type":"integer",
                                            "description":"audience的类型1-event"
                                        },
                                        "frequency":{
                                            "type":"integer",
                                            "description":"频率1-daily"
                                        },
                                        "country":{
                                            "items":{
                                                "type":"string",
                                                "description":"国家\"all\"代表所有"
                                            },
                                            "type":"array",
                                            "description":"国家\"all\"代表所有"
                                        },
                                        "os":{
                                            "type":"string",
                                            "description":"系统\"all\"代表所有"
                                        },
                                        "install_range":{
                                            "type":"integer",
                                            "description":"<0负数代表前x天"
                                        },
                                        "install_register":{
                                            "type":"integer",
                                            "description":"<0负数代表前x天"
                                        },
                                        "install_active":{
                                            "type":"integer",
                                            "description":"<0负数代表前x天"
                                        },
                                        "install_ex_active":{
                                            "type":"integer",
                                            "description":"<0负数代表前x天"
                                        }
                                    }
                                }
                            }
                        },
                        "type":"array",
                        "description":"卡片详情列表"
                    }
                }
            },
            "type":"array",
            "description":""
        }
    }
}

```

- 示例

```json
{
    "card_group_list":[
        {
            "card_info_list":[
                {
                    "card_type":0,
                    "ext_info":{
                        "high_type":0,
                        "install_active":0,
                        "country":[
                            ""
                        ],
                        "install_ex_active":0,
                        "optimize_goal":0,
                        "created_by":0,
                        "live":0,
                        "frequency":0,
                        "audience_type":0,
                        "optimize_retarget":0,
                        "install_range":0,
                        "media_type":0,
                        "history_account":0,
                        "os":"",
                        "high_percent":0.0,
                        "install_register":0
                    },
                    "base_info":{
                        "label_type":0,
                        "launcher_id":"",
                        "launcher_intro":"",
                        "launcher_name":""
                    }
                }
            ],
            "group_name":""
        }
    ],
    "result":{
        "error_message":"",
        "error_code":0
    }
}

```

