package data

import (
	"context"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"github.com/go-pg/pg/v10"
	"github.com/thoas/go-funk"
)

// InsertChannelAssetLablesTransaction 插入渠道素材标签，会先删除旧标签，事务中处理
// deleteRows 先删除那些符合条件的行 (channel_type, channel_account_id, channel_asset_id)
// labels 待插入的数据
func InsertChannelAssetLablesTransaction(ctx context.Context,
	gameCode string, deleteRows []*model.ChannelAssetLabel, lables []*model.ChannelAssetLabel) error {
	if len(deleteRows) == 0 || len(lables) == 0 {
		return nil
	}

	var multi [][]interface{}
	for _, row := range deleteRows {
		one := []interface{}{row.ChannelType, row.ChannelAccountID, row.ChannelAssetId}
		multi = append(multi, one)
	}

	table := model.GetChannelAssetLabelTableName(gameCode)
	err := postgresql.Transaction(ctx, func(tx *pg.Tx) error {
		// 先删除之前的标签
		deleteQuery := tx.Model(&model.ChannelAssetLabel{}).Table(table)
		deleteQuery.WhereInMulti("(channel_type, channel_account_id, channel_asset_id) in ?", multi)
		_, err := deleteQuery.Delete()
		if err != nil {
			return err
		}

		// 分批插入新的标签
		chunks := funk.Chunk(lables, 1000).([][]*model.ChannelAssetLabel)
		for _, chunk := range chunks {
			insertQuery := tx.Model(&chunk).Table(table)
			_, err = insertQuery.Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})

	return err
}

// GetBatchChannelAssetsLables 拉取批量渠道素材的所有标签
func GetBatchChannelAssetsLables(ctx context.Context, gameCode string, assetIDs []string) ([]*model.ChannelAssetLabel, error) {
	if len(assetIDs) == 0 {
		return nil, nil
	}
	var rlt []*model.ChannelAssetLabel
	table := model.GetChannelAssetLabelTableName(gameCode)
	// 分批
	chunks := funk.ChunkStrings(assetIDs, 100)
	for _, chunk := range chunks {
		var found []*model.ChannelAssetLabel
		query := postgresql.GetDBWithContext(ctx).Model(&found).Table(table)
		query.WhereIn("channel_asset_id IN (?)", chunk)
		err := query.Select()
		if err != nil {
			return nil, err
		}

		rlt = append(rlt, found...)
	}

	return rlt, nil
}
