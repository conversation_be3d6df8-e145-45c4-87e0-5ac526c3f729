package data

import (
	"context"
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
)

// UpsertMediaUploadTask 插入更新上传任务
func UpsertMediaUploadTask(ctx context.Context, gameCode string, task *model.CreativeMediaUploadTask) error {
	return BatchUpsertMediaUploadTask(ctx, gameCode, []*model.CreativeMediaUploadTask{task})
}

// BatchUpsertMediaUploadTask 批量插入更新上传任务
func BatchUpsertMediaUploadTask(ctx context.Context, gameCode string, tasks []*model.CreativeMediaUploadTask) error {
	if len(tasks) == 0 {
		return nil
	}

	table := getMediaUploadTaskTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	query := db.Model(&tasks).Table(table)
	query.OnConflict("(asset_id, channel) DO UPDATE")
	query.Set("asset_name = EXCLUDED.asset_name")
	query.Set("format_type = EXCLUDED.format_type")
	query.Set("full_path_id = EXCLUDED.full_path_id")
	query.Set("full_path_name = EXCLUDED.full_path_name")
	query.Set("status = EXCLUDED.status")
	query.Set("active = EXCLUDED.active")
	query.Set("retry = EXCLUDED.retry")
	query.Set("country = EXCLUDED.country")
	query.Set("directory_id = EXCLUDED.directory_id")
	query.Set("directory_name = EXCLUDED.directory_name")
	query.Set("err_memo = EXCLUDED.err_memo")
	query.Set("err_detail = EXCLUDED.err_detail")
	query.Set("create_time = EXCLUDED.create_time") // 更新creat_time，使任务排序靠前
	query.Set("update_time = EXCLUDED.update_time")
	query.Set("accounts = EXCLUDED.accounts")
	query.Set("notify_days = EXCLUDED.notify_days")
	query.Set("notify_try_cnt = EXCLUDED.notify_try_cnt")
	query.Set("language = EXCLUDED.language")
	query.Set("sort_num = EXCLUDED.sort_num")
	query.Set("extra_info = EXCLUDED.extra_info")
	// resource_name 和 youtube_id不更新，保留后续使用
	// automatic_sync_task_rule_id不更新，以第一次为准，0-表示手动上传

	_, err := query.Insert()
	return err
}

// QueryUploadTasksByAssetIDList 根据素材id列表查历史任务
func QueryUploadTasksByAssetIDList(ctx context.Context, gameCode string, assetIDList []string) ([]*model.CreativeMediaUploadTask, error) {
	table := getMediaUploadTaskTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	var founds []*model.CreativeMediaUploadTask
	query := db.Model(&model.CreativeMediaUploadTask{}).Table(table)
	err := query.WhereIn("asset_id in (?)", assetIDList).Select(&founds)

	return founds, err
}

// CancelUploadTaskByTaskIDList 取消正在运行的上传任务
func CancelUploadTaskByTaskIDList(ctx context.Context, gameCode string, taskIDList []int64) error {
	table := getMediaUploadTaskTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	query := db.Model(&model.CreativeMediaUploadTask{}).Table(table)
	query.WhereIn("id in (?)", taskIDList)
	query.Where("status = 1") // 正在上传
	_, err := query.Set("status = 4").Update()
	return err
}

// CancelUploadTaskByGame 取消游戏下的上传任务
func CancelUploadTaskByGame(ctx context.Context, gameCode string) error {
	table := getMediaUploadTaskTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	query := db.Model(&model.CreativeMediaUploadTask{}).Table(table)
	query.Where("status = ?", 1) // 正在上传
	_, err := query.Set("status = 4").Update()
	return err
}

// ResumeUploadTaskByTaskIDList 重启上传任务
func ResumeUploadTaskByTaskIDList(ctx context.Context, gameCode string, taskIDList []int64) error {
	table := getMediaUploadTaskTable(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	query := db.Model(&model.CreativeMediaUploadTask{}).Table(table)
	query.WhereIn("id in (?)", taskIDList)
	query.Set("status = 1").Set("retry = 0").Set("sort_num = 0")
	query.Set("err_memo = ?", "").Set("err_detail = ?", "")
	// 更新creat_time，使任务排序靠前
	query.Set("create_time = ?", utils.GetNowStr())
	query.Set("update_time = ?", utils.GetNowStr())
	_, err := query.Update()
	return err
}

func getMediaUploadTaskTable(gameCode string) string {
	return fmt.Sprintf("arthub_sync.tb_creative_media_upload_task_%s", gameCode)
}
