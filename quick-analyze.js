#!/usr/bin/env node

/**
 * 快速Go结构体分析工具
 * 简化版本，专注于快速分析和输出
 */

const { analyzeStruct } = require('./struct-analyzer.js');

/**
 * 快速分析并输出结构体依赖
 * @param {string} fullStructName - 完整结构体名称，如 "material_display.GetKeywordListReq"
 * @param {boolean} showRaw - 是否显示原始格式
 */
function quickAnalyze(fullStructName, showRaw = false) {
    console.log(`🔍 正在分析结构体: ${fullStructName}`);
    console.log('='.repeat(60));

    const startTime = Date.now();
    const dependencies = analyzeStruct(fullStructName, '.', showRaw);
    const endTime = Date.now();
    
    if (!dependencies || dependencies.size === 0) {
        console.log(`❌ 未找到结构体 ${fullStructName}`);
        return;
    }
    
    console.log(`✅ 分析完成! 耗时: ${endTime - startTime}ms`);
    console.log(`📊 找到 ${dependencies.size} 个相关结构体\n`);
    
    // 输出依赖树
    console.log('📋 依赖关系:');
    console.log('-'.repeat(40));
    
    const structList = Array.from(dependencies.keys());
    structList.forEach((name, index) => {
        const isLast = index === structList.length - 1;
        const prefix = isLast ? '└── ' : '├── ';
        console.log(`${prefix}${name}`);
    });
    
    console.log('\n' + '='.repeat(60));
    console.log('📝 完整结构体定义:');
    console.log('='.repeat(60));
    
    // 输出每个结构体的简化信息
    for (const [structName, structDef] of dependencies) {
        // 显示结构体注释（如果有）
        if (structDef.comment) {
            console.log(`\n${structDef.comment}`);
        } else {
            console.log(`\n// ${structName}`);
        }

        console.log('type', structName, 'struct {');

        // 只显示业务字段，过滤protobuf内部字段
        const businessFields = structDef.fields.filter(field =>
            !['state', 'sizeCache', 'unknownFields'].includes(field.name)
        );

        if (businessFields.length === 0) {
            console.log('    // 只包含基础类型字段');
        } else {
            businessFields.forEach(field => {
                // 优先使用字段的comment，如果没有则从tags中提取
                let comment = field.comment;
                if (!comment && field.tags.includes('//')) {
                    comment = field.tags.split('//')[1]?.trim();
                }
                const commentStr = comment ? ` // ${comment}` : '';
                console.log(`    ${field.name.padEnd(15)} ${field.type}${commentStr}`);
            });
        }

        console.log('}');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 分析完成!');
    
    return dependencies;
}

/**
 * 显示使用帮助
 */
function showHelp() {
    console.log(`
🚀 Go结构体快速分析工具

用法:
    node quick-analyze.js <完整结构体名称> [选项]

参数:
    <完整结构体名称>  格式: package.StructName
                     如: material_display.GetKeywordListReq
                         material_sync.StartMediaContentMapReq
                         aix.Result

选项:
    --raw, -r     显示原始格式（包含所有protobuf字段和完整注释）
    --help, -h    显示帮助信息

示例:
    node quick-analyze.js material_display.GetKeywordListReq
    node quick-analyze.js material_sync.StartMediaContentMapReq --raw
    node quick-analyze.js aix.Result
    node quick-analyze.js material_display.GetMaterialInfoRsp

支持的结构体:
    - material_display.GetKeywordListReq     (获取关键词列表请求)
    - material_display.GetMaterialInfoRsp    (获取素材信息响应)
    - material_sync.StartMediaContentMapReq  (开始媒体内容映射请求)
    - aix.Result                             (通用结果)
    - 以及其他protos目录下的所有结构体

更多信息请查看 README.md
`);
}

/**
 * 主函数
 */
function main() {
    const args = process.argv.slice(2);

    if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
        showHelp();
        return;
    }

    const fullStructName = args[0];
    const showRaw = args.includes('--raw') || args.includes('-r');

    try {
        quickAnalyze(fullStructName, showRaw);
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
        console.log('\n💡 提示: 请确保在项目根目录下运行此命令');
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    quickAnalyze,
    showHelp
};
