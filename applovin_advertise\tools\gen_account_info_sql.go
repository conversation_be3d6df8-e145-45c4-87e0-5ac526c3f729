package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/http"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/applovin"

	jsoniter "github.com/json-iterator/go"
	"gopkg.in/yaml.v2"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

type accountConfig struct {
	Updater      string `yaml:"updater"`
	GameCode     string `yaml:"game_code"`
	AccountID    string `yaml:"account_id"`
	AccountName  string `yaml:"account_name"`
	ClientID     string `yaml:"client_id"`
	ClientSecret string `yaml:"client_secret"`
}

func main() {
	ctx := context.Background()

	// 取账号配置
	config, err := getAccountConfig()
	if err != nil {
		log.Fatalf("getAccountConfig err:%v", err)
	}

	// 取授权信息
	s := &applovin.Config{
		AccountID:    config.AccountID,
		ClientID:     config.ClientID,
		ClientSecret: config.ClientSecret,
		RestyClient:  http.NewDefaultClientStdLog(),
	}
	client := applovin.NewClient(s)
	token, err := client.GetFirstPartyToken(ctx)
	if err != nil {
		log.Fatalf("RefreshToken err:%v", err)
	}

	auth := model.Oauth2Token{
		AccessToken:  token.AccessToken,
		ExpiresIn:    token.ExpiresIn,
		TokenType:    token.TokenType,
		ExpiresAtAIX: time.Now().Add(time.Duration(token.ExpiresIn) * time.Second).Unix(),
	}

	applovinAccountConfig := &model.ApplovinAccountConfig{
		Authorization: auth,
		ClientID:      config.ClientID,
		ClientSecret:  config.ClientSecret,
	}

	mediaAccount := &model.MediaAccount{
		GameCode:     config.GameCode,
		Media:        constant.MediaNameApplovin,
		AccountId:    config.AccountID,
		AccountName:  config.AccountName,
		CreateUser:   config.Updater,
		UpdateUser:   config.Updater,
		EnabledState: 1,
	}

	mediaAccount.AccountConfig, err = json.MarshalToString(applovinAccountConfig)
	if err != nil {
		log.Fatalf("json.MarshalToString :%v", err)
	}

	mediaAccount.AccountDetail = "{}"

	// 生成sql语句
	sql := fmt.Sprintf(`
	INSERT INTO common_config.media_account(
		game_code, media, account_name, account_id, account_config, enabled_state, 
		create_time, update_time, create_user, update_user, account_detail)
		VALUES ('%v', '%v', '%v', '%v', '%v', %d, 
				now(), now(), '%v', '%v', '%v');`,
		mediaAccount.GameCode, mediaAccount.Media, mediaAccount.AccountName, mediaAccount.AccountId, mediaAccount.AccountConfig, mediaAccount.EnabledState,
		mediaAccount.CreateUser, mediaAccount.UpdateUser, mediaAccount.AccountDetail)

	log.Print(sql)
}

func getAccountConfig() (*accountConfig, error) {
	config := &accountConfig{}
	f, err := os.Open("./account.yaml")
	if err != nil {
		return nil, err
	}
	defer f.Close()

	err = yaml.NewDecoder(f).Decode(config)
	if err != nil {
		return nil, err
	}
	return config, nil
}
