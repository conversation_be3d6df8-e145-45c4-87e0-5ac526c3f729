package service

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	aixwebRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/aix_web"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"github.com/thoas/go-funk"
	"github.com/xuri/excelize/v2"
)

// V2批量打标任务处理逻辑, 同时处理serial和asset层级
func doLableRuleTaskV2(ctx context.Context, task *model.TbLabelRuleTask, localFile string) error {
	// 处理xlsx文件
	f, err := excelize.OpenFile(localFile)
	if err != nil {
		return fmt.Errorf("excelize.OpenFile err:%v", err)
	}
	defer func() {
		// Close the spreadsheet.
		if err := f.Close(); err != nil {
			log.DebugContextf(ctx, "excelize.Close err:%v", err)
		}
	}()

	// 处理第一个sheet
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return fmt.Errorf("no sheet in file")
	}
	sheetName := sheets[0]
	rows, err := f.Rows(sheetName)
	if err != nil {
		return fmt.Errorf("excelize f.Rows err:%v", err)
	}
	defer rows.Close()

	// 第一行是title
	var title []string
	if rows.Next() {
		title, err = rows.Columns()
		if err != nil {
			return fmt.Errorf("excelize get title rows.Columns err:%v", err)
		}
	}
	// 第一列是 serial Name, 第二列是asset anme，后面列是一级标签, 至少要三列
	if len(title) < 3 {
		return fmt.Errorf("title len(title) < 3")
	}

	// 跳两行
	rows.Next()
	rows.Next()

	// 加载标签库
	creativeLabels, err := aixwebRepo.LoadCreativeLabels(ctx, task.GameCode)
	if err != nil {
		return fmt.Errorf("aixwebRepo.LoLoadCreativeLabelsad err:%v", err)
	}

	var retErr error
	// 遍历行
	for rows.Next() {
		retErr := doXlsxOneRowV2(ctx, task, title, rows, creativeLabels)
		// 对处理过标签
		if retErr != nil {
			break
		}
	}

	// 处理完文件后，更新标签库
	// 有变化更新数据
	var saves []*model.CreativeLabel
	changedLabels := creativeLabels.GetChangedLabels()
	for _, t := range changedLabels {
		t.UpdatedAt = time.Now()
		t.Updater = "CronLabelRuleTask"
		saves = append(saves, t)
	}
	if len(saves) > 0 {
		err = data.BatchUpdateCreativeLabelsSecondLabel(ctx, saves)
		if err != nil {
			log.ErrorContextf(ctx, "data.UpdateCreativeLabelsSecondLabel err:%v", err)
		}
	}

	return retErr
}

func doXlsxOneRowV2(ctx context.Context,
	task *model.TbLabelRuleTask, title []string, rows *excelize.Rows,
	creativeLabels *aixwebRepo.CreativeLabels) error {
	cells, err := rows.Columns()
	if err != nil {
		return fmt.Errorf("excelize get row rows.Columns err:%v", err)
	}

	// 第一列是serial name, 第二列 asset name, 第三列开始是二级标签
	if len(cells) < 3 {
		return nil
	}

	// 尝试处理serial 层级标签
	serailLabels, err := doXlsxOneRowV2Serial(ctx, task, title, cells, creativeLabels)
	if err != nil {
		return fmt.Errorf("doXlsxOneRowV2Serial err:%v", err)
	}
	// 对处理过的标签，如果标签库里没有，需要加进去
	for _, label := range serailLabels {
		creativeLabels.AddSerialLevelLabel(label.FirstLabel, label.SecondLabel)
	}

	// 尝试处理asset 层级标签
	assetLabels, err := doXlsxOneRowV2Asset(ctx, task, title, cells, creativeLabels)
	if err != nil {
		return fmt.Errorf("doXlsxOneRowV2Asset err:%v", err)
	}
	// 对处理过的标签，如果标签库里没有，需要加进去
	for _, label := range assetLabels {
		creativeLabels.AddAssetLevelLabel(label.FirstLabel, label.SecondLabel)
	}

	return nil
}

func doXlsxOneRowV2Serial(ctx context.Context, task *model.TbLabelRuleTask, title []string, cells []string,
	creativeLabels *aixwebRepo.CreativeLabels) ([]model.AssetLabel, error) {
	// 处理serial 层级标签
	ruleName := strings.TrimSpace(cells[0])
	// serial name列表为空 不处理
	if ruleName == "" {
		return nil, nil
	}

	normalizeName, ok := model.CheckAndNormalizeLabelRuleName(task.Type, ruleName, false)
	// 标签规则校验， 不通过则忽略
	if !ok {
		log.WarningContextf(ctx, "model.CheckAndNormalizeLabelRuleName failed, ignore rule:%+v", ruleName)
		return nil, nil
	}

	// 从数据库中读取规则
	rule, err := data.GetLabelRule(ctx, task.GameCode, normalizeName, task.Type)
	if err != nil {
		return nil, fmt.Errorf("data.GetLabelRule err:%v", err)
	}
	if rule == nil {
		// 数据库里面没有，新生成标签规则
		rule = &model.TbAssetLabelRule{
			GameCode:   task.GameCode,
			Rule:       normalizeName,
			Type:       task.Type,
			CreateTime: time.Now(),
			CreateUser: task.CreateUser,
		}
	}
	rule.UpdateUser = fmt.Sprintf("material_sync_task_%v", task.ID)
	rule.UpdateTime = time.Now()

	// 机器打标的标签，需要记录下来， 人工打标任务要忽略这些标签不能覆盖
	// 不在此次处理表格里的一级标签保留
	var keepLabels []model.AssetLabel
	for _, label := range rule.Labels {
		if creativeLabels.IsFirstLabelIntelligent(label.FirstLabel) ||
			!funk.ContainsString(title, label.FirstLabel) {
			keepLabels = append(keepLabels, label)
		}
	}
	// 保留标签保存下来后，清空
	rule.Labels = nil

	// 记录处理过的标签， 需要返回
	var retLabels []model.AssetLabel
	// 处理标签，从第三列开始
	for i := 2; i < len(cells) && i < len(title); i++ {
		// title里的是一级标签
		firstLabel := title[i]
		// 非机器打标的serial层级标签，才能覆盖
		if !creativeLabels.IsFirstLabelIntelligent(firstLabel) &&
			creativeLabels.IsFirstLabelSerialLevel(firstLabel) {
			// 多个二级标签以换行分割
			s := cells[i]
			lines := splitLineAndTrim(s)
			for _, line := range lines {
				label := model.AssetLabel{
					LabelName:   "",         // 标签规则的label_name固定为空，已不再使用
					FirstLabel:  firstLabel, //
					SecondLabel: line,
				}
				rule.Labels = append(rule.Labels, label)
				retLabels = append(retLabels, label)
			}
		}
	}
	// 把原来保留的标签，放进来
	rule.Labels = append(rule.Labels, keepLabels...)
	removeRuleDuplicateLables(rule)

	log.DebugContextf(ctx, "get one serail rule:%+v", rule)

	err = repo.UpsertLabelRule(ctx, rule, false)
	if err != nil {
		return nil, fmt.Errorf("repo.UpsertLabelRule err:%v", err)
	}

	return retLabels, nil

}

func doXlsxOneRowV2Asset(ctx context.Context, task *model.TbLabelRuleTask, title []string, cells []string,
	creativeLabels *aixwebRepo.CreativeLabels) ([]model.AssetLabel, error) {
	// 处理asset 层级标签
	assetName := strings.TrimSpace(cells[1])
	// 去掉扩展名
	assetName = strings.TrimSuffix(assetName, filepath.Ext(assetName))
	// asset name列表为空 不处理
	if assetName == "" {
		return nil, nil
	}

	// 从数据库中读取规则
	rule, err := data.GetLabelRule(ctx, task.GameCode, assetName, constant.LabelRuleAssetLebel)
	if err != nil {
		return nil, fmt.Errorf("data.GetLabelRule err:%v", err)
	}
	if rule == nil {
		// 数据库里面没有，新生成标签规则
		rule = &model.TbAssetLabelRule{
			GameCode:   task.GameCode,
			Rule:       assetName,
			Type:       constant.LabelRuleAssetLebel,
			CreateTime: time.Now(),
			CreateUser: task.CreateUser,
		}
	}
	rule.UpdateUser = fmt.Sprintf("material_sync_task_%v", task.ID)
	rule.UpdateTime = time.Now()

	// 机器打标的标签，需要记录下来， 人工打标任务要忽略这些标签不能覆盖
	// 不在此次处理表格里的一级标签保留
	var keepLabels []model.AssetLabel
	for _, label := range rule.Labels {
		if creativeLabels.IsFirstLabelIntelligent(label.FirstLabel) ||
			!funk.ContainsString(title, label.FirstLabel) {
			keepLabels = append(keepLabels, label)
		}
	}
	// 保留标签保存下来后，清空
	rule.Labels = nil

	// 记录处理过的标签， 需要返回
	var retLabels []model.AssetLabel
	// 处理标签，从第三列开始
	for i := 2; i < len(cells) && i < len(title); i++ {
		// title里的是一级标签
		firstLabel := title[i]
		// 非机器打标的asset层级标签，才能覆盖
		if !creativeLabels.IsFirstLabelIntelligent(firstLabel) &&
			creativeLabels.IsFirstLabelAssetLevel(firstLabel) {
			// 多个二级标签以换行分割
			s := cells[i]
			lines := splitLineAndTrim(s)
			for _, line := range lines {
				label := model.AssetLabel{
					LabelName:   "",         // 标签规则的label_name固定为空，已不再使用
					FirstLabel:  firstLabel, //
					SecondLabel: line,
				}
				rule.Labels = append(rule.Labels, label)
				retLabels = append(retLabels, label)
			}
		}
	}
	// 把原来保留的标签，放进来
	rule.Labels = append(rule.Labels, keepLabels...)
	removeRuleDuplicateLables(rule)

	log.DebugContextf(ctx, "get one asset rule:%+v", rule)

	err = repo.UpsertLabelRule(ctx, rule, false)
	if err != nil {
		return nil, fmt.Errorf("repo.UpsertLabelRule err:%v", err)
	}

	return retLabels, nil
}
