package service

import (
	"fmt"
	"strings"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
)

// checkParamInCreateMaterialDirectory ...
func checkParamInCreateMaterialDirectory(req *material_display.DirectoryCreateReq, gamecode string) error {
	if req == nil {
		return fmt.Errorf("request data cannot be null")
	}
	if err := checkGameCodeKey(gamecode); err != nil {
		return err
	}
	if req.Name == "" {
		return fmt.Errorf("directory name cannot be empty")
	}
	if req.ParentId == "" {
		return fmt.Errorf("directory parent'Id cannot be 0")
	}
	//switch req.GetDirectoryType() {
	////depot:根目录，project:一级目录,其它:directory
	//case "depot", "project", "directory":
	//default:
	//	return fmt.Errorf("Directory type error, just support 'depot'、'project'、'directory'")
	//}
	return nil
}

// DirectoryCreate ...
func DirectoryCreate(ctx *gin.Context, req *material_display.DirectoryCreateReq) (*material_display.DirectoryCreateRsp, error) {
	var resp material_display.DirectoryCreateRsp
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if err := checkParamInCreateMaterialDirectory(req, gameCode); err != nil {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	db := postgresql.GetDBWithContext(ctx)
	result, err := db.Exec(`SELECT COUNT(*) FROM  pg_class WHERE relname = ?`,
		fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gameCode))
	if err != nil {
		log.ErrorContextf(ctx, "fail to check gamecode in postgresql, gamecode: %v, err: %v", gameCode, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	if result.RowsReturned() < 1 {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "resource db not exist")
	}

	// 校验父目录是否存在
	parentDirectory := model.CreativeDirectory{}
	sql := fmt.Sprintf(`SELECT * FROM %s.tb_creative_directory_%s WHERE id = ?`,
		"arthub_sync", gameCode)
	_, err = db.QueryOne(&parentDirectory, sql, req.ParentId)
	if err != nil {
		if err == pg.ErrNoRows {
			log.InfoContextf(ctx, "Material directory information not exist, "+
				"ParentId: %v, err: %v", req.ParentId, err)
			return &resp, nil
		}
		log.ErrorContextf(ctx, "Fail to get material directory information by id, "+
			"ParentId: %v, err: %v", req.ParentId, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}

	if parentDirectory.Type != "project" && parentDirectory.Type != "directory" && parentDirectory.Type != "depot" {
		log.ErrorContextf(ctx, "parent directory type error, parentDirectory type: %v", parentDirectory.Type)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}

	// 校验该父目录下是否有同名目录
	talbeName := fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gameCode)
	nameExisted, err := db.Model((*model.CreativeDirectory)(nil)).
		Table(talbeName).
		Where("parent_id = ? and name = ?", req.GetParentId(), req.GetName()).
		Exists()
	if err != nil {
		log.InfoContextf(ctx, "Check material directory name fail, "+
			"ParentId: %v, name: %v, err: %v", req.GetParentId(), req.GetName(), err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	if nameExisted {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error directroy name repeated")
	}

	// 获取根节点信息
	depot, err := data.GetDepot(gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get arthub token information, gamecode: %v, err: %v", gameCode, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}

	var (
		id               string
		subDirectoryType string
	)
	if parentDirectory.Type == "depot" {
		// 一级目录
		subDirectoryType = "project"
	} else {
		subDirectoryType = "directory"
	}
	switch depot.Type {
	case utils.GAME_DEPOT_TYPE_ARTHUB:
		if parentDirectory.Type == "depot" {
			// 创建一级目录
			id, err = arthub.CreateArthubProject(ctx, depot.ArthubCode, depot.PublicToken, req.GetName(), req.ParentId)
		} else {
			id, err = createArthubDir(ctx, &depot, req.GetName(), req.ParentId, req.RenameRepeatedCategory)
		}
	case utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER:
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "fail to create directory, google drive not suppport")
	case utils.GAME_DEPOT_TYPE_DROPBOX:
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "fail to create directory, dropbox not suppport")
	default:
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "fail to create directory, depot.Type:%v not suppport", depot.Type)
	}
	if err != nil {
		log.ErrorContextf(ctx, "Fail to create directory, depot.Type: %v, error: %v", depot.Type, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "fail to create directory err:%v", err)
	}

	// 创建一级目录时根目录full_path_name、full_path_id 为空
	var fullPathName, fullPathId string
	if parentDirectory.Type == "depot" {
		fullPathName = parentDirectory.Name
		fullPathId = parentDirectory.ID
	} else {
		if len(parentDirectory.FullPathID) == 0 {
			fullPathId = parentDirectory.ID
		} else {
			fullPathId = fmt.Sprintf("%s,%s", parentDirectory.FullPathID, parentDirectory.ID)
		}
		if len(parentDirectory.ParentName) == 0 {
			fullPathName = parentDirectory.Name
		} else {
			fullPathName = fmt.Sprintf("%s,%s", parentDirectory.FullPathName, parentDirectory.Name)
		}
	}
	now := time.Now().Format("2006-01-02 15:04:05")
	err = syncDbInCreateMaterialDirectory(ctx, subDirectoryType, id, req.GetParentId(), req.GetName(),
		parentDirectory.Name, fullPathName, fullPathId, now, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "fail to insert directory in postgresql, gamecode: %v, "+
			"directory object id: %+v, err: %v", gameCode, id, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}

	fullPathId, fullPathName = correctFullPathForDirectoryCreate(depot.DepotId, fullPathId, fullPathName)
	resp.Dir = &material_display.Directory{
		Id:                   id,
		Name:                 req.GetName(),
		ParentId:             req.ParentId,
		ParentName:           parentDirectory.Name,
		CreateDate:           now,
		UpdateDate:           now,
		DirectChildCount:     0,
		TotalLeafCount:       0,
		DirectDirectoryCount: 0,
		FullPathName:         fullPathName,
		FullPathId:           fullPathId,
		MediaDirectoryName:   "",
		MediaDirectoryId:     "",
	}

	return &resp, nil
}

// correctFullPathForDirectoryCreate 根据根目录ID矫正full_path_id及full_path_name
func correctFullPathForDirectoryCreate(depot_id, full_path_id, full_path_name string) (string, string) {
	path_ids := strings.Split(full_path_id, ",")
	path_names := strings.Split(full_path_name, ",")
	if len(path_ids) != len(path_names) {
		return full_path_id, full_path_name
	}

	depot_index := 0
	for i, id := range path_ids {
		if depot_id == id {
			depot_index = i
			break
		}
	}

	correct_path := strings.Join(path_ids[depot_index:], ",")
	correct_name := strings.Join(path_names[depot_index:], ",")
	return correct_path, correct_name
}
