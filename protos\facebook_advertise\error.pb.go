// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.5.0
// source: facebook_advertise/error.proto

package facebook_advertise

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AixFbErrCode int32

const (
	AixFbErrCode_AIX_FB_SUCCESS AixFbErrCode = 0
	AixFbErrCode_AIX_FB_DEMO    AixFbErrCode = 1002001 // error code from 1002001 to 1002999
)

// Enum value maps for AixFbErrCode.
var (
	AixFbErrCode_name = map[int32]string{
		0:       "AIX_FB_SUCCESS",
		1002001: "AIX_FB_DEMO",
	}
	AixFbErrCode_value = map[string]int32{
		"AIX_FB_SUCCESS": 0,
		"AIX_FB_DEMO":    1002001,
	}
)

func (x AixFbErrCode) Enum() *AixFbErrCode {
	p := new(AixFbErrCode)
	*p = x
	return p
}

func (x AixFbErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AixFbErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_facebook_advertise_error_proto_enumTypes[0].Descriptor()
}

func (AixFbErrCode) Type() protoreflect.EnumType {
	return &file_facebook_advertise_error_proto_enumTypes[0]
}

func (x AixFbErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AixFbErrCode.Descriptor instead.
func (AixFbErrCode) EnumDescriptor() ([]byte, []int) {
	return file_facebook_advertise_error_proto_rawDescGZIP(), []int{0}
}

var File_facebook_advertise_error_proto protoreflect.FileDescriptor

var file_facebook_advertise_error_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2a, 0x35, 0x0a, 0x0c, 0x41, 0x69, 0x78, 0x46, 0x62, 0x45, 0x72, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x49, 0x58, 0x5f, 0x46, 0x42, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0b, 0x41, 0x49, 0x58, 0x5f,
	0x46, 0x42, 0x5f, 0x44, 0x45, 0x4d, 0x4f, 0x10, 0x91, 0x94, 0x3d, 0x42, 0x41, 0x5a, 0x3f, 0x65,
	0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x66, 0x61, 0x63, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_facebook_advertise_error_proto_rawDescOnce sync.Once
	file_facebook_advertise_error_proto_rawDescData = file_facebook_advertise_error_proto_rawDesc
)

func file_facebook_advertise_error_proto_rawDescGZIP() []byte {
	file_facebook_advertise_error_proto_rawDescOnce.Do(func() {
		file_facebook_advertise_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_facebook_advertise_error_proto_rawDescData)
	})
	return file_facebook_advertise_error_proto_rawDescData
}

var file_facebook_advertise_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_facebook_advertise_error_proto_goTypes = []interface{}{
	(AixFbErrCode)(0), // 0: facebook_advertise.AixFbErrCode
}
var file_facebook_advertise_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_facebook_advertise_error_proto_init() }
func file_facebook_advertise_error_proto_init() {
	if File_facebook_advertise_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_facebook_advertise_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_facebook_advertise_error_proto_goTypes,
		DependencyIndexes: file_facebook_advertise_error_proto_depIdxs,
		EnumInfos:         file_facebook_advertise_error_proto_enumTypes,
	}.Build()
	File_facebook_advertise_error_proto = out.File
	file_facebook_advertise_error_proto_rawDesc = nil
	file_facebook_advertise_error_proto_goTypes = nil
	file_facebook_advertise_error_proto_depIdxs = nil
}
