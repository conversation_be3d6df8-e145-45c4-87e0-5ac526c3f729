package bigquery

import (
	"context"

	ckmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
	bq "e.coding.intlgame.com/ptc/aix-backend/common/pkg/bigquery"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

// 内部用bigquery表结构
type creativeDailyPivotNew struct {
	GameCode   string `bigquery:"game_code"`  // game code
	Network    string `bigquery:"network"`    // 所属渠道
	AccountID  string `bigquery:"account_id"` // 渠道账号
	AssetID    string `bigquery:"asset_id"`   // 素材ID
	AssetName  string `bigquery:"asset_name"` // 素材名称
	AssetType  string `bigquery:"asset_type"` // 素材类型 IMAGE/VIDEO 等等
	YoutubeID  string `bigquery:"youtube_id"` // youtube id
	Dtstatdate int64  `bigquery:"dtstatdate"` // 统计日期  格式********
}

// GetViewCreativeDailyPivotNewMaxDate 获取渠道素材按network分组的最大日期
func GetViewCreativeDailyPivotNewMaxDate(ctx context.Context, gameCode string) ([]*ckmodel.ViewCreativeDailyPivotNew, error) {
	// 没有使用bigquery, 直接返回
	if !setting.GetConf().UseBigquery {
		return nil, nil
	}

	proxy := bq.GetProxy(ctx, gameCode)
	tableID := genCreativeDailyPivotNewTableID(gameCode)
	filter := &bq.QueryFilter{
		Wheres: map[string]interface{}{
			"game_code = ?": gameCode,
		},
		Columns: []string{"network", "max(dtstatdate) as dtstatdate"},
		GroupBy: "network",
		Limit:   100, // 100个渠道够了
	}

	var rows []*creativeDailyPivotNew
	err := proxy.QueryToStructs(ctx, tableID, filter, &rows)
	if err != nil {
		return nil, err
	}

	var rlt []*ckmodel.ViewCreativeDailyPivotNew
	for _, row := range rows {
		rlt = append(rlt, toCKViewCreativeDailyPivotNew(row))
	}
	return rlt, nil
}

// GetViewCreativeDailyPivotNewByNetwork 从bigquery读取渠道素材
func GetViewCreativeDailyPivotNewByNetwork(ctx context.Context, gameCode string, network string, date string) ([]*ckmodel.ViewCreativeDailyPivotNew, error) {
	// 没有使用bigquery, 直接返回
	if !setting.GetConf().UseBigquery {
		return nil, nil
	}

	proxy := bq.GetProxy(ctx, gameCode)
	tableID := genCreativeDailyPivotNewTableID(gameCode)

	filter := &bq.QueryFilter{
		Wheres: map[string]interface{}{
			"game_code = ?":     gameCode,
			"network = ?":       network,
			"dtstatdate = ?":    cast.ToInt(date),
			"asset_type IN (?)": []string{"IMAGE", "VIDEO"},
		},
		Columns: []string{"asset_id", "network", "asset_name", "youtube_id", "account_id", "asset_type"},
		GroupBy: "asset_id, network, asset_name, youtube_id, account_id, asset_type",
		OrderBy: "asset_id, asset_name",
	}

	var rlt []*ckmodel.ViewCreativeDailyPivotNew
	limit := 1000
	for offset := 0; offset < 100000; offset = offset + limit {
		filter.Offset = offset
		filter.Limit = limit

		var rows []*creativeDailyPivotNew
		err := proxy.QueryToStructs(ctx, tableID, filter, &rows)
		if err != nil {
			return nil, err
		}

		for _, row := range rows {
			rlt = append(rlt, toCKViewCreativeDailyPivotNew(row))
		}

		if len(rows) < limit {
			break
		}
	}
	return rlt, nil
}

// GetViewCreativeDailyPivotNew 从bigquery读取渠道某天素材 不指定network
func GetViewCreativeDailyPivotNew(ctx context.Context, gameCode string, date string) ([]*ckmodel.ViewCreativeDailyPivotNew, error) {
	// 没有使用bigquery, 直接返回
	if !setting.GetConf().UseBigquery {
		return nil, nil
	}

	proxy := bq.GetProxy(ctx, gameCode)
	tableID := genCreativeDailyPivotNewTableID(gameCode)

	filter := &bq.QueryFilter{
		Wheres: map[string]interface{}{
			"game_code = ?":     gameCode,
			"dtstatdate = ?":    cast.ToInt(date),
			"asset_type IN (?)": []string{"IMAGE", "VIDEO"},
		},
		Columns: []string{"asset_id", "network", "asset_name", "youtube_id", "account_id", "asset_type"},
		GroupBy: "asset_id, network, asset_name, youtube_id, account_id, asset_type",
		OrderBy: "asset_id, network, asset_name",
	}

	var rlt []*ckmodel.ViewCreativeDailyPivotNew
	limit := 1000
	for offset := 0; offset < 100000; offset = offset + limit {
		filter.Offset = offset
		filter.Limit = limit

		var rows []*creativeDailyPivotNew
		err := proxy.QueryToStructs(ctx, tableID, filter, &rows)
		if err != nil {
			return nil, err
		}

		for _, row := range rows {
			rlt = append(rlt, toCKViewCreativeDailyPivotNew(row))
		}

		if len(rows) < limit {
			break
		}
	}
	return rlt, nil
}

func genCreativeDailyPivotNewTableID(gameCode string) string {
	tableID := "creative_daily_pivot_new"
	if funk.ContainsString(conf.GetBizConf().CreativeDailyPivotNewSuffixGames, gameCode) {
		tableID = tableID + "_" + gameCode
	}
	return tableID
}

func toCKViewCreativeDailyPivotNew(row *creativeDailyPivotNew) *ckmodel.ViewCreativeDailyPivotNew {
	t := &ckmodel.ViewCreativeDailyPivotNew{
		GameCode:   row.GameCode,
		Network:    row.Network,
		AccountID:  row.AccountID,
		AssetID:    row.AssetID,
		AssetName:  row.AssetName,
		AssetType:  row.AssetType,
		YoutubeID:  row.YoutubeID,
		Dtstatdate: cast.ToString(row.Dtstatdate),
	}
	return t
}
