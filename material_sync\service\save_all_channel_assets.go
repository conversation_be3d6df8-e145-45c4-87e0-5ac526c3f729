package service

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	ckmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	channelRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/channel_asset"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data/bigquery"
	"github.com/thoas/go-funk"
)

// SaveAllChannelAssets 定时同步每天的渠道素材到总表
func SaveAllChannelAssets(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/SaveAllChannelAssets")
	// 由于同步时间比较长，这里加锁，避免同时去同步
	key := "cron/SaveAllChannelAssets"
	locked, _ := redis.GetMaterialSyncLock(ctx, key, 2*time.Hour)
	if !locked {
		log.WarningContextf(ctx, "SaveAllChannelAssets GetLock false")
		return
	}
	// 释放锁
	defer redis.DelMaterialSyncLock(ctx, key)

	log.DebugContextf(ctx, "SaveAllChannelAssets do work start...")

	start := time.Now()
	// 获取所有的素材库
	depots, err := repo.GetAllDepots(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "SaveAllChannelAssets repo.GetAllDepots error: %v", err)
		return
	}

	for _, depot := range depots {
		newCtx := log.NewSessionIDContext()
		saveGameAllChannelAssets(newCtx, depot)
	}
	log.DebugContextf(ctx, "SaveAllChannelAssets do work end, cost:%v", time.Since(start))
	return
}

// saveGameAllChannelAssets 定时同步某个游戏每天的渠道素材到总表
func saveGameAllChannelAssets(ctx context.Context, depot *model.ArthubDepot) error {
	// 没有开启同步
	if depot.SaveAllChannelAssetsDate == "" {
		return nil
	}
	log.DebugContextf(ctx, "saveGameAllChannelAssets, depot: %v, date:%v, direction:%v, TimeZoneOffset:%v",
		depot.GameCode, depot.SaveAllChannelAssetsDate, depot.SaveAllChannelAssetsDirection, depot.TimeZoneOffset)

	loc := time.FixedZone("", int(depot.TimeZoneOffset))
	lastDateHour := depot.SaveAllChannelAssetsDate
	// 日期小时格式 2024-04-04 13
	t, err := time.ParseInLocation(utils.TimeFormatDateHour, lastDateHour, loc)
	if err != nil {
		log.ErrorContextf(ctx, "saveGameAllChannelAssets, depot: %v, time.Parse err:%v", depot.GameCode, err)
		return err
	}
	// 延迟1小时处理, 今天的可以每小时执行一次，即避免了数据延迟，也能及时同步回来
	nowTime := time.Now().In(loc)
	maxDateHour := nowTime.Add(-1 * time.Hour).Format(utils.TimeFormatDateHour)
	// 判断日期小时，如果大于当前小时-1，则不执行
	if lastDateHour > maxDateHour {
		log.InfoContextf(ctx, "saveGameAllChannelAssets depot: %v, lastDateHour: %v > maxDateHour:%v, break",
			depot.GameCode, lastDateHour, maxDateHour)
		return nil
	}

	saveDate := t.Format(utils.NumericTimeFormatDate)
	currDate := nowTime.Format(utils.NumericTimeFormatDate)
	isNowDate := currDate == saveDate // 是否是今天
	err = saveGameAllChannelAssetsByDate(ctx, depot.GameCode, saveDate, isNowDate)
	if err != nil {
		log.ErrorContextf(ctx, "saveGameAllChannelAssets, depot: %v, saveGameAllChannelAssetsByDate error: %v", depot.GameCode, err)
		return err
	}

	// 更新下一次同步的时间
	if depot.SaveAllChannelAssetsDirection == "backward" {
		// 往后同步
		depot.SaveAllChannelAssetsDate = t.AddDate(0, 0, -1).Format(utils.TimeFormatDateHour)
	} else {
		// forward 或者 默认往前同步
		if t.AddDate(0, 0, 1).Format(utils.NumericTimeFormatDate) >= nowTime.Format(utils.NumericTimeFormatDate) {
			// 如果+1天>=今天，说明已经同步到今天，保存为当前时间小时，然后每个小时同步一次
			depot.SaveAllChannelAssetsDate = nowTime.Format(utils.TimeFormatDateHour)
		} else {
			// 往前+1天
			depot.SaveAllChannelAssetsDate = t.AddDate(0, 0, 1).Format(utils.TimeFormatDateHour)
		}
	}
	err = repo.UpdateDepotByFields(ctx, depot, []string{"save_all_channel_assets_date"})
	if err != nil {
		log.ErrorContextf(ctx, "saveGameAllChannelAssets, depot: %v, repo.UpdateDepotByFields error: %v", depot.GameCode, err)
		return err
	}

	return nil
}

// saveGameAllChannelAssetsByDate 同步某个游戏指定日期的渠道素材到总表
func saveGameAllChannelAssetsByDate(ctx context.Context, gameCode string, date string, isNowDate bool) error {
	var channelAssets []*ckmodel.ViewCreativeDailyPivotNew
	var err error
	if isNowDate {
		// 如果是同步今天的数据的话，因为数据指标拉取可能有延迟，先取 max(dtstatdate) ，再分渠道同步
		maxRows, err := getViewCreativeDailyPivotNewMaxDate(ctx, gameCode)
		if err != nil {
			log.ErrorContextf(ctx, "saveGameAllChannelAssetsByDate, depot: %v, getViewCreativeDailyPivotNewMaxDate error: %v", gameCode, err)
			return err
		}
		// 分渠道取指定日期的曝光素材
		for _, row := range maxRows {
			log.DebugContextf(ctx, "saveGameAllChannelAssetsByDate, gameCode:%v, Network:%v, Dtstatdate:%v", gameCode, row.Network, row.Dtstatdate)
			tmpAssets, err := getViewCreativeDailyPivotNewByNetwork(ctx, gameCode, row.Network, row.Dtstatdate)
			if err != nil {
				log.ErrorContextf(ctx, "saveGameAllChannelAssetsByDate, depot: %v, getViewCreativeDailyPivotNewByNetwork error: %v", gameCode, err)
				return err
			}
			channelAssets = append(channelAssets, tmpAssets...)
		}
	} else {
		// 获取这一天的渠道素材信息
		channelAssets, err = getViewCreativeDailyPivotNew(ctx, gameCode, date)
		if err != nil {
			log.ErrorContextf(ctx, "saveGameAllChannelAssetsByDate, depot: %v, getViewCreativeDailyPivotNew error: %v", gameCode, err)
			return err
		}
	}
	log.DebugContextf(ctx, "saveGameAllChannelAssetsByDate, game_code:%v, date:%v, len(channelAssets):%v", gameCode, date, len(channelAssets))
	// 没数据直接返回
	if len(channelAssets) == 0 {
		return nil
	}

	// 过滤已处理的素材
	uniqKey := make(map[string]bool)
	// 分批处理
	chunks := funk.Chunk(channelAssets, 500).([][]*ckmodel.ViewCreativeDailyPivotNew)
	for _, chunk := range chunks {
		// 待插入的数据
		var inserts []*model.AllChannelAssets
		for _, asset := range chunk {
			t := &model.AllChannelAssets{
				ChannelType:      int32(model.GenChannelTypeFromNetwork(asset.Network)),
				ChannelAccountID: asset.AccountID,
				ChannelAssetID:   asset.AssetID,
				AssetName:        asset.AssetName,
				YoutubeID:        asset.YoutubeID,
				CreateBy:         "material_sync",
				UpdateBy:         "material_sync",
				CreateTime:       utils.GetNowStr(),
				UpdateTime:       utils.GetNowStr(),
				SDate:            date,
				AssetType:        asset.AssetType,
				ImpressionDate:   asset.ImpressionDate,
			}
			// 补全渠道素材的account_id和asset_id (可能拉回来没有)
			completionAllChannelAssets(t)

			// 过滤已处理的素材
			key := fmt.Sprintf("%v,%v,%v", t.ChannelType, t.ChannelAccountID, t.ChannelAssetID)
			if uniqKey[key] == true {
				log.DebugContextf(ctx, "saveGameAllChannelAssetsByDate, depot: %v, duplicate key: %v, skip", gameCode, key)
				continue
			}
			uniqKey[key] = true

			inserts = append(inserts, t)
		}
		// 批量插入
		err = channelRepo.UpsertBatchChanneAssetsToAll(ctx, gameCode, inserts)
		if err != nil {
			log.ErrorContextf(ctx, "saveGameAllChannelAssetsByDate, depot: %v, channelRepo.UpsertBatchChanneAssetsToAll error: %v", gameCode, err)
			return err
		}

		// 根据渠道素材的信息更新aix library本地素材的impression_date
		err = updateOverviewImpressionDateByChannelAssets(ctx, gameCode, inserts)
		if err != nil {
			log.ErrorContextf(ctx, "updateOverviewImpressionDateByChannelAssets, depot: %v, channelRepo.UpsertBatchChanneAssetsToAll error: %v", gameCode, err)
			return err
		}
	}

	// # 是否聚合统计标签规则的曝光日期 (标签规则只需要计算最终的曝光日期即可)
	// # 因为是在定时任务同步全量渠道素材的时候触发的
	// # 可能会回溯历史日期渠道素材，这里可以关闭，避免不必要的计算，以提高效率
	// # 待正常每天同步的时候，再打开
	if conf.GetBizConf().AggLabelRuleImpressionDate {
		rules, err := repo.GetAllLabelRulesByGameCode(ctx, gameCode)
		if err != nil {
			log.Errorf("saveGameAllChannelAssetsByDate repo.GetAllLabelRulesByGameCode err:%v", err)
			return err
		}

		// 统计每个标签规则的最小曝光日期
		for _, rule := range rules {
			minImpressionDate, err := getLableRuleMinImpressionDate(ctx, rule)
			if err != nil {
				log.Errorf("saveGameAllChannelAssetsByDate getLableRuleMinImpressionDate err:%v", err)
				return err
			}
			rule.UpdateTime = time.Now()
			rule.ImpressionDate = minImpressionDate
		}

		// 保存数据
		err = repo.BatchUpdateLabelRuleFiels(ctx, rules, []string{"update_time", "impression_date"})
		if err != nil {
			log.Errorf("saveGameAllChannelAssetsByDate BatchUpdateLabelRuleFiels err:%v", err)
			return err
		}
	}
	return nil
}

// 获取某天的渠道素材信息
func getViewCreativeDailyPivotNew(ctx context.Context, gameCode string, date string) ([]*ckmodel.ViewCreativeDailyPivotNew, error) {
	if setting.GetConf().UseBigquery {
		return bigquery.GetViewCreativeDailyPivotNew(ctx, gameCode, date)
	}

	if conf.GetBizConf().DisabelClickhouse {
		return nil, nil
	}

	var assets []*ckmodel.ViewCreativeDailyPivotNew
	query := clickhouse.GetGORMDBWithGameCode(ctx, gameCode).Model(&ckmodel.ViewCreativeDailyPivotNew{})
	query.Table(ckmodel.GetViewCreativeDailyPivotNewTableName(conf.GetBizConf().ClickhouseEnv, gameCode))
	query.Select("asset_id, network, impression_date, asset_name, youtube_id, account_id, asset_type")
	query.Where("game_code = ?", gameCode)
	query.Where("dtstatdate = ?", date)
	query.Where("asset_type in ?", []string{"IMAGE", "VIDEO"}) // 只查图片和视频
	query.Group("asset_id, network, impression_date, asset_name, youtube_id, account_id, asset_type")
	err := query.Find(&assets).Error
	return assets, err
}

// 获取某天指定渠道素材信息
func getViewCreativeDailyPivotNewByNetwork(ctx context.Context, gameCode string, network string, date string) ([]*ckmodel.ViewCreativeDailyPivotNew, error) {
	if setting.GetConf().UseBigquery {
		return bigquery.GetViewCreativeDailyPivotNewByNetwork(ctx, gameCode, network, date)
	}

	if conf.GetBizConf().DisabelClickhouse {
		return nil, nil
	}

	var assets []*ckmodel.ViewCreativeDailyPivotNew
	query := clickhouse.GetGORMDBWithGameCode(ctx, gameCode).Model(&ckmodel.ViewCreativeDailyPivotNew{})
	query.Table(ckmodel.GetViewCreativeDailyPivotNewTableName(conf.GetBizConf().ClickhouseEnv, gameCode))
	query.Select("asset_id, network, impression_date, asset_name, youtube_id, account_id, asset_type")
	query.Where("game_code = ?", gameCode)
	query.Where("network = ?", network)
	query.Where("dtstatdate = ?", date)
	query.Where("asset_type in ?", []string{"IMAGE", "VIDEO"}) // 只查图片和视频
	query.Group("asset_id, network, impression_date, asset_name, youtube_id, account_id, asset_type")
	err := query.Find(&assets).Error
	return assets, err
}

// 获取渠道素材信息指标数据按network max(dtstatdate)
func getViewCreativeDailyPivotNewMaxDate(ctx context.Context, gameCode string) ([]*ckmodel.ViewCreativeDailyPivotNew, error) {
	if setting.GetConf().UseBigquery {
		return bigquery.GetViewCreativeDailyPivotNewMaxDate(ctx, gameCode)
	}

	if conf.GetBizConf().DisabelClickhouse {
		return nil, nil
	}

	var assets []*ckmodel.ViewCreativeDailyPivotNew
	query := clickhouse.GetGORMDBWithGameCode(ctx, gameCode).Model(&ckmodel.ViewCreativeDailyPivotNew{})
	query.Table(ckmodel.GetViewCreativeDailyPivotNewTableName(conf.GetBizConf().ClickhouseEnv, gameCode))
	query.Select("network, max(dtstatdate) as dtstatdate")
	query.Where("game_code = ?", gameCode)
	query.Group("network")

	err := query.Find(&assets).Error
	return assets, err
}

// 根据渠道素材的信息更新aix library本地素材的impression_date
// 是根据渠道素材名字反向更新到overview表的
func updateOverviewImpressionDateByChannelAssets(ctx context.Context, gameCode string, assets []*model.AllChannelAssets) error {
	nameImpressionDateMap := make(map[string]string)
	// 保存素材名对应的曝光日期，取最小的日期
	addNameImpressionDate := func(name, impressionDate string) {
		if impressionDate == "" {
			return
		}

		old := nameImpressionDateMap[name]
		if old == "" || old < impressionDate {
			nameImpressionDateMap[name] = impressionDate
		}
	}

	for _, a := range assets {
		// 原始素材名
		name := removeFileExtension(a.AssetName) // 去掉文件名后缀
		addNameImpressionDate(name, a.ImpressionDate)
		// 将 -/_ 转为 空格
		name = strings.ReplaceAll(name, "-", " ")
		nameTab := strings.ReplaceAll(name, "_", " ")
		addNameImpressionDate(nameTab, a.ImpressionDate)
		// 将空格转为 -
		name = strings.ReplaceAll(nameTab, " ", "-")
		addNameImpressionDate(name, a.ImpressionDate)
		// 将空格转为 _
		name = strings.ReplaceAll(nameTab, " ", "_")
		addNameImpressionDate(name, a.ImpressionDate)
	}

	var views []*model.CreativeOverview
	for k, v := range nameImpressionDateMap {
		t := &model.CreativeOverview{
			AssetName:      k,
			ImpressionDate: v,
		}
		views = append(views, t)
	}

	// 更新到overview表
	return repo.BatchUpdateOverviewImpressionDateByName(ctx, gameCode, views)
}

func getLableRuleMinImpressionDate(ctx context.Context, rule *model.TbAssetLabelRule) (string, error) {
	// 获取aix library overviw 表中该标签规则下所有素材的最小曝光时间
	min1, err := repo.GetOverviewsMinImpressionDateByRule(ctx, rule)
	if err != nil {
		return "", err
	}

	// 获取渠道素材表中该标签规则下所有素材的最小曝光时间
	min2, err := channelRepo.GetChannelAssetsMinImpressionDateByRule(ctx, rule)
	if err != nil {
		return "", err
	}

	// 返回两者中的较小值
	minImpressionDate := min1
	if minImpressionDate == "" {
		minImpressionDate = min2
	} else if min2 != "" && min2 < minImpressionDate {
		minImpressionDate = min2
	}

	return minImpressionDate, nil
}

func removeFileExtension(name string) string {
	ext := filepath.Ext(name)
	if ext != "" {
		name = strings.TrimSuffix(name, ext)
	}

	return name
}

// 渠道素材的account_id和asset_id可能为空，补全为uuid
func completionAllChannelAssets(asset *model.AllChannelAssets) {
	if asset.ChannelAccountID != "" &&
		asset.ChannelAssetID != "" {
		return
	}

	uuid := utils.NewUUIDReplace()
	if asset.ChannelAccountID == "" {
		asset.ChannelAccountID = uuid
	}

	if asset.ChannelAssetID == "" {
		asset.ChannelAssetID = uuid
	}
}
