# readme

## 方案设计

### pubgm历史数据挖掘流程

```mermaid
flowchart TB

get_input[获取所有上传素材asset_set]

get_input --> get_min_date[获取realtime表最小时间min_date]

get_min_date --> loop
subgraph loop[从min_date开始处理每一天的数据]
    direction TB
    b_a[获取一天中的所有上线素材] --> b_b[根据asset_set中的youtube_id定位到上线素材]
    b_b --> b_c[将定位到的素材添加到complete_set, 并到从asset_set中移除]
end

loop --> upsert[(将complete_set更新到数据库)]
```

说明:

1. realtime表: `creative_center.facebook_realtime_asset_info_{game_code}`

### 素材关联流程

```mermaid
flowchart

get_launches[获取所有投放的广告]
filter_exist[过滤已经匹配过的广告]
get_launches --> filter_exist

map_by_upload[通过upload表映射到素材库素材ID]
filter_exist --> map_by_upload

filter_upload[将通过upload表映射成功的广告过滤]
map_by_upload --> filter_upload

map_by_video[将未找到对应素材的广告通过调用算法内容匹配接口进行匹配]
filter_upload --> map_by_video
```
