# Go结构体依赖分析器

这是一个用JavaScript实现的Go结构体依赖分析工具，能够递归分析Go结构体的依赖关系，返回完整的所有相关结构体定义。

## 功能特性

- 🔍 **递归依赖分析**: 自动分析结构体的所有依赖关系，直到不再依赖其他结构体
- 📦 **跨包支持**: 支持分析跨包的结构体引用（如 `aix.Result`）
- 🚀 **高性能**: 使用缓存机制避免重复解析
- 🔄 **循环依赖检测**: 防止无限递归
- 📝 **完整输出**: 返回所有相关结构体的完整定义

## 安装和使用

### 1. 命令行使用

```bash
# 分析指定结构体
node struct-analyzer.js GetMaterialInfoRsp

# 指定项目根目录
node struct-analyzer.js GetMaterialInfoRsp /path/to/project
```

### 2. 作为模块使用

```javascript
const { analyzeStruct, GoStructAnalyzer } = require('./struct-analyzer.js');

// 简单使用
const dependencies = analyzeStruct('GetMaterialInfoRsp', '.');

// 高级使用
const analyzer = new GoStructAnalyzer();
const result = analyzer.analyzeStructDependencies('GetMaterialInfoRsp', 'material_display', '.');
```

## 示例

### 输入
```bash
node struct-analyzer.js GetMaterialInfoRsp
```

### 输出
```
正在分析结构体: GetMaterialInfoRsp
==================================================
找到 7 个相关结构体:
- GetMaterialInfoRsp
- Result
- MaterialExt
- Universal
- Label
- Statistics
- Video

==================================================
完整的结构体定义:
==================================================
// GetMaterialInfoRsp
type GetMaterialInfoRsp struct {
    state         protoimpl.MessageState
    sizeCache     protoimpl.SizeCache
    unknownFields protoimpl.UnknownFields

    Result      *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
    MaterialExt *MaterialExt `protobuf:"bytes,2,opt,name=material_ext,json=materialExt,proto3" json:"material_ext,omitempty"`
}

// Result (来自aix包)
type Result struct {
    state         protoimpl.MessageState
    sizeCache     protoimpl.SizeCache
    unknownFields protoimpl.UnknownFields

    ErrorCode    uint32 `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
    ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

// MaterialExt
type MaterialExt struct {
    // ... 完整的结构体定义
}

// ... 其他相关结构体
```

## 依赖关系图

以 `GetMaterialInfoRsp` 为例：

```
GetMaterialInfoRsp
├── aix.Result (跨包引用)
│   ├── ErrorCode (uint32)
│   └── ErrorMessage (string)
└── MaterialExt
    ├── AssetId (string)
    ├── Universal
    │   ├── Format (string)
    │   ├── Size (uint64)
    │   └── ... (其他基础类型字段)
    ├── Label
    │   ├── ManualFirstLabel ([]string)
    │   └── ... (其他字段)
    ├── Statistics
    │   ├── TakeUsers (uint64)
    │   └── TakeCount (uint64)
    └── Video
        ├── Width (uint32)
        ├── High (uint32)
        └── ... (其他字段)
```

## API 文档

### `analyzeStruct(structName, rootDir)`

分析指定结构体的依赖关系。

**参数:**
- `structName` (string): 要分析的结构体名称
- `rootDir` (string): 项目根目录路径，默认为当前目录

**返回值:**
- `Map`: 包含所有相关结构体定义的Map对象

### `GoStructAnalyzer` 类

高级分析器类，提供更多控制选项。

**主要方法:**
- `analyzeStructDependencies(structName, packageName, rootDir)`: 分析结构体依赖
- `parseGoFile(filePath)`: 解析单个Go文件
- `formatStructDefinitions(structs)`: 格式化输出结构体定义

## 支持的Go语法

- ✅ 基本结构体定义
- ✅ 指针类型 (`*Type`)
- ✅ 切片类型 (`[]Type`)
- ✅ 跨包引用 (`package.Type`)
- ✅ protobuf标签解析
- ✅ 注释保留

## 测试

运行测试套件：

```bash
node test-analyzer.js
```

测试包括：
- GetMaterialInfoRsp结构体分析
- MaterialExt结构体分析
- 不存在结构体的错误处理
- 直接使用分析器类

## 项目结构

```
.
├── struct-analyzer.js    # 主要分析器代码
├── test-analyzer.js      # 测试套件
├── README.md            # 文档
└── protos/              # Go protobuf文件
    ├── aix/
    │   └── aix_common_message.pb.go
    └── material_display/
        └── material_display.pb.go
```

## 注意事项

1. 目前主要针对protobuf生成的Go代码进行优化
2. 支持的包路径需要在代码中预先配置
3. 基础类型（string, int, uint32等）不会被递归分析
4. 自动过滤protobuf内部字段（state, sizeCache, unknownFields）

## 扩展

如需支持更多包或自定义Go代码，可以：

1. 修改 `initializePackagePaths()` 方法添加新的包路径
2. 调整正则表达式以支持不同的Go语法
3. 扩展基础类型列表

## 许可证

MIT License
