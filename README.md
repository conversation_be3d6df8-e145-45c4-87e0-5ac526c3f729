# Go结构体依赖分析器

这是一个用JavaScript实现的Go结构体依赖分析工具，能够递归分析Go结构体的依赖关系，返回完整的所有相关结构体定义。

## 功能特性

- 🔍 **递归依赖分析**: 自动分析结构体的所有依赖关系，直到不再依赖其他结构体
- 📦 **跨包支持**: 支持分析跨包的结构体引用（如 `aix.Result`）
- 🚀 **高性能**: 使用缓存机制避免重复解析
- 🔄 **循环依赖检测**: 防止无限递归
- 📝 **完整输出**: 返回所有相关结构体的完整定义
- 💬 **注释保留**: 完整保留Go代码中的结构体注释和字段注释
- 🎨 **多种格式**: 支持简化格式和原始格式输出

## 安装和使用

### 1. 命令行使用

```bash
# 基本分析（简化格式，保留注释）
node struct-analyzer.js GetMaterialInfoRsp

# 原始格式（包含所有protobuf字段）
node struct-analyzer.js GetMaterialInfoRsp --raw

# 快速分析（推荐）
node quick-analyze.js GetMaterialInfoRsp

# 快速分析 - 原始格式
node quick-analyze.js GetMaterialInfoRsp --raw

# 使用npm脚本
npm run quick GetMaterialInfoRsp
npm run analyze MaterialExt
```

### 2. 作为模块使用

```javascript
const { analyzeStruct, GoStructAnalyzer } = require('./struct-analyzer.js');

// 简单使用（简化格式）
const dependencies = analyzeStruct('GetMaterialInfoRsp', '.');

// 原始格式
const dependencies = analyzeStruct('GetMaterialInfoRsp', '.', true);

// 高级使用
const analyzer = new GoStructAnalyzer();
const result = analyzer.analyzeStructDependencies('GetMaterialInfoRsp', 'material_display', '.');
```

## 示例

### 输入
```bash
node struct-analyzer.js GetMaterialInfoRsp
```

### 输出（简化格式，保留注释）

```
正在分析结构体: GetMaterialInfoRsp
==================================================
找到 7 个相关结构体:
- GetMaterialInfoRsp
- Result
- MaterialExt
- Universal
- Label
- Statistics
- Video

==================================================
完整的结构体定义:
==================================================

type GetMaterialInfoRsp struct {
    Result          *aix.Result
    MaterialExt     *MaterialExt
}

// Aix公共错误结构体
type Result struct {
    ErrorCode       uint32
    ErrorMessage    string
}

// 素材详情扩展数据
type MaterialExt struct {
    AssetId         string      // 素材索引
    Universal       *Universal  // 通用
    Label           *Label      // 标签
    Statistics      *Statistics // 统计
    Video           *Video      // 视频
}

// 素材通用数据
type Universal struct {
    Format          string // 文件格式
    Size            uint64 // 文件大小
    CreateTime      string // 创建时间
    // ... 其他字段（都保留了完整注释）
}

// ... 其他相关结构体
```

### 原始格式输出（使用 --raw 选项）

原始格式会保留所有protobuf字段和完整的结构体定义，包括所有注释。

## 依赖关系图

以 `GetMaterialInfoRsp` 为例：

```
GetMaterialInfoRsp
├── aix.Result (跨包引用)
│   ├── ErrorCode (uint32)
│   └── ErrorMessage (string)
└── MaterialExt
    ├── AssetId (string)
    ├── Universal
    │   ├── Format (string)
    │   ├── Size (uint64)
    │   └── ... (其他基础类型字段)
    ├── Label
    │   ├── ManualFirstLabel ([]string)
    │   └── ... (其他字段)
    ├── Statistics
    │   ├── TakeUsers (uint64)
    │   └── TakeCount (uint64)
    └── Video
        ├── Width (uint32)
        ├── High (uint32)
        └── ... (其他字段)
```

## API 文档

### `analyzeStruct(structName, rootDir)`

分析指定结构体的依赖关系。

**参数:**
- `structName` (string): 要分析的结构体名称
- `rootDir` (string): 项目根目录路径，默认为当前目录

**返回值:**
- `Map`: 包含所有相关结构体定义的Map对象

### `GoStructAnalyzer` 类

高级分析器类，提供更多控制选项。

**主要方法:**
- `analyzeStructDependencies(structName, packageName, rootDir)`: 分析结构体依赖
- `parseGoFile(filePath)`: 解析单个Go文件
- `formatStructDefinitions(structs)`: 格式化输出结构体定义

## 支持的Go语法

- ✅ 基本结构体定义
- ✅ 指针类型 (`*Type`)
- ✅ 切片类型 (`[]Type`)
- ✅ 跨包引用 (`package.Type`)
- ✅ protobuf标签解析
- ✅ 注释保留

## 测试

运行测试套件：

```bash
node test-analyzer.js
```

测试包括：
- GetMaterialInfoRsp结构体分析
- MaterialExt结构体分析
- 不存在结构体的错误处理
- 直接使用分析器类

## 项目结构

```
.
├── struct-analyzer.js    # 主要分析器代码
├── test-analyzer.js      # 测试套件
├── README.md            # 文档
└── protos/              # Go protobuf文件
    ├── aix/
    │   └── aix_common_message.pb.go
    └── material_display/
        └── material_display.pb.go
```

## 注意事项

1. 目前主要针对protobuf生成的Go代码进行优化
2. 支持的包路径需要在代码中预先配置
3. 基础类型（string, int, uint32等）不会被递归分析
4. 自动过滤protobuf内部字段（state, sizeCache, unknownFields）

## 扩展

如需支持更多包或自定义Go代码，可以：

1. 修改 `initializePackagePaths()` 方法添加新的包路径
2. 调整正则表达式以支持不同的Go语法
3. 扩展基础类型列表

## 许可证

MIT License
