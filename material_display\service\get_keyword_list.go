package service

import (
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/gin-gonic/gin"
)

// GetKeywordList ...
func GetKeywordList(ctx *gin.Context, req *material_display.GetKeywordListReq) (*material_display.GetKeywordListRsp, error) {
	var resp material_display.GetKeywordListRsp
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if err := checkGameCodeKey(gameCode); err != nil {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	limit := int(req.Limit)
	if limit <= 0 {
		limit = 10
	}
	keywordList, err := getKeywordListFromDb(ctx, gameCode, req.Prefix, limit)
	if err != nil {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	for _, keyword := range keywordList {
		resp.Suggestions = append(resp.Suggestions, &material_display.Suggestion{Keyword: keyword.Word, Frequency: uint32(keyword.Frequency)})
	}
	return &resp, nil
}

// getKeywordListFromDb 从数据库中根据前缀获取关键词列表
func getKeywordListFromDb(ctx *gin.Context, gameCode, prefix string, limit int) ([]model.CreativeKeyword, error) {
	db := postgresql.GetDBWithContext(ctx)
	keywordList := make([]model.CreativeKeyword, 0)
	_, err := db.Query(&keywordList, fmt.Sprintf(`SELECT word,frequency FROM arthub_sync.tb_creative_keyword_%s WHERE word LIKE ? ORDER BY frequency DESC LIMIT ?`, gameCode), prefix+"%", limit)
	return keywordList, err
}
