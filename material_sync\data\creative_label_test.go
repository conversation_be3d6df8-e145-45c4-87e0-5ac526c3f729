package data

import (
	"context"
	"testing"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
)

func init() {
	setting.SetupByPath("../conf/server_conf.yaml")
	log.Setup()
	postgresql.Setup()
}

func TestGetGameCreativeLabels(t *testing.T) {

	ctx := context.Background()
	labels, err := GetGameCreativeLabels(ctx, "nikke_test")
	if err != nil {
		t.Error("failed to call GetGameCreativeLabels")
	}
	for _, l := range labels {
		t.Logf("Label: %+v", l)
	}
}

func TestUpsertCreativeLabels(t *testing.T) {
	ctx := context.Background()
	labels := []*model.CreativeLabel{
		{
			GameCode:  "nikke_test",
			Name:      "test_label",
			Options:   []string{"test1", "test2"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Creator:   "test",
			Updater:   "test",
		},
		{
			GameCode:  "nikke_test",
			ID:        6,
			Name:      "test_label_update",
			Options:   []string{"test1_update", "test2_update"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Creator:   "test",
			Updater:   "test",
		},
	}

	err := UpsertCreativeLabels(ctx, labels)
	if err != nil {
		t.Error("failed to call UpsertCreativeLabels", err)
	}
}
