syntax = "proto3";

package slack_bot;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/slack_bot";

import "aix/aix_common_message.proto";

// 自测接口, POST, /api/v1/slack_bot/say_hi
message SayHiReq {
}

message Attachment {
    string title        = 1;  // 必填 标题
    string download_url = 2;  // 必填 下载链接
}

// 发送bot消息, POST, /api/v1/slack_bot/send_message
message SendMessageReq {
    string   studio                 = 1;  // 必填 studio
    string   channel_id             = 2;  // 必填 channel
    string   thread_id              = 3;  // 必填 thread
    string   mrkdwn                 = 4;  // 必填 正文, mrkdw格式: https://api.slack.com/reference/surfaces/formatting
    repeated Attachment attachments = 5;  // 附件列表
}

message SendMessageRsp {
    aix.Result result = 1;  // 返回结果
}