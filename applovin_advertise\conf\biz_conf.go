package conf

import (
	"os"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"gopkg.in/yaml.v2"
)

// BizConfStruct ...
type BizConfStruct struct {
	// UploadAssetCampaigns 上传素材的campaign, 每个账号配置一个
	UploadAssetCampaigns []*UploadAssetCampaign `yaml:"upload_asset_campaigns"`
}

// UploadAssetCampaign 上传素材的campaign
type UploadAssetCampaign struct {
	GameCode     string `yaml:"game_code"`
	AccountID    string `yaml:"account_id"`
	CampaignID   string `yaml:"campaign_id"`
	CampaignName string `yaml:"campaign_name"`
}

// BizConf 业务配置
var BizConf BizConfStruct

// LoadBizConf 读取业务配置
func LoadBizConf() {
	LoadBizConfByFile("conf/biz_conf.yaml")
}

// LoadBizConfByFile ...
func LoadBizConfByFile(bizPath string) {
	log.Debugf("LoadBizConf bizPath:%s", bizPath)
	content, err := os.ReadFile(bizPath)
	if err != nil {
		log.Fatalf("setting.LoadBizConf, fail to read '%v': %v", bizPath, err)
	}

	err = yaml.Unmarshal(content, &BizConf)
	if err != nil {
		log.Fatalf("error: %v", err)
	}

	log.Debugf("load biz conf: %s", utils.ToJson(BizConf))
}

// GetUploadAssetCampaign 获取上传素材的campaign, nil 表示没有配置
func GetUploadAssetCampaign(gameCode, accountID string) *UploadAssetCampaign {
	for _, c := range BizConf.UploadAssetCampaigns {
		if c.GameCode == gameCode && c.AccountID == accountID {
			return c
		}
	}

	return nil
}

// GetAllUploadAssetCampaign 获取所有的上传素材的campaign
func GetAllUploadAssetCampaign() []*UploadAssetCampaign {
	return BizConf.UploadAssetCampaigns
}
