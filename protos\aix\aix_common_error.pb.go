// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: aix/aix_common_error.proto

package aix

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AixCommErrCode int32

const (
	AixCommErrCode_AIX_COMM_SUCC                  AixCommErrCode = 0       // 成功
	AixCommErrCode_AIX_COMM_ERR                   AixCommErrCode = 1       // 失败
	AixCommErrCode_AIX_COMM_ERR_UNKNOWN           AixCommErrCode = 8000000 // 未知错误
	AixCommErrCode_AIX_COMM_ERR_PARAM             AixCommErrCode = 8000001 // 参数错误
	AixCommErrCode_AIX_COMM_ERR_RET               AixCommErrCode = 8000002 // 返回错误
	AixCommErrCode_AIX_COMM_ERR_TIMEOUT           AixCommErrCode = 8000003 // 超时错误
	AixCommErrCode_AIX_COMM_ERR_EMPTY             AixCommErrCode = 8000004 // 返回为空
	AixCommErrCode_AIX_COMM_ERR_DB                AixCommErrCode = 8000005 // DB错误
	AixCommErrCode_AIX_COMM_ERR_GET_L5            AixCommErrCode = 8000006 // 获取L5失败
	AixCommErrCode_AIX_COMM_ERR_NOT_LOGIN         AixCommErrCode = 8000007 // 未登陆
	AixCommErrCode_AIX_COMM_ERR_NOT_SUPPORT       AixCommErrCode = 8000008 // 不支持
	AixCommErrCode_AIX_COMM_ERR_NOT_ALLOW         AixCommErrCode = 8000009 // 不允许
	AixCommErrCode_AIX_COMM_ERR_JSON              AixCommErrCode = 8000010 // json解析错误
	AixCommErrCode_AIX_COMM_ERR_SQL               AixCommErrCode = 8000011 // sql错误
	AixCommErrCode_AIX_COMM_ERR_DECODE            AixCommErrCode = 8000012 // decode错误
	AixCommErrCode_AIX_COMM_ERR_ENCODE            AixCommErrCode = 8000013 // encode错误
	AixCommErrCode_AIX_COMM_ERR_SEND              AixCommErrCode = 8000014 // send错误
	AixCommErrCode_AIX_COMM_ERR_EXPIRED           AixCommErrCode = 8000015 // 过期错误
	AixCommErrCode_AIX_COMM_ERR_INIT              AixCommErrCode = 8000016 // 初始化错误
	AixCommErrCode_AIX_COMM_ERR_NULL              AixCommErrCode = 8000017 // 空指针错误
	AixCommErrCode_AIX_COMM_ERR_ID                AixCommErrCode = 8000018 // ID错误
	AixCommErrCode_AIX_COMM_ERR_TIME              AixCommErrCode = 8000019 // 时间错误
	AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR    AixCommErrCode = 8000020 // 服务内部错误
	AixCommErrCode_AIX_COMM_ERR_USER_CONFIG_LIMIT AixCommErrCode = 8000021 // 用户隐私设置权限限制
	AixCommErrCode_AIX_COMM_ERR_HM_RECONGNITION   AixCommErrCode = 8000022 // 验证码识别
	AixCommErrCode_AIX_COMM_ERR_DELAY_PUBLISH     AixCommErrCode = 8000023 // 延迟发布
	AixCommErrCode_AIX_COMM_ERR_DUP_NAME          AixCommErrCode = 8000024 // 重名错误
	AixCommErrCode_AIX_COMM_ERR_CONFIG            AixCommErrCode = 8000025 // 程序配置文件错误
	AixCommErrCode_AIX_COMM_ERR_MEDIA             AixCommErrCode = 8000026 // 渠道侧请求错误
	AixCommErrCode_AIX_COMM_ERR_RATE_LIMITING     AixCommErrCode = 8000027 // 频率限制
)

// Enum value maps for AixCommErrCode.
var (
	AixCommErrCode_name = map[int32]string{
		0:       "AIX_COMM_SUCC",
		1:       "AIX_COMM_ERR",
		8000000: "AIX_COMM_ERR_UNKNOWN",
		8000001: "AIX_COMM_ERR_PARAM",
		8000002: "AIX_COMM_ERR_RET",
		8000003: "AIX_COMM_ERR_TIMEOUT",
		8000004: "AIX_COMM_ERR_EMPTY",
		8000005: "AIX_COMM_ERR_DB",
		8000006: "AIX_COMM_ERR_GET_L5",
		8000007: "AIX_COMM_ERR_NOT_LOGIN",
		8000008: "AIX_COMM_ERR_NOT_SUPPORT",
		8000009: "AIX_COMM_ERR_NOT_ALLOW",
		8000010: "AIX_COMM_ERR_JSON",
		8000011: "AIX_COMM_ERR_SQL",
		8000012: "AIX_COMM_ERR_DECODE",
		8000013: "AIX_COMM_ERR_ENCODE",
		8000014: "AIX_COMM_ERR_SEND",
		8000015: "AIX_COMM_ERR_EXPIRED",
		8000016: "AIX_COMM_ERR_INIT",
		8000017: "AIX_COMM_ERR_NULL",
		8000018: "AIX_COMM_ERR_ID",
		8000019: "AIX_COMM_ERR_TIME",
		8000020: "AIX_COMM_ERR_INTERNAL_ERROR",
		8000021: "AIX_COMM_ERR_USER_CONFIG_LIMIT",
		8000022: "AIX_COMM_ERR_HM_RECONGNITION",
		8000023: "AIX_COMM_ERR_DELAY_PUBLISH",
		8000024: "AIX_COMM_ERR_DUP_NAME",
		8000025: "AIX_COMM_ERR_CONFIG",
		8000026: "AIX_COMM_ERR_MEDIA",
		8000027: "AIX_COMM_ERR_RATE_LIMITING",
	}
	AixCommErrCode_value = map[string]int32{
		"AIX_COMM_SUCC":                  0,
		"AIX_COMM_ERR":                   1,
		"AIX_COMM_ERR_UNKNOWN":           8000000,
		"AIX_COMM_ERR_PARAM":             8000001,
		"AIX_COMM_ERR_RET":               8000002,
		"AIX_COMM_ERR_TIMEOUT":           8000003,
		"AIX_COMM_ERR_EMPTY":             8000004,
		"AIX_COMM_ERR_DB":                8000005,
		"AIX_COMM_ERR_GET_L5":            8000006,
		"AIX_COMM_ERR_NOT_LOGIN":         8000007,
		"AIX_COMM_ERR_NOT_SUPPORT":       8000008,
		"AIX_COMM_ERR_NOT_ALLOW":         8000009,
		"AIX_COMM_ERR_JSON":              8000010,
		"AIX_COMM_ERR_SQL":               8000011,
		"AIX_COMM_ERR_DECODE":            8000012,
		"AIX_COMM_ERR_ENCODE":            8000013,
		"AIX_COMM_ERR_SEND":              8000014,
		"AIX_COMM_ERR_EXPIRED":           8000015,
		"AIX_COMM_ERR_INIT":              8000016,
		"AIX_COMM_ERR_NULL":              8000017,
		"AIX_COMM_ERR_ID":                8000018,
		"AIX_COMM_ERR_TIME":              8000019,
		"AIX_COMM_ERR_INTERNAL_ERROR":    8000020,
		"AIX_COMM_ERR_USER_CONFIG_LIMIT": 8000021,
		"AIX_COMM_ERR_HM_RECONGNITION":   8000022,
		"AIX_COMM_ERR_DELAY_PUBLISH":     8000023,
		"AIX_COMM_ERR_DUP_NAME":          8000024,
		"AIX_COMM_ERR_CONFIG":            8000025,
		"AIX_COMM_ERR_MEDIA":             8000026,
		"AIX_COMM_ERR_RATE_LIMITING":     8000027,
	}
)

func (x AixCommErrCode) Enum() *AixCommErrCode {
	p := new(AixCommErrCode)
	*p = x
	return p
}

func (x AixCommErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AixCommErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_aix_aix_common_error_proto_enumTypes[0].Descriptor()
}

func (AixCommErrCode) Type() protoreflect.EnumType {
	return &file_aix_aix_common_error_proto_enumTypes[0]
}

func (x AixCommErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AixCommErrCode.Descriptor instead.
func (AixCommErrCode) EnumDescriptor() ([]byte, []int) {
	return file_aix_aix_common_error_proto_rawDescGZIP(), []int{0}
}

// 上传素材任务错误码, 9000000 开始
type UploadTaskErrCode int32

const (
	UploadTaskErrCode_Success             UploadTaskErrCode = 0
	UploadTaskErrCode_DownloadAsset       UploadTaskErrCode = 9000000 // 下载素材失败
	UploadTaskErrCode_YoutubeNoQuota      UploadTaskErrCode = 9000001 // youtube channel无配额
	UploadTaskErrCode_NotSupportByChannle UploadTaskErrCode = 9000002 // 该渠道不支持上
)

// Enum value maps for UploadTaskErrCode.
var (
	UploadTaskErrCode_name = map[int32]string{
		0:       "Success",
		9000000: "DownloadAsset",
		9000001: "YoutubeNoQuota",
		9000002: "NotSupportByChannle",
	}
	UploadTaskErrCode_value = map[string]int32{
		"Success":             0,
		"DownloadAsset":       9000000,
		"YoutubeNoQuota":      9000001,
		"NotSupportByChannle": 9000002,
	}
)

func (x UploadTaskErrCode) Enum() *UploadTaskErrCode {
	p := new(UploadTaskErrCode)
	*p = x
	return p
}

func (x UploadTaskErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadTaskErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_aix_aix_common_error_proto_enumTypes[1].Descriptor()
}

func (UploadTaskErrCode) Type() protoreflect.EnumType {
	return &file_aix_aix_common_error_proto_enumTypes[1]
}

func (x UploadTaskErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadTaskErrCode.Descriptor instead.
func (UploadTaskErrCode) EnumDescriptor() ([]byte, []int) {
	return file_aix_aix_common_error_proto_rawDescGZIP(), []int{1}
}

// demo_server 8201000-8201999
type DemoServerErrCode int32

const (
	DemoServerErrCode_DEMO_SERVER_SUCC              DemoServerErrCode = 0
	DemoServerErrCode_DEMO_SERVER_BEGIN             DemoServerErrCode = 8201000
	DemoServerErrCode_DEMO_SERVER_INVALID_PARAMETER DemoServerErrCode = 8201001
	DemoServerErrCode_DEMO_SERVER_QUERY_DB_ERROR    DemoServerErrCode = 8201002
	DemoServerErrCode_DEMO_SERVER_END               DemoServerErrCode = 8201999
)

// Enum value maps for DemoServerErrCode.
var (
	DemoServerErrCode_name = map[int32]string{
		0:       "DEMO_SERVER_SUCC",
		8201000: "DEMO_SERVER_BEGIN",
		8201001: "DEMO_SERVER_INVALID_PARAMETER",
		8201002: "DEMO_SERVER_QUERY_DB_ERROR",
		8201999: "DEMO_SERVER_END",
	}
	DemoServerErrCode_value = map[string]int32{
		"DEMO_SERVER_SUCC":              0,
		"DEMO_SERVER_BEGIN":             8201000,
		"DEMO_SERVER_INVALID_PARAMETER": 8201001,
		"DEMO_SERVER_QUERY_DB_ERROR":    8201002,
		"DEMO_SERVER_END":               8201999,
	}
)

func (x DemoServerErrCode) Enum() *DemoServerErrCode {
	p := new(DemoServerErrCode)
	*p = x
	return p
}

func (x DemoServerErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DemoServerErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_aix_aix_common_error_proto_enumTypes[2].Descriptor()
}

func (DemoServerErrCode) Type() protoreflect.EnumType {
	return &file_aix_aix_common_error_proto_enumTypes[2]
}

func (x DemoServerErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DemoServerErrCode.Descriptor instead.
func (DemoServerErrCode) EnumDescriptor() ([]byte, []int) {
	return file_aix_aix_common_error_proto_rawDescGZIP(), []int{2}
}

// 【TikTok错误码】 范围: 1003001 - 1003999
type AixTikTokErrCode int32

const (
	AixTikTokErrCode_AIX_TikTok_SUCCESS                                AixTikTokErrCode = 0
	AixTikTokErrCode_AIX_TIKTOK_CAMPAIGN_DISABLE_FAIL_WHEN_PUBLISH_SUC AixTikTokErrCode = 1003001 // 发布渠道campaign成功，设置渠道campaign为disable状态失败
	AixTikTokErrCode_AIX_TIKTOK_AD_ACO_PUBLISHING                      AixTikTokErrCode = 1003002 // aco 创意广告发布中
)

// Enum value maps for AixTikTokErrCode.
var (
	AixTikTokErrCode_name = map[int32]string{
		0:       "AIX_TikTok_SUCCESS",
		1003001: "AIX_TIKTOK_CAMPAIGN_DISABLE_FAIL_WHEN_PUBLISH_SUC",
		1003002: "AIX_TIKTOK_AD_ACO_PUBLISHING",
	}
	AixTikTokErrCode_value = map[string]int32{
		"AIX_TikTok_SUCCESS": 0,
		"AIX_TIKTOK_CAMPAIGN_DISABLE_FAIL_WHEN_PUBLISH_SUC": 1003001,
		"AIX_TIKTOK_AD_ACO_PUBLISHING":                      1003002,
	}
)

func (x AixTikTokErrCode) Enum() *AixTikTokErrCode {
	p := new(AixTikTokErrCode)
	*p = x
	return p
}

func (x AixTikTokErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AixTikTokErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_aix_aix_common_error_proto_enumTypes[3].Descriptor()
}

func (AixTikTokErrCode) Type() protoreflect.EnumType {
	return &file_aix_aix_common_error_proto_enumTypes[3]
}

func (x AixTikTokErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AixTikTokErrCode.Descriptor instead.
func (AixTikTokErrCode) EnumDescriptor() ([]byte, []int) {
	return file_aix_aix_common_error_proto_rawDescGZIP(), []int{3}
}

var File_aix_aix_common_error_proto protoreflect.FileDescriptor

var file_aix_aix_common_error_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x61, 0x69,
	0x78, 0x2a, 0xe4, 0x06, 0x0a, 0x0e, 0x41, 0x69, 0x78, 0x43, 0x6f, 0x6d, 0x6d, 0x45, 0x72, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x49, 0x58, 0x5f, 0x43,
	0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x14, 0x41, 0x49, 0x58,
	0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x80, 0xa4, 0xe8, 0x03, 0x12, 0x19, 0x0a, 0x12, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x10, 0x81, 0xa4, 0xe8,
	0x03, 0x12, 0x17, 0x0a, 0x10, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52,
	0x52, 0x5f, 0x52, 0x45, 0x54, 0x10, 0x82, 0xa4, 0xe8, 0x03, 0x12, 0x1b, 0x0a, 0x14, 0x41, 0x49,
	0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f,
	0x55, 0x54, 0x10, 0x83, 0xa4, 0xe8, 0x03, 0x12, 0x19, 0x0a, 0x12, 0x41, 0x49, 0x58, 0x5f, 0x43,
	0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x84, 0xa4,
	0xe8, 0x03, 0x12, 0x16, 0x0a, 0x0f, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45,
	0x52, 0x52, 0x5f, 0x44, 0x42, 0x10, 0x85, 0xa4, 0xe8, 0x03, 0x12, 0x1a, 0x0a, 0x13, 0x41, 0x49,
	0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x4c,
	0x35, 0x10, 0x86, 0xa4, 0xe8, 0x03, 0x12, 0x1d, 0x0a, 0x16, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e,
	0x10, 0x87, 0xa4, 0xe8, 0x03, 0x12, 0x1f, 0x0a, 0x18, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52,
	0x54, 0x10, 0x88, 0xa4, 0xe8, 0x03, 0x12, 0x1d, 0x0a, 0x16, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57,
	0x10, 0x89, 0xa4, 0xe8, 0x03, 0x12, 0x18, 0x0a, 0x11, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x8a, 0xa4, 0xe8, 0x03, 0x12,
	0x17, 0x0a, 0x10, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f,
	0x53, 0x51, 0x4c, 0x10, 0x8b, 0xa4, 0xe8, 0x03, 0x12, 0x1a, 0x0a, 0x13, 0x41, 0x49, 0x58, 0x5f,
	0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x44, 0x45, 0x43, 0x4f, 0x44, 0x45, 0x10,
	0x8c, 0xa4, 0xe8, 0x03, 0x12, 0x1a, 0x0a, 0x13, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d,
	0x5f, 0x45, 0x52, 0x52, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x8d, 0xa4, 0xe8, 0x03,
	0x12, 0x18, 0x0a, 0x11, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52,
	0x5f, 0x53, 0x45, 0x4e, 0x44, 0x10, 0x8e, 0xa4, 0xe8, 0x03, 0x12, 0x1b, 0x0a, 0x14, 0x41, 0x49,
	0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x45, 0x44, 0x10, 0x8f, 0xa4, 0xe8, 0x03, 0x12, 0x18, 0x0a, 0x11, 0x41, 0x49, 0x58, 0x5f, 0x43,
	0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x90, 0xa4, 0xe8,
	0x03, 0x12, 0x18, 0x0a, 0x11, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52,
	0x52, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x91, 0xa4, 0xe8, 0x03, 0x12, 0x16, 0x0a, 0x0f, 0x41,
	0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x92,
	0xa4, 0xe8, 0x03, 0x12, 0x18, 0x0a, 0x11, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f,
	0x45, 0x52, 0x52, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x93, 0xa4, 0xe8, 0x03, 0x12, 0x22, 0x0a,
	0x1b, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x94, 0xa4, 0xe8,
	0x03, 0x12, 0x25, 0x0a, 0x1e, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52,
	0x52, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x10, 0x95, 0xa4, 0xe8, 0x03, 0x12, 0x23, 0x0a, 0x1c, 0x41, 0x49, 0x58, 0x5f,
	0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x48, 0x4d, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x4e, 0x47, 0x4e, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x96, 0xa4, 0xe8, 0x03, 0x12, 0x21, 0x0a,
	0x1a, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x44, 0x45,
	0x4c, 0x41, 0x59, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x10, 0x97, 0xa4, 0xe8, 0x03,
	0x12, 0x1c, 0x0a, 0x15, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52,
	0x5f, 0x44, 0x55, 0x50, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x98, 0xa4, 0xe8, 0x03, 0x12, 0x1a,
	0x0a, 0x13, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x43,
	0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x99, 0xa4, 0xe8, 0x03, 0x12, 0x19, 0x0a, 0x12, 0x41, 0x49,
	0x58, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x41,
	0x10, 0x9a, 0xa4, 0xe8, 0x03, 0x12, 0x21, 0x0a, 0x1a, 0x41, 0x49, 0x58, 0x5f, 0x43, 0x4f, 0x4d,
	0x4d, 0x5f, 0x45, 0x52, 0x52, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x49, 0x4e, 0x47, 0x10, 0x9b, 0xa4, 0xe8, 0x03, 0x2a, 0x69, 0x0a, 0x11, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x0d, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x10, 0xc0, 0xa8, 0xa5, 0x04,
	0x12, 0x15, 0x0a, 0x0e, 0x59, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x4e, 0x6f, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x10, 0xc1, 0xa8, 0xa5, 0x04, 0x12, 0x1a, 0x0a, 0x13, 0x4e, 0x6f, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x6c, 0x65, 0x10, 0xc2,
	0xa8, 0xa5, 0x04, 0x2a, 0xa4, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6d, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x4d,
	0x4f, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x00, 0x12,
	0x18, 0x0a, 0x11, 0x44, 0x45, 0x4d, 0x4f, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x42,
	0x45, 0x47, 0x49, 0x4e, 0x10, 0xa8, 0xc6, 0xf4, 0x03, 0x12, 0x24, 0x0a, 0x1d, 0x44, 0x45, 0x4d,
	0x4f, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x10, 0xa9, 0xc6, 0xf4, 0x03, 0x12,
	0x21, 0x0a, 0x1a, 0x44, 0x45, 0x4d, 0x4f, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x51,
	0x55, 0x45, 0x52, 0x59, 0x5f, 0x44, 0x42, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xaa, 0xc6,
	0xf4, 0x03, 0x12, 0x16, 0x0a, 0x0f, 0x44, 0x45, 0x4d, 0x4f, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45,
	0x52, 0x5f, 0x45, 0x4e, 0x44, 0x10, 0x8f, 0xce, 0xf4, 0x03, 0x2a, 0x87, 0x01, 0x0a, 0x10, 0x41,
	0x69, 0x78, 0x54, 0x69, 0x6b, 0x54, 0x6f, 0x6b, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x12, 0x41, 0x49, 0x58, 0x5f, 0x54, 0x69, 0x6b, 0x54, 0x6f, 0x6b, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x37, 0x0a, 0x31, 0x41, 0x49, 0x58, 0x5f, 0x54,
	0x49, 0x4b, 0x54, 0x4f, 0x4b, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49, 0x47, 0x4e, 0x5f, 0x44,
	0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x5f, 0x57, 0x48, 0x45, 0x4e,
	0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x5f, 0x53, 0x55, 0x43, 0x10, 0xf9, 0x9b, 0x3d,
	0x12, 0x22, 0x0a, 0x1c, 0x41, 0x49, 0x58, 0x5f, 0x54, 0x49, 0x4b, 0x54, 0x4f, 0x4b, 0x5f, 0x41,
	0x44, 0x5f, 0x41, 0x43, 0x4f, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47,
	0x10, 0xfa, 0x9b, 0x3d, 0x42, 0x32, 0x5a, 0x30, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74,
	0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x69, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aix_aix_common_error_proto_rawDescOnce sync.Once
	file_aix_aix_common_error_proto_rawDescData = file_aix_aix_common_error_proto_rawDesc
)

func file_aix_aix_common_error_proto_rawDescGZIP() []byte {
	file_aix_aix_common_error_proto_rawDescOnce.Do(func() {
		file_aix_aix_common_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_aix_aix_common_error_proto_rawDescData)
	})
	return file_aix_aix_common_error_proto_rawDescData
}

var file_aix_aix_common_error_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_aix_aix_common_error_proto_goTypes = []interface{}{
	(AixCommErrCode)(0),    // 0: aix.AixCommErrCode
	(UploadTaskErrCode)(0), // 1: aix.UploadTaskErrCode
	(DemoServerErrCode)(0), // 2: aix.DemoServerErrCode
	(AixTikTokErrCode)(0),  // 3: aix.AixTikTokErrCode
}
var file_aix_aix_common_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_aix_aix_common_error_proto_init() }
func file_aix_aix_common_error_proto_init() {
	if File_aix_aix_common_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aix_aix_common_error_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_aix_aix_common_error_proto_goTypes,
		DependencyIndexes: file_aix_aix_common_error_proto_depIdxs,
		EnumInfos:         file_aix_aix_common_error_proto_enumTypes,
	}.Build()
	File_aix_aix_common_error_proto = out.File
	file_aix_aix_common_error_proto_rawDesc = nil
	file_aix_aix_common_error_proto_goTypes = nil
	file_aix_aix_common_error_proto_depIdxs = nil
}
