**Generated by protoc-gen-md. DO NOT EDIT.**
[TOC]
# 内部测试接口, POST, /api/v1/arthub_data_sync/say_hi
test curl:
```shell
curl 'http://target/api/v1/arthub_data_sync/say_hi' -H 'Content-Type:application/json' -d '{"arthub_code":"","ids":[0],"token":""}'
```
#### say hi req
parameter explain:
```yaml
SayHiReq: # 内部测试接口, POST, /api/v1/arthub_data_sync/say_hi
  arthub_code: "" # arthub code
  token: "" # token
  ids: [0] # 素材列表

```
data example:
```json
{
    "arthub_code": "",
    "ids": [
        0
    ],
    "token": ""
}
```
#### say hi rsp
parameter explain:
```yaml
SayHiRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
