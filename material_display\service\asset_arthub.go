package service

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
)

type ArthubTempDownloadUrl struct {
	ObjectId   uint64 `json:"object_id"`
	ObjectMeta string `json:"object_meta"`
	//ContentType  string `json:"content_type"`
	DownloadName       string `json:"download_name"`
	ContentDisposition string `json:"content_disposition"`
}

type ArthubTempDownloadUrlRespResultItem struct {
	ObjectId   uint64 `json:"object_id"`
	ObjectMeta string `json:"object_meta"`
	FileName   string `json:"file_name"`
	SignedUrl  string `json:"signed_url"`
	OriginUrl  string `json:"origin_url"`
	Expire     uint64 `json:"expire"`
	ParamIndex uint64 `json:"param_index"`
}

type ArthubTempDownloadUrlRespResult struct {
	Items []ArthubTempDownloadUrlRespResultItem `json:"items"`
}

type ArthubTempDownloadUrlRespResultError struct {
	ParamIndex uint64 `json:"param_index"`
	Id         uint64 `json:"id"`
	Message    string `json:"message"`
}

type ArthubTempDownloadUrlResp struct {
	Code   int                             `json:"code"`
	Result ArthubTempDownloadUrlRespResult `json:"result"`
	// Error  []ArthubTempDownloadUrlRespResultError `json:"error"`
}

func GenerateArthubTempDownloadUrl(ctx *gin.Context, req *material_display.
	GenerateArthubTempDownloadUrlReq) (*material_display.GenerateArthubTempDownloadUrlRsp, error) {
	resp := &material_display.GenerateArthubTempDownloadUrlRsp{}
	gamecode := req.GetDepotCode()
	//获取根节点信息
	depot, err := data.GetDepot(gamecode)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get arthub token information, gamecode: %v, err: %v", gamecode, err)
		return resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	if depot.Type != utils.GAME_DEPOT_TYPE_ARTHUB {
		log.ErrorContextf(ctx, "depot type not supported, type: %v, depot: %+v", depot.Type, depot)
		return resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR), "depot type not supported")
	}

	items := req.GetItems()
	if len(items) > 0 {
		url := fmt.Sprintf("https://service.arthub.qq.com/%s/data/openapi/v2/core/get-download-signature",
			depot.ArthubCode)
		//contentType := "application%2Foctet-stream"
		reqObjs := []ArthubTempDownloadUrl{}

		for idx := range items {
			assetID, err := strconv.ParseUint(items[idx].AssetId, 10, 64)
			if err != nil {
				log.ErrorContextf(ctx, "Fail to parse assetID, assetID: %v, err: %v", items[idx].AssetId, err)
				return resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR), "Fail to parse assetID")
			}
			obj := ArthubTempDownloadUrl{
				ObjectId:   assetID,
				ObjectMeta: "origin_url",
				//ContentType:  contentType,
				DownloadName:       items[idx].DownloadName,
				ContentDisposition: items[idx].GetContentDisposition(),
			}
			reqObjs = append(reqObjs, obj)
		}
		body, err := json.Marshal(&reqObjs)
		if err != nil {
			log.ErrorContextf(ctx, "fail to generate arthub temporary download url, json.Marshal request object: %v, err: %v", reqObjs, err)
			return resp, errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_JSON), "arthub body format json.Marshal err:%v", err)
		}
		log.DebugContextf(ctx, "GenerateArthubTempDownloadUrl http req body:%v", string(body))
		arthubRespStr, err := HTTPClientDo(ctx, http.MethodPost, url, depot.PublicToken, body)
		if err != nil {
			log.ErrorContextf(ctx, "Fail to generate arthub temporary download url, url: %v, token: %v, "+
				"body: %v, error: %v", url, depot.PublicToken, string(body), err)
			return resp, errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "HTTPClientDo error:%v", err)
		}
		log.DebugContextf(ctx, "arthub temporary url:%s, body:%s, resp: %s", url, string(body), arthubRespStr)

		arthubRespObj := ArthubTempDownloadUrlResp{}
		if err := json.Unmarshal([]byte(arthubRespStr), &arthubRespObj); err != nil {
			log.ErrorContextf(ctx, "Fail to ugenerate arthub temporary download url, json.Unmarshal resp: %v, error: %v", arthubRespStr, err)
			return resp, errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_JSON), "arthub response json.Unmarshal err:%v, body:%v", err, arthubRespStr)
		}
		if arthubRespObj.Code != 0 {
			log.ErrorContextf(ctx, "Fail to generate arthub temporary download url, code: %v, body:%v", arthubRespObj.Code, arthubRespStr)
			return resp, errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "arthubRespObj.Code:%v, body:%v", arthubRespObj.Code, arthubRespStr)
		}

		// 格式化数据
		resp = formatArthubTempDownloadUrlnfoToProto(&arthubRespObj)
	}

	return resp, nil
}

func formatArthubTempDownloadUrlnfoToProto(obj *ArthubTempDownloadUrlResp) *material_display.GenerateArthubTempDownloadUrlRsp {
	arthubInfo := material_display.GenerateArthubTempDownloadUrlRsp{}
	if obj == nil {
		return &arthubInfo
	}
	succeedItems := []*material_display.GenerateArthubTempDownloadUrlSucceedItem{}
	failedItems := []*material_display.GenerateArthubTempDownloadUrlFailedItem{}
	items := obj.Result.Items
	for idx := range items {
		signedUrl := items[idx].SignedUrl
		if strings.Contains(signedUrl, `\u0026`) {
			signedUrl = strings.ReplaceAll(signedUrl, `\u0026`, "&")
		}
		succeedItem := material_display.GenerateArthubTempDownloadUrlSucceedItem{
			AssetId:   strconv.FormatUint(items[idx].ObjectId, 10),
			SignedUrl: signedUrl,
			Expire:    items[idx].Expire,
		}
		succeedItems = append(succeedItems, &succeedItem)
	}
	// errorItems := obj.Error
	// for idx := range errorItems {
	// 	failedItem := material_display.GenerateArthubTempDownloadUrlFailedItem{
	// 		AssetId: strconv.FormatUint(errorItems[idx].Id, 10),
	// 		Message: errorItems[idx].Message,
	// 	}
	// 	failedItems = append(failedItems, &failedItem)
	// }
	arthubInfo.FailedItems = failedItems
	arthubInfo.SucceedItems = succeedItems
	return &arthubInfo
}
