syntax = "proto3";

package material_sync;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync";

import "aix/aix_common_message.proto";

// 内部接口 触发定时任务, POST, /api/v1/material_sync/cron_trigger
message CronTriggerReq {
    string cron_name = 1;  // 定时任务名字
    string game_code = 2;
    string date      = 3;  // 日期格式：20231201
}

message CronTriggerRsp {
    aix.Result result = 1;  // 返回结果
}

// 编辑素材机器标签和封面, POST, /api/v1/material_sync/set_material_label
message SetMaterialLabelReq {
    string asset_id                = 1;  // 素材id
    string algorithm_thumbnail_url = 2;  // 新封面
    string first_level_labels      = 3;  // 机器一级标签
    string second_level_labels     = 4;  // 机器二级标签
    string depot_name              = 5;  // 游戏代码
    string cos_url                 = 6;  // cos url
}

message SetRes {
    string asset_id = 1;  // 素材id
    uint32 set_res  = 2;  // 设置结果 0-失败 1-成功
}

message SetMaterialLabelRsp {
    aix.Result result   = 1;  // 返回结果
    SetRes set_res_list = 2;  // 设置结果列表
}

// 自测接口, POST, /api/v1/material_sync/say_hi
message SayHiReq {
}

message SayHiRsp {
    aix.Result result = 1;  // 返回结果
}

// 自测接口, POST, /api/v1/material_sync/verify_asset
message VerifyAssetReq {
    string asset_id = 1;
}

message VerifyAssetRsp {
    aix.Result result         = 1;  // 返回结果
    string full_path_name     = 2;  // ...
    string youtube_id         = 3;  // ...
    string channel_account_id = 4;  // ...
    string channel_asset_id   = 5;  // ...
    string asset_name         = 6;  // ...
}

// 自测接口, POST, /api/v1/material_sync/sync_creative_recommend_manual
message SyncCreativeRecommendManualReq {
}

message SyncCreativeRecommendManualRsp {
    aix.Result result = 1;  // 返回结果
}

// 自测接口, POST, /api/v1/material_sync/rectify_creative_upload_manual
message RectifyCreativeUploadManualReq {
}

message RectifyCreativeUploadManualRsp {
    aix.Result result = 1;  // 返回结果
}

// 非实体素材
message VirtualAsset {
    string asset_id      = 1;  // 素材ID
    string asset_name    = 2;  // 素材名称
    int32 asset_type     = 3;  // 素材类型. 1-youtube video id; 2-素材编号
    string asset_version = 4;  // 素材版本, 弃用
    string asset_theme   = 5;  // 素材主题, 弃用
    string label_name    = 6;  // 标签名
    string first_label   = 7;  // 一级标签
    string second_label  = 8;  // 二级标签
}

// 上传非实体素材, POST, /api/v1/material_sync/upload_virtual_assets
message UploadVirtualAssetsReq {
    repeated VirtualAsset assets = 1;  // 虚拟素材列表
    string user_name             = 2;  // 上传用户名
    int32 dig_asset_version      = 3;  // 是否挖掘素材版本. 0-否, 1-是
    int32 map_history_type       = 4;  // 映射历史广告素材方式. 1-使用新插入的虚拟标签进行映射; 2-直接使用上传的虚拟标签进行映射. 默认使用1.
}

message UploadVirtualAssetsRsp {
    aix.Result result = 1;  // 返回结果
}

// 设置素材映射, POST, /api/v1/material_sync/set_asset_map
message SetAssetMapReq {
    string req_id           = 1;  // 请求ID
    string video_id         = 2;  // 视频ID
    string mapping_asset_id = 3;  // 映射到的素材ID, 为空表示没有映射到
    int32 storage_type      = 4;  // 存储类型, 1-cos，2-arthub，3-google_driver，4-web_http，5-youtube_url
    string error_msg        = 5;  // 没有映射到素材ID时返回的错误信息
}

message SetAssetMapRsq {
    aix.Result result = 1;  // 返回结果
}

// 自测接口, POST, /api/v1/material_sync/start_media_content_map
message StartMediaContentMapReq {
}

message StartMediaContentMapRsp {
    aix.Result result = 1;  // 返回结果
}

// 同步游戏历史上线数据, POST, /api/v1/material_sync/sync_overview_online_status_history
message SyncOverviewOnlineStatusHistoryReq {
    string game_code  = 1;  // game code
    string start_time = 2;  // 开始时间
    string end_time   = 3;  // 结束时间
}

message SyncOverviewOnlineStatusHistoryRsp {
    aix.Result result = 1;  // 返回结果
}

// 通知接口，将某个标签规则应用到aix library, POST, /api/v1/material_sync/notify_apply_label_rule_to_aix
message NotifyApplyLabelRuleToAixReq {
    string game_code = 1;  // game code
    int64  rule_id   = 2;
}

message NotifyApplyLabelRuleToAixRsp {
    aix.Result result = 1;  // 返回结果
}