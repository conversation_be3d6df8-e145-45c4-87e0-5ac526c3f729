package service

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// CheckAutomaticSyncTaskRuleName 检查自动化上传任务规则名称是否重复
func CheckAutomaticSyncTaskRuleName(ctx *gin.Context, req *pb.CheckAutomaticSyncTaskRuleNameReq, rsp *pb.CheckAutomaticSyncTaskRuleNameRsp) error {
	oldTaskRule, err := repo.GetAutomaticSyncTaskRuleByName(ctx, req.GetGameCode(), req.GetName())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"CheckAutomaticSyncTaskRuleName repo.GetAutomaticSyncTaskRuleByName err:%v", err)
	}
	// 同时支持了新增 id=0 的情况
	if oldTaskRule != nil && oldTaskRule.ID != req.GetId() {
		rsp.Duplicate = true
	}

	return nil
}

// AddAutomaticSyncTaskRule 增加/更新自动化上传任务规则
func AddAutomaticSyncTaskRule(ctx *gin.Context, req *pb.AddAutomaticSyncTaskRuleReq, rsp *pb.AddAutomaticSyncTaskRuleRsp) error {
	user := utils.GetHeaderUser(ctx)

	taskRule := req.GetTaskRule()
	err := checkAddAutomaticSyncTaskRuleReq(taskRule)
	if err != nil {
		return err
	}
	taskRule.CreateUser = user
	taskRule.UpdateUser = user
	taskRule.StatusChangeTime = utils.FormatDefault(time.Now().UTC())

	if taskRule.GetId() == 0 {
		// 新增, 先查是否已经有同名的任务规则
		oldTaskRule, err := repo.GetAutomaticSyncTaskRuleByName(ctx, taskRule.GetGameCode(), taskRule.GetName())
		if err != nil {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddAutomaticSyncTaskRule repo.GetAutomaticSyncTaskRuleByName err:%v", err)
		}
		if oldTaskRule != nil {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
				"Duplicate Rule Name. Please change the name.")
		}

		err = data.InsertAutomaticSyncTaskRule(ctx, taskRule)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddAutomaticSyncTaskRule data.InsertAutomaticSyncTaskRule err:%v", err)
		}
	} else {
		// 更新，先查是否已经有同名的其他任务规则
		oldTaskRule, err := repo.GetAutomaticSyncTaskRuleByName(ctx, taskRule.GetGameCode(), taskRule.GetName())
		if err != nil {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddAutomaticSyncTaskRule repo.GetAutomaticSyncTaskRuleByName err:%v", err)
		}
		if oldTaskRule != nil && oldTaskRule.ID != taskRule.GetId() {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
				"Duplicate Rule Name. Please change the name.")
		}
		// 更新
		fiedls := []string{
			"name",
			"sync_dir_info",
			"asset_type",
			"sync_media_info",
			"end_date",
			"update_user",
			"update_time",
			"status",
			"status_change_time",
			"start_cloud_upload_time",
		}

		err = data.UpdateAutomaticSyncTaskRuleFields(ctx, taskRule, fiedls)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddAutomaticSyncTaskRule data.InsertAutomaticSyncTaskRule err:%v", err)
		}
	}

	rsp.Id = taskRule.GetId()
	return nil
}

// GetAutomaticSyncTaskRule 拉取自动化上传任务规则
func GetAutomaticSyncTaskRule(ctx *gin.Context, req *pb.GetAutomaticSyncTaskRuleReq, rsp *pb.GetAutomaticSyncTaskRuleRsp) error {
	var rows []*pb.AutomaticSyncTaskRule
	var total int
	var err error

	if req.GetId() != 0 {
		// 获取单个任务规则
		row, err := data.GetAutomaticSyncTaskRule(ctx, req.GetId())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"GetAutomaticSyncTaskRule data.GetAutomaticSyncTaskRule err:%v", err)
		}
		if row == nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "task_rule_id:%v not found", req.GetId())
		}

		rows = append(rows, row)
		total = 1
	} else {
		page := req.GetPage()
		pageSize := req.GetPageSize()
		maxPageSize := int32(100)
		if page < 0 || pageSize <= 0 || pageSize > maxPageSize {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "page or page_size error")
		}

		offset := int(page * pageSize)
		limit := int(pageSize)
		filter := &repo.AutomaticSyncTaskRuleFilter{
			GameCode: req.GetGameCode(),
			Offset:   offset,
			Limit:    limit,
		}
		rows, total, err = data.FilterAutomaticSyncTaskRule(ctx, filter)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"GetAutomaticSyncTaskRule data.FilterAutomaticSyncTaskRule err:%v", err)
		}
	}

	// 获取一下目录信息，因为可能目录名会变化
	var dirIDs []string
	for _, row := range rows {
		for _, dir := range row.GetDirs() {
			dirIDs = append(dirIDs, dir.GetId())
		}
	}
	// 去重
	dirIDs = funk.UniqString(dirIDs)
	dirMap, err := repo.BatchGetCreativeDirectoryByIDMap(ctx, req.GetGameCode(), dirIDs)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"GetAutomaticSyncTaskRule repo.BatchGetCreativeDirectoryByIDMap err:%v", err)
	}
	for _, row := range rows {
		for _, dir := range row.GetDirs() {
			if dirInfo, ok := dirMap[dir.GetId()]; ok {
				dir.Name = dirInfo.Name
				dir.FullPathName = dirInfo.FullPathName
			}
		}
	}

	rsp.TaskRules = rows
	rsp.Total = uint32(total)
	return nil
}

// ChangeAutomaticSyncTaskRuleStatus 更新自动化上传任务规则状态
func ChangeAutomaticSyncTaskRuleStatus(ctx *gin.Context,
	req *pb.ChangeAutomaticSyncTaskRuleStatusReq, rsp *pb.ChangeAutomaticSyncTaskRuleStatusRsp) error {
	if req.GetStatus() != constant.AutomaticSyncTaskRuleStatusOff &&
		req.GetStatus() != constant.AutomaticSyncTaskRuleStatusOn {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
			"request status:%v error", req.GetStatus())
	}

	taskRule, err := repo.GetAutomaticSyncTaskRule(ctx, req.GetId())
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"ChangeAutomaticSyncTaskRuleStatus repo.GetAutomaticSyncTaskRule err:%v", err)
	}
	if taskRule == nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"no such task rule id:%v", req.GetId())
	}
	if taskRule.GameCode != req.GameCode {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
			"task rule id:%v game_code:%v not match request game_code:%v",
			taskRule.ID, taskRule.GameCode, req.GameCode)
	}
	if taskRule.Deleted {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"task rule id:%v has been deleted", req.GetId())
	}

	taskRule.Status = req.Status
	taskRule.UpdateUser = utils.GetHeaderUser(ctx)
	taskRule.UpdateTime = time.Now()
	taskRule.StatusChangeTime = time.Now().UTC()
	err = repo.UpdateAutomaticSyncTaskRuleFields(ctx, taskRule, []string{"status", "update_user", "update_time", "status_change_time"})
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"ChangeAutomaticSyncTaskRuleStatus repo.UpdateAutomaticSyncTaskRuleFields err:%v", err)
	}
	return nil
}

// DeleteAutomaticSyncTaskRule 删除自动化上传任务规则
func DeleteAutomaticSyncTaskRule(ctx *gin.Context,
	req *pb.DeleteAutomaticSyncTaskRuleReq, rsp *pb.DeleteAutomaticSyncTaskRuleRsp) error {
	user := utils.GetHeaderUser(ctx)

	taskRule, err := repo.GetAutomaticSyncTaskRule(ctx, req.GetId())
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"ChangeAutomaticSyncTaskRuleStatus repo.GetAutomaticSyncTaskRule err:%v", err)
	}
	if taskRule == nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"no such task rule id:%v", req.GetId())
	}
	if taskRule.GameCode != req.GameCode {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
			"task rule id:%v game_code:%v not match request game_code:%v",
			taskRule.ID, taskRule.GameCode, req.GameCode)
	}

	err = repo.DeleteAutomaticSyncTaskRule(ctx, taskRule.ID, user)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"ChangeAutomaticSyncTaskRuleStatus repo.DeleteAutomaticSyncTaskRule err:%v", err)
	}
	return nil
}

func checkAddAutomaticSyncTaskRuleReq(taskRule *pb.AutomaticSyncTaskRule) error {
	if taskRule.GetGameCode() == "" ||
		taskRule.GetName() == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code or name error")
	}

	if taskRule.GetStatus() != constant.AutomaticSyncTaskRuleStatusOff &&
		taskRule.GetStatus() != constant.AutomaticSyncTaskRuleStatusOn {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
			"request status:%v error", taskRule.GetStatus())
	}

	assetType := taskRule.GetAssetType()
	if assetType != 0 && // 不限
		assetType != constant.AssetVideoType && // 视频
		assetType != constant.AssetImageType && // 图片
		assetType != constant.AssetHTMLType { // html
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "asset type error")
	}

	if len(taskRule.GetDirs()) == 0 {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "selected folder not set")
	}
	for _, d := range taskRule.GetDirs() {
		if d.GetId() == "" || d.GetName() == "" || d.GetFullPathName() == "" {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "sync folder information empty")
		}
	}

	if len(taskRule.GetMedias()) == 0 {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "sync media channel not set")
	}
	for _, m := range taskRule.GetMedias() {
		if !funk.ContainsInt(constant.SupportUploadMedias, int(m.GetChannel())) {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
				"sync media channel:%v not support", m.GetChannel())
		}

		if m.GetAccounts() == "" {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "sync media channel accounts empty")
		}

		if m.GetChannel() == constant.MediaUnity && m.GetLanguage() == "" {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "sync media unity language empty")
		}
	}

	if taskRule.GetEndDate() != "" {
		if !utils.IsNumericDate(taskRule.GetEndDate()) {
			return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
				"end date err:%v", taskRule.GetEndDate())
		}
	}

	// 格式: 2024-01-01 01:02:03  表示自动化规则 处理那些 > start_cloud_upload_time 的素材
	// 必填
	_, err := utils.ParseUTCTime(taskRule.GetStartCloudUploadTime())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
			"start cloud upload time err:%v", taskRule.GetStartCloudUploadTime())
	}
	return nil
}
