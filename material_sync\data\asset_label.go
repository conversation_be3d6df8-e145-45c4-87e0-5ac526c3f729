package data

import (
	"context"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"github.com/go-pg/pg/v10"
	"github.com/thoas/go-funk"
)

// InsertAssetLablesTransaction 插入素材标签，会先删除素材的旧标签，事务中处理
func InsertAssetLablesTransaction(ctx context.Context, gameCode string, assetIDs []string, lables []*model.AssetLabel) error {
	if len(assetIDs) == 0 || len(lables) == 0 {
		return nil
	}

	table := model.GetAssetLabelTableName(gameCode)
	err := postgresql.Transaction(ctx, func(tx *pg.Tx) error {
		// 先删除之前的标签
		deleteQuery := tx.Model(&model.AssetLabel{}).Table(table)
		deleteQuery.Where("asset_id IN (?)", pg.In(assetIDs))
		_, err := deleteQuery.Delete()
		if err != nil {
			return err
		}

		// 插入新的标签
		insertQuery := tx.Model(&lables).Table(table)
		_, err = insertQuery.Insert()
		return err
	})

	return err
}

// GetBatchAssetsLables 拉取批量素材的所有标签
func GetBatchAssetsLables(ctx context.Context, gameCode string, assetIDs []string) ([]*model.AssetLabel, error) {
	if len(assetIDs) == 0 {
		return nil, nil
	}
	var rlt []*model.AssetLabel
	table := model.GetAssetLabelTableName(gameCode)
	// 分批
	chunks := funk.ChunkStrings(assetIDs, 100)
	for _, chunk := range chunks {
		var found []*model.AssetLabel
		query := postgresql.GetDBWithContext(ctx).Model(&found).Table(table)
		query.WhereIn("asset_id IN (?)", chunk)
		err := query.Select()
		if err != nil {
			return nil, err
		}

		rlt = append(rlt, found...)
	}

	return rlt, nil
}
