package service

import (
	"fmt"
	"strings"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/spf13/cast"
)

// UploadToChannel 素材上传至媒体
func UploadToChannel(ctx *gin.Context, req *pb.UploadToChannelReq, rsp *pb.UploadToChannelRsp) error {
	if req.GetAssetMediaPath() == "" || len(req.GetAssets()) <= 0 {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "param error")
	}
	err := syncChannelDataToDBV2(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "UploadToChannel fail to sync media asset info to db, err: %v", err)
		return errs.NewFormat(int(pbAix.DemoServerErrCode_DEMO_SERVER_QUERY_DB_ERROR),
			"UploadToChannel err:%v", err)
	}

	return nil
}

// arthub的数据上传到媒体后，同步到media目录，
func syncChannelDataToDB(ctx *gin.Context, req *pb.UploadToChannelReq) error {
	gamecode := ctx.Request.Header.Get(GAME_CODE_KEY)
	assetlist := req.GetAssets()
	assetIds := []string{}
	assetReqMap := map[string]*pb.UploadAsset{}
	log.DebugContextf(ctx, "syncChannelDataToDB assetIds length: %d", len(assetlist))
	for idx := range assetlist {
		log.DebugContextf(ctx, "[%d] syncChannelDataToDB asset: %v", idx, assetlist[idx])
		if assetlist[idx] != nil {
			assetIds = append(assetIds, assetlist[idx].AssetId)
			assetReqMap[assetlist[idx].AssetId] = assetlist[idx]
		} else {
			log.DebugContextf(ctx, "[%d] syncChannelDataToDB asset is nil", idx)
		}
	}
	if len(assetIds) == 0 {
		log.Logger.Debug("syncDbInUploadToChannel asset id list is 0")
		return nil
	}

	// 获取根节点信息
	depot, err := data.GetDepot(gamecode)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get depot information, gamecode: %v, err: %v", gamecode, err)
		return err
	}

	tx, err := postgresql.GetDBWithContext(ctx).Begin()
	if err != nil {
		return err
	}

	// 过滤删除状态的素材
	assetList := []model.CreativeOverview{}
	err = tx.Model(&assetList).
		Table(fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", depot.GameCode)).
		WhereIn("asset_id IN (?)", assetIds).
		Where("asset_status = ?", arthub.ARTHUB_ASSET_STATUS_NORMAL).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "fail to get asset info, asset ids: %v, err: %v", assetIds, err)
		return err
	}
	ids := []string{}
	for _, asset := range assetList {
		ids = append(ids, asset.AssetID)
	}

	// 过滤media_directory_id为空的素材信息
	// 该部分数据需要更新edia_directory_id、media_directory_name信息
	assetNotUploadedList := []model.CreativeOverview{}
	tablename := fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", depot.GameCode)

	err = tx.Model(&assetNotUploadedList).
		Table(tablename).
		WhereIn("asset_id IN (?)", ids).
		Where(" (media_directory_id = ? or media_directory_id is NULL ) AND asset_status = ? ",
			"", arthub.ARTHUB_ASSET_STATUS_NORMAL).
		Select()
	if err != nil {
		tx.Rollback()
		log.ErrorContextf(ctx, "fail to get assset details from table %q, asset_ids: %v,"+
			" err: %v", tablename, assetIds, err)
		return err
	}

	// assetOverviewMediaDirectoryIdZeroList 保存还从来没有被上传过，即没有被维护到新media目录树中的素材
	// 找到之前上传过的素材进行重新上传
	assetNotUploadedListSet := make(map[string]struct{})
	for _, asset := range assetNotUploadedList {
		assetNotUploadedListSet[asset.AssetID] = struct{}{}
	}
	reUploadAssetIDList := []string{}
	for _, assetID := range ids {
		if _, ok := assetNotUploadedListSet[assetID]; ok {
			continue
		}
		reUploadAssetIDList = append(reUploadAssetIDList, assetID)
	}
	// 重新上传之前上传过的
	db := postgresql.GetDBWithContext(ctx)
	for _, assetID := range reUploadAssetIDList {
		assetReq := assetReqMap[assetID]
		if assetReq == nil {
			log.DebugContextf(ctx, "asset id %q request is nil", assetID)
			continue
		}
		uploadToChannel := []string{}
		for _, c := range assetReq.GetToChannel() {
			uploadToChannel = append(uploadToChannel, cast.ToString(c))
		}
		sql := fmt.Sprintf(`UPDATE %s.tb_creative_overview_%s SET upload_state = 0,
							upload_enable = 1, upload_to_channel = ?, country = ?, upline_date = ?, status = 1
							WHERE asset_id = ?`, "arthub_sync", depot.GameCode)
		_, err = db.Exec(sql, strings.Join(uploadToChannel, ","),
			strings.Join(assetReq.GetCountry(), ","), time.Now().Unix(),
			assetID)
		if err != nil {
			log.ErrorContextf(ctx, "fail to reUpload asset, asset_id: %d, err: %v", assetID, err)
		}
	}

	log.DebugContextf(ctx, "assetOverviewMediaDirectoryIdZeroList: %v, reUploadAssetIDList: %v",
		assetNotUploadedList, reUploadAssetIDList)
	if len(assetNotUploadedList) == 0 {
		log.DebugContextf(ctx, "assetOverviewMediaDirectoryIdZeroList length: %d", 0)
		tx.Rollback()
		return nil
	}

	// 获取指定gamecode根目录信息
	arthubRootDirectory := model.CreativeDirectory{}
	tablename = fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", depot.GameCode)
	sql := fmt.Sprintf("SELECT * FROM %s WHERE id=?", tablename)
	_, err = tx.QueryOne(&arthubRootDirectory, sql, depot.DepotId)
	if err != nil {
		tx.Rollback()
		log.ErrorContextf(ctx, "fail to get root directory information from table %q, asset_ids: %v, rootId: %v"+
			" err: %v", tablename, depot.DepotId, err)
		return err
	}

	// 校验media根目录是否存在
	tablename = fmt.Sprintf("%s.tb_creative_media_directory_%s", "arthub_sync", depot.GameCode)
	mediaRootExisted, err := tx.Model((*model.CreativeMediaDirectory)(nil)).Table(tablename).Where("id = ?", arthubRootDirectory.ID).Exists()
	if err != nil {
		tx.Rollback()
		log.ErrorContextf(ctx, "fail to get media root directory information from table %q, asset_ids: %v, rootId: %v"+
			" err: %v", tablename, depot.DepotId, err)
		return err
	}
	if !mediaRootExisted {
		now := time.Now().Format("2006-01-02 15:04:05")
		mediaDirectoryObject := model.CreativeMediaDirectory{
			ID:                   arthubRootDirectory.ID,
			Name:                 arthubRootDirectory.Name,
			ParentID:             "", //arthubRootDirectory.ParentId,
			ParentName:           "", //arthubRootDirectory.ParentName,
			CreateDate:           now,
			UpdateDate:           now,
			DirectChildCount:     0,
			TotalLeafCount:       0,
			DirectDirectoryCount: 0,
			FullPathName:         arthubRootDirectory.FullPathName,
			FullPathID:           arthubRootDirectory.FullPathID,
			Type:                 arthubRootDirectory.Type,
		}
		_, err = tx.Model(&mediaDirectoryObject).Table(tablename).Insert()
		if err != nil {
			tx.Rollback()
			log.DebugContextf(ctx, "fail to insert media root directory info, root: %+v, err: %v", mediaDirectoryObject, err)
			return err
		}
	}

	// media overview 素材数量更新
	// 新增media目录信息
	mediaDirectoryMapToUpdate := make(map[string]mediaDirectoryUpdate) // key 为media目录id
	for _, item := range assetNotUploadedList {
		// 更新root目录素材数量
		rootDirectory, rootDirectoryExisted := mediaDirectoryMapToUpdate[depot.DepotId]
		rootDirectoryObject := mediaDirectoryUpdate{
			DirectDirectoryCount: rootDirectory.DirectDirectoryCount,
			DirectChildCount:     rootDirectory.DirectChildCount,
		}
		if rootDirectoryExisted {
			rootDirectoryObject.TotalLeafCount = rootDirectory.TotalLeafCount + 1
		} else {
			rootDirectoryObject.TotalLeafCount = 1
		}
		mediaDirectoryMapToUpdate[depot.DepotId] = rootDirectoryObject

		assetReq := assetReqMap[item.AssetID]
		if assetReq == nil {
			log.DebugContextf(ctx, "asset id '%d' request is nil", item.AssetID)
			continue
		}
		if item.FormatType != 1 && item.FormatType != 2 {
			tx.Rollback()
			log.DebugContextf(ctx, "item format type invalid, item: %+v", item)
			err = fmt.Errorf("asset format error")
			return err
		}
		mediaDirectorySlice := []string{}
		if assetReq.AssetMediaPath != "" {
			mediaDirectoryStrSlice := strings.Split(strings.TrimSuffix(assetReq.AssetMediaPath, "/"), "/")
			for idx := range mediaDirectoryStrSlice {
				if mediaDirectoryStrSlice[idx] != "" {
					mediaDirectorySlice = append(mediaDirectorySlice, mediaDirectoryStrSlice[idx])
				}
			}
		}

		parentId := arthubRootDirectory.ID
		parentName := arthubRootDirectory.Name
		fullPathId := fmt.Sprintf("%s", arthubRootDirectory.ID)
		fullPathName := arthubRootDirectory.Name
		mediaDirectoryType := arthub.DIRECTORY_TYPR_PROJECT //一级目录
		log.DebugContextf(ctx, " mediaDirectorySlice: %v, lenght: %d", mediaDirectorySlice, len(mediaDirectorySlice))
		for idx := range mediaDirectorySlice {
			if idx > 0 {
				mediaDirectoryType = arthub.DIRECTORY_TYPR_DIRECTORY
			}
			log.DebugContextf(ctx, "校验目录 目录名称： %v", mediaDirectorySlice[idx])
			// 判断media目录是否存在，不存在则创建
			tablename = fmt.Sprintf("%s.tb_creative_media_directory_%s", "arthub_sync", depot.GameCode)
			var existed bool
			existed, err = tx.Model((*model.CreativeMediaDirectory)(nil)).
				Table(tablename).
				Where("name = ? and parent_name = ?", mediaDirectorySlice[idx], parentName).
				Exists()
			if err != nil {
				log.ErrorContextf(ctx, "fail to check media directory in table %q, name: %s, "+
					"err: %v", tablename, mediaDirectorySlice[idx], err)
				tx.Rollback()
				return err
			}
			if existed {
				mediaDirectory := model.CreativeMediaDirectory{}
				err = tx.Model(&mediaDirectory).
					Table(tablename).
					Where("name = ?", mediaDirectorySlice[idx]).Select()
				if err != nil {
					log.ErrorContextf(ctx, "fail to get media directory in table %q, name: %s, "+
						"err: %v", tablename, mediaDirectorySlice[idx], err)
					tx.Rollback()
					return err
				}
				parentId = mediaDirectory.ID
				parentName = mediaDirectory.Name
				fullPathId = fmt.Sprintf("%s,%s", fullPathId, mediaDirectory.ID)
				fullPathName = fmt.Sprintf("%s,%s", fullPathName, mediaDirectory.Name)
				mediaDirectoryObject, mediaDirectoryIdExisted := mediaDirectoryMapToUpdate[mediaDirectory.ID]
				mediaDirectoryNewObject := mediaDirectoryUpdate{
					DirectChildCount:     mediaDirectoryObject.DirectChildCount,
					DirectDirectoryCount: mediaDirectoryObject.DirectDirectoryCount,
				}
				if mediaDirectoryIdExisted {
					mediaDirectoryNewObject.TotalLeafCount = mediaDirectoryObject.TotalLeafCount + 1
				} else {
					mediaDirectoryNewObject.TotalLeafCount = 1
				}
				mediaDirectoryMapToUpdate[mediaDirectory.ID] = mediaDirectoryNewObject
			} else {
				// 创建media目录
				now := time.Now().Format("2006-01-02 15:04:05")
				mediaDirectoryObject := model.CreativeMediaDirectory{
					ID:                   "",
					Name:                 mediaDirectorySlice[idx],
					ParentID:             parentId,
					ParentName:           parentName,
					CreateDate:           now,
					UpdateDate:           now,
					DirectChildCount:     0,
					TotalLeafCount:       0,
					DirectDirectoryCount: 0,
					FullPathName:         fullPathName,
					FullPathID:           fullPathId,
					Type:                 mediaDirectoryType,
				}
				_, err = tx.Model(&mediaDirectoryObject).
					Table(tablename).
					Insert()
				if err != nil {
					log.ErrorContextf(ctx, "fail to get insert media directory in table %q, media directory: %v, "+
						"err: %v", tablename, mediaDirectoryObject, err)
					tx.Rollback()
					return err
				}
				// 新增的目录需要更新对应父目录的目录数量
				parentMediaDirectoryObject, parentMediaDirectoryExisted := mediaDirectoryMapToUpdate[parentId]
				parentMediaDirectoryNewObject := mediaDirectoryUpdate{
					TotalLeafCount: parentMediaDirectoryObject.TotalLeafCount,
				}
				if parentMediaDirectoryExisted {
					parentMediaDirectoryNewObject.DirectChildCount = parentMediaDirectoryObject.DirectChildCount + 1
					parentMediaDirectoryNewObject.DirectDirectoryCount = parentMediaDirectoryObject.DirectDirectoryCount + 1
				} else {
					parentMediaDirectoryNewObject.DirectChildCount = 1
					parentMediaDirectoryNewObject.DirectDirectoryCount = 1
				}
				mediaDirectoryMapToUpdate[parentId] = parentMediaDirectoryNewObject

				// 更新新建目录的素材数量
				// 该目录为新建的目录，故无需更新目录数量
				mediaDirectoryNewObject := mediaDirectoryUpdate{
					TotalLeafCount: 1,
				}
				mediaDirectoryMapToUpdate[mediaDirectoryObject.ID] = mediaDirectoryNewObject

				parentId = mediaDirectoryObject.ID
				parentName = mediaDirectoryObject.Name
				fullPathId = fmt.Sprintf("%s,%s", fullPathId, mediaDirectoryObject.ID)
				fullPathName = fmt.Sprintf("%s,%s", fullPathName, mediaDirectoryObject.Name)
			}

		}
		uploadToChannel := []string{}
		for _, c := range assetReq.GetToChannel() {
			uploadToChannel = append(uploadToChannel, cast.ToString(c))
		}
		// 更新overview media目录id和名称 以及 上传记录
		sql = fmt.Sprintf(`UPDATE %s.tb_creative_overview_%s SET media_directory_id = ?,
			media_directory_name = ? , media_full_path_name = ? , media_full_path_id = ?,
			upload_enable = 1 , upload_to_channel = ?, country = ?, upline_date = ?, status = 1
			WHERE asset_id = ?`, "arthub_sync", depot.GameCode)
		_, err = tx.Exec(sql, parentId, parentName, fullPathId, fullPathName, strings.Join(uploadToChannel, ","),
			strings.Join(assetReq.GetCountry(), ","), time.Now().Unix(),
			item.AssetID)
		if err != nil {
			log.ErrorContextf(ctx, "fail to update media directory information by asset_id,"+
				" asset_id: %d, parentId: %v, parentName: %v, fullPathId: %v, fullPathName: %v, "+
				"err: %v", item.AssetID, parentId, parentName, fullPathId, fullPathName, err)
			tx.Rollback()
			return err
		}
	}

	// 更新media 目录数量和arthub 目录数量
	tablename = fmt.Sprintf("%s.tb_creative_media_directory_%s", "arthub_sync", depot.GameCode)
	for mediaDirectoryId, mediaDirectoryUpdateInfo := range mediaDirectoryMapToUpdate {
		// 数据库里部分字段默认值可能是null, 故不可以直接加减运算
		mediaDirectoryDb := model.CreativeMediaDirectory{}
		sql = fmt.Sprintf("SELECT * FROM %s WHERE id = ?", tablename)
		_, err = tx.QueryOne(&mediaDirectoryDb, sql, mediaDirectoryId)
		if err != nil {
			tx.Rollback()
			log.ErrorContextf(ctx, "fail to get asset info, sql: %v, err: %v", sql, err)
			return err
		}
		directChildCount := uint32(mediaDirectoryDb.DirectChildCount) + mediaDirectoryUpdateInfo.DirectChildCount
		directDirectoryCount := uint32(mediaDirectoryDb.DirectDirectoryCount) + mediaDirectoryUpdateInfo.DirectDirectoryCount
		totalLeafCount := uint32(mediaDirectoryDb.TotalLeafCount) + mediaDirectoryUpdateInfo.TotalLeafCount
		sql = fmt.Sprintf(`UPDATE %s.tb_creative_media_directory_%s
				SET direct_child_count = ?,
				direct_directory_count = ?,
				total_leaf_count = ? WHERE id = ?`,
			"arthub_sync", depot.GameCode)
		_, err = tx.Exec(sql, directChildCount, directDirectoryCount, totalLeafCount, mediaDirectoryId)
		log.DebugContextf(ctx, "update media directory sql: %v", sql)
		if err != nil {
			log.ErrorContextf(ctx, "fail to update media directory information by id,"+
				" id: %d, mediaDirectoryUpdate: %+v, err: %v\n", mediaDirectoryId, mediaDirectoryUpdateInfo, err)
			tx.Rollback()
			return err
		}
		log.DebugContextf(ctx, "update directory '%s' success, update value: %+v", mediaDirectoryId, mediaDirectoryUpdateInfo)
	}
	err = tx.Commit()
	if err != nil {
		log.ErrorContextf(ctx, "fail to commit info, err: %v", err)
	}
	return err
}

// syncChannelDataToDBV2 arthub的数据上传到媒体，同步到media目录，支持重复上传
func syncChannelDataToDBV2(ctx *gin.Context, req *pb.UploadToChannelReq) error {
	// 保存相关入参
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	assetIds := []string{}
	assetReqMap := map[string]*pb.UploadAsset{}
	for _, asset := range req.GetAssets() {
		log.DebugContextf(ctx, "syncChannelDataToDBV2 asset: %v", asset)
		// 判断参数
		if asset.GetAssetId() == "" ||
			len(asset.GetToChannel()) == 0 {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM),
				"syncChannelDataToDBV2 param err")
		}
		assetIds = append(assetIds, asset.GetAssetId())
		assetReqMap[asset.GetAssetId()] = asset
	}
	if len(assetIds) == 0 {
		log.DebugContextf(ctx, "syncChannelDataToDBV2 param emtpy")
		return nil
	}

	now := utils.GetNowStr()

	// ---  创建根目录 -----
	// 拉取depot信息
	depot, err := data.GetDepotWithContext(ctx, gameCode)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"syncChannelDataToDBV2 data.GetDepotWithContext err:%v", err)
	}
	// 拉取depot 根目录信息
	depotDirectory, err := data.GetCreativeDirectoryByID(ctx, gameCode, depot.DepotId)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"syncChannelDataToDBV2 data.GetCreativeDirectoryByID err:%v", err)
	}
	// 构建上传根目录
	rootCreativeMediaDirectory := &model.CreativeMediaDirectory{
		ID:                   depotDirectory.ID,
		Name:                 depotDirectory.Name,
		ParentID:             "", //arthubRootDirectory.ParentId,
		ParentName:           "", //arthubRootDirectory.ParentName,
		CreateDate:           now,
		UpdateDate:           now,
		DirectChildCount:     0,
		TotalLeafCount:       0,
		DirectDirectoryCount: 0,
		FullPathName:         depotDirectory.FullPathName,
		FullPathID:           depotDirectory.FullPathID,
		Type:                 depotDirectory.Type,
	}
	err = data.UpsertRootCreativeMediaDirectory(ctx, gameCode, rootCreativeMediaDirectory)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"syncChannelDataToDBV2 data.UpsertRootCreativeMediaDirectory err:%v", err)
	}
	// ---  创建根目录 结束 -----

	// ---- 构建上传子目录 -----
	parentID := rootCreativeMediaDirectory.ID
	parentName := rootCreativeMediaDirectory.Name
	fullPathID := rootCreativeMediaDirectory.ID
	fullPathName := rootCreativeMediaDirectory.Name
	mediaDirectoryType := arthub.DIRECTORY_TYPR_PROJECT //一级目录

	// 需要支持多级上传子目录
	directories := strings.Split(strings.TrimSuffix(req.GetAssetMediaPath(), "/"), "/")
	for _, directoryName := range directories {
		if directoryName == "" {
			continue
		}
		mediaDirectory := &model.CreativeMediaDirectory{
			Name:                 directoryName,
			ParentID:             parentID,
			ParentName:           parentName,
			CreateDate:           now,
			UpdateDate:           now,
			DirectChildCount:     0,
			TotalLeafCount:       0,
			DirectDirectoryCount: 0,
			FullPathName:         fullPathName,
			FullPathID:           fullPathID,
			Type:                 mediaDirectoryType,
		}

		log.DebugContextf(ctx, "begin upsert mediaDirectory:%+v", mediaDirectory)
		err := data.UpsertCreativeMediaDirectory(ctx, gameCode, mediaDirectory)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"syncChannelDataToDBV2 data.UpsertCreativeMediaDirectory err:%v", err)
		}
		log.DebugContextf(ctx, "after upsert mediaDirectory:%+v", mediaDirectory)

		fullPathID = fmt.Sprintf("%s,%s", fullPathID, mediaDirectory.ID)
		fullPathName = fmt.Sprintf("%s,%s", fullPathName, mediaDirectory.Name)
		parentID = mediaDirectory.ID
		parentName = mediaDirectory.Name
		mediaDirectoryType = arthub.DIRECTORY_TYPR_DIRECTORY
	}
	// ---- 构建上传子目录 结束 -----

	// 查素材overview
	assetOverviewList, err := data.QueryCreativeOverviewByIDList(ctx, gameCode, assetIds)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"syncChannelDataToDBV2 data.QueryCreativeOverviewByIDList err:%v", err)
	}

	for _, assetOverview := range assetOverviewList {
		log.DebugContextf(ctx, "syncChannelDataToDBV2 begin deal assetOverview:%+v", assetOverview)
		// 素材状态不正常的不处理
		if assetOverview.AssetStatus != arthub.ARTHUB_ASSET_STATUS_NORMAL {
			log.DebugContextf(ctx, "syncChannelDataToDBV2 AssetStatus not normal:%+v", assetOverview)
			continue
		}
		// 找asset入参
		assetReq, ok := assetReqMap[assetOverview.AssetID]
		if !ok {
			log.DebugContextf(ctx, "syncChannelDataToDBV2 not found assetReqMap, assetOverview:%+v", assetOverview)
			continue
		}

		// 事务中处理
		err := postgresql.Transaction(ctx, func(tx *pg.Tx) error {
			// 写对上传目录和素材映射关系
			err := data.UpsertCreativeMediaMaterail(tx, gameCode, parentID, assetReq.GetAssetId())
			if err != nil {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
					"syncChannelDataToDBV2 data.UpsertCreativeMediaMaterail err:%v", err)
			}

			// 写异步上传信息， 更新overview上传记录
			assetOverview.UploadEnable = 1 // 上传标志置1
			assetOverview.UploadState = 0  // 上传标志置0
			assetOverview.UplineDate = now // 上传时间
			assetOverview.Country = strings.Join(assetReq.GetCountry(), ",")
			assetOverview.Status = 1 // 正在上传标志
			// 上传到哪些渠道
			uploadToChannel := []string{}
			for _, c := range assetReq.GetToChannel() {
				uploadToChannel = append(uploadToChannel, cast.ToString(c))
			}
			assetOverview.UploadToChannel = strings.Join(uploadToChannel, ",")

			err = data.UpdateCreativeOverviewUploadMediaInfo(tx, gameCode, assetOverview)
			if err != nil {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
					"syncChannelDataToDBV2 data.UpdateCreativeOverviewUploadMediaInfo err:%v", err)
			}
			return nil
		})

		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"syncChannelDataToDBV2 asset_id:%v postgresql.Transaction err:%v", assetReq, err)
		}
	}

	// ---------  更新目录数量，最终一致

	// 统计目录下关联的素材数量
	directoryMaterialCountMap, err := data.CountCreativeMediaMaterailGroupByDirectory(ctx, gameCode)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"syncChannelDataToDBV2 data.CountCreativeMediaMaterailGroupByDirectory err:%v", err)
	}
	// 查所有的目录
	allDirectory, err := data.GetAllCreativeMediaDirectory(ctx, gameCode)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"syncChannelDataToDBV2 data.GetAllCreativeMediaDirectory err:%v", err)
	}
	// 初始化目录数量统计数据
	allDirectoryMap := make(map[string]*model.CreativeMediaDirectory)
	for _, directory := range allDirectory {
		directory.DirectChildCount = 0
		directory.DirectDirectoryCount = 0
		directory.TotalLeafCount = 0

		allDirectoryMap[directory.ID] = directory
	}
	// 计算相关数量
	for _, item := range allDirectory {
		directoryInfo := allDirectoryMap[item.ID]

		// 素材数量
		count := int64(0)
		if m, ok := directoryMaterialCountMap[directoryInfo.ID]; ok {
			count = cast.ToInt64(m)
		}
		// 当前目录下包含素材数量（递归），素材数量+count
		directoryInfo.TotalLeafCount += count
		// 当前目录包含子目录和素材数量（不递归）素材数量+count
		directoryInfo.DirectChildCount += count

		// 处理上一级父节点
		if parent, ok := allDirectoryMap[directoryInfo.ParentID]; ok {
			// 父节点 当前目录下子目录数量（不递归）+1
			parent.DirectDirectoryCount++
			// 父节点 当前目录包含子目录和素材数量（不递归）目录数+1
			parent.DirectChildCount++
		}

		// 遍历所有的父节点
		for _, parentID := range strings.Split(directoryInfo.FullPathID, ",") {
			if parent, ok := allDirectoryMap[parentID]; ok {
				// 父节点 当前目录下包含素材数量（递归），素材数量+count
				parent.TotalLeafCount += count
			}
		}
	}

	// 更新目录统计数据
	for _, directory := range allDirectoryMap {
		log.DebugContextf(ctx, "syncChannelDataToDBV2 begin UpdateCreativeMediaDirectoryCount :%+v", directory)
		err := data.UpdateCreativeMediaDirectoryCount(ctx, gameCode, directory)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"syncChannelDataToDBV2 data.UpdateCreativeMediaDirectoryCount err:%v", err)
		}
	}
	return nil
}

type mediaDirectoryUpdate struct {
	DirectChildCount     uint32 `pg:"direct_child_count"`     // 当前目录包含子目录和素材数量（不递归）
	TotalLeafCount       uint32 `pg:"total_leaf_count"`       // 当前目录下包含素材数量（递归）
	DirectDirectoryCount uint32 `pg:"direct_directory_count"` // 当前目录下子目录数量（不递归）
}
