// Package service 服务接口实现代码
package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/constant"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

// MaterialList 拉取素材列表
func MaterialList(ctx *gin.Context, req *pb.MaterialListReq, rsp *pb.MaterialListRsp) error {
	var overviews []*model.CreativeOverview
	var err error
	var total uint32
	game_code := ctx.Request.Header.Get(GAME_CODE_KEY)

	depot, err := data.GetDepot(game_code)
	if err != nil {
		log.ErrorContextf(ctx, "error data.GetDepot, err: %v", err)
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "system error")
	}
	directory_id := req.GetDirectoryId()
	if len(directory_id) == 0 {
		directory_id = depot.DepotId
	}

	full_path_id, err := getFullPathIDForDir(ctx, game_code, directory_id)
	if err == pg.ErrNoRows {
		return errs.New(constant.ERR_NODIR, constant.ERR_NODIR_MSG)
	}
	if err != nil {
		log.ErrorContextf(ctx, "getFullPathIDForDir failed: %s", err)
		return errs.New(constant.ERR_SYS, constant.ERR_SYS_MSG)
	}
	var path_profix string
	if len(full_path_id) > 0 {
		path_profix = fmt.Sprintf("%s,%s", full_path_id, directory_id)
	} else {
		path_profix = directory_id
	}

	total, overviews, err = data.GetMaterialList(ctx, req.GetOffset(), req.GetCount(), req.GetFilterOnlineStatus(), req.GetOnlineStatus(), path_profix, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "error data.GetMaterialList, err: %v", err)
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "system error")
	}

	overviews = correctFullPath(ctx, game_code, depot.DepotId, overviews)
	rsp.Materials = fillMeterialMetas(ctx, directory_id, arthubOverViews2MaterialMetas(ctx, overviews), depot.Type, game_code, depot.ArthubCode)
	rsp.Total = total
	assetIDList := []string{}
	for _, asset := range rsp.GetMaterials() {
		assetIDList = append(assetIDList, cast.ToString(asset.GetAssetId()))
	}
	if req.GetWithDetail() > 0 {
		detailMap, err := data.BtGetMaterialDetail(ctx, game_code, assetIDList)
		if err != nil {
			log.ErrorContextf(ctx, "error data.BtGetMaterialDetail, err: %v", err)
		}
		log.DebugContextf(ctx, "detailMap: %v", detailMap)
		for _, asset := range rsp.GetMaterials() {
			asset.MaterialExt = detail2proto(ctx, detailMap[cast.ToString(asset.AssetId)])
		}
	}

	return nil
}

// correctFullPath 矫正素材全路径
func correctFullPath(ctx context.Context, game_code, depot_id string, overviews []*model.CreativeOverview) []*model.CreativeOverview {
	table_name := genCreativeDirectoryTableName(game_code)
	mdl := postgresql.GetDBWithContext(ctx).Model(&model.CreativeDirectory{}).Table(table_name)
	mdl.Where("id=?", depot_id)

	var record model.CreativeDirectory
	err := mdl.Select(&record)
	if err != nil && err != pg.ErrNoRows {
		log.ErrorContextf(ctx, "get depot directory failed: %s", err)
		return overviews
	}

	if err == pg.ErrNoRows {
		return overviews
	}

	if len(record.FullPathID) == 0 {
		return overviews
	}
	cut_id := fmt.Sprintf("%s,", record.FullPathID)
	cut_name := fmt.Sprintf("%s,", record.FullPathName)
	for _, overview := range overviews {
		overview.FullPathID = strings.TrimPrefix(overview.FullPathID, cut_id)
		overview.FullPathName = strings.TrimPrefix(overview.FullPathName, cut_name)
	}

	return overviews
}

// getPathPrefix 获取某个节点的路径前缀, 以递归获取子目录
func getPathPrefix(ctx context.Context, game_code, node_id string) (string, error) {
	full_path_id, err := getFullPathIDForDir(ctx, game_code, node_id)
	if err != nil {
		return "", fmt.Errorf("getFullPathIDForDir failed: %s", err)
	}
	var path_prefix string
	if len(full_path_id) > 0 {
		path_prefix = fmt.Sprintf("%s,%s", full_path_id, node_id)
	} else {
		path_prefix = node_id
	}

	return path_prefix, nil
}

// getFullPathIDForDir 获取全路径ID
func getFullPathIDForDir(ctx context.Context, game_code, node_id string) (string, error) {
	table_name := genCreativeDirectoryTableName(game_code)
	mdl := postgresql.GetDBWithContext(ctx).Model(&model.CreativeDirectory{}).Table(table_name)
	mdl.Where("id=?", node_id)

	var record model.CreativeDirectory
	err := mdl.Select(&record)
	if err != nil {
		return "", err
	}

	return record.FullPathID, nil
}

// genCreativeDirectoryTableName ...
func genCreativeDirectoryTableName(game_code string) string {
	return fmt.Sprintf("arthub_sync.tb_creative_directory_%s", game_code)
}

// arthub数据列表转aix数据列表
func arthubOverViews2MaterialMetas(ctx context.Context, overviews []*model.CreativeOverview) []*pb.MaterialMeta {
	materials := make([]*pb.MaterialMeta, 0, len(overviews))
	for _, overview := range overviews {
		materials = append(materials, arthubOverView2MaterialMeta(ctx, overview))
	}
	return materials
}

// getOnlineDays 获取素材真实在线时间
// 注意: online_date表示的是素材的第一次上线时间, 当online_status为1, 即素材正在投放时, 使用offline_date表示素材的最近上线时间
func getOnlineDays(ctx context.Context, online_status int, offline_date string, online_days uint32) uint32 {
	switch online_status {
	case 1: // 正在投放
		online_time, err := time.Parse("2006-01-02 15:04:05", offline_date)
		if err != nil {
			log.ErrorContextf(ctx, "parse time failed: %s", err)
			return online_days
		} else {
			hours := time.Since(online_time).Hours()
			if hours > 0 && hours < 87600000 { // 过滤异常值
				online_days += uint32(hours) / 24
			}
		}
	}

	return online_days
}

// arthub数据转aix数据
func arthubOverView2MaterialMeta(ctx context.Context, overview *model.CreativeOverview) *pb.MaterialMeta {
	material := &pb.MaterialMeta{}
	material.OnlineDays = getOnlineDays(ctx, overview.OnlineStatus, overview.OfflineDate, overview.OnlineDays)
	material.AssetId = overview.AssetID
	material.Name = overview.AssetName
	material.Status = overview.Status
	material.UplineDate = overview.UplineDate
	material.OfflineDate = overview.OfflineDate
	material.CreateDate = overview.CreateDate
	material.PreviewUrl = overview.PreviewURL
	material.AssetStatus = int32(overview.AssetStatus)
	material.OnlineStatus = int32(overview.OnlineStatus)
	material.OnlineDate = overview.OnlineDate
	material.OfflineDate = overview.OfflineDate
	material.FullPathId = overview.FullPathID
	material.FullPathName = overview.FullPathName
	material.AixUploader = overview.AixUploader
	material.GoogleOnlineState = overview.GoogleOnlineState
	material.GoogleOnlineTime = overview.GoogleOnlineTime
	material.GoogleOfflineTime = overview.GoogleOfflineTime
	material.FacebookOnlineState = overview.FacebookOnlineState
	material.FacebookOnlineTime = overview.FacebookOnlineTime
	material.FacebookOfflineTime = overview.FacebookOfflineTime

	material.Number = overview.Number
	material.ProjectName = overview.ProjectName
	material.AssetFormat = overview.AssetFormat
	material.AssetSize = overview.AssetSize
	material.Language = overview.Language
	material.AgentName = overview.AgentName
	material.SimpleAssetName = overview.SimpleAssetName
	material.PlayingMethod = overview.PlayingMethod
	material.AssetForm = overview.AssetForm
	material.AssetStage = overview.AssetStage
	material.DeliveryDate = overview.DeliveryDate
	material.Custom1 = overview.Custom1
	material.Custom2 = overview.Custom2
	material.Custom3 = overview.Custom3

	material.ImpressionDate = overview.ImpressionDate
	material.ImpressionStatus = getImpressionStatusByImpressionDate(overview.ImpressionDate)

	material.SyncTime = overview.SyncTime
	return material
}

// 查询数量
type QueryCount struct {
	Count uint32 `json:"count,omitempty"` // 统计数量
}

// 获取素材详情列表
func materialDetails(ctx *gin.Context, assetIDs []string, gameCode string) (map[string]*model.CreativeAssetDetails, error) {
	var details []*model.CreativeAssetDetails
	//var assetIDsStr, splitor string
	//for _, assetID := range assetIDs {
	//	assetIDsStr += splitor + cast.ToString(assetID)
	//	splitor = ","
	//}
	//assetIDsStr = fmt.Sprintf("(%s)", assetIDsStr)
	//_, err := postgresql.GetDBWithContext(ctx).Query(&details, fmt.Sprintf("SELECT * FROM %s.tb_creative_details_%s WHERE asset_id in ", "arthub_sync", gameCode)+assetIDsStr)
	err := postgresql.GetDBWithContext(ctx).Model(&details).
		Table(fmt.Sprintf("%s.tb_creative_details_%s", "arthub_sync", gameCode)).
		WhereIn("asset_id IN (?)", assetIDs).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "get material details failed, assetIDs: %v, err: %v", assetIDs, err.Error())
		return nil, err
	}

	assetID2Detail := make(map[string]*model.CreativeAssetDetails)
	for _, detail := range details {
		assetID2Detail[detail.AssetID] = detail
	}
	return assetID2Detail, nil
}

func HTTPDo(ctx *gin.Context, method string, url string, body []byte, token string) (string, error) {
	client := &http.Client{}
	reader := bytes.NewReader(body)
	req, err := http.NewRequest(method, url, reader)
	if err != nil {
		log.ErrorContextf(ctx, "[error] get http req error %v", err)
		return "", err
	}
	if token == "" {
		return "", fmt.Errorf("error token")
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Publictoken", token)
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorContextf(ctx, "[error] send http req error %v", err)
		return "", err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "[error] get http rsp error %v", err)
		return "", err
	}
	log.InfoContextf(ctx, "[info] http rsp: %s", res)
	return string(res), nil
}

type GetAssetInfo struct {
	IDs  []uint64 `json:"ids"`
	Meta []string `json:"meta"`
}

// AssetInfo ...
type AssetInfo struct {
	ID         uint64 `json:"id"`
	PreviewUrl string `json:"preview_url"`
	Status     string `json:"status"`
}

// Item ...
type Item struct {
	Items []AssetInfo `json:"items"`
}

// AssetRsp ...
type AssetRsp struct {
	Code   int32 `json:"code"`
	Result Item  `json:"result"`
	// Error  string `json:"error"`
}

// TODO(waynebfhu): 暂时废弃，待删除
// func getOneAssetPreview(ctx *gin.Context, gameCode string, arthubCode string, id string) (*AssetInfo, error) {
// 	assetId, _ := strconv.ParseUint(id, 10, 64)
// 	assetReq := GetAssetInfo{
// 		IDs: []uint64{assetId},
// 		Meta: []string{
// 			"id", "preview_url", "status",
// 		},
// 	}
// 	var assetRsp AssetRsp
// 	log.DebugContextf(ctx, "[info] http get asset detail req: %v", assetReq)
// 	url := fmt.Sprintf("https://service.arthub.qq.com/%v/data/openapi/v2/core/get-asset-detail-by-id", arthubCode)
// 	var reqStr []byte
// 	reqStr, err := json.Marshal(&assetReq)
// 	if err != nil {
// 		log.ErrorContextf(ctx, "[error] req json parse error %v", err)
// 		return nil, err
// 	}
// 	res, err := HTTPDo(ctx, "POST", url, reqStr, cache.DepotTbArthubDepotCache[gameCode].PublicToken)
// 	if err != nil {
// 		log.ErrorContextf(ctx, "[error] send http req error %v", err)
// 		return nil, err
// 	}
// 	log.DebugContextf(ctx, "getAssetPreviewUrl url:%s, reqStr:%s, res: %v", url, string(reqStr), res)
// 	err = json.Unmarshal([]byte(res), &assetRsp)
// 	if err != nil {
// 		log.ErrorContextf(ctx, "[error] rsp parse error %v", err)
// 		return nil, err
// 	}
// 	if assetRsp.Code != 0 {
// 		log.DebugContextf(ctx, "[error] assetRsp code error %d, erro:%s", assetRsp.Code, assetRsp.Error)
// 		return nil, errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR), assetRsp.Error)
// 	}

// 	return &assetRsp.Result.Items[0], nil
// }

// TODO(waynebfhu): 暂时废弃，待删除
// getAssetPreviewUrl 获取素材preview_url
// func getMediaAssetPreviewUrl(ctx *gin.Context, gameCode string, arthubCode string, ids []string) ([]AssetInfo, []string, error) {
// 	rlt := []AssetInfo{}

// 	for _, id := range ids {
// 		asset, err := getOneAssetPreview(ctx, gameCode, arthubCode, id)
// 		if err != nil {
// 			continue
// 		}
// 		rlt = append(rlt, *asset)
// 	}
// 	return rlt, []string{}, nil
// }

// getAssetPreviewUrl 获取素材preview_url
func getAssetPreviewUrl(ctx *gin.Context, gameCode string, arthubCode string, ids []string) ([]AssetInfo, []string, error) {
	var assetIDs = make([]uint64, 0)
	for _, id := range ids {
		assetID, _ := strconv.ParseUint(id, 10, 64)
		assetIDs = append(assetIDs, assetID)
	}
	assetReq := GetAssetInfo{
		IDs: assetIDs,
		Meta: []string{
			"id", "preview_url", "status",
		},
	}
	var assetRsp AssetRsp
	log.DebugContextf(ctx, "[info] http get asset detail req: %v", assetReq)
	url := fmt.Sprintf("https://service.arthub.qq.com/%v/data/openapi/v2/core/get-node-brief-by-id", arthubCode)
	reqStr, err := json.Marshal(&assetReq)
	if err != nil {
		log.ErrorContextf(ctx, "[error] req json parse error %v", err)
		return assetRsp.Result.Items, nil, err
	}
	res, err := HTTPDo(ctx, "POST", url, reqStr, cache.DepotTbArthubDepotCache[gameCode].PublicToken)
	if err != nil {
		log.ErrorContextf(ctx, "[error] send http req error %v", err)
		return assetRsp.Result.Items, nil, err
	}
	log.DebugContextf(ctx, "getAssetPreviewUrl url:%s, reqStr:%s, res: %v", url, string(reqStr), res)
	err = json.Unmarshal([]byte(res), &assetRsp)
	if err != nil {
		return assetRsp.Result.Items, nil, fmt.Errorf("json.Unmarshal error %v, res:%v", err, res)
	}
	var asset_ids []string
	if assetRsp.Code != 0 {
		log.DebugContextf(ctx, "[error] assetRsp code error %d", assetRsp.Code)

		// for _, e := range assetRsp.Error {
		// 	if e.ErrorCode == arthub.NODE_NOT_EXIST {
		// 		asset_ids = append(asset_ids, cast.ToString(e.ID))
		// 	}
		// }
		// go updateAssetStatus(ctx, gameCode, asset_ids)
	}
	return assetRsp.Result.Items, asset_ids, nil
}

// updateAssetStatus 更新arthub素材状态
func updateAssetStatus(ctx context.Context, game_code string, asset_ids []string) error {
	db := postgresql.GetDBWithContext(ctx)
	parameters := strings.Join(asset_ids, "','")
	sql := fmt.Sprintf("update arthub_sync.tb_creative_overview_%s set asset_status=3 where asset_id in ('%s')", game_code, parameters)
	_, err := db.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update deleted asset status failed: %s", err)
	}
	return nil
}

// 填充返回结果
func fillMeterialMetas(ctx *gin.Context, parentID string, metas []*pb.MaterialMeta, depotType int, gameCode, arthubCode string) []*pb.MaterialMeta {
	var assetIDs []string
	var assetPreviewUrlIDs []string
	for _, meta := range metas {
		assetIDs = append(assetIDs, meta.GetAssetId())
		if meta.AssetStatus == arthub.ARTHUB_ASSET_STATUS_NORMAL {
			assetPreviewUrlIDs = append(assetPreviewUrlIDs, meta.GetAssetId())
		}
	}

	if len(assetIDs) == 0 {
		return nil
	}
	assetID2Detail, err := materialDetails(ctx, assetIDs, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "materialDetails failed, err: %s", err.Error())
		return metas
	}

	var new_metas []*pb.MaterialMeta
	switch depotType {
	case utils.GAME_DEPOT_TYPE_ARTHUB:
		urlRsp, not_exist_ids, err := getAssetPreviewUrl(ctx, gameCode, arthubCode, assetPreviewUrlIDs)
		if err != nil {
			log.ErrorContextf(ctx, "getAssetPreviewUrl failed, game_code:%v, err: %s", gameCode, err.Error())
			return metas
		}

		for _, meta := range metas {
			detail := assetID2Detail[meta.GetAssetId()]
			if detail == nil {
				continue
			}
			meta.Formate = detail.Format
			meta.Duration = detail.Duration
			previewUrl := ""
			for _, asset := range urlRsp {
				assetID, _ := strconv.ParseUint(meta.AssetId, 10, 64)
				if asset.ID == assetID {
					previewUrl = asset.PreviewUrl
					break
				}
			}
			// 被删除的素材
			if funk.ContainsString(not_exist_ids, meta.AssetId) {
				meta.AssetStatus = 3
			}
			meta.PreviewUrl = previewUrl
			new_metas = append(new_metas, meta)
		}

	case utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER:
		//assetIDList := []string{}
		//for _, meta := range metas {
		//	assetIDList = append(assetIDList, meta.AssetId)
		//}
		//parentIDList := []string{}
		//if parentID != "" {
		//	parentIDList = []string{parentID}
		//} else {
		//	// 获取素材parentID
		//	parentIDList, err = data.GetParentIDByAssetIDS(gameCode, assetIDList)
		//	if err != nil {
		//		log.ErrorContextf(ctx, "data.GetParentIDByAssetIDS failed in google asset, "+
		//			"gameCode: %v, assetIDList: %v, err: %s", gameCode, assetIDList, err.Error())
		//		return metas
		//	}
		//}
		//srv, err := cache.GoogleServiceCache{}.Get(ctx, gameCode)
		//if err != nil {
		//	log.ErrorContextf(ctx, "cache.GoogleServiceCache fail to get google service, gameCode: %v, err: %v", gameCode, err)
		//	return metas
		//}
		//previewURLMap, err := srv.ListPreviewURLByParentID(parentIDList)
		//if err != nil {
		//	log.ErrorContextf(ctx, "ListPreviewURLByParentID failed in google asset, parentIDList: %v, err: %s", parentIDList, err.Error())
		//	return metas
		//}
		for _, meta := range metas {
			detail := assetID2Detail[meta.GetAssetId()]
			if detail == nil {
				continue
			}
			meta.Formate = detail.Format
			meta.Duration = detail.Duration
			new_metas = append(new_metas, meta)
			// google素材封面从db获取

			// meta.PreviewUrl = previewURLMap[meta.GetAssetId()]
		}
	case utils.GAME_DEPOT_TYPE_DROPBOX:
		for _, meta := range metas {
			detail := assetID2Detail[meta.GetAssetId()]
			if detail == nil {
				continue
			}
			meta.Formate = detail.Format
			meta.Duration = detail.Duration
			new_metas = append(new_metas, meta)
		}
	default:
		log.ErrorContextf(ctx, "depot type error '%v'", depotType)
		return metas
	}

	return new_metas
}
