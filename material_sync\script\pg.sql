CREATE TABLE IF NOT EXISTS arthub_sync.tb_asset_rule_labels
(
    id bigserial,
    game_code text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    rule text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    type integer NOT NULL DEFAULT 0,
    label_name text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    first_label text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    second_label text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    create_by text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    create_time text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    update_by text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    update_time text COLLATE pg_catalog."default" NOT NULL DEFAULT ''::text,
    CONSTRAINT channel_asset_label_all_aix_pkey PRIMARY KEY (id),
    CONSTRAINT channel_asset_label_all_aix_unique UNIQUE (game_code, rule, type, label_name, first_label, second_label)
);