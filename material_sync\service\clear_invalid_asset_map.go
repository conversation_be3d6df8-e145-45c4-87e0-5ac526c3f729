package service

import (
	"context"
	"fmt"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// clearInvalidAssetMap 清理不可用的素材映射
func clearInvalidAssetMap(game_code string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "clearInvalidAssetMap start, game code: %s", game_code)

	err := clearInvalidMediaContentMap(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "clearInvalidMediaContentMap failed: %s", err)
		return
	}

	err = clearInvalidAssetIdMap(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "clearInvalidAssetIdMap failed: %s", err)
	}

	log.InfoContextf(ctx, "clearInvalidAssetMap end, game code: %s", game_code)
}

// clearInvalidMediaContentMap 清理素材内容映射
func clearInvalidMediaContentMap(ctx context.Context, game_code string) error {
	asset_ids, err := getAssetIdsFromMediaContentMap(ctx, game_code)
	if err != nil {
		return fmt.Errorf("getAssetIdsFromMediaContentMap failed: %s", err)
	}

	invalid_asset_ids, err := getInvalidAssetIds(ctx, game_code, asset_ids)
	if err != nil {
		return fmt.Errorf("getInvalidAssetIds failed: %s", err)
	}

	log.DebugContextf(ctx, "get invalid asset ids number: %d, game code: %s", len(invalid_asset_ids), game_code)

	err = deleteByAssetIdsFromMediaContentMap(ctx, game_code, invalid_asset_ids)
	if err != nil {
		return fmt.Errorf("deleteByAssetIdsFromMediaContentMap failed: %s", err)
	}

	return nil
}

// clearInvalidAssetIdMap 清理素材内容映射
func clearInvalidAssetIdMap(ctx context.Context, game_code string) error {
	asset_ids, err := getAssetIdsFromAssetMap(ctx, game_code)
	if err != nil {
		return fmt.Errorf("getAssetIdsFromAssetMap failed: %s", err)
	}

	invalid_asset_ids, err := getInvalidAssetIds(ctx, game_code, asset_ids)
	if err != nil {
		return fmt.Errorf("getInvalidAssetIds failed: %s", err)
	}

	log.DebugContextf(ctx, "get invalid asset ids number: %d, game code: %s", len(invalid_asset_ids), game_code)

	err = deleteByAssetIdsFromAssetMap(ctx, game_code, invalid_asset_ids)
	if err != nil {
		return fmt.Errorf("deleteByAssetIdsFromAssetMap failed: %s", err)
	}

	return nil
}

// getAssetIdsFromMediaContentMap 获取内容映射中的asset ids
func getAssetIdsFromMediaContentMap(ctx context.Context, game_code string) ([]string, error) {
	var media_content_maps []pgmodel.CreativeRecommendMediaContentMap
	table_name := pgmodel.GetCreativeRecommendMediaContentMapTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&media_content_maps).Table(table_name)
	pg_query.Column("aix_asset_id")
	pg_query.Group("aix_asset_id")
	var asset_ids []string
	err := pg_query.Select(&asset_ids)
	if err != nil {
		return nil, fmt.Errorf("select asset ids from media content map failed: %s", err)
	}

	return asset_ids, err
}

// getAssetIdsFromAssetMap 获取素材映射中的asset_ids
func getAssetIdsFromAssetMap(ctx context.Context, game_code string) ([]string, error) {
	var asset_maps []pgmodel.CreativeRecommendAssetMap
	table_name := pgmodel.GetCreativeRecommendAssetMapTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&asset_maps).Table(table_name)
	pg_query.Column("aix_asset_id")
	pg_query.Group("aix_asset_id")
	var asset_ids []string
	err := pg_query.Select(&asset_ids)
	if err != nil {
		return nil, fmt.Errorf("select asset ids from asset map failed: %s", err)
	}

	return asset_ids, err
}

// getInvalidAssetIds 获取不可用的素材ID
func getInvalidAssetIds(ctx context.Context, game_code string, asset_ids []string) ([]string, error) {
	if len(asset_ids) == 0 {
		return nil, nil
	}

	var overviews []pgmodel.CreativeOverview
	table_name := pgmodel.GetCreativeOverviewTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&overviews).Table(table_name)
	pg_query.Column("asset_id")
	pg_query.Where("asset_status != 1")
	pg_query.WhereIn("asset_id in (?)", asset_ids)
	var invalid_asset_ids []string
	err := pg_query.Select(&invalid_asset_ids)
	if err != nil {
		return nil, fmt.Errorf("select invalid asset ids failed: %s", err)
	}

	return invalid_asset_ids, nil
}

// deleteByAssetIdsFromMediaContentMap 通过asset ids列表删除部分映射
func deleteByAssetIdsFromMediaContentMap(ctx context.Context, game_code string, asset_ids []string) error {
	if len(asset_ids) == 0 {
		return nil
	}

	table_name := pgmodel.GetCreativeRecommendMediaContentMapTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendMediaContentMap{}).Table(table_name)
	pg_query.WhereIn("aix_asset_id in (?)", asset_ids)
	_, err := pg_query.Delete()
	if err != nil {
		return fmt.Errorf("delete media content map failed: %s", err)
	}

	return nil
}

// deleteByAssetIdsFromAssetMap 通过asset ids列表删除部分映射
func deleteByAssetIdsFromAssetMap(ctx context.Context, game_code string, asset_ids []string) error {
	if len(asset_ids) == 0 {
		return nil
	}

	table_name := pgmodel.GetCreativeRecommendAssetMapTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendAssetMap{}).Table(table_name)
	pg_query.WhereIn("aix_asset_id in (?)", asset_ids)
	_, err := pg_query.Delete()
	if err != nil {
		return fmt.Errorf("delete asset map failed: %s", err)
	}

	return nil
}
