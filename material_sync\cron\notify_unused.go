package cron

import (
	"context"
	"fmt"
	"strings"

	pgModel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	msg "e.coding.intlgame.com/ptc/aix-backend/material_sync/pkg/rpc/qiwei-msg"
	"github.com/go-pg/pg/v10"
	"github.com/pkg/errors"
)

// notifyUnusedMaterial 通知未使用的素材
func notifyUnusedMaterial(ctx context.Context) {
	gameCodeList := conf.GetBizConf().NotifyGameCodeList
	if gameCodeList == nil || len(gameCodeList) == 0 {
		var err error
		gameCodeList, err = getAllGameCodeList()
		if err != nil {
			log.ErrorContextf(ctx, "error getAllGameCodeList error: %s", err)
			return
		}
	}
	for _, gameCode := range gameCodeList {
		need2NotifyList, err := getNeed2Notify(ctx, gameCode)
		if err != nil {
			log.ErrorContextf(ctx, "error getNeed2Notify data error: %s", err)
			continue
		}
		notifyQiwei(ctx, need2NotifyList, gameCode)
	}
}

// notifyQiwei 发送企业微信通知
func notifyQiwei(ctx context.Context, need2NotifyList []Need2Notify, gameCode string) {
	idList := make([]int, 0)
	user2days2notifyList := handleNotifyData(need2NotifyList)
	for user, days2notifyList := range user2days2notifyList {
		message := data2Message(days2notifyList, gameCode)
		err := msg.SendQiWeiMsg(ctx, user, message)
		if err != nil {
			log.ErrorContextf(ctx, "error send qiwei msg error: %s", err)
			continue
		}
		need2decIdList := getIdListFromDays2notifyList(days2notifyList)
		idList = append(idList, need2decIdList...)
	}
	err := decreaseNotifyTryCnt(ctx, gameCode, idList)
	if err != nil {
		log.ErrorContextf(ctx, "error decrease database NotifyTryCnt error: %s", err)
	}
}

// handleNotifyData 处理数据，根据接收人，天数设置分组
func handleNotifyData(need2NotifyList []Need2Notify) map[string]map[int][]Need2Notify {
	user2days2notifyList := make(map[string]map[int][]Need2Notify)
	for _, need2Notify := range need2NotifyList {
		if _, ok := user2days2notifyList[need2Notify.Creator]; !ok {
			user2days2notifyList[need2Notify.Creator] = make(map[int][]Need2Notify)
		}
		if _, ok := user2days2notifyList[need2Notify.Creator][need2Notify.NotifyDays]; !ok {
			user2days2notifyList[need2Notify.Creator][need2Notify.NotifyDays] = make([]Need2Notify, 0)
		}
		user2days2notifyList[need2Notify.Creator][need2Notify.NotifyDays] = append(user2days2notifyList[need2Notify.Creator][need2Notify.NotifyDays], need2Notify)
	}
	return user2days2notifyList
}

func getIdListFromDays2notifyList(days2notifyList map[int][]Need2Notify) []int {
	idList := make([]int, 0)
	for _, notifyList := range days2notifyList {
		for _, need2Notify := range notifyList {
			idList = append(idList, need2Notify.Id)
		}
	}
	return idList
}

func data2Message(days2notifyList map[int][]Need2Notify, gameCode string) string {
	if days2notifyList == nil {
		return ""
	}
	message := ""
	maxDay := 0
	for days := range days2notifyList {
		if maxDay < days {
			maxDay = days
		}
	}
	// 桶排序
	notifyListList := make([][]Need2Notify, maxDay+1)
	for days, notifyList := range days2notifyList {
		notifyListList[days] = notifyList
	}

	//for days, notifyList := range notifyListList {
	for i := len(notifyListList) - 1; i >= 0; i-- {
		days := i
		notifyList := notifyListList[days]

		if notifyList == nil || len(notifyList) == 0 {
			continue
		}
		message += fmt.Sprintf("您上传的以下素材已超过<font color=\"warning\">**%d**</font>天未上线：\n> ", days)
		nameList := make([]string, 0, len(notifyList))
		assetIdSet := make(map[string]bool)
		for _, notify := range notifyList {
			if _, b := assetIdSet[notify.AssetId]; b {
				continue
			}
			nameList = append(nameList, notify.AssetName)
			assetIdSet[notify.AssetId] = true
		}
		message += strings.Join(nameList, "\n")
		message += "\n\n"
	}
	if len(message) > 2048 { // 超过企业微信限制的长度，则跳转系统素材库页面
		message = decMessageLen(message, gameCode)
	}
	return message
}

// decMessageLen 减少消息长度
func decMessageLen(message, gameCode string) string {
	additionalMessage := "……\n\n" + fmt.Sprintf("[**跳转素材库页面**](%s)", conf.GetBizConf().AixUrl+gameCode)
	additionalLen := len(additionalMessage)
	for len(message)+additionalLen > 2000 { // 超过企业微信限制的长度(2048)，则跳转系统素材库页面
		index := strings.LastIndex(message, "\n")
		message = message[:index]
	}
	return message + additionalMessage
}

/*-----------------------------------db操作---------------------------------------------*/

type Need2Notify struct {
	Id           int    `pg:"id"`             // tb_creative_media_upload_task表 任务id（主键）
	AssetId      string `pg:"asset_id"`       // 素材id
	Creator      string `pg:"creator"`        // 创建人，提醒企微id
	NotifyDays   int    `pg:"notify_days"`    // 提醒天数，为0不提醒
	AssetName    string `pg:"asset_name"`     // 素材名字
	NotifyTryCnt int    `pg:"notify_try_cnt"` // 剩余提醒次数
}

// getNeed2Notify 获取需要通知的素材
func getNeed2Notify(ctx context.Context, gameCode string) ([]Need2Notify, error) {
	queryColumns := []string{
		fmt.Sprintf("tb_creative_media_upload_task_%s.id", gameCode),
		fmt.Sprintf("tb_creative_media_upload_task_%s.asset_id", gameCode),
		fmt.Sprintf("tb_creative_media_upload_task_%s.creator", gameCode),
		fmt.Sprintf("tb_creative_media_upload_task_%s.notify_days", gameCode),
		fmt.Sprintf("tb_creative_overview_%s.asset_name", gameCode),
		fmt.Sprintf("tb_creative_media_upload_task_%s.notify_try_cnt", gameCode),
	}
	whereList := []string{
		// 剩余提醒次数大于0
		fmt.Sprintf("tb_creative_media_upload_task_%s.notify_try_cnt>0", gameCode),
		// 设置了提醒天数
		fmt.Sprintf("tb_creative_media_upload_task_%s.notify_days>0", gameCode),
		// 上传成功的素材
		fmt.Sprintf("tb_creative_media_upload_task_%s.status=2", gameCode), // 1-正在上传，2-上传成功，3-上传失败，4-上传取消
		// 没有使用过的素材
		fmt.Sprintf("(tb_creative_overview_%s.online_status IS NULL OR tb_creative_overview_%s.online_status=0)", gameCode, gameCode),
		// 上传时间大于设置的提醒天数
		fmt.Sprintf("tb_creative_media_upload_task_%s.update_time < (SELECT to_char(to_timestamp(extract(epoch from now())-tb_creative_media_upload_task_%s.notify_days*86400 ),'YYYY-MM-DD HH24:MI:SS'))", gameCode, gameCode),
	}
	schemaName := "arthub_sync"
	whereSql := strings.Join(whereList, " AND ")
	queryColumnsSql := strings.Join(queryColumns, ",")
	sql := fmt.Sprintf("SELECT %s FROM %s.tb_creative_media_upload_task_%s INNER JOIN %s.tb_creative_overview_%s ON %s.tb_creative_media_upload_task_%s.asset_id=%s.tb_creative_overview_%s.asset_id WHERE %s;",
		queryColumnsSql, schemaName, gameCode, schemaName, gameCode, schemaName, gameCode, schemaName, gameCode, whereSql)

	need2NotifyList := make([]Need2Notify, 0)
	_, err := pgdb.Pgdb.WithContext(ctx).Query(&need2NotifyList, sql)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return need2NotifyList, nil
}

// decreaseNotifyTryCnt 根据id列表减少提醒次数
func decreaseNotifyTryCnt(ctx context.Context, gameCode string, idList []int) error {
	if idList == nil || len(idList) == 0 {
		return nil
	}
	_, err := pgdb.Pgdb.WithContext(ctx).Model(&pgModel.CreativeMediaUploadTask{}).
		Table(fmt.Sprintf("%s.tb_creative_media_upload_task_%s", "arthub_sync", gameCode)).
		Set("notify_try_cnt = notify_try_cnt - 1").
		Where("id IN (?)", pg.In(idList)).
		Update()

	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

type Depot struct {
	GameCode string `pg:"game_code"`
}

// getAllGameCodeList 获取game_code列表
func getAllGameCodeList() ([]string, error) {
	gameCodeSet := make(map[string]bool)
	var depots []Depot
	db := pgdb.Pgdb
	_, err := db.Query(&depots,
		fmt.Sprintf(`SELECT game_code FROM %s.tb_arthub_depot`, "arthub_sync"))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// 去重
	for _, depot := range depots {
		gameCodeSet[depot.GameCode] = true
	}
	gameCodeList := make([]string, 0)
	for gameCode := range gameCodeSet {
		gameCodeList = append(gameCodeList, gameCode)
	}
	return gameCodeList, nil
}
