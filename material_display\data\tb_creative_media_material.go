package data

import (
	"context"
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"github.com/go-pg/pg/v10"
)

// UpsertCreativeMediaMaterail 事务中：插入上传目录和素材映射关系，忽略已存在
func UpsertCreativeMediaMaterail(tx *pg.Tx, gameCode string, directoryID string, assetID string) error {
	table := getCreativeMediaMaterailTable(gameCode)
	t := &model.CreativeMediaMaterial{
		DirectoryID: directoryID,
		AssetID:     assetID,
		UpdateTime:  utils.GetNowStr(),
	}

	_, err := tx.Model(t).Table(table).OnConflict("(directory_id, asset_id) DO NOTHING").Insert()
	return err
}

// CountCreativeMediaMaterailGroupByDirectory ...
func CountCreativeMediaMaterailGroupByDirectory(ctx context.Context, gameCode string) (map[string]int, error) {
	table := getCreativeMediaMaterailTable(gameCode)

	var res []struct {
		DirectoryID string
		Count       int
	}

	db := postgresql.GetDBWithContext(ctx)
	err := db.Model(&model.CreativeMediaMaterial{}).Table(table).
		Column("directory_id").ColumnExpr("count(*) AS count").
		Group("directory_id").Select(&res)
	if err != nil {
		return nil, err
	}

	rlt := make(map[string]int)
	for _, one := range res {
		rlt[one.DirectoryID] = one.Count
	}

	return rlt, err
}

func getCreativeMediaMaterailTable(gameCode string) string {
	return fmt.Sprintf("arthub_sync.tb_creative_media_material_%s", gameCode)
}
