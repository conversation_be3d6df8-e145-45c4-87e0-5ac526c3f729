// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0-devel
// 	protoc        v3.21.1
// source: asa_advertise/asa_advertise.proto

package asa_advertise

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 自测接口, POST, /api/v1/asa/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{0}
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{1}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 拉取账号列表, POST, /api/v1/asa_advertise/get_accounts
type GetAccountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAccountsReq) Reset() {
	*x = GetAccountsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsReq) ProtoMessage() {}

func (x *GetAccountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsReq.ProtoReflect.Descriptor instead.
func (*GetAccountsReq) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{2}
}

type Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	AccountName string `protobuf:"bytes,2,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`
}

func (x *Account) Reset() {
	*x = Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{3}
}

func (x *Account) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Account) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

type GetAccountsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Accounts []*Account  `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
}

func (x *GetAccountsRsp) Reset() {
	*x = GetAccountsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsRsp) ProtoMessage() {}

func (x *GetAccountsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsRsp.ProtoReflect.Descriptor instead.
func (*GetAccountsRsp) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{4}
}

func (x *GetAccountsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAccountsRsp) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

// 手动触发属性数据同步
type SyncStrategyDataReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCodes  []string `protobuf:"bytes,1,rep,name=game_codes,json=gameCodes,proto3" json:"game_codes,omitempty"`
	AccountIds []string `protobuf:"bytes,2,rep,name=account_ids,json=accountIds,proto3" json:"account_ids,omitempty"`
}

func (x *SyncStrategyDataReq) Reset() {
	*x = SyncStrategyDataReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncStrategyDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStrategyDataReq) ProtoMessage() {}

func (x *SyncStrategyDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStrategyDataReq.ProtoReflect.Descriptor instead.
func (*SyncStrategyDataReq) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{5}
}

func (x *SyncStrategyDataReq) GetGameCodes() []string {
	if x != nil {
		return x.GameCodes
	}
	return nil
}

func (x *SyncStrategyDataReq) GetAccountIds() []string {
	if x != nil {
		return x.AccountIds
	}
	return nil
}

type SyncStrategyDataRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SyncStrategyDataRsp) Reset() {
	*x = SyncStrategyDataRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncStrategyDataRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStrategyDataRsp) ProtoMessage() {}

func (x *SyncStrategyDataRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStrategyDataRsp.ProtoReflect.Descriptor instead.
func (*SyncStrategyDataRsp) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{6}
}

func (x *SyncStrategyDataRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 拉取campaign树形结构简要信息(status,name), POST, /api/v1/asa_advertise/get_campaign_tree_summary
type GetCampaignTreeSummaryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId  string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`    // 前端填写
	CampaignId string `protobuf:"bytes,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"` // campaign id信息
}

func (x *GetCampaignTreeSummaryReq) Reset() {
	*x = GetCampaignTreeSummaryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignTreeSummaryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignTreeSummaryReq) ProtoMessage() {}

func (x *GetCampaignTreeSummaryReq) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignTreeSummaryReq.ProtoReflect.Descriptor instead.
func (*GetCampaignTreeSummaryReq) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{7}
}

func (x *GetCampaignTreeSummaryReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetCampaignTreeSummaryReq) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

type GetCampaignTreeSummaryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result       *aix.Result   `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	CampaignTree *CampaignTree `protobuf:"bytes,2,opt,name=campaign_tree,json=campaignTree,proto3" json:"campaign_tree,omitempty"` // 返回的campaign树形结构信息
}

func (x *GetCampaignTreeSummaryRsp) Reset() {
	*x = GetCampaignTreeSummaryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignTreeSummaryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignTreeSummaryRsp) ProtoMessage() {}

func (x *GetCampaignTreeSummaryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignTreeSummaryRsp.ProtoReflect.Descriptor instead.
func (*GetCampaignTreeSummaryRsp) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{8}
}

func (x *GetCampaignTreeSummaryRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetCampaignTreeSummaryRsp) GetCampaignTree() *CampaignTree {
	if x != nil {
		return x.CampaignTree
	}
	return nil
}

type CampaignTree struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Campaign *Campaign  `protobuf:"bytes,1,opt,name=campaign,proto3" json:"campaign,omitempty"`                 // campaign信息
	AdGroups []*Adgroup `protobuf:"bytes,2,rep,name=ad_groups,json=adGroups,proto3" json:"ad_groups,omitempty"` // 子级ad_group信息
}

func (x *CampaignTree) Reset() {
	*x = CampaignTree{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignTree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignTree) ProtoMessage() {}

func (x *CampaignTree) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignTree.ProtoReflect.Descriptor instead.
func (*CampaignTree) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{9}
}

func (x *CampaignTree) GetCampaign() *Campaign {
	if x != nil {
		return x.Campaign
	}
	return nil
}

func (x *CampaignTree) GetAdGroups() []*Adgroup {
	if x != nil {
		return x.AdGroups
	}
	return nil
}

type Campaign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode               string  `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Media                  string  `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`
	AccountId              string  `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	AccountName            string  `protobuf:"bytes,4,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`
	CampaignId             string  `protobuf:"bytes,5,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	CampaignName           string  `protobuf:"bytes,6,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`
	CampaignStatus         string  `protobuf:"bytes,7,opt,name=campaign_status,json=campaignStatus,proto3" json:"campaign_status,omitempty"`
	CampaignDeliveryStatus string  `protobuf:"bytes,8,opt,name=campaign_delivery_status,json=campaignDeliveryStatus,proto3" json:"campaign_delivery_status,omitempty"`
	CampaignCreateTime     string  `protobuf:"bytes,9,opt,name=campaign_create_time,json=campaignCreateTime,proto3" json:"campaign_create_time,omitempty"`
	CampaignModifyTime     string  `protobuf:"bytes,10,opt,name=campaign_modify_time,json=campaignModifyTime,proto3" json:"campaign_modify_time,omitempty"`
	AixCurrency            string  `protobuf:"bytes,15,opt,name=aix_currency,json=aixCurrency,proto3" json:"aix_currency,omitempty"`
	AixDailyBudget         float64 `protobuf:"fixed64,16,opt,name=aix_daily_budget,json=aixDailyBudget,proto3" json:"aix_daily_budget,omitempty"`
	AixTotalBudget         float64 `protobuf:"fixed64,17,opt,name=aix_total_budget,json=aixTotalBudget,proto3" json:"aix_total_budget,omitempty"`
	AdChannelType          string  `protobuf:"bytes,19,opt,name=ad_channel_type,json=adChannelType,proto3" json:"ad_channel_type,omitempty"`
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{10}
}

func (x *Campaign) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *Campaign) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *Campaign) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Campaign) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *Campaign) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *Campaign) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *Campaign) GetCampaignStatus() string {
	if x != nil {
		return x.CampaignStatus
	}
	return ""
}

func (x *Campaign) GetCampaignDeliveryStatus() string {
	if x != nil {
		return x.CampaignDeliveryStatus
	}
	return ""
}

func (x *Campaign) GetCampaignCreateTime() string {
	if x != nil {
		return x.CampaignCreateTime
	}
	return ""
}

func (x *Campaign) GetCampaignModifyTime() string {
	if x != nil {
		return x.CampaignModifyTime
	}
	return ""
}

func (x *Campaign) GetAixCurrency() string {
	if x != nil {
		return x.AixCurrency
	}
	return ""
}

func (x *Campaign) GetAixDailyBudget() float64 {
	if x != nil {
		return x.AixDailyBudget
	}
	return 0
}

func (x *Campaign) GetAixTotalBudget() float64 {
	if x != nil {
		return x.AixTotalBudget
	}
	return 0
}

func (x *Campaign) GetAdChannelType() string {
	if x != nil {
		return x.AdChannelType
	}
	return ""
}

type Adgroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode              string  `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Media                 string  `protobuf:"bytes,2,opt,name=media,proto3" json:"media,omitempty"`
	AccountId             string  `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	AccountName           string  `protobuf:"bytes,4,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`
	CampaignId            string  `protobuf:"bytes,5,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	CampaignName          string  `protobuf:"bytes,6,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`
	AdgroupId             string  `protobuf:"bytes,7,opt,name=adgroup_id,json=adgroupId,proto3" json:"adgroup_id,omitempty"`
	AdgroupName           string  `protobuf:"bytes,8,opt,name=adgroup_name,json=adgroupName,proto3" json:"adgroup_name,omitempty"`
	AdgroupStatus         string  `protobuf:"bytes,9,opt,name=adgroup_status,json=adgroupStatus,proto3" json:"adgroup_status,omitempty"`
	AdgroupDeliveryStatus string  `protobuf:"bytes,10,opt,name=adgroup_delivery_status,json=adgroupDeliveryStatus,proto3" json:"adgroup_delivery_status,omitempty"`
	AdgroupCreateTime     string  `protobuf:"bytes,11,opt,name=adgroup_create_time,json=adgroupCreateTime,proto3" json:"adgroup_create_time,omitempty"`
	AdgroupModifyTime     string  `protobuf:"bytes,12,opt,name=adgroup_modify_time,json=adgroupModifyTime,proto3" json:"adgroup_modify_time,omitempty"`
	AixCurrency           string  `protobuf:"bytes,17,opt,name=aix_currency,json=aixCurrency,proto3" json:"aix_currency,omitempty"`
	DefaultBidAmount      float64 `protobuf:"fixed64,18,opt,name=default_bid_amount,json=defaultBidAmount,proto3" json:"default_bid_amount,omitempty"`
	CpaGoal               float64 `protobuf:"fixed64,19,opt,name=cpa_goal,json=cpaGoal,proto3" json:"cpa_goal,omitempty"`
}

func (x *Adgroup) Reset() {
	*x = Adgroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Adgroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adgroup) ProtoMessage() {}

func (x *Adgroup) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adgroup.ProtoReflect.Descriptor instead.
func (*Adgroup) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{11}
}

func (x *Adgroup) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *Adgroup) GetMedia() string {
	if x != nil {
		return x.Media
	}
	return ""
}

func (x *Adgroup) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Adgroup) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *Adgroup) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *Adgroup) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *Adgroup) GetAdgroupId() string {
	if x != nil {
		return x.AdgroupId
	}
	return ""
}

func (x *Adgroup) GetAdgroupName() string {
	if x != nil {
		return x.AdgroupName
	}
	return ""
}

func (x *Adgroup) GetAdgroupStatus() string {
	if x != nil {
		return x.AdgroupStatus
	}
	return ""
}

func (x *Adgroup) GetAdgroupDeliveryStatus() string {
	if x != nil {
		return x.AdgroupDeliveryStatus
	}
	return ""
}

func (x *Adgroup) GetAdgroupCreateTime() string {
	if x != nil {
		return x.AdgroupCreateTime
	}
	return ""
}

func (x *Adgroup) GetAdgroupModifyTime() string {
	if x != nil {
		return x.AdgroupModifyTime
	}
	return ""
}

func (x *Adgroup) GetAixCurrency() string {
	if x != nil {
		return x.AixCurrency
	}
	return ""
}

func (x *Adgroup) GetDefaultBidAmount() float64 {
	if x != nil {
		return x.DefaultBidAmount
	}
	return 0
}

func (x *Adgroup) GetCpaGoal() float64 {
	if x != nil {
		return x.CpaGoal
	}
	return 0
}

// 拉取campaign列表, POST, /api/v1/asa_advertise/get_campaigns
type GetCampaignsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string   `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`       // 前端填写
	CampaignIds []string `protobuf:"bytes,2,rep,name=campaign_ids,json=campaignIds,proto3" json:"campaign_ids,omitempty"` // campaign id列表
}

func (x *GetCampaignsReq) Reset() {
	*x = GetCampaignsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignsReq) ProtoMessage() {}

func (x *GetCampaignsReq) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignsReq.ProtoReflect.Descriptor instead.
func (*GetCampaignsReq) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{12}
}

func (x *GetCampaignsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetCampaignsReq) GetCampaignIds() []string {
	if x != nil {
		return x.CampaignIds
	}
	return nil
}

type GetCampaignsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 结果
	Campaigns []*Campaign `protobuf:"bytes,2,rep,name=campaigns,proto3" json:"campaigns,omitempty"` // campaign列表
}

func (x *GetCampaignsRsp) Reset() {
	*x = GetCampaignsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignsRsp) ProtoMessage() {}

func (x *GetCampaignsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignsRsp.ProtoReflect.Descriptor instead.
func (*GetCampaignsRsp) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{13}
}

func (x *GetCampaignsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetCampaignsRsp) GetCampaigns() []*Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

// 拉取adgroup列表, POST, /api/v1/asa_advertise/get_campaigns
type GetAdgroupsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId  string   `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`    // 前端填写
	CampaignId string   `protobuf:"bytes,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"` // campaign id
	AdgroupIds []string `protobuf:"bytes,3,rep,name=adgroup_ids,json=adgroupIds,proto3" json:"adgroup_ids,omitempty"` // adgroup列表
}

func (x *GetAdgroupsReq) Reset() {
	*x = GetAdgroupsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdgroupsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdgroupsReq) ProtoMessage() {}

func (x *GetAdgroupsReq) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdgroupsReq.ProtoReflect.Descriptor instead.
func (*GetAdgroupsReq) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{14}
}

func (x *GetAdgroupsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetAdgroupsReq) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *GetAdgroupsReq) GetAdgroupIds() []string {
	if x != nil {
		return x.AdgroupIds
	}
	return nil
}

type GetAdgroupsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`     // 结果
	Adgroups []*Adgroup  `protobuf:"bytes,2,rep,name=adgroups,proto3" json:"adgroups,omitempty"` // adgroup列表
}

func (x *GetAdgroupsRsp) Reset() {
	*x = GetAdgroupsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdgroupsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdgroupsRsp) ProtoMessage() {}

func (x *GetAdgroupsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdgroupsRsp.ProtoReflect.Descriptor instead.
func (*GetAdgroupsRsp) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{15}
}

func (x *GetAdgroupsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAdgroupsRsp) GetAdgroups() []*Adgroup {
	if x != nil {
		return x.Adgroups
	}
	return nil
}

type CampaignAdgroupUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CampaignId string   `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"` // campaign id
	AdgroupIds []string `protobuf:"bytes,2,rep,name=adgroup_ids,json=adgroupIds,proto3" json:"adgroup_ids,omitempty"` // adgroup列表
}

func (x *CampaignAdgroupUnit) Reset() {
	*x = CampaignAdgroupUnit{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CampaignAdgroupUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CampaignAdgroupUnit) ProtoMessage() {}

func (x *CampaignAdgroupUnit) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CampaignAdgroupUnit.ProtoReflect.Descriptor instead.
func (*CampaignAdgroupUnit) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{16}
}

func (x *CampaignAdgroupUnit) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *CampaignAdgroupUnit) GetAdgroupIds() []string {
	if x != nil {
		return x.AdgroupIds
	}
	return nil
}

type BatchGetAdgroupsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string                 `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	Units     []*CampaignAdgroupUnit `protobuf:"bytes,2,rep,name=units,proto3" json:"units,omitempty"` // campaignadgroup集合列表
}

func (x *BatchGetAdgroupsReq) Reset() {
	*x = BatchGetAdgroupsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAdgroupsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAdgroupsReq) ProtoMessage() {}

func (x *BatchGetAdgroupsReq) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAdgroupsReq.ProtoReflect.Descriptor instead.
func (*BatchGetAdgroupsReq) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{17}
}

func (x *BatchGetAdgroupsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *BatchGetAdgroupsReq) GetUnits() []*CampaignAdgroupUnit {
	if x != nil {
		return x.Units
	}
	return nil
}

type BatchGetAdgroupsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`     // 结果
	Adgroups []*Adgroup  `protobuf:"bytes,2,rep,name=adgroups,proto3" json:"adgroups,omitempty"` // adgroup列表
}

func (x *BatchGetAdgroupsRsp) Reset() {
	*x = BatchGetAdgroupsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asa_advertise_asa_advertise_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetAdgroupsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetAdgroupsRsp) ProtoMessage() {}

func (x *BatchGetAdgroupsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asa_advertise_asa_advertise_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetAdgroupsRsp.ProtoReflect.Descriptor instead.
func (*BatchGetAdgroupsRsp) Descriptor() ([]byte, []int) {
	return file_asa_advertise_asa_advertise_proto_rawDescGZIP(), []int{18}
}

func (x *BatchGetAdgroupsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *BatchGetAdgroupsRsp) GetAdgroups() []*Adgroup {
	if x != nil {
		return x.Adgroups
	}
	return nil
}

var File_asa_advertise_asa_advertise_proto protoreflect.FileDescriptor

var file_asa_advertise_asa_advertise_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2f,
	0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x0a, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71, 0x22, 0x2f, 0x0a, 0x08,
	0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x10, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x22,
	0x4b, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x69, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x32, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x55, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x53,
	0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x3a,
	0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x5b, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x0d, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x74, 0x72, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x52, 0x0c,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x22, 0x78, 0x0a, 0x0c,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x72, 0x65, 0x65, 0x12, 0x33, 0x0a, 0x08,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x08, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x12, 0x33, 0x0a, 0x09, 0x61, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x08, 0x61, 0x64,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0xab, 0x04, 0x0a, 0x08, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x63, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x69, 0x78,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x69, 0x78, 0x5f,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x61, 0x69, 0x78, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x42, 0x75, 0x64, 0x67,
	0x65, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x69, 0x78, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x62, 0x75, 0x64, 0x67, 0x65, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x61, 0x69,
	0x78, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x75, 0x64, 0x67, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x61, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xb1, 0x04, 0x0a, 0x07, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69,
	0x67, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x64, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13,
	0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x64, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x69, 0x78, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x69, 0x78, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x2c, 0x0a, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x42, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x63, 0x70, 0x61, 0x5f, 0x67, 0x6f, 0x61, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x07, 0x63, 0x70, 0x61, 0x47, 0x6f, 0x61, 0x6c, 0x22, 0x53, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x6d, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x35, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x22, 0x71, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x22,
	0x69, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x32, 0x0a, 0x08, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x08, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x57, 0x0a, 0x13, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x6e, 0x69,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x73, 0x22, 0x6e, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x05, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x05, 0x75, 0x6e,
	0x69, 0x74, 0x73, 0x22, 0x6e, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x32, 0x0a, 0x08, 0x61, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x41, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x08, 0x61, 0x64, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63,
	0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x73, 0x2f, 0x61, 0x73, 0x61, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_asa_advertise_asa_advertise_proto_rawDescOnce sync.Once
	file_asa_advertise_asa_advertise_proto_rawDescData = file_asa_advertise_asa_advertise_proto_rawDesc
)

func file_asa_advertise_asa_advertise_proto_rawDescGZIP() []byte {
	file_asa_advertise_asa_advertise_proto_rawDescOnce.Do(func() {
		file_asa_advertise_asa_advertise_proto_rawDescData = protoimpl.X.CompressGZIP(file_asa_advertise_asa_advertise_proto_rawDescData)
	})
	return file_asa_advertise_asa_advertise_proto_rawDescData
}

var file_asa_advertise_asa_advertise_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_asa_advertise_asa_advertise_proto_goTypes = []interface{}{
	(*SayHiReq)(nil),                  // 0: asa_advertise.SayHiReq
	(*SayHiRsp)(nil),                  // 1: asa_advertise.SayHiRsp
	(*GetAccountsReq)(nil),            // 2: asa_advertise.GetAccountsReq
	(*Account)(nil),                   // 3: asa_advertise.Account
	(*GetAccountsRsp)(nil),            // 4: asa_advertise.GetAccountsRsp
	(*SyncStrategyDataReq)(nil),       // 5: asa_advertise.SyncStrategyDataReq
	(*SyncStrategyDataRsp)(nil),       // 6: asa_advertise.SyncStrategyDataRsp
	(*GetCampaignTreeSummaryReq)(nil), // 7: asa_advertise.GetCampaignTreeSummaryReq
	(*GetCampaignTreeSummaryRsp)(nil), // 8: asa_advertise.GetCampaignTreeSummaryRsp
	(*CampaignTree)(nil),              // 9: asa_advertise.CampaignTree
	(*Campaign)(nil),                  // 10: asa_advertise.Campaign
	(*Adgroup)(nil),                   // 11: asa_advertise.Adgroup
	(*GetCampaignsReq)(nil),           // 12: asa_advertise.GetCampaignsReq
	(*GetCampaignsRsp)(nil),           // 13: asa_advertise.GetCampaignsRsp
	(*GetAdgroupsReq)(nil),            // 14: asa_advertise.GetAdgroupsReq
	(*GetAdgroupsRsp)(nil),            // 15: asa_advertise.GetAdgroupsRsp
	(*CampaignAdgroupUnit)(nil),       // 16: asa_advertise.CampaignAdgroupUnit
	(*BatchGetAdgroupsReq)(nil),       // 17: asa_advertise.BatchGetAdgroupsReq
	(*BatchGetAdgroupsRsp)(nil),       // 18: asa_advertise.BatchGetAdgroupsRsp
	(*aix.Result)(nil),                // 19: aix.Result
}
var file_asa_advertise_asa_advertise_proto_depIdxs = []int32{
	19, // 0: asa_advertise.SayHiRsp.result:type_name -> aix.Result
	19, // 1: asa_advertise.GetAccountsRsp.result:type_name -> aix.Result
	3,  // 2: asa_advertise.GetAccountsRsp.accounts:type_name -> asa_advertise.Account
	19, // 3: asa_advertise.SyncStrategyDataRsp.result:type_name -> aix.Result
	19, // 4: asa_advertise.GetCampaignTreeSummaryRsp.result:type_name -> aix.Result
	9,  // 5: asa_advertise.GetCampaignTreeSummaryRsp.campaign_tree:type_name -> asa_advertise.CampaignTree
	10, // 6: asa_advertise.CampaignTree.campaign:type_name -> asa_advertise.Campaign
	11, // 7: asa_advertise.CampaignTree.ad_groups:type_name -> asa_advertise.Adgroup
	19, // 8: asa_advertise.GetCampaignsRsp.result:type_name -> aix.Result
	10, // 9: asa_advertise.GetCampaignsRsp.campaigns:type_name -> asa_advertise.Campaign
	19, // 10: asa_advertise.GetAdgroupsRsp.result:type_name -> aix.Result
	11, // 11: asa_advertise.GetAdgroupsRsp.adgroups:type_name -> asa_advertise.Adgroup
	16, // 12: asa_advertise.BatchGetAdgroupsReq.units:type_name -> asa_advertise.CampaignAdgroupUnit
	19, // 13: asa_advertise.BatchGetAdgroupsRsp.result:type_name -> aix.Result
	11, // 14: asa_advertise.BatchGetAdgroupsRsp.adgroups:type_name -> asa_advertise.Adgroup
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_asa_advertise_asa_advertise_proto_init() }
func file_asa_advertise_asa_advertise_proto_init() {
	if File_asa_advertise_asa_advertise_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_asa_advertise_asa_advertise_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncStrategyDataReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncStrategyDataRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignTreeSummaryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignTreeSummaryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignTree); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Campaign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Adgroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAdgroupsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAdgroupsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CampaignAdgroupUnit); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAdgroupsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asa_advertise_asa_advertise_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetAdgroupsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_asa_advertise_asa_advertise_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_asa_advertise_asa_advertise_proto_goTypes,
		DependencyIndexes: file_asa_advertise_asa_advertise_proto_depIdxs,
		MessageInfos:      file_asa_advertise_asa_advertise_proto_msgTypes,
	}.Build()
	File_asa_advertise_asa_advertise_proto = out.File
	file_asa_advertise_asa_advertise_proto_rawDesc = nil
	file_asa_advertise_asa_advertise_proto_goTypes = nil
	file_asa_advertise_asa_advertise_proto_depIdxs = nil
}
