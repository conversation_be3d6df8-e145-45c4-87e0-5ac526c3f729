package data

import (
	"context"

	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/channel_asset"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// GetChannelAssetsLabels 拉取多个渠道素材的标签信息，返回map
func GetChannelAssetsLabels(ctx context.Context, gameCode string, ids []string) (map[string][]*pb.AssetLabel, error) {
	labels, err := repo.GetChannelAssetsLabels(ctx, gameCode, ids)
	if err != nil {
		return nil, err
	}

	rlt := make(map[string][]*pb.AssetLabel)
	for _, l := range labels {
		t := &pb.AssetLabel{
			LabelName:   l.LabelName,
			FirstLabel:  l.FirstLabel,
			SecondLabel: l.SecondLabel,
		}
		rlt[l.ChannelAssetId] = append(rlt[l.ChannelAssetId], t)
	}

	return rlt, nil
}
