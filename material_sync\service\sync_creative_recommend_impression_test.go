package service

import (
	"context"
	"testing"
)

func TestParsePUBGMFullPathName(t *testing.T) {
	type test_unit struct {
		Name         string
		FullPathName string
		Label        *pubgmPathLabel
		Err          error
	}
	test_cases := []test_unit{
		{"1", "2022,1.9.0,aftermath,视频,AR", &pubgmPathLabel{"1.9.0", "aftermath"}, nil},
		{"2", "2022,1.9x.0,aftermath,视频,AR", nil, nil},
		{"3", "2022,2.1.0,IP合作,BLACK PINK,HQ图片,ID", &pubgmPathLabel{"2.1.0", "BLACK PINK"}, nil},
		{"4", "2022,1.7.0,本地化素材,CIS", &pubgmPathLabel{"1.7.0", "CIS"}, nil},
		{"5", "2022,1.7.0,本地素材,CIS", &pubgmPathLabel{"1.7.0", "CIS"}, nil},
	}

	ctx := context.Background()
	for _, c := range test_cases {
		label, err := parsePUBGMFullPathName(ctx, c.FullPathName)
		if err != c.Err {
			t.Errorf("%s wrong err, get: %s, want: %s", c.Name, err, c.Err)
			continue
		}

		if label == nil {
			if label != c.Label {
				t.Errorf("%s wrong label, get: %v, want: %v", c.Name, label, c.Label)
			}
			continue
		}
		if label.Version != c.Label.Version {
			t.Errorf("%s wrong version, get: %s, want: %s", c.Name, label.Version, c.Label.Version)
		}
		if label.Theme != c.Label.Theme {
			t.Errorf("%s wrong theme, get: %s, want: %s", c.Name, label.Theme, c.Label.Theme)
		}
	}
}
