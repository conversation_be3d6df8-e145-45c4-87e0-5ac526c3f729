#!/usr/bin/env node

/**
 * Go结构体依赖分析器演示脚本
 * 展示注释保留功能和不同输出格式
 */

const { analyzeStruct } = require('./struct-analyzer.js');

console.log('🚀 Go结构体依赖分析器演示');
console.log('='.repeat(60));

console.log('\n📋 演示内容:');
console.log('1. 简化格式输出（保留注释，过滤protobuf内部字段）');
console.log('2. 原始格式输出（完整保留所有字段和注释）');
console.log('3. 依赖关系分析');

console.log('\n' + '='.repeat(60));
console.log('🔍 演示1: 简化格式分析 GetMaterialInfoRsp');
console.log('='.repeat(60));

// 简化格式
const dependencies1 = analyzeStruct('GetMaterialInfoRsp', '.', false);

console.log('\n' + '='.repeat(60));
console.log('🔍 演示2: 原始格式分析 GetMaterialInfoRsp');
console.log('='.repeat(60));

// 原始格式
const dependencies2 = analyzeStruct('GetMaterialInfoRsp', '.', true);

console.log('\n' + '='.repeat(60));
console.log('📊 演示3: 依赖关系分析');
console.log('='.repeat(60));

if (dependencies1 && dependencies1.size > 0) {
    console.log('\n🔗 依赖关系树:');
    console.log('GetMaterialInfoRsp');
    
    const deps = Array.from(dependencies1.keys());
    deps.forEach((structName, index) => {
        if (structName === 'GetMaterialInfoRsp') return;
        
        const isLast = index === deps.length - 1;
        const prefix = isLast ? '└── ' : '├── ';
        
        const structDef = dependencies1.get(structName);
        const comment = structDef.comment ? ` (${structDef.comment.replace('//', '').trim()})` : '';
        
        console.log(`${prefix}${structName}${comment}`);
        
        // 显示字段数量
        const businessFields = structDef.fields.filter(field => 
            !['state', 'sizeCache', 'unknownFields'].includes(field.name)
        );
        
        if (businessFields.length > 0) {
            const fieldPrefix = isLast ? '    ' : '│   ';
            console.log(`${fieldPrefix}└── ${businessFields.length} 个业务字段`);
        }
    });
    
    console.log('\n📈 统计信息:');
    console.log(`- 总结构体数量: ${dependencies1.size}`);
    console.log(`- 跨包引用: aix.Result`);
    
    let totalFields = 0;
    let totalComments = 0;
    
    for (const [structName, structDef] of dependencies1) {
        const businessFields = structDef.fields.filter(field => 
            !['state', 'sizeCache', 'unknownFields'].includes(field.name)
        );
        totalFields += businessFields.length;
        
        // 统计有注释的字段
        const fieldsWithComments = businessFields.filter(field => field.comment);
        totalComments += fieldsWithComments.length;
        
        if (structDef.comment) totalComments++;
    }
    
    console.log(`- 总字段数量: ${totalFields}`);
    console.log(`- 包含注释的项目: ${totalComments}`);
}

console.log('\n' + '='.repeat(60));
console.log('✨ 功能特性展示');
console.log('='.repeat(60));

console.log('\n🎯 核心功能:');
console.log('✅ 递归依赖分析 - 自动找到所有相关结构体');
console.log('✅ 跨包引用支持 - 正确处理 aix.Result 等跨包类型');
console.log('✅ 注释完整保留 - 保留所有Go代码中的注释信息');
console.log('✅ 多种输出格式 - 简化格式和原始格式可选');
console.log('✅ 智能字段过滤 - 自动过滤protobuf内部字段');

console.log('\n🛠️ 使用方式:');
console.log('# 简化格式（推荐）');
console.log('node quick-analyze.js GetMaterialInfoRsp');
console.log('');
console.log('# 原始格式');
console.log('node quick-analyze.js GetMaterialInfoRsp --raw');
console.log('');
console.log('# 作为模块使用');
console.log('const { analyzeStruct } = require("./struct-analyzer.js");');
console.log('const deps = analyzeStruct("GetMaterialInfoRsp", ".");');

console.log('\n' + '='.repeat(60));
console.log('🎉 演示完成！');
console.log('='.repeat(60));

console.log('\n💡 提示:');
console.log('- 使用 --raw 选项查看完整的protobuf字段');
console.log('- 所有注释都被完整保留，包括中文注释');
console.log('- 支持分析任何Go结构体，不仅限于protobuf生成的代码');
console.log('- 运行 npm test 查看完整测试套件');

console.log('\n📚 更多信息请查看:');
console.log('- README.md - 详细文档');
console.log('- USAGE.md - 使用指南');
console.log('- test-analyzer.js - 测试用例');
