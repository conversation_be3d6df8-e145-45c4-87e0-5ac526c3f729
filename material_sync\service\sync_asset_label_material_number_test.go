package service

import "testing"

func TestGetMaterialNumber(t *testing.T) {
	test_cases := []struct {
		GameCode string
		Name     string
		Exp      string
	}{
		{"pubgm", "RG02516-PUBGM-V-1-AR-TENCENT.mp4", "RG02516"},
		{"pubgm", "UA15980-PUBGM-V-3-ID-TENCENT.", "UA15980"},
		{"pubgm", "U115980-PUBGM-V-3-ID-TENCENT.", ""},
		{"hok_prod", "P-00010镜单人-团战-角色展示-PTBR-F-CBT-220909-设计中心-zhinan.jpg", "P-00010"},
		{"hok_prod", "V-0063-EC测试赵云6s-英雄展示-剪辑包装-PTBR-H-CBT-221116-月退-GRAY.mp4", "V-0063"},
		{"hok_prod", "V-0053-EC测试澜-英雄展示-包装剪辑-PTBR-H-CBT-221114-IH-WZN.mp4", "V-0053"},
		{"hok_prod", "V-G16匹配战斗-操作-AR-H-CBT-220818.mp4", "V-G16"},
		{"hok_prod", "VG16匹配战斗-操作-AR-H-CBT-220818.mp4", "VG16"},
		{"hok_prod", "VG?16匹配战斗-操作-AR-H-CBT-220818.mp4", ""},
		{"hok_prod", "VG*16匹配战斗-操作-AR-H-CBT-220818.mp4", ""},
		{"hok_prod", "VG111167匹配战斗-操作-AR-H-CBT-220818.mp4", ""},
		{"hok_prod", "V-G26云中君云缨-英雄展示-包装剪辑-AR-F-CBT-220802", "V-G26"},
		{"hok_prod", "V-0046LeoVeio小乔-KOL-配音解说-PTBR-F-CBT-220923", "V-0046"},
		{"hok_prod", "V-G3匹配战斗赵云-打野-AR-H-CBT-220707", "V-G3"},
	}
	for i, test_case := range test_cases {
		result := getMaterialNumber(test_case.GameCode, test_case.Name)
		if result != test_case.Exp {
			t.Errorf("index: %d, test case: %+v, result: %s", i, test_case, result)
		}
	}
}
