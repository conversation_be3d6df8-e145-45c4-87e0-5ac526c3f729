package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"github.com/thoas/go-funk"
)

// SyncOverviewOnlineStatusGameCode 同步overview表中的素材上线状态
func SyncOverviewOnlineStatusGameCode(game_code string, key2asset_id map[string]string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncOverviewOnlineStatusGameCode start, game code: %s", game_code)

	st := time.Now()

	date := time.Now().AddDate(0, 0, -1) // 数据拉取有一天的延时
	err := updateOverviewOnlineStatusForGameCode(ctx, game_code, key2asset_id, date)
	if err != nil {
		log.ErrorContextf(ctx, "updateOverviewOnlineStatusForGameCode failed: %s", err)
	}

	// 旧的素材上线更新逻辑, 在素材库2.0上线后可以注释掉
	asset_ids, err := updateOverviewOnlineStatusForGameCodeOnline(ctx, game_code, key2asset_id)
	if err != nil {
		log.ErrorContextf(ctx, "updateOverviewOnlineStatusForGameCodeOnline failed: %s", err)
		return
	}

	// 旧的素材上线更新逻辑, 在素材库2.0上线后可以注释掉
	err = updateOverviewOnlineStatusForGameCodeOffline(ctx, game_code, asset_ids, key2asset_id)
	if err != nil {
		log.ErrorContextf(ctx, "updateOverviewOnlineStatusForGameCodeOffline failed: %s", err)
		return
	}

	// 旧的素材上线更新逻辑, 在素材库2.0上线后可以注释掉
	updateOverviewOnlineStatusClearOld(ctx, game_code)

	cost := time.Since(st)
	log.DebugContextf(ctx, "SyncOverviewOnlineStatusGameCode end, game code: %s, cost: %v", game_code, cost)
}

// updateOverviewOnlineStatusForGameCode 更新素材库在线状态
func updateOverviewOnlineStatusForGameCode(ctx context.Context, game_code string, key2asset_id map[string]string, date time.Time) error {
	err := updateOverviewOnlineStatusForGameCodeGoogle(ctx, game_code, key2asset_id, date)
	if err != nil { // 不阻塞后续流程
		log.ErrorContextf(ctx, "updateOverviewOnlineStatusForGameCodeGoogle failed: %s", err)
	}

	err = updateOverviewOnlineStatusForGameCodeFacebook(ctx, game_code, key2asset_id, date)
	if err != nil {
		log.ErrorContextf(ctx, "updateOverviewOnlineStatusForGameCodeFacebook failed: %s", err)
	}

	return nil
}

// updateOverviewOnlineStatusForGameCodeFacebook 更新Facebook在线状态
func updateOverviewOnlineStatusForGameCodeFacebook(ctx context.Context, game_code string, key2asset_id map[string]string, date time.Time) error {
	channel_assets, err := getOnlineChannelAssetsFacebook(ctx, game_code, date)
	if err != nil {
		log.ErrorContextf(ctx, "getOnlineChannelAssetsFacebook failed: %s", err)
		return err
	}

	var asset_ids []string
	for _, channel_asset := range channel_assets {
		k := genChannelAssetKey(channel_asset.ChannelType, channel_asset.AccountId, channel_asset.AssetId)
		asset_id, ok := key2asset_id[k]
		if !ok {
			continue
		}

		asset_ids = append(asset_ids, asset_id)
	}

	log.DebugContextf(ctx, "game code: %s get facebook online assets number: %d", game_code, len(asset_ids))

	// 更新上线素材数据
	update_time := time.Now().Format("2006-01-02 15:04:05")
	asset_ids = funk.UniqString(asset_ids)
	asset_ids_join := strings.Join(asset_ids, "','")
	date_str := date.Format("2006-01-02 15:04:05")
	sql := fmt.Sprintf(`update arthub_sync.tb_creative_overview_%s
	set
	    facebook_online_time = case
	        when (coalesce(char_length(facebook_online_time), 0) = 0) or (facebook_online_time > '%s') then '%s'
	        else facebook_online_time
	    end,
	    facebook_offline_time = '',
	    online_status_update_time = '%s',
	    facebook_online_state = 1
	where
	    asset_id in ('%s');`, game_code, date_str, date_str, update_time, asset_ids_join)

	postdb := pgdb.GetDBWithContext(ctx)
	_, err = postdb.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update overview failed: %s", err)
		return err
	}

	// 更新下线素材数据
	sql = fmt.Sprintf(`update arthub_sync.tb_creative_overview_%s
	set
		facebook_offline_time = '%s',
	    facebook_online_state = 2
	where
		facebook_online_state=1 and online_status_update_time < '%s';`, game_code, date_str, update_time)

	_, err = postdb.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update overview failed: %s", err)
		return err
	}

	return nil
}

// getOnlineChannelAssetsFacebook 获取facebook在线素材列表
func getOnlineChannelAssetsFacebook(ctx context.Context, game_code string, date time.Time) ([]*channelAsset, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.FacebookRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(game_code))
	pg_query.Where("impressions>0")
	date_str := date.Format("********")
	pg_query.Where("dtstatdate=?", date_str)
	pg_query.Where("account_id != '' and account_id is not null")
	pg_query.Where("asset_id != '' and asset_id is not null")
	select_columns := []string{"account_id", "asset_id"}
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)

	var fb_records []pgmodel.FacebookRealtimeAssetInfo
	err := pg_query.Select(&fb_records)
	if err != nil {
		log.ErrorContextf(ctx, "select facebook ad assets failed: %s", err)
		fb_records = nil
	}

	log.DebugContextf(ctx, "game %s get facebook ad assets number: %d", game_code, len(fb_records))

	var channel_assets []*channelAsset
	for _, record := range fb_records {
		channel_asset := channelAsset{}
		channel_asset.AccountId = record.AccountId
		channel_asset.AssetId = record.AssetId
		channel_asset.ChannelType = 2 // facebook

		channel_assets = append(channel_assets, &channel_asset)
	}

	return channel_assets, nil
}

// updateOverviewOnlineStatusForGameCodeGoogle 更新Google在线状态
func updateOverviewOnlineStatusForGameCodeGoogle(ctx context.Context, game_code string, key2asset_id map[string]string, date time.Time) error {
	channel_assets, err := getOnlineChannelAssetsGoogle(ctx, game_code, date)
	if err != nil {
		log.ErrorContextf(ctx, "getOnlineChannelAssetsGoogle failed: %s", err)
		return err
	}

	var asset_ids []string
	for _, channel_asset := range channel_assets {
		k := genChannelAssetKey(channel_asset.ChannelType, channel_asset.AccountId, channel_asset.AssetId)
		asset_id, ok := key2asset_id[k]
		if !ok {
			continue
		}

		asset_ids = append(asset_ids, asset_id)
	}

	log.DebugContextf(ctx, "game code: %s get google online assets number: %d", game_code, len(asset_ids))

	// 更新上线素材数据
	update_time := time.Now().Format("2006-01-02 15:04:05")
	asset_ids = funk.UniqString(asset_ids)
	asset_ids_join := strings.Join(asset_ids, "','")
	date_str := date.Format("2006-01-02 15:04:05")
	sql := fmt.Sprintf(`update arthub_sync.tb_creative_overview_%s
	set
	    google_online_time = case
	        when (coalesce(char_length(google_online_time), 0) = 0) or (google_online_time > '%s') then '%s'
	        else google_online_time
	    end,
	    google_offline_time = '',
	    online_status_update_time = '%s',
	    google_online_state = 1
	where
	    asset_id in ('%s');`, game_code, date_str, date_str, update_time, asset_ids_join)

	postdb := pgdb.GetDBWithContext(ctx)
	_, err = postdb.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update overview failed: %s", err)
		return err
	}

	// 更新下线素材数据
	sql = fmt.Sprintf(`update arthub_sync.tb_creative_overview_%s
	set
	    google_offline_time = '%s',
	    google_online_state = 2
	where
		google_online_state=1 and online_status_update_time < '%s';`, game_code, date_str, update_time)

	_, err = postdb.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update overview failed: %s", err)
		return err
	}

	return nil
}

// getOnlineChannelAssetsGoogle 获取Google在线素材
func getOnlineChannelAssetsGoogle(ctx context.Context, game_code string, date time.Time) ([]*channelAsset, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.Where("impressions>0")
	select_columns := []string{"account_id", "asset_id"}
	date_str := date.Format("********")
	pg_query.Where("dtstatdate=?", date_str)
	pg_query.Where("account_id != '' and account_id is not null")
	pg_query.Where("asset_id != '' and asset_id is not null")
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)

	var gg_records []pgmodel.FacebookRealtimeAssetInfo
	err := pg_query.Select(&gg_records)
	if err != nil {
		log.ErrorContextf(ctx, "select google ad assets failed: %s", err)
		gg_records = nil
	}

	log.DebugContextf(ctx, "game %s get google ad assets number: %d", game_code, len(gg_records))

	var channel_assets []*channelAsset
	for _, record := range gg_records {
		channel_asset := channelAsset{}
		channel_asset.AccountId = record.AccountId
		channel_asset.AssetId = record.AssetId
		channel_asset.ChannelType = 1 // google

		channel_assets = append(channel_assets, &channel_asset)
	}

	return channel_assets, nil
}

// updateOverviewOnlineStatusForGameCodeOnline 更新某个游戏的素材上线
// 第一个返回参数为上线素材
//lint:ignore U1000 Ignore unused function temporarily for debugging
func updateOverviewOnlineStatusForGameCodeOnline(ctx context.Context, game_code string, key2asset_id map[string]string) ([]string, error) {
	// 获取impressions>0对应的asset_ids
	channel_assets, err := getChannelAssetsWithImpressionType(ctx, game_code, 1)
	if err != nil {
		return nil, fmt.Errorf("getOnlineAdAssets failed: %s", err)
	}

	var asset_ids []string
	for _, channel_asset := range channel_assets {
		key := genChannelAssetKey(channel_asset.ChannelType, channel_asset.AccountId, channel_asset.AssetId)
		asset_id, ok := key2asset_id[key]
		if !ok {
			continue
		}

		asset_ids = append(asset_ids, asset_id)
	}

	log.DebugContextf(ctx, "game %s get online assets number: %d", game_code, len(asset_ids))

	if len(asset_ids) == 0 {
		return nil, nil
	}

	// 更新上线(impressions>0)的数据
	asset_ids = funk.UniqString(asset_ids)
	asset_ids_join := strings.Join(asset_ids, "','")
	sql := fmt.Sprintf(`update arthub_sync.tb_creative_overview_%s
	set
	    online_date = case
	        when coalesce(char_length(online_date), 0) = 0 then to_char(now(), 'YYYY-MM-DD HH:MI:SS')
	        else online_date
	    end,
	    offline_date = case
	        when online_status = 1 then offline_date
	        else to_char(now(), 'YYYY-MM-DD HH:MI:SS')
	    end,
	    online_status_update_time = to_char(now(), 'YYYY-MM-DD HH:MI:SS'),
	    online_status = 1
	where
	    asset_id in ('%s');`, game_code, asset_ids_join)

	postdb := pgdb.GetDBWithContext(ctx)
	_, err = postdb.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update overview failed: %s", err)
		return nil, err
	}

	return asset_ids, nil
}

// updateOverviewOnlineStatusForGameCodeOffline 更新某个游戏的素材下线
//lint:ignore U1000 Ignore unused function temporarily for debugging
func updateOverviewOnlineStatusForGameCodeOffline(ctx context.Context, game_code string, online_asset_ids []string, key2asset_id map[string]string) error {
	// 获取impressions=0对应的asset_ids
	channel_assets, err := getChannelAssetsWithImpressionType(ctx, game_code, 2)
	if err != nil {
		return fmt.Errorf("getChannelAssetsForOverviewOnlineStatus failed: %s", err)
	}

	var asset_ids []string
	for _, channel_asset := range channel_assets {
		key := genChannelAssetKey(channel_asset.ChannelType, channel_asset.AccountId, channel_asset.AssetId)
		asset_id, ok := key2asset_id[key]
		if !ok {
			continue
		}

		asset_ids = append(asset_ids, asset_id)
	}

	online_asset_id_set := make(map[string]bool)
	for _, asset_id := range online_asset_ids {
		online_asset_id_set[asset_id] = true
	}

	asset_ids = funk.UniqString(asset_ids)
	var new_asset_ids []string
	for _, asset_id := range asset_ids {
		if online_asset_id_set[asset_id] {
			continue
		}

		new_asset_ids = append(new_asset_ids, asset_id)
	}

	log.DebugContextf(ctx, "game %s get offline assets number: %d", game_code, len(new_asset_ids))

	if len(new_asset_ids) == 0 {
		return nil
	}

	// 更新impressions=0的数据
	asset_ids_join := strings.Join(new_asset_ids, "','")
	sql := fmt.Sprintf(`update arthub_sync.tb_creative_overview_%s
	set
	    online_days = case
	        when online_status = 1 then coalesce(online_days, 0) + date_part(
	            'day',
	            now() - to_date(offline_date, 'YYYY-MM-DD HH:MI:SS')
	        )
	        else online_days
	    end,
	    offline_date = case
	        when online_status = 1 then to_char(now(), 'YYYY-MM-DD HH:MI:SS')
	        else offline_date
	    end,
	    online_status_update_time = to_char(now(), 'YYYY-MM-DD HH:MI:SS'),
	    online_status = case
	        when coalesce(char_length(online_date), 0) = 0 then 0
	        else 2
	    end
	where
	    asset_id in ('%s');`, game_code, asset_ids_join)

	postdb := pgdb.GetDBWithContext(ctx)
	_, err = postdb.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update overview failed: %s", err)
		return err
	}

	return nil
}

// updateOverviewOnlineStatusClearOld 清理旧数据
//lint:ignore U1000 Ignore unused function temporarily for debugging
func updateOverviewOnlineStatusClearOld(ctx context.Context, game_code string) {
	clear_time := time.Now().AddDate(0, 0, -14).Format("2006-01-02 15:04:05")
	sql := fmt.Sprintf(`update
    arthub_sync.tb_creative_overview_%s
set
    online_days = coalesce(online_days, 0) + date_part(
        'day',
        now() - to_date(offline_date, 'YYYY-MM-DD HH:MI:SS')
    ),
    offline_date = to_char(now(), 'YYYY-MM-DD HH:MI:SS'),
    online_status_update_time = to_char(now(), 'YYYY-MM-DD HH:MI:SS'),
    online_status = 2
where
    online_status = 1
    and online_status_update_time < '%s';`, game_code, clear_time)

	postdb := pgdb.GetDBWithContext(ctx)
	_, err := postdb.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "update overview failed: %s", err)
		return
	}
}

// getChannelAssetsWithImpressionType 获取google在线素材列表, impression_type: 1表示大于impression>0; 2表示=0; 3表示不做限制
func getChannelAssetsWithImpressionType(ctx context.Context, game_code string, impression_type int) ([]channelAsset, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.FacebookRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(game_code))
	if impression_type == 1 {
		pg_query.Where("impressions>0")
	} else if impression_type == 2 {
		pg_query.Where("impressions=0")
	}
	today := time.Now().AddDate(0, 0, -1).Format("********") // 延迟一天以消除时差
	pg_query.Where("dtstatdate=?", today)
	pg_query.Where("account_id != '' and account_id is not null")
	pg_query.Where("asset_id != '' and asset_id is not null")
	select_columns := []string{"account_id", "asset_id", "asset_name"}
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)

	var fb_records []pgmodel.FacebookRealtimeAssetInfo
	err := pg_query.Select(&fb_records)
	if err != nil {
		log.ErrorContextf(ctx, "select facebook ad assets failed: %s", err)
		fb_records = nil
	}

	log.DebugContextf(ctx, "game %s get facebook ad assets number: %d, impression_type: %d", game_code, len(fb_records), impression_type)

	pg_query = pgdb.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	if impression_type == 1 {
		pg_query.Where("impressions>0")
	} else if impression_type == 2 {
		pg_query.Where("impressions=0")
	}
	pg_query.Where("dtstatdate=?", today)
	pg_query.Where("account_id != '' and account_id is not null")
	pg_query.Where("asset_id != '' and asset_id is not null")
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)

	var gg_records []pgmodel.FacebookRealtimeAssetInfo
	err = pg_query.Select(&gg_records)
	if err != nil {
		log.ErrorContextf(ctx, "select google ad assets failed: %s", err)
		gg_records = nil
	}

	log.DebugContextf(ctx, "game %s get google ad assets number: %d, impression_type: %d", game_code, len(gg_records), impression_type)

	var channel_assets []channelAsset
	for _, record := range fb_records {
		channel_asset := channelAsset{}
		channel_asset.AccountId = record.AccountId
		channel_asset.AssetId = record.AssetId
		channel_asset.ChannelType = 2 // facebook
		channel_asset.AssetName = record.AssetName

		channel_assets = append(channel_assets, channel_asset)
	}

	for _, record := range gg_records {
		channel_asset := channelAsset{}
		channel_asset.AccountId = record.AccountId
		channel_asset.AssetId = record.AssetId
		channel_asset.ChannelType = 1 // google
		channel_asset.AssetName = record.AssetName

		channel_assets = append(channel_assets, channel_asset)
	}

	return channel_assets, nil
}
