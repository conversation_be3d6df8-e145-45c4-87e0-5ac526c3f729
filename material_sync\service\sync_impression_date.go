package service

import (
	"context"
	"fmt"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	ckmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
	ckdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
)

// SyncImpressionDate ...
func SyncImpressionDate(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/SyncImpressionDate")

	// 这里加锁，避免同时去处理
	key := "cron/SyncImpressionDate"
	locked, _ := redis.GetMaterialSyncLock(ctx, key, 2*time.Hour)
	if !locked {
		log.WarningContextf(ctx, "SyncImpressionDate GetLock false")
		return
	}
	// 释放锁
	defer redis.DelMaterialSyncLock(ctx, key)

	start := time.Now()
	SyncImpressionDateLoop(ctx)
	log.InfoContextf(ctx, "SyncImpressionDate end, cost: %v", time.Since(start))
}

// SyncImpressionDateLoop ...
func SyncImpressionDateLoop(ctx context.Context) {
	log.DebugContextf(ctx, "SyncImpressionDateLoop start")

	st := time.Now()
	tasks := conf.GetBizConf().SyncImpressionDateHistorys

	date := time.Now().AddDate(0, 0, -1)
	for _, t := range tasks {
		if t.IfExecute {
			continue
		}

		syncImpressionDateOneDay(t.GameCode, date)
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "SyncImpressionDateLoop end, cost: %v", cost)
}

// syncImpressionDateOneDay 同步某游戏一天的数据
func syncImpressionDateOneDay(game_code string, date time.Time) {
	ctx := log.NewSessionIDContext()
	log.InfoContextf(ctx, "syncImpressionDateOneDay, game code: %s, date: %v", game_code, date)

	err := syncImpressionDateOneDayByChannelType(ctx, game_code, date, constant.MediaGoogle)
	if err != nil {
		log.ErrorContextf(ctx, "syncImpressionDateOneDayByChannelType failed: %s", err)
	}

	err = syncImpressionDateOneDayByChannelType(ctx, game_code, date, constant.MediaFacebook)
	if err != nil {
		log.ErrorContextf(ctx, "syncImpressionDateOneDayByChannelType failed: %s", err)
	}
}

// syncImpressionDateOneDayByChannelType 同步一天Google的数据
func syncImpressionDateOneDayByChannelType(ctx context.Context, game_code string, date time.Time, channel_type int) error {
	online_assets, err := getOnlineChannelAssetsGoogleByChannelType(ctx, game_code, date, channel_type)
	if err != nil {
		log.ErrorContextf(ctx, "getOnlineChannelAssetsGoogleByChannelType failed: %s", err)
		return err
	}

	existing_assets, err := getImpressionDateAssetsMapByChannelType(ctx, game_code, channel_type)
	if err != nil {
		log.ErrorContextf(ctx, "getImpressionDateAssetsMapGoogle failed: %s", err)
		return err
	}

	var new_assets []*channelAsset
	for _, a := range online_assets {
		k := genChannelAssetKeyByStruct(a)
		if existing_assets[k] {
			continue
		}

		new_assets = append(new_assets, a)
	}

	err = insertImpressionDateByChannelAssets(ctx, game_code, new_assets, date)
	if err != nil {
		log.ErrorContextf(ctx, "insertImpressionDateByChannelAssets failed: %s", err)
		return err
	}

	return nil
}

func getOnlineChannelAssetsGoogleByChannelType(ctx context.Context, game_code string, date time.Time, channel_type int) ([]*channelAsset, error) {
	switch channel_type {
	case constant.MediaGoogle:
		return getOnlineChannelAssetsGoogle(ctx, game_code, date)
	case constant.MediaFacebook:
		return getOnlineChannelAssetsFacebook(ctx, game_code, date)
	}

	return nil, fmt.Errorf("get unknown channel type: %d", channel_type)
}

// insertImpressionDateByChannelAssets 新增impression date
func insertImpressionDateByChannelAssets(ctx context.Context, game_code string, assets []*channelAsset, date time.Time) error {
	if len(assets) == 0 {
		return nil
	}

	var impression_dates []*ckmodel.ImpressionDate
	author := "material_sync.sync_impression_date"
	time_now := time.Now().Format("2006-01-02 15:04:05")
	date_str := date.Format("2006-01-02")
	for _, a := range assets {
		d := ckmodel.ImpressionDate{}
		d.GameCode = game_code
		d.ChannelType = a.ChannelType
		d.ChannelAccountID = a.AccountId
		d.ChannelAssetId = a.AssetId
		d.ImpressionDate = date_str
		d.CreateBy = author
		d.CreateTime = time_now
		d.UpdateBy = author
		d.UpdateTime = time_now
		impression_dates = append(impression_dates, &d)
	}

	ckQuery := ckdb.GetGORMDBWithGameCode(ctx, game_code).Table(ckmodel.GetImpressionDateTableName(conf.GetBizConf().ClickhouseEnv))
	result := ckQuery.Create(&impression_dates)

	return result.Error
}

// getImpressionDateAssetsMapByChannelType 获取已经上线素材集合
func getImpressionDateAssetsMapByChannelType(ctx context.Context, game_code string, channel_type int) (map[string]bool, error) {
	channel_assets, err := getImpressionDateAssetsByChannelType(ctx, game_code, channel_type)
	if err != nil {
		log.ErrorContextf(ctx, "getImpressionDateAssetsGoogle failed: %s", err)
		return nil, err
	}

	m := make(map[string]bool)
	for _, asset := range channel_assets {
		key := genChannelAssetKeyByStruct(asset)
		m[key] = true
	}

	return m, nil
}

// genChannelAssetKeyByStruct ...
func genChannelAssetKeyByStruct(asset *channelAsset) string {
	return genChannelAssetKey(asset.ChannelType, asset.AccountId, asset.AssetId)
}

// getImpressionDateAssetsByChannelType 获取Google上线素材
func getImpressionDateAssetsByChannelType(ctx context.Context, game_code string, channel_type int) ([]*channelAsset, error) {
	ck_query := ckdb.GetGORMDBWithGameCode(ctx, game_code).Table(ckmodel.GetImpressionDateTableName(conf.GetBizConf().ClickhouseEnv))
	var ck_records []ckmodel.ImpressionDate
	res := ck_query.Select([]string{"channel_type", "channel_account_id", "channel_asset_id"}).Where("game_code=? AND channel_type=?", game_code, channel_type).Find(&ck_records)
	if res.Error != nil {
		log.ErrorContextf(ctx, "query ck failed: %s", res.Error)
		return nil, res.Error
	}

	var assets []*channelAsset
	for _, record := range ck_records {
		var asset channelAsset
		asset.ChannelType = record.ChannelType
		asset.AccountId = record.ChannelAccountID
		asset.AssetId = record.ChannelAssetId
		assets = append(assets, &asset)
	}

	return assets, nil
}
