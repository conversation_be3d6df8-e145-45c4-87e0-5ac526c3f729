package service

import (
	"context"
	"fmt"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// SyncChannelAssetLabelForGameCode 同步渠道素材的标签信息
func SyncChannelAssetLabelForGameCode(game_code string, key2asset_id map[string]string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncChannelAssetLabelForGameCode %s start", game_code)

	st := time.Now()

	asset_id2label2, err := getAssetLabelMap(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getAssetLabelMap failed: %s", err)
		return
	}

	channel_assets, err := getChannelAssetsWithImpressionType(ctx, game_code, 3)
	if err != nil {
		log.ErrorContextf(ctx, "getChannelAssetsForOverviewOnlineStatus failed: %s", err)
		return
	}

	var channel_asset_labels []pgmodel.ChannelAssetLabel
	for _, channel_asset := range channel_assets {
		key := genChannelAssetKey(channel_asset.ChannelType, channel_asset.AccountId, channel_asset.AssetId)
		asset_id, ok := key2asset_id[key]
		if !ok {
			continue
		}

		labels, ok := asset_id2label2[asset_id]
		if !ok {
			continue
		}

		inner_channel_asset_labels := genChannelAssetLabelsFromLabels(channel_asset, labels)
		channel_asset_labels = append(channel_asset_labels, inner_channel_asset_labels...)
	}

	log.DebugContextf(ctx, "get channel asset labels number: %d", len(channel_asset_labels))

	author := "material_sync.sync_channel_asset_label"
	err = upsertChannelAssetLabelsWithAuthor(ctx, game_code, author, channel_asset_labels)
	if err != nil {
		log.ErrorContextf(ctx, "upsertChannelAssetLabels failed: %s", err)
		return
	}

	cost := time.Since(st)
	log.InfoContextf(ctx, "SyncChannelAssetLabelForGameCode %s end, cost: %v", game_code, cost)
}

// genChannelAssetLabelsFromLabels 从标签中生成广告素材标签
func genChannelAssetLabelsFromLabels(channel_asset channelAsset, labels []pgmodel.AssetLabel) []pgmodel.ChannelAssetLabel {
	var channel_asset_labels []pgmodel.ChannelAssetLabel
	var channel_asset_label pgmodel.ChannelAssetLabel
	channel_asset_label.ChannelType = channel_asset.ChannelType
	channel_asset_label.ChannelAssetId = channel_asset.AssetId
	channel_asset_label.ChannelAccountID = channel_asset.AccountId
	channel_asset_label.AssetName = channel_asset.AssetName
	for _, label := range labels {
		if len(label.SecondLabel) == 0 || label.SecondLabel == "-" {
			continue
		}

		channel_asset_label.LabelName = label.LabelName
		channel_asset_label.FirstLabel = label.FirstLabel
		channel_asset_label.SecondLabel = label.SecondLabel

		channel_asset_labels = append(channel_asset_labels, channel_asset_label)
	}

	return channel_asset_labels
}

// getAssetLabelMap 获取素材到素材标签映射表
func getAssetLabelMap(ctx context.Context, game_code string) (map[string][]pgmodel.AssetLabel, error) {
	var records []pgmodel.AssetLabel
	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(pgmodel.GetAssetLabelTableName(game_code))
	pg_query.Column("asset_id", "label_name", "first_label", "second_label")
	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select asset label failed: %s", err)
	}

	log.DebugContextf(ctx, "get asset labels number: %d", len(records))

	id2labels := make(map[string][]pgmodel.AssetLabel)
	for _, record := range records {
		id2labels[record.AssetId] = append(id2labels[record.AssetId], record)
	}

	return id2labels, nil
}

// upsertChannelAssetLabelsWithAuthor 插入/更新广告素材标签
func upsertChannelAssetLabelsWithAuthor(ctx context.Context, game_code string, author string, channel_asset_labels []pgmodel.ChannelAssetLabel) error {
	if len(channel_asset_labels) == 0 {
		return nil
	}

	time_now := time.Now().Format("2006-01-02 15:04:05")
	new_channel_asset_labels := make([]pgmodel.ChannelAssetLabel, 0, len(channel_asset_labels))
	for _, label := range channel_asset_labels {
		label.CreateBy = author
		label.UpdateBy = author
		label.CreateTime = time_now
		label.UpdateTime = time_now

		new_channel_asset_labels = append(new_channel_asset_labels, label)
	}

	table_name := pgmodel.GetChannelAssetLabelTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&new_channel_asset_labels).Table(table_name)
	pg_query.OnConflict("(channel_type, channel_account_id, channel_asset_id, label_name, first_label, second_label, asset_name) do update")
	pg_query.Set("label_name=excluded.label_name")
	pg_query.Set("first_label=excluded.first_label")
	pg_query.Set("second_label=excluded.second_label")
	pg_query.Set("update_by=excluded.update_by")
	pg_query.Set("update_time=excluded.update_time")

	_, err := pg_query.Insert()
	if err != nil {
		return fmt.Errorf("insert channel asset label failed: %s", err)
	}

	return nil
}
