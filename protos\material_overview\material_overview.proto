syntax = "proto3";

package material_overview;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/material_overview";

import "aix/aix_common_message.proto";

// DateNode 图标数据节点
message DateNode {
    int32 date = 1;  // 日期，型如 20220210
    double num = 2;  // 浮点数据
}

// Country 国家
message Country {
    string country_code = 1;  // 国家缩写，用于请求
    string en_name      = 2;  // 国家英文名
    string ch_name      = 3;  // 国家中文名
}

// 获取顶部汇总数据, POST, /api/v1/material_overview/general_data
message GeneralDataReq {
}

message GeneralDataRsp {
    aix.Result result                     = 1;  // 返回结果
    int32 iegg_total_material             = 2;  // iegg素材库总量
    repeated DateNode online_num_list     = 3;  // 素材全球在线数量折线 日期从大到小排序
    repeated DateNode impression_num_list = 4;  // 素材总曝光量折线 日期从大到小排序
}

// 较上周新增-下降数据, POST, /api/v1/material_overview/incr_decr_data
message IncrDecrDataReq {
    repeated string country = 1;  // 国家筛选列表，全球不用传
}

message IncrDecrDataRsp {
    aix.Result result             = 1;  // 返回结果
    int32 iegg_total_material     = 2;  // iegg素材库总量
    repeated DateNode incr_num    = 3;  // 较上周新增折线 日期从大到小排序
    repeated DateNode decr_num    = 4;  // 较上周下架折线 日期从大到小排序
    repeated Country country_list = 5;  // 国家下拉列表
}

// DataItem 数据节点
message DataItem {
    string name       = 1;  // 数据节点名称
    int32 data_type   = 2;  // 数据类型 0-整数 1-float
    int32 int_data    = 3;  // int数据, data_type = 0 时有效
    double float_data = 4;  // float数据, data_type = 1 时有效
}

// 素材主题分析, POST, /api/v1/material_overview/asset_theme_analyze
message AssetThemeAnalyzeReq {
    uint32 label_type = 1;  // 请求的素材主题类型 1-人工标签 2-机器标签
}

message ChartItem {
    string name                 = 1;  // 表横坐标名称
    repeated DataItem data_list = 2;  // 数据类型列表
}

message AssetThemeAnalyzeRsp {
    aix.Result result             = 1;  // 返回结果
    repeated ChartItem label_list = 2;  // 标签列表 data_list名称列表: num-素材数量 ctr-点击率 cvr-转化率
}

// 素材观看时长分析, POST, /api/v1/material_overview/video_played_time_analysis
message VideoPlayedTimeAnalysisReq {
    repeated string country_code_list = 1;  // 国家筛选列表，空列表表示全部国家
    repeated string language_list     = 2;  // 语言列表, 空列表表示全部语言
    repeated int32 need_data_list     = 3;  // 需要的数据, 1表示watch_count_distribution, 2表示country_watch_count_distribution, 3表示country_watch_duration
}

message VideoPlayedTimeAnalysisRsp {
    aix.Result result                                   = 1;  // 返回结果
    repeated ChartItem watch_count_distribution         = 2;  // 不同观看时长人数分布
    repeated ChartItem country_watch_count_distribution = 3;  // 对比分析, 即不同国家不同观看时长人数分布
    repeated ChartItem country_watch_duration           = 4;  // 对比分析, 不同国家观看素材时长分布
}

/*

// analysis_pivot元数据
message AnalysisPivotMeta {
    int32  count        = 1;   // group by 项目的 count
    int64  dtstatdate   = 2;   // 时间
    string country_code = 3;   // 国家代码
    string asset_type   = 4;   // 素材类型
    string lang         = 5;   // 语言
    string show_network = 6;   // 渠道
    int32  impressions  = 7;   //
    int64  clicks       = 8;   //
    int64  conversions  = 9;   //
    double spend        = 10;  //
    double installs     = 11;  //
}

// where
message AnalysisPivotWhere {
    string   name           = 1; // 字段名
    int32    upper          = 2; // 最大值，type为2时适用
    int32    lower          = 3; // 最小值，type为2时适用
    repeated string in_list = 4; // 列表值，type为1时适用
    string   like           = 5; // 模糊匹配值，type为3时适用
    int32    type           = 6; // 1-in多个值任一匹配 2-range范围内查找 3-like模糊匹配
    int32    is_upper_equal = 7; // 最大值是否可等于，0不等于 1等于，type为2时适用
    int32    is_lower_equal = 8; // 最小值是否可等于，0不等于 1等于，type为2时适用
}

// order by
message OrderBy {
    string by    = 1;  // 字段名
    string order = 2;  // 'desc' 'asc'
}

// analysis_pivot列表请求, POST, /api/v1/material_overview/analysis_pivot_list
message AnalysisPivotListReq {
    repeated string              group       = 1; // 聚合字段
    repeated AnalysisPivotWhere  where       = 2; // 筛选条件
    uint32                       pageIndex   = 3; // 起始偏移
    uint32                       pageSize    = 4; // 拉取数量
    repeated string              metric      = 5; // 需要的字段
    repeated OrderBy             orderby     = 6; // order by
}

message AnalysisPivotListRsp {
    aix.Result result                   = 1; // 返回结果
    repeated   AnalysisPivotMeta pivots = 2; // 列表
}
*/
