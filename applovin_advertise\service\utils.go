package service

import (
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/applovin"
)

// newApplovinTokenClient 生成applovin token client
// 需要传入client_id, client_secret
func newApplovinTokenClient(a *model.MediaAccount) *applovin.Client {
	c := &applovin.Config{
		AccountID:    a.AccountId,
		ClientID:     a.ApplovinAccountConfig.ClientID,
		ClientSecret: a.ApplovinAccountConfig.ClientSecret,
	}

	return applovin.NewClient(c)
}

// newApplovinAPIClient 生成applovin api client
// 需要传入access_token
func newApplovinAPIClient(a *model.MediaAccount) *applovin.Client {
	c := &applovin.Config{
		AccountID:   a.AccountId,
		AccessToken: a.ApplovinAccountConfig.Authorization.AccessToken,
	}

	return applovin.NewClient(c)
}
