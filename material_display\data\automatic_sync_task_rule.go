package data

import (
	"context"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// InsertAutomaticSyncTaskRule 插入自动化上传任务规则
func InsertAutomaticSyncTaskRule(ctx context.Context, taskRule *pb.AutomaticSyncTaskRule) error {
	t := pbAutomaticSyncTaskRule2DB(taskRule)
	err := repo.InsertAutomaticSyncTaskRule(ctx, t)
	if err != nil {
		return err
	}

	taskRule.Id = t.ID
	return nil
}

// UpdateAutomaticSyncTaskRuleFields 更新自动化上传任务规则指定字段
func UpdateAutomaticSyncTaskRuleFields(ctx context.Context, taskRule *pb.AutomaticSyncTaskRule, fields []string) error {
	t := pbAutomaticSyncTaskRule2DB(taskRule)
	return repo.UpdateAutomaticSyncTaskRuleFields(ctx, t, fields)
}

// GetAutomaticSyncTaskRule 获取自动化上传任务规则, 无数据返回nil,nil
func GetAutomaticSyncTaskRule(ctx context.Context, id int64) (*pb.AutomaticSyncTaskRule, error) {
	row, err := repo.GetAutomaticSyncTaskRule(ctx, id)
	if err != nil {
		return nil, err
	}
	if row == nil {
		return nil, nil
	}

	t := dbAutomaticSyncTaskRule2PB(row)
	return t, nil
}

// DelAutomaticSyncTaskRule 删除游戏下的自动化上传任务规则
func DelAutomaticSyncTaskRule(ctx context.Context, gameCode string) error {
	query := postgresql.GetDBWithContext(ctx).Model(&model.AutomaticSyncTaskRule{})
	query.Where("game_code = ?", gameCode)
	query.Set("deleted = ?", true) // 软删除
	query.Set("update_time = ?", time.Now())
	_, err := query.Update()
	return err
}

// FilterAutomaticSyncTaskRule 过滤查询自动化上传任务规则
func FilterAutomaticSyncTaskRule(ctx context.Context, filter *repo.AutomaticSyncTaskRuleFilter) ([]*pb.AutomaticSyncTaskRule, int, error) {
	dbRows, total, err := repo.FilterAutomaticSyncTaskRule(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	var rlt []*pb.AutomaticSyncTaskRule
	for _, row := range dbRows {
		t := dbAutomaticSyncTaskRule2PB(row)
		rlt = append(rlt, t)
	}
	return rlt, total, nil
}

// pb结构转成db结构
func pbAutomaticSyncTaskRule2DB(t *pb.AutomaticSyncTaskRule) *model.AutomaticSyncTaskRule {
	d := &model.AutomaticSyncTaskRule{
		ID:         t.GetId(),
		GameCode:   t.GetGameCode(),
		Name:       t.GetName(),
		Status:     t.GetStatus(),
		EndDate:    t.GetEndDate(),
		AssetType:  t.GetAssetType(),
		CreateUser: t.GetCreateUser(),
		CreateTime: time.Now(),
		UpdateUser: t.GetUpdateUser(),
		UpdateTime: time.Now(),
	}
	d.StatusChangeTime, _ = utils.ParseUTCTime(t.GetStatusChangeTime())
	d.StartCloudUploadTime, _ = utils.ParseUTCTime(t.GetStartCloudUploadTime())

	for _, taskDir := range t.GetDirs() {
		tmp := model.SyncDir{
			ID:            taskDir.GetId(),
			Name:          taskDir.GetName(),
			FullPathName:  taskDir.GetFullPathName(),
			IncludeSubDir: taskDir.GetIncludeSubDir(),
		}
		d.SyncDirInfo.Dirs = append(d.SyncDirInfo.Dirs, tmp)
	}

	for _, syncMedia := range t.GetMedias() {
		tmp := model.SyncMedia{
			Channel:  int(syncMedia.GetChannel()),
			Accounts: syncMedia.GetAccounts(),
			Language: syncMedia.GetLanguage(),
		}
		d.SyncMediaInfo.Medias = append(d.SyncMediaInfo.Medias, tmp)
	}
	return d
}

// db结构转成pb结构
func dbAutomaticSyncTaskRule2PB(row *model.AutomaticSyncTaskRule) *pb.AutomaticSyncTaskRule {
	t := &pb.AutomaticSyncTaskRule{
		Id:                   row.ID,
		GameCode:             row.GameCode,
		Name:                 row.Name,
		Status:               row.Status,
		EndDate:              row.EndDate,
		AssetType:            row.AssetType,
		CreateUser:           row.CreateUser,
		CreateTime:           utils.FormatDefault(OrNormalizeUTCTime(row.CreateTime)),
		UpdateUser:           row.UpdateUser,
		UpdateTime:           utils.FormatDefault(OrNormalizeUTCTime(row.UpdateTime)),
		StatusChangeTime:     utils.FormatDefault(OrNormalizeUTCTime(row.StatusChangeTime)),
		StartCloudUploadTime: utils.FormatDefault(row.StartCloudUploadTime.UTC()), // UTC时区
	}

	for _, dir := range row.SyncDirInfo.Dirs {
		t.Dirs = append(t.Dirs, &pb.SyncTaskDir{
			Id:            dir.ID,
			Name:          dir.Name,
			FullPathName:  dir.FullPathName,
			IncludeSubDir: dir.IncludeSubDir,
		})
	}
	for _, media := range row.SyncMediaInfo.Medias {
		t.Medias = append(t.Medias, &pb.SyncTaskMedia{
			Channel:  int32(media.Channel),
			Accounts: media.Accounts,
			Language: media.Language,
		})
	}

	return t
}
