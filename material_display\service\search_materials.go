// Package service 服务接口实现代码
package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	baseConstant "e.coding.intlgame.com/ptc/aix-backend/common/constant"
	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/constant"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/go-pg/pg/v10/orm"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

// CheckSearchMaterials ...
func CheckSearchMaterials(ctx context.Context, req *pb.SearchMaterialsReq) error {
	if len(req.GetNames()) > 100 {
		return errs.New(constant.ERR_PARAM, "The number of creatives you search exceeds 100. You can search maximize 100 creatives at one time.")
	}

	// 格式 2021-01-01
	if req.GetStartCreateDate() != "" {
		if _, err := time.Parse(utils.DefaultTimeFormatDate, req.GetStartCreateDate()); err != nil {
			return errs.New(constant.ERR_PARAM, "The start create date format is incorrect.")
		}
	}
	if req.GetEndCreateDate() != "" {
		if _, err := time.Parse(utils.DefaultTimeFormatDate, req.GetEndCreateDate()); err != nil {
			return errs.New(constant.ERR_PARAM, "The end create date format is incorrect.")
		}
	}
	return nil
}

// SearchMaterials 拉取素材列表
func SearchMaterials(ctx *gin.Context, req *pb.SearchMaterialsReq, rsp *pb.SearchMaterialsRsp) error {
	// 参数校验
	if err := CheckSearchMaterials(ctx, req); err != nil {
		return err
	}

	// 搜索素材
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	depot, err := data.GetDepotWithContext(ctx, gameCode)
	if err != nil {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error data.GetDepot, err: %v", err)
	}
	source := &searchMaterialsSource{}
	source.Offset = req.GetOffset()
	source.Count = req.GetCount()
	source.Text = req.GetText()
	source.Status = req.GetStatus()
	source.DirectoryID = req.GetDirectoryId()
	source.GameCode = gameCode
	source.UploadState = req.GetUploadState()
	source.SearchType = req.GetSearchType()
	source.Names = funk.UniqString(req.GetNames())
	source.FilterOnlineStatus = req.GetFilterOnlineStatus()
	source.OnlineStatus = req.GetOnlineStatus()
	source.Depot = depot
	source.IsAd = req.GetIsAd()
	source.AssetIDs = req.GetAssetIds()
	source.AixUploader = req.GetAixUploader()
	source.Labels = req.GetLabels()
	source.LabelsSearchType = req.GetLabelsSearchType()
	source.AssetFieldFilters = req.GetAssetFieldFilters()
	source.Media = req.GetMedia()
	source.FormatType = req.GetFormatType()
	source.AssetRatios = req.GetAssetRatios()
	source.SyncMediaFilter = req.GetSyncMediaFilter()
	source.SyncMediaList = req.GetSyncMediaList()
	// 不为空，过滤 >= 上传网盘时间, 格式为 2021-01-01
	if req.GetStartCreateDate() != "" {
		// 转为开始时间 时分秒
		source.StartCreateDate = req.GetStartCreateDate() + " 00:00:00"
	}
	// 不为空，过滤 <= 上传网盘时间, 格式为 2021-01-01
	if req.GetEndCreateDate() != "" {
		// 转为结束时间 时分秒
		source.EndCreateDate = req.GetEndCreateDate() + " 23:59:59"
	}
	source.FormatTypeList = req.GetFormatTypeList()

	total, overviews, err := searchMaterials(ctx, source)
	if err != nil {
		return err
	}

	// 补充相关信息
	overviews = correctFullPath(ctx, gameCode, depot.DepotId, overviews)
	rsp.Materials = fillMeterialMetas(ctx, req.GetDirectoryId(), arthubOverViews2MaterialMetas(ctx, overviews), depot.Type, source.GameCode, depot.ArthubCode)
	rsp.Total = total

	assetMap := make(map[string]*pb.MaterialMeta)
	assetIDList := []string{}
	for _, asset := range rsp.GetMaterials() {
		assetIDList = append(assetIDList, asset.GetAssetId())
		assetMap[asset.GetAssetId()] = asset
	}
	if req.GetWithDetail() > 0 {
		detailMap, err := data.BtGetMaterialDetail(ctx, source.GameCode, assetIDList)
		if err != nil {
			log.ErrorContextf(ctx, "error data.BtGetMaterialDetail, err: %v", err)
			return err
		}
		for _, asset := range rsp.GetMaterials() {
			asset.MaterialExt = detail2proto(ctx, detailMap[cast.ToString(asset.AssetId)])
		}
	}

	// 查询这些素材的上传了哪些渠道
	uploads, err := data.BatchGroupCreativeMediaUploadByAssetIds(ctx, gameCode, assetIDList)
	if err != nil {
		log.ErrorContextf(ctx, "data.BatchGroupCreativeMediaUploadByAssetIds, err: %v", err)
		return err
	}
	for _, upload := range uploads {
		if a, ok := assetMap[upload.AssetId]; ok {
			a.SyncMediaList = append(a.SyncMediaList, &pb.SyncMedia{
				Channel: cast.ToInt32(upload.Channel),
			})
		}
	}

	return nil
}

// 搜索素材列表依赖结构体
type searchMaterialsSource struct {
	Offset             uint32                 // 偏移
	Count              uint32                 // 请求数量
	Text               string                 // 搜索文本
	Status             uint32                 // 素材状态
	DirectoryID        string                 // 目录ID
	GameCode           string                 // 游戏
	UploadState        uint32                 // 上传状态
	OnlineStatus       uint32                 // 在线状态
	FilterOnlineStatus bool                   // 是否过滤在线状态
	SearchType         int32                  // 搜索类型, 1-搜索单个名称(模糊搜索); 2-搜索多个名称(精确搜索)
	Names              []string               // 搜索名称
	Depot              *pgmodel.ArthubDepot   // 仓库信息
	IsAd               bool                   // 是否是搜索广告素材库
	AssetIDs           []string               // 批量查询的素材ID列表
	AixUploader        string                 // aix平台的上传者
	Labels             []*pb.AssetLabel       // 素材标签
	LabelsSearchType   int32                  // 素材标签搜索方式
	AssetFieldFilters  []*pb.AssetFieldFilter // 素材字段筛选条件
	Media              uint32                 // 1-google 目前只用到google
	FormatType         uint32                 // 是否类型，0-全部，1-视频，2-图片
	AssetRatios        []string               // 需要筛选的素材比例, 不填默认不进行筛选
	SyncMediaFilter    int32                  // 0-不过滤，1-未上传渠道，2-已上传渠道
	SyncMediaList      []int32                // sync_media_filter=2有效，空为只要上传过就行
	StartCreateDate    string
	EndCreateDate      string
	FormatTypeList     []int32
}

// searchMaterials ...
func searchMaterials(ctx context.Context, source *searchMaterialsSource) (uint32, []*pgmodel.CreativeOverview, error) {
	pgQuery := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeOverview{})
	pgQuery.Table(pgmodel.GetCreativeOverviewTableName(source.GameCode))
	switch source.SearchType {
	case 1: // 模糊搜索单个名称
		if len(source.Text) > 0 {
			words := strings.Split(source.Text, " ")
			if len(words) == 1 {
				pgQuery.Where("asset_name ilike ?", fmt.Sprintf("%%%s%%", source.Text))
			} else {
				pgQuery = pgQuery.Where(fmt.Sprintf("arthub_sync.split_asset_name(asset_name) @> arthub_sync.split_asset_name('%s')", source.Text))
			}
		}
	case 2: // 素材名字只要包含一个关键字即可
		if len(source.Names) > 0 {
			pgQuery = textSearchMultipleAssetName(ctx, pgQuery, source.Names, true)
		}
	case 3: // 批量查询asset id
		if len(source.AssetIDs) > 0 {
			pgIn := pg.In(source.AssetIDs)
			pgQuery.Where("asset_id in (?)", pgIn)
		}
	case 4: // 素材名字需要包含所有的关键字
		if len(source.Names) > 0 {
			pgQuery = textSearchMultipleAssetName(ctx, pgQuery, source.Names, false)
		}
	default:
		return 0, nil, errs.New(constant.ERR_PARAM, "get unknown search type")
	}

	if source.IsAd { // 搜索广告库
		ids, err := data.ListAssetIdInMediaMaterial(source.GameCode, source.DirectoryID, 0, 0, true)
		if err != nil {
			log.ErrorContextf(ctx, "error data.ListAssetIdInMediaMaterial, gamecode: %v, "+
				"directoryID: %v, err: %v", source.GameCode, source.DirectoryID, err)
			return 0, nil, err
		}
		ids = funk.UniqString(ids)
		if len(ids) == 0 {
			log.InfoContextf(ctx, "data.ListAssetIdInMediaMaterial ids is 0, gamecode: %v, "+
				"directoryID: %v", source.GameCode, source.DirectoryID)
			return 0, nil, nil
		}
		pgIn := pg.In(ids)
		pgQuery.Where("asset_id in (?)", pgIn)
	} else { // 搜索素材库
		directoryID := source.DirectoryID
		if len(directoryID) == 0 {
			directoryID = source.Depot.DepotId
		}

		pathPrefix, err := getPathPrefix(ctx, source.GameCode, directoryID)
		if err != nil {
			return 0, nil, fmt.Errorf("getPathPrefix failed: %s", err)
		}
		pgQuery.Where("full_path_id like ?", fmt.Sprintf("%s%%", pathPrefix))
	}

	if source.FilterOnlineStatus {
		if source.OnlineStatus == 0 {
			pgQuery.Where("(online_status is null or online_status=0)")
		} else {
			pgQuery.Where("online_status=?", source.OnlineStatus)
		}
	}

	if len(source.AixUploader) > 0 {
		pgQuery.Where("aix_uploader=?", source.AixUploader)
	}

	if len(source.Labels) > 0 {
		// 切换到新标签体系，label_name固定为空
		// 使用子查询
		subQuery := postgresql.GetDBWithContext(ctx).Model(&pgmodel.AssetLabel{})
		subQuery.Table(pgmodel.GetAssetLabelTableName(source.GameCode))
		subQuery.Column("asset_id")
		if source.LabelsSearchType == 2 {
			// 交集，一个标签是一行，所以先or查出来，看个数是否和期望的标签个数相等
			for _, label := range source.Labels {
				subQuery.WhereOr("label_name = ? and first_label = ? and second_label = ?", "", label.GetFirstLabel(), label.GetSecondLabel())
			}
			subQuery.Group("asset_id").Having("count(asset_id) = ?", len(source.Labels))
		} else {
			// 并集
			for _, label := range source.Labels {
				subQuery.WhereOr("label_name = ? and first_label = ? and second_label = ?", "", label.GetFirstLabel(), label.GetSecondLabel())
			}
			subQuery.Group("asset_id")
		}

		pgQuery.Where("asset_id in (?)", subQuery)
	}

	for _, filter := range source.AssetFieldFilters {
		pgQuery.WhereGroup(func(q *pg.Query) (*pg.Query, error) {
			if len(filter.GetName()) == 0 || len(filter.GetValues()) == 0 {
				return q, nil
			}

			sql := fmt.Sprintf("%s in (?)", filter.GetName())
			in := pg.In(filter.GetValues())
			q = q.WhereOr(sql, in)
			return q, nil
		})
	}

	if source.FormatType > 0 {
		pgQuery.Where("format_type = ?", source.FormatType)
	}

	if source.Media == baseConstant.MediaGoogle && len(source.AssetRatios) > 0 {
		// google渠道要特殊处理, 需要过滤指定图片asset_ratios
		joinTable := pgmodel.GetCreativeAssetDetailsTableName(source.GameCode)
		pgQuery.Join(fmt.Sprintf("left join (SELECT asset_id, case when high > 0 then round(width::numeric/high::numeric, 2) else 0 end as asset_ratio from %s) as creative_detail", joinTable))
		pgQuery.JoinOn("creative_overview.asset_id = creative_detail.asset_id")

		if source.FormatType == 0 {
			pgQuery.WhereGroup(func(sub *pg.Query) (*pg.Query, error) {
				sub.WhereIn("asset_ratio in (?)", source.AssetRatios)
				sub.WhereOr("format_type = ?", baseConstant.AssetVideoType)
				return sub, nil
			})
		} else {
			pgQuery.WhereIn("asset_ratio in (?)", source.AssetRatios)
		}
	}

	// 0-不过滤，1-未上传渠道，2-已上传渠道
	if source.SyncMediaFilter == 1 {
		subQ := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeMediaUpload{})
		subQ.ColumnExpr("distinct asset_id")
		subQ.Where("game_code = ?", source.GameCode)
		pgQuery.Where("asset_id not in (?)", subQ)
	} else if source.SyncMediaFilter == 2 {
		subQ := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeMediaUpload{})
		subQ.ColumnExpr("distinct asset_id")
		subQ.Where("game_code = ?", source.GameCode)
		if len(source.SyncMediaList) > 0 {
			subQ.WhereIn("channel in (?)", source.SyncMediaList)
		}
		pgQuery.Where("asset_id in (?)", subQ)
	}

	if source.StartCreateDate != "" {
		pgQuery.Where("create_date >= ?", source.StartCreateDate)
	}
	if source.EndCreateDate != "" {
		pgQuery.Where("create_date <= ?", source.EndCreateDate)
	}
	if len(source.FormatTypeList) > 0 {
		pgQuery.WhereIn("format_type in (?)", source.FormatTypeList)
	}

	pgQuery.Where("asset_status=?", arthub.ARTHUB_ASSET_STATUS_NORMAL)
	pgQuery.Order("updated_date desc")
	pgQuery.Order("creative_overview.asset_id")
	pgQuery.OrderExpr("random()")

	pgQuery.Limit(int(source.Count))
	pgQuery.Offset(int(source.Offset))

	var overviews []*pgmodel.CreativeOverview
	total, err := pgQuery.SelectAndCount(&overviews)
	if err != nil {
		return 0, nil, errs.New(constant.ERR_SYS, fmt.Sprintf("select overview failed: %s", err))
	}

	return uint32(total), overviews, nil
}

// searchAssetIDsByLabel 搜索素材标签
func searchAssetIDsByLabel(ctx context.Context, gameCode string, labels []*pb.AssetLabel, labelsSearchType int32) ([]string, error) {
	switch labelsSearchType {
	case 1: // 并集
		return searchAssetIDsByLabelUnion(ctx, gameCode, labels)
	case 2: // 交集
		return searchAssetIDsByLabelInter(ctx, gameCode, labels)
	}

	// 默认搜索方式
	return searchAssetIDsByLabelUnion(ctx, gameCode, labels)
}

// searchAssetIDsByLabelUnion 标签搜索取并集
func searchAssetIDsByLabelUnion(ctx context.Context, gameCode string, labels []*pb.AssetLabel) ([]string, error) {
	var assetLabels []pgmodel.AssetLabel
	tableName := pgmodel.GetAssetLabelTableName(gameCode)
	pgQuery := postgresql.GetDBWithContext(ctx).Model(&assetLabels).Table(tableName)
	pgQuery.Column("asset_id")
	for _, label := range labels {
		pgQuery.WhereOrGroup(func(q *pg.Query) (*pg.Query, error) {
			// 新标签体系label_name为空
			q = q.Where("label_name=?", "")
			q = q.Where("first_label=?", label.FirstLabel)
			q = q.Where("second_label=?", label.SecondLabel)

			return q, nil
		})
	}

	var assetIds []string
	err := pgQuery.Select(&assetIds)
	if err != nil {
		return nil, fmt.Errorf("select asset labels failed: %s", err)
	}

	assetIds = funk.UniqString(assetIds)

	return assetIds, nil
}

// searchAssetIDsByLabelInter 标签搜索取交集
func searchAssetIDsByLabelInter(ctx context.Context, gameCode string, labels []*pb.AssetLabel) ([]string, error) {
	var assetLabels []pgmodel.AssetLabel
	tableName := pgmodel.GetAssetLabelTableName(gameCode)
	pgQuery := postgresql.GetDBWithContext(ctx).Model(&assetLabels).Table(tableName)
	pgQuery.Column("asset_id")
	for _, label := range labels {
		pgQuery.WhereOrGroup(func(q *pg.Query) (*pg.Query, error) {
			// 新标签体系label_name为空
			q = q.Where("label_name=?", "")
			q = q.Where("first_label=?", label.FirstLabel)
			q = q.Where("second_label=?", label.SecondLabel)

			return q, nil
		})
	}
	pgQuery.Group("asset_id")
	pgQuery.Having("count(asset_id)=?", len(labels))

	var assetIds []string
	err := pgQuery.Select(&assetIds)
	if err != nil {
		return nil, fmt.Errorf("select asset labels failed: %s", err)
	}

	assetIds = funk.UniqString(assetIds)

	return assetIds, nil
}

// textSearchMultipleAssetName 文本搜索多行素材,isUnion:true 表示只要有一个关键字即可， false表示需要包含所有关键字
func textSearchMultipleAssetName(ctx context.Context, pgQuery *orm.Query, textList []string, isUnion bool) *orm.Query {
	pgQuery.WhereGroup(func(q *pg.Query) (*pg.Query, error) {
		keywords := []string{}
		for _, text := range textList {
			keywords = append(keywords, fmt.Sprintf("%%%v%%", text))
		}

		if isUnion {
			q = q.Where("asset_name ILIKE ANY(?)", pg.Array(keywords))
		} else {
			q = q.Where("asset_name ILIKE ALL(?)", pg.Array(keywords))
		}
		return q, nil
	})

	return pgQuery
}
