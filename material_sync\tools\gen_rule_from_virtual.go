package main

import (
	"context"
	"fmt"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
	"github.com/thoas/go-funk"
)

func init() {
	setting.SetupByPath("../conf/server_conf.yaml")
	log.Setup()
	postgresql.Setup()
}

var firstNameWheres = []string{"素材主题", "素材版本", "素材分类", "是否含有FPS第一视角", "有无文案"}

func main() {
	gameCode := "pubgm"
	ctx := context.Background()
	allVirtual, err := getAllGameVirtualPrefix(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "getAllGameVirtualPrefix error: %v", err)
		return
	}

	// 去重标签
	uniq := make(map[string]bool)
	firstLabelUnique := make(map[string]bool)
	// 按前缀聚合
	m := make(map[string]*model.TbAssetLabelRule)
	var rules []*model.TbAssetLabelRule
	for _, v := range allVirtual {
		log.DebugContextf(ctx, "virtual:%+v", v)
		key := fmt.Sprintf("%v,%v,%v,%v", v.AssetId, v.LabelName, v.FirstLabel, v.SecondLabel)
		if uniq[key] {
			log.WarningContextf(ctx, "dup key:%v", key)
			continue
		}
		uniq[key] = true

		// pubgm素材标签去重
		if gameCode == "pubgm" && funk.ContainsString(firstNameWheres, v.FirstLabel) {
			firstKey := fmt.Sprintf("%v,%v", v.AssetId, v.FirstLabel)
			if firstLabelUnique[firstKey] {
				log.WarningContextf(ctx, "firstLabel dup key:%v", firstKey)
				continue
			}
			firstLabelUnique[firstKey] = true
		}

		if _, ok := m[v.AssetId]; !ok {
			rule := &model.TbAssetLabelRule{
				GameCode:   gameCode,
				Rule:       v.AssetId,
				Type:       constant.LabelRulePrefix,
				CreateUser: v.CreateBy,
				UpdateUser: v.UpdateBy,
				CreateTime: time.Now(),
				UpdateTime: time.Now(),
			}

			m[v.AssetId] = rule
			rules = append(rules, rule)
		}

		lable := model.AssetLabel{
			LabelName:   v.LabelName,
			FirstLabel:  v.FirstLabel,
			SecondLabel: v.SecondLabel,
		}
		m[v.AssetId].Labels = append(m[v.AssetId].Labels, lable)
	}
	for _, rule := range rules {
		log.DebugContextf(ctx, "rule: %+v", rule)
	}
	log.DebugContextf(ctx, "rule len:%v", len(rules))
	chunks := funk.Chunk(rules, 500).([][]*model.TbAssetLabelRule)
	for _, chunk := range chunks {
		db := postgresql.GetDBWithContext(ctx)
		query := db.Model(&chunk)
		query.OnConflict("(game_code, rule, type) DO UPDATE")
		query.Set("labels=EXCLUDED.labels")
		query.Set("label_options=EXCLUDED.label_options")
		query.Set("update_user=EXCLUDED.update_user")
		query.Set("update_time=EXCLUDED.update_time")
		_, err := query.Insert()
		if err != nil {
			log.ErrorContextf(ctx, "insert error: %v", err)
			return
		}
	}
}

func getAllGameVirtualPrefix(ctx context.Context, gameCode string) ([]*model.VirtualAsset, error) {
	var rlt []*model.VirtualAsset

	limit := 500
	for offset := 0; offset < 1000000; offset = offset + limit {
		rows, err := getPage(ctx, gameCode, offset, limit)
		if err != nil {
			log.ErrorContextf(ctx, "getPage error: %v", err)
			return nil, err
		}

		rlt = append(rlt, rows...)

		if len(rows) < limit {
			break
		}
	}

	return rlt, nil
}

func getPage(ctx context.Context, gameCode string, offset, limit int) ([]*model.VirtualAsset, error) {
	var tmp []*model.VirtualAsset
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(&tmp).Table(model.GetVirtualAssetTableName(gameCode))
	query.Where("asset_type = 2")
	query.Order("update_time desc")
	query.Order("asset_id")
	query.Order("first_label")
	query.Order("second_label")
	query.Offset(offset).Limit(limit)
	err := query.Select()
	if err != nil {
		return nil, err
	}

	return tmp, nil
}
