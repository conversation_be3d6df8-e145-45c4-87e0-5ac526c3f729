syntax = "proto3";

package aix;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/aix";

enum AixCommErrCode {
    AIX_COMM_SUCC                  = 0;        // 成功
    AIX_COMM_ERR                   = 1;        // 失败
    AIX_COMM_ERR_UNKNOWN           = 8000000;  // 未知错误
    AIX_COMM_ERR_PARAM             = 8000001;  // 参数错误
    AIX_COMM_ERR_RET               = 8000002;  // 返回错误
    AIX_COMM_ERR_TIMEOUT           = 8000003;  // 超时错误
    AIX_COMM_ERR_EMPTY             = 8000004;  // 返回为空
    AIX_COMM_ERR_DB                = 8000005;  // DB错误
    AIX_COMM_ERR_GET_L5            = 8000006;  // 获取L5失败
    AIX_COMM_ERR_NOT_LOGIN         = 8000007;  // 未登陆
    AIX_COMM_ERR_NOT_SUPPORT       = 8000008;  // 不支持
    AIX_COMM_ERR_NOT_ALLOW         = 8000009;  // 不允许
    AIX_COMM_ERR_JSON              = 8000010;  // json解析错误
    AIX_COMM_ERR_SQL               = 8000011;  // sql错误
    AIX_COMM_ERR_DECODE            = 8000012;  // decode错误
    AIX_COMM_ERR_ENCODE            = 8000013;  // encode错误
    AIX_COMM_ERR_SEND              = 8000014;  // send错误
    AIX_COMM_ERR_EXPIRED           = 8000015;  // 过期错误
    AIX_COMM_ERR_INIT              = 8000016;  // 初始化错误
    AIX_COMM_ERR_NULL              = 8000017;  // 空指针错误
    AIX_COMM_ERR_ID                = 8000018;  // ID错误
    AIX_COMM_ERR_TIME              = 8000019;  // 时间错误
    AIX_COMM_ERR_INTERNAL_ERROR    = 8000020;  // 服务内部错误
    AIX_COMM_ERR_USER_CONFIG_LIMIT = 8000021;  // 用户隐私设置权限限制
    AIX_COMM_ERR_HM_RECONGNITION   = 8000022;  // 验证码识别
    AIX_COMM_ERR_DELAY_PUBLISH     = 8000023;  // 延迟发布
    AIX_COMM_ERR_DUP_NAME          = 8000024;  // 重名错误
    AIX_COMM_ERR_CONFIG            = 8000025;  // 程序配置文件错误
    AIX_COMM_ERR_MEDIA             = 8000026;  // 渠道侧请求错误
    AIX_COMM_ERR_RATE_LIMITING     = 8000027;  // 频率限制
}

 // 上传素材任务错误码, 9000000 开始
enum UploadTaskErrCode {
    Success        = 0;
    DownloadAsset  = 9000000;  // 下载素材失败
    YoutubeNoQuota = 9000001;  // youtube channel无配额
    NotSupportByChannle = 9000002;  // 该渠道不支持上
}

// demo_server 8201000-8201999
enum DemoServerErrCode {
    DEMO_SERVER_SUCC              = 0;
    DEMO_SERVER_BEGIN             = 8201000;
    DEMO_SERVER_INVALID_PARAMETER = 8201001;
    DEMO_SERVER_QUERY_DB_ERROR    = 8201002;
    DEMO_SERVER_END               = 8201999;
}




// 【TikTok错误码】 范围: 1003001 - 1003999
enum AixTikTokErrCode {
    AIX_TikTok_SUCCESS = 0;

    // ---------------------------- 自定义错误码 start 范围: 1003001 - 1003500

    AIX_TIKTOK_CAMPAIGN_DISABLE_FAIL_WHEN_PUBLISH_SUC = 1003001;  // 发布渠道campaign成功，设置渠道campaign为disable状态失败
    AIX_TIKTOK_AD_ACO_PUBLISHING                      = 1003002;  // aco 创意广告发布中

    // ---------------------------- 自定义错误码 end



    // ---------------------------- TikTok渠道错误码 start 范围: 1003501 - 1003999
    // xxx
    // ---------------------------- TikTok渠道错误码 end
}

