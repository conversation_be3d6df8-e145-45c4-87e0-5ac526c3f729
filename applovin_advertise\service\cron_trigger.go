package service

import (
	"context"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
)

// CronTriggerReq ...
type CronTriggerReq struct {
	CronName  string `json:"cron_name"` // 定时任务名字
	GameCode  string `json:"game_code"`
	AccountID string `json:"account_id"`
}

// CronTriggerRsp ...
type CronTriggerRsp struct {
	Result *aix.Result `json:"result"`
}

// CronTrigger 手动触发定时任务
func CronTrigger(ctx context.Context, req *CronTriggerReq, rsp *CronTriggerRsp) error {
	if req.CronName == "RefreshToken" {
		// 触发刷新token
		newCtx := log.CopySessionIDContext(ctx)
		utils.GoNoTimeout(newCtx, func(context.Context) {
			RefreshToken(ctx)
		})
	}
	if req.CronName == "CheckAssetCampaignStatus" {
		// 触发检查配置的素材上传campaign状态
		newCtx := log.CopySessionIDContext(ctx)
		utils.GoNoTimeout(newCtx, func(context.Context) {
			CheckAssetCampaignStatus(newCtx)
		})
	}

	return nil
}
