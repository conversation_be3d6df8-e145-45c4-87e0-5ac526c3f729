// Package msg 发送企业微信消息
package msg

import (
	"context"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/pkg/rpc"
	"github.com/pkg/errors"
)

// Result 企微返回结果信息
type Result struct {
	Errcode int    `json:"errcode"` // 错误码
	Errmsg  string `json:"errmsg"`  // 错误短语
}

// Request 企微请求数据结构体
type Request struct {
	Chatid   string `json:"chatid"`  // 个人RTX, 支持给多人发送消息, 使用'|'分割
	Msgtype  string `json:"msgtype"` // 消息类型, 目前支持: markdown
	Markdown struct {
		Content string `json:"content"` // markdown内容
	} `json:"markdown"` // markdown 结构体
}

// SendQiWeiMsg 发送企微消息, 目前使用固定的企微机器人
// users: 目标用户RTX, 使用'|'分隔
func SendQiWeiMsg(ctx context.Context, users string, msg string) error {
	if users == "" {
		return errors.New("qiwei users is empty,message: " + msg)
	}
	req := &Request{}
	req.Chatid = users
	req.Msgtype = "markdown"
	req.Markdown.Content = msg

	url := conf.GetBizConf().QiweiURL
	rsp, err := rpc.NewRequest(ctx).SetBody(req).SetResult(Result{}).Post(url)
	if err != nil {
		return err
	}

	result := rsp.Result().(*Result)
	if result.Errcode != 0 {
		return errs.New(result.Errcode, result.Errmsg)
	}
	return nil
}
