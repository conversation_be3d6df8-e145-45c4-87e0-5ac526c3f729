package service

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/cos"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	aixwebRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/aix_web"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	localConst "e.coding.intlgame.com/ptc/aix-backend/material_sync/constant"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"github.com/thoas/go-funk"
	"github.com/xuri/excelize/v2"
)

// CronLabelRuleTask 标签规则打标定时任务处理
func CronLabelRuleTask(ctx context.Context) {
	ctx = log.SetXAPIPath(ctx, "cron/CronLabelRuleTask")

	// 这里加锁，避免同时去处理
	key := "cron/CronLabelRuleTask"
	locked, _ := redis.GetMaterialSyncLock(ctx, key, 2*time.Hour)
	if !locked {
		log.WarningContextf(ctx, "CronLabelRuleTask GetLock false")
		return
	}
	// 释放锁
	defer redis.DelMaterialSyncLock(ctx, key)

	start := time.Now()
	defer func() {
		log.DebugContextf(ctx, "CronLabelRuleTask end, cost:%v", time.Since(start))
	}()
	// 一次处理10个
	tasks, err := repo.GetPendingLabelRuleTaskPage(ctx, 10)
	if err != nil {
		log.ErrorContextf(ctx, "CronLabelRuleTask repo.GetPendingLabelRuleTaskPage err:%v", err)
		return
	}
	for _, task := range tasks {
		err := doLableRuleTask(ctx, task)
		task.Status = constant.LabelRuleTaskStatusEnd
		task.Detail = ""
		task.UpdateTime = time.Now()
		task.UpdateUser = "material_sync"
		if err != nil {
			log.ErrorContextf(ctx, "CronLabelRuleTask task:%+v, doLableRuleTask err:%v", task, err)
			task.Status = constant.LabelRuleTaskStatusFail
			task.Detail = err.Error()
		}

		// 更新任务状态
		err = repo.UpdateLabelRuleTaskByField(ctx, task, []string{"status", "detail", "update_time", "update_user"})
		if err != nil {
			log.ErrorContextf(ctx, "CronLabelRuleTask task:%+v, repo.UpdateLabelRuleTaskByField err:%v", task, err)
			return
		}
	}

	return
}

func doLableRuleTask(ctx context.Context, task *model.TbLabelRuleTask) error {
	log.DebugContextf(ctx, "CronLabelRuleTask begin task:%+v", task)
	// 判断是否是支持的类型
	if !funk.ContainsInt(constant.SupportLabelRuleType, int(task.Type)) {
		return fmt.Errorf("task type:%d not support", task.Type)
	}

	// 下载cos文件
	localFile := fmt.Sprintf("%v/label_rule_task_%v.xlsx", localConst.TmpDataDir, task.ID)
	log.DebugContextf(ctx, "download cos file to localFile:%v", localFile)
	os.Remove(localFile) // 清理旧临时文件
	err := cos.GetToFile(ctx, task.CosFile, localFile)
	if err != nil {
		return fmt.Errorf("download cos file failed, err:%v", err)
	}
	// 处理完成后删除本地临时文件
	defer os.Remove(localFile)

	if task.LabelLevel == constant.LabelLevelAsset {
		// V2版本的批量打标逻辑, 同时处理serial和asset层级
		return doLableRuleTaskV2(ctx, task, localFile)
	}

	// 处理xlsx文件
	f, err := excelize.OpenFile(localFile)
	if err != nil {
		return fmt.Errorf("excelize.OpenFile err:%v", err)
	}
	defer func() {
		// Close the spreadsheet.
		if err := f.Close(); err != nil {
			log.DebugContextf(ctx, "excelize.Close err:%v", err)
		}
	}()

	// 处理第一个sheet
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return fmt.Errorf("no sheet in file")
	}
	sheetName := sheets[0]
	rows, err := f.Rows(sheetName)
	if err != nil {
		return fmt.Errorf("excelize f.Rows err:%v", err)
	}
	defer rows.Close()

	// 第一行是title
	var title []string
	if rows.Next() {
		title, err = rows.Columns()
		if err != nil {
			return fmt.Errorf("excelize get title rows.Columns err:%v", err)
		}
	}
	// 第一列是 Serial Name，后面列是一级标签, 至少要两列
	if len(title) < 2 {
		return fmt.Errorf("title len(title) < 2")
	}

	// 处理title
	var realTitle []string
	for _, one := range title {
		one = strings.TrimSpace(one)
		if one == "" {
			return fmt.Errorf("title has empty cell")
		}
		realTitle = append(realTitle, one)
	}

	// 加载标签库
	creativeLabels, err := aixwebRepo.LoadCreativeLabels(ctx, task.GameCode)
	if err != nil {
		return fmt.Errorf("aixwebRepo.LoLoadCreativeLabelsad err:%v", err)
	}

	var retErr error
	// 遍历行
	for rows.Next() {
		labels, retErr := doXlsxOneRow(ctx, task, realTitle, rows, creativeLabels)
		// 对处理过的标签，如果标签库里没有，需要加进去
		for _, label := range labels {
			creativeLabels.AddSerialLevelLabel(label.FirstLabel, label.SecondLabel)
		}

		if retErr != nil {
			break
		}
	}

	// 处理完文件后，更新标签库
	// 有变化更新数据
	var saves []*model.CreativeLabel
	changedLabels := creativeLabels.GetChangedLabels()
	for _, t := range changedLabels {
		t.UpdatedAt = time.Now()
		t.Updater = "CronLabelRuleTask"
		saves = append(saves, t)
	}
	if len(saves) > 0 {
		err = data.BatchUpdateCreativeLabelsSecondLabel(ctx, saves)
		if err != nil {
			log.ErrorContextf(ctx, "data.UpdateCreativeLabelsSecondLabel err:%v", err)
		}
	}

	return retErr
}

func doXlsxOneRow(ctx context.Context,
	task *model.TbLabelRuleTask, title []string, rows *excelize.Rows,
	creativeLabels *aixwebRepo.CreativeLabels) ([]model.AssetLabel, error) {
	cells, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("excelize get row rows.Columns err:%v", err)
	}

	titleLen := len(title)

	// 第一列是素材规则名
	if len(cells) < 2 {
		return nil, nil
	}
	name := strings.TrimSpace(cells[0])
	if name == "" {
		return nil, nil
	}

	normalizeName, ok := model.CheckAndNormalizeLabelRuleName(task.Type, name, false)
	// 标签规则校验， 不通过则忽略
	if !ok {
		log.WarningContextf(ctx, "model.CheckAndNormalizeLabelRuleName failed, ignore rule:%+v", name)
		return nil, nil
	}
	name = normalizeName

	// 从数据库中读取规则
	rule, err := data.GetLabelRule(ctx, task.GameCode, name, task.Type)
	if err != nil {
		return nil, fmt.Errorf("data.GetLabelRule err:%v", err)
	}
	if rule == nil {
		// 数据库里面没有，新生成标签规则
		rule = &model.TbAssetLabelRule{
			GameCode:   task.GameCode,
			Rule:       name,
			Type:       task.Type,
			CreateTime: time.Now(),
			CreateUser: task.CreateUser,
		}
	}
	rule.UpdateUser = fmt.Sprintf("material_sync_task_%v", task.ID)
	rule.UpdateTime = time.Now()

	// 机器打标的标签，需要记录下来， 人工打标任务要忽略这些标签不能覆盖
	// 不在此次处理表格里的一级标签保留
	var keepLabels []model.AssetLabel
	for _, label := range rule.Labels {
		if creativeLabels.IsFirstLabelIntelligent(label.FirstLabel) ||
			!funk.ContainsString(title, label.FirstLabel) {
			keepLabels = append(keepLabels, label)
		}
	}
	// 机器打标的标签保存下来后，清空
	rule.Labels = nil

	// 记录处理过的标签， 需要返回
	var retLabels []model.AssetLabel
	// 处理标签，从第二列开始
	for i := 1; i < len(cells) && i < titleLen; i++ {
		// title里的是一级标签
		firstLabel := title[i]
		// 非机器打标的serial层级标签，才能覆盖
		if !creativeLabels.IsFirstLabelIntelligent(firstLabel) &&
			creativeLabels.IsFirstLabelSerialLevel(firstLabel) {
			// 多个二级标签以换行分割
			s := cells[i]
			lines := splitLineAndTrim(s)
			for _, line := range lines {
				label := model.AssetLabel{
					LabelName:   "",         // 标签规则的label_name固定为空，已不再使用
					FirstLabel:  firstLabel, //
					SecondLabel: line,
				}
				rule.Labels = append(rule.Labels, label)
				retLabels = append(retLabels, label)
			}
		}
	}
	// 把原来的机器打标的标签，放进来
	rule.Labels = append(rule.Labels, keepLabels...)
	removeRuleDuplicateLables(rule)

	log.DebugContextf(ctx, "get one rule:%+v", rule)

	err = repo.UpsertLabelRule(ctx, rule, true)
	if err != nil {
		return nil, fmt.Errorf("repo.UpsertLabelRule err:%v", err)
	}

	return retLabels, nil
}

// 删除重复的标签
func removeRuleDuplicateLables(rule *model.TbAssetLabelRule) {
	var labels []model.AssetLabel
	uniq := make(map[string]bool)
	for _, label := range rule.Labels {
		key := fmt.Sprintf("%v,%v,%v", label.LabelName, label.FirstLabel, label.SecondLabel)
		if uniq[key] == false {
			labels = append(labels, label)
		}
		uniq[key] = true
	}

	rule.Labels = labels
}
