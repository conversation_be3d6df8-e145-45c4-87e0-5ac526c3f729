syntax = "proto3";

package google_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/google_advertise";

import "aix/aix_common_message.proto";

// https://developers.google.com/google-ads/api/reference/rpc/v12/CustomerStatusEnum.CustomerStatus
enum CustomerStatus {
    CUSTOMERSTATUS_UNSPECIFIED = 0;
    CUSTOMERSTATUS_UNKNOWN     = 1;
    CUSTOMERSTATUS_ENABLED     = 2;
    CUSTOMERSTATUS_CANCELED    = 3;
    CUSTOMERSTATUS_SUSPENDED   = 4;
    CUSTOMERSTATUS_CLOSED      = 5;
}

message App {
    string app_id   = 1;  // 应用ID
    string app_name = 2;  // APP应用名称
    string platform = 3;  // 应用平台，可选值: ANDROID,IOS,PC,UNKNOWN,UNLIMITED
}

message GoogleAuth {
	string developer_token   = 1;
	string client_id         = 2;
	string client_secret     = 3;
	string refresh_token     = 4;
	string login_customer_id = 5;
	string use_proto_plus    = 6;
}

message Account {
    string account_id = 1;
    string name       = 2;
    int32  status     = 3; // 参看枚举 CustomerStatus
    repeated App apps = 4; // 账号关联的app列表
    string time_zone  = 5; // 账号的时区
    GoogleAuth google_auth = 6; // 账号的google auth信息, nil的话表示没有
}

// game_code下的account信息
message GameCodeAccounts {
    string game_code = 1;
    repeated Account accounts = 2;
}

// 拉取游戏配置, POST, /api/v1/google_advertise/GetGameConfig
message GetGameConfigReq {
    string game_code = 1;
}

message GetGameConfigRsp {
    aix.Result result          = 1;
    repeated Account accounts  = 2;
    string android_app_id      = 3;
    string ios_app_id          = 4;
}

enum CampaignObjective {
    OBJECTIVE_UNKNOWN = 0;
    APP_PROMOTION     = 1;
    WEB_TRAFFIC       = 2;
}

enum CampaignType {
    TYPE_UNKNOWN = 0;
    APP          = 1;
    SEARCH       = 2; // web traffic - search
    DISPLAY      = 3; // web traffic - display

    PERFORMANCE_MAX = 4;  // web traffic - performance-max
    DEMAND_GEN       = 5;  // web traffic - discovery -> demand_gen v18
}

enum CampaignSubType {
    SUBTYPE_UNKNOWN     = 0;
    APP_INSTALL         = 1;
    APP_ENGAGEMENT      = 2;
    APP_PREREGISTRATION = 3;
}

// 平台类型
enum Platform {
    PLATFORM_UNKNOWN = 0;
    ANDROID          = 1;
    IOS              = 2;
}

enum FocusOn {
    FOCUSON_UNKNOWN     = 0;
    INSTALL_VOLUME      = 1;
    IN_APP_ACTION       = 2;
    IN_APP_ACTION_VALUE = 3;

    // web traffic 新增
    MAXIMIZE_CONVERSIONS      = 10; // focus on conversions
    MAXIMIZE_CONVERSION_VALUE = 11; // focus on conversion value
    TARGET_SPEND              = 12; // focus on click
    TARGET_IMPRESSION_SHARE   = 13; // focus on impression share

    // display 新增
    TARGET_CPA = 14;
    MANUAL_CPC = 15;
    MANUAL_CPM = 16;
}

enum TargetUsers {
    TARGETUSERS_UNKNOWN                      = 0;
    ALL_USERS                                = 1;
    USERS_LIKELY_TO_PERFORM_AN_IN_APP_ACTION = 2;
}

enum Status {
    STATUS_DRAFT   = 0;  // 草稿状态，complete
    STATUS_PAUSED  = 1;  // 已发布-暂停
    STATUS_ENABLED = 2;  // 已发布-生效
    STATUS_REMOVED = 3;  // 已发布-删除
    STATUS_INCOMPLETE     = 4;  // 草稿状态，incomplete
    STATUS_PUBLISHING     = 5;  // 发布中
    STATUS_PUBLISH_FAILED = 6;  // 发布失败
}

// 发布状态枚举值
enum PublishStatus {
    PUBLISHSTATUS_DRAFT       = 0;  // 未发布
    PUBLISHSTATUS_PRE_PUBLISH = 1;  // 预发布
    PUBLISHSTATUS_COMPLETE    = 2;  // 发布完成
    PUBLISHSTATUS_CRON_FAILED = 3;  // 定时同步失败
}

enum AppStore {
    APP_STORE_UNKOWN = 0;
    APPLE_APP_STORE  = 1;
    GOOGLE_APP_STORE = 2;
}

enum TargetImpressionShareLocation {
    TARGET_IMPRESSION_SHARE_LOCATION_UNKOWN = 0;
    ANYWHERE_ON_PAGE     = 1;  // Any location on the web page.
    TOP_OF_PAGE          = 2;  // Top box of ads.
    ABSOLUTE_TOP_OF_PAGE = 3;  // Top slot in the top box of ads.
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/KeywordMatchTypeEnum.KeywordMatchType?hl=en
enum KeywordMatchType {
    KEYWORDMATCHTYPE_UNSPECIFIED = 0;
    KEYWORDMATCHTYPE_UNKNOWN = 1;
    KEYWORDMATCHTYPE_EXACT   = 2;  // Exact match. 用[]括起来，如： [哈哈]
    KEYWORDMATCHTYPE_PHRASE  = 3;  // Phrase match. 用""括起来，如： "video games"
    KEYWORDMATCHTYPE_BROAD   = 4;  // broad match. 如：play games
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ConversionActionCategoryEnum.ConversionActionCategory]
enum ConversionActionCategory {
    CONVERSIONACTIONCATEGORY_UNSPECIFIED      = 0;
    CONVERSIONACTIONCATEGORY_UNKNOWN          = 1;
    CONVERSIONACTIONCATEGORY_DEFAULT          = 2;
    CONVERSIONACTIONCATEGORY_PAGE_VIEW        = 3;
    CONVERSIONACTIONCATEGORY_PURCHASE         = 4;
    CONVERSIONACTIONCATEGORY_SIGNUP           = 5;
    CONVERSIONACTIONCATEGORY_DOWNLOAD         = 7;
    CONVERSIONACTIONCATEGORY_ADD_TO_CART      = 8;
    CONVERSIONACTIONCATEGORY_BEGIN_CHECKOUT   = 9;
    CONVERSIONACTIONCATEGORY_SUBSCRIBE_PAID   = 10;
    CONVERSIONACTIONCATEGORY_PHONE_CALL_LEAD  = 11;
    CONVERSIONACTIONCATEGORY_IMPORTED_LEAD    = 12;
    CONVERSIONACTIONCATEGORY_SUBMIT_LEAD_FORM = 13;
    CONVERSIONACTIONCATEGORY_BOOK_APPOINTMENT = 14;
    CONVERSIONACTIONCATEGORY_REQUEST_QUOTE    = 15;
    CONVERSIONACTIONCATEGORY_GET_DIRECTIONS   = 16;
    CONVERSIONACTIONCATEGORY_OUTBOUND_CLICK   = 17;
    CONVERSIONACTIONCATEGORY_CONTACT          = 18;
    CONVERSIONACTIONCATEGORY_ENGAGEMENT       = 19;
    CONVERSIONACTIONCATEGORY_STORE_VISIT      = 20;
    CONVERSIONACTIONCATEGORY_STORE_SALE       = 21;
    CONVERSIONACTIONCATEGORY_QUALIFIED_LEAD   = 22;
    CONVERSIONACTIONCATEGORY_CONVERTED_LEAD   = 23;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ConversionOriginEnum.ConversionOrigin]
enum ConversionOrigin {
    CONVERSIONORIGIN_UNSPECIFIED    = 0;
    CONVERSIONORIGIN_UNKNOWN        = 1;
    CONVERSIONORIGIN_WEBSITE        = 2;
    CONVERSIONORIGIN_GOOGLE_HOSTED  = 3;
    CONVERSIONORIGIN_APP            = 4;
    CONVERSIONORIGIN_CALL_FROM_ADS  = 5;
    CONVERSIONORIGIN_STORE          = 6;
    CONVERSIONORIGIN_YOUTUBE_HOSTED = 7;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/BiddingStrategyTypeEnum.BiddingStrategyType?hl=en]
enum BiddingStrategyType {
    BIDDINGSTRATEGYTYPE_UNSPECIFIED               = 0;
    BIDDINGSTRATEGYTYPE_UNKNOWN                   = 1;
    BIDDINGSTRATEGYTYPE_COMMISSION                = 16;
    BIDDINGSTRATEGYTYPE_ENHANCED_CPC              = 2;
    BIDDINGSTRATEGYTYPE_INVALID                   = 17;
    BIDDINGSTRATEGYTYPE_MANUAL_CPC                = 3;
    BIDDINGSTRATEGYTYPE_MANUAL_CPM                = 4;
    BIDDINGSTRATEGYTYPE_MANUAL_CPV                = 13;
    BIDDINGSTRATEGYTYPE_MAXIMIZE_CONVERSIONS      = 10;
    BIDDINGSTRATEGYTYPE_MAXIMIZE_CONVERSION_VALUE = 11;
    BIDDINGSTRATEGYTYPE_PAGE_ONE_PROMOTED         = 5;
    BIDDINGSTRATEGYTYPE_PERCENT_CPC               = 12;
    BIDDINGSTRATEGYTYPE_TARGET_CPA                = 6;
    BIDDINGSTRATEGYTYPE_TARGET_CPM                = 14;
    BIDDINGSTRATEGYTYPE_TARGET_IMPRESSION_SHARE   = 15;
    BIDDINGSTRATEGYTYPE_TARGET_OUTRANK_SHARE      = 7;
    BIDDINGSTRATEGYTYPE_TARGET_ROAS               = 8;
    BIDDINGSTRATEGYTYPE_TARGET_SPEND              = 9;
}

message TargetImpressionShare {
    TargetImpressionShareLocation location                    = 1; // where do you want your ads to appear, 枚举TargetImpressionShareLocation
    double impression_share_to_target                         = 2; // 百分比小数：大于0小于等于100
}

message NetworkSettings {
    bool target_search_network   = 1;  // search network
    bool target_content_network  = 2;  // display network
}

message CustomParameter {
    string key   = 1;
    string value = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/DeviceEnum.Device?hl=en
enum Device {
    DEVICE_UNSPECIFIED  = 0;
    DEVICE_UNKNOWN      = 1;
    DEVICE_MOBILE       = 2;
    DEVICE_TABLET       = 3;
    DEVICE_DESKTOP      = 4;
    DEVICE_CONNECTED_TV = 6;
    DEVICE_OTHER        = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CampaignCriterion?hl=en#device
message DeviceInfo {
    int32  type         = 1;  // 参看枚举 Device
    string criterion_id = 2;  // criterion_id
    double bid_modifier = 3;  // 0-排除该divice，1-生效该device
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/OperatingSystemVersionInfo?hl=en
message OperatingSystemVersionInfo {
    repeated string android = 1;  // adroid operating_system_version
    repeated string ios     = 2;  // ios operating_system_version
}

message Budget {
    string budget_name          = 1;  // 后台自动生成
    string budget_resource_name = 2;  // 发布之后才有
    double budget_amount        = 3;  // 前端填写 金额美元，小数
}

message AdGroupAd {
    int64 inner_ad_group_id     = 1;   // 前端填写 所属的ad_group
    int64 inner_ad_id           = 2;   // 前端填写，inner_ad_id=0新增 inner_ad_id!=0为更新
    string ad_name              = 3;   // 后台自动生成
    string ad_resource_name     = 4;   // 发布后才有
    Status ad_status            = 5;   // 后台自己填充 参看枚举 Status
    repeated string head_line   = 6;   // 前端填写
    repeated string description = 7;   // 前端填写
    repeated string images      = 8;   // 前端填写，图片素材id
    repeated string videos      = 9;   // 前端填写，视频素材id
    repeated string htmls       = 10;  // 这期不开放
    string create_time          = 11;
    string update_time          = 12;
    string app_url              = 13;  // 前端填写，当选择APP-ENGAGEMENT时，需要填写

    string ad_group_resource_name = 14; // 前端填写，父级的ad_group_resource_name,

    // web-traffic 新增
    repeated string   final_urls                   = 15;  // final url
    string   display_path1                         = 16;  // display path
    string   display_path2                         = 17;  // display path
    string   tracking_url_template                 = 18;  // tracking template
    string   final_url_suffix                      = 19;  // final url suffix
    repeated CustomParameter url_custom_parameters = 20;  // custom parameter
    repeated string   final_mobile_urls            = 21;  // use a different final url for mobile

    string creator = 22;
    string memo    = 23;  // 提示信息：发布失败是错误提示

    // display新增
    string   business_name                  = 24;
    repeated string marketing_images        = 25;  // 1.91:1  尺寸
    repeated string square_marketing_images = 26;  // 1:1 尺寸
    repeated string logo_images             = 27;  // 4:1 尺寸
    repeated string square_logo_images      = 28;  // 1:1 尺寸
    string   long_headline                  = 29;

    repeated string long_headlines           = 30;
    repeated string portrait_marketing_image = 31;  // 4:5 尺寸
    repeated string logos                    = 32;  // 1:1 尺寸
    repeated string landscape_logos          = 33;  // 4:1 尺寸
    AssetGroupSignal   audience_signal       = 34;  // audience_signal
    int32 call_to_action_type                = 35;  // CallToActionType
    repeated Asset sitelinks                 = 36;  // sitelink

    string call_to_action_text = 37;  // 默认为空 （automated）

    string inner_status = 38; // 内部用status
}

enum CallToActionType {
    CALLTOACTIONTYPE_UNSPECIFIED = 0;
    CALLTOACTIONTYPE_UNKNOWN     = 1;
    CALLTOACTIONTYPE_LEARN_MORE  = 2;
    CALLTOACTIONTYPE_GET_QUOTE   = 3;
    CALLTOACTIONTYPE_APPLY_NOW   = 4;
    CALLTOACTIONTYPE_SIGN_UP     = 5;
    CALLTOACTIONTYPE_CONTACT_US  = 6;
    CALLTOACTIONTYPE_SUBSCRIBE   = 7;
    CALLTOACTIONTYPE_DOWNLOAD    = 8;
    CALLTOACTIONTYPE_BOOK_NOW    = 9;
    CALLTOACTIONTYPE_SHOP_NOW    = 10;
}


// https://developers.google.com/google-ads/api/reference/rpc/v10/AssetTypeEnum.AssetType?hl=en
enum AssetType {
    ASSETTYPE_UNSPECIFIED        = 0;
    ASSETTYPE_UNKNOWN            = 1;
    ASSETTYPE_YOUTUBE_VIDEO      = 2;
    ASSETTYPE_MEDIA_BUNDLE       = 3;
    ASSETTYPE_IMAGE              = 4;
    ASSETTYPE_TEXT               = 5;
    ASSETTYPE_LEAD_FORM          = 6;
    ASSETTYPE_BOOK_ON_GOOGLE     = 7;
    ASSETTYPE_PROMOTION          = 8;
    ASSETTYPE_CALLOUT            = 9;
    ASSETTYPE_STRUCTURED_SNIPPET = 10;
    ASSETTYPE_SITELINK           = 11;
    ASSETTYPE_PAGE_FEED          = 12;
    ASSETTYPE_DYNAMIC_EDUCATION  = 13;
    ASSETTYPE_MOBILE_APP         = 14;
    ASSETTYPE_HOTEL_CALLOUT      = 15;
    ASSETTYPE_CALL               = 16;
    ASSETTYPE_PRICE              = 17;
    ASSETTYPE_CALL_TO_ACTION     = 18;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/MimeTypeEnum.MimeType?hl=en
enum MimeType {
    MIMETYPE_UNSPECIFIED  = 0;
    MIMETYPE_UNKNOWN      = 1;
    MIMETYPE_IMAGE_JPEG   = 2;
    MIMETYPE_IMAGE_GIF    = 3;
    MIMETYPE_IMAGE_PNG    = 4;
    MIMETYPE_FLASH        = 5;
    MIMETYPE_TEXT_HTML    = 6;
    MIMETYPE_PDF          = 7;
    MIMETYPE_MSWORD       = 8;
    MIMETYPE_MSEXCEL      = 9;
    MIMETYPE_RTF          = 10;
    MIMETYPE_AUDIO_WAV    = 11;
    MIMETYPE_AUDIO_MP3    = 12;
    MIMETYPE_HTML5_AD_ZIP = 13;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/TextAsset?hl=en
message TextAsset {
    string text = 1;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/ImageDimension?hl=en
message ImageDimension {
int64  height_pixels = 1;
int64  width_pixels  = 2;
string url           = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/ImageAsset?hl=en
message ImageAsset {
    int32          mime_type = 1;  // // 参看枚举 MimeType
    ImageDimension full_size = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/YoutubeVideoAsset?hl=en
message YoutubeVideoAsset {
    string youtube_video_id    = 1;
    string youtube_video_title = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/SitelinkAsset?hl=en
message SitelinkAsset {
    string link_text    = 1;
    string description1 = 2;
    string description2 = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/Asset?hl=en
message Asset {
    int64  id            = 1;
    string resource_name = 2;
    int32  type          = 3;  // 参看枚举 enum AssetType
    string name          = 4;

    repeated string final_urls                     = 5;
    string   tracking_url_template                 = 6;
    string   final_url_suffix                      = 7;
    repeated CustomParameter url_custom_parameters = 8;
    repeated string final_mobile_urls              = 9;

    TextAsset         text_asset          = 10;
    ImageAsset        image_asset         = 11;
    YoutubeVideoAsset youtube_video_asset = 12;
    SitelinkAsset     sitelink_asset      = 13;
}


// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceTypeEnum.CustomAudienceType?hl=en
enum CustomAudienceType {
    CUSTOMAUDIENCETYPE_UNSPECIFIED     = 0;
    CUSTOMAUDIENCETYPE_UNKNOWN         = 1;
    CUSTOMAUDIENCETYPE_AUTO            = 2;
    CUSTOMAUDIENCETYPE_INTEREST        = 3;
    CUSTOMAUDIENCETYPE_PURCHASE_INTENT = 4;
    CUSTOMAUDIENCETYPE_SEARCH          = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceMemberTypeEnum.CustomAudienceMemberType?hl=en
enum CustomAudienceMemberType {
    CUSTOMAUDIENCEMEMBERTYPE_UNSPECIFIED    = 0;
    CUSTOMAUDIENCEMEMBERTYPE_UNKNOWN        = 1;
    CUSTOMAUDIENCEMEMBERTYPE_KEYWORD        = 2;
    CUSTOMAUDIENCEMEMBERTYPE_URL            = 3;
    CUSTOMAUDIENCEMEMBERTYPE_PLACE_CATEGORY = 4;
    CUSTOMAUDIENCEMEMBERTYPE_APP            = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceMember?hl=en
message CustomAudienceMember {
    int32  member_type    = 1;  // enum CustomAudienceMemberType
    string keyword        = 2;
    string url            = 3;
    int64  place_category = 4;
    string app            = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudience?hl=en
message CustomAudience {
    string   resource_name                = 1;  // The resource name of the custom audience
    int64    id                           = 2;  // ID of the custom audience.
    int32    status                       = 3;  // enum CustomAudienceStatus
    string   name                         = 4;
    int32    type                         = 5;  // enum CustomAudienceType, Type of the custom audience. ("INTEREST" OR "PURCHASE_INTENT" is not allowed for newly created custom audience but kept for existing audiences)
    string   description                  = 6;
    repeated CustomAudienceMember members = 7;
}


// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceStatusEnum.AudienceStatus?hl=en
enum AudienceStatus {
    AUDIENCESTATUS_UNSPECIFIED = 0;
    AUDIENCESTATUS_UNKNOWN     = 1;
    AUDIENCESTATUS_ENABLED     = 2;
    AUDIENCESTATUS_REMOVED     = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AgeSegment?hl=en
message AgeSegment {
    int32 min_age = 1; // Minimum age to include. A minimum age must be specified and must be at least 18. Allowed values are 18, 25, 35, 45, 55, and 65.
    int32 max_age = 2; // Maximum age to include. A maximum age need not be specified. If specified, max_age must be greater than min_age, and allowed values are 24, 34, 44, 54, and 64.
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AgeDimension?hl=en
message AgeDimension {
    repeated AgeSegment  age_ranges = 1;
    bool     include_undetermined   = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/GenderTypeEnum.GenderType?hl=en
enum GenderType {
    GENDERTYPE_UNSPECIFIED  = 0;
    GENDERTYPE_UNKNOWN      = 1;
    GENDERTYPE_MALE         = 10;
    GENDERTYPE_FEMALE       = 11;
    GENDERTYPE_UNDETERMINED = 20;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/GenderDimension?hl=en
message GenderDimension {
    repeated int32 genders        = 1;  // enum GenderType
    bool     include_undetermined = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/UserListSegment?hl=en
message UserListSegment {
    string user_list = 1; // The user list resource name
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/UserInterestSegment?hl=en
message UserInterestSegment {
    string user_interest_category = 1; // The user interest resource name
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceSegment?hl=en
message CustomAudienceSegment {
    string custom_audience = 1; // The custom audience resource name
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceSegment?hl=en
message AudienceSegment {
    UserListSegment       user_list       = 1;
    UserInterestSegment   user_interest   = 2;
    CustomAudienceSegment custom_audience = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceSegmentDimension?hl=en
message AudienceSegmentDimension {
    repeated AudienceSegment segments = 1; // Union field
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceDimension?hl=en
message AudienceDimension {
    AgeDimension             age               = 1;
    GenderDimension          gender            = 2;
    AudienceSegmentDimension audience_segments = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/Audience?hl=en
message Audience {
    int64 id             = 1;  // comment[id]
    string resource_name = 2;  // comment[resource name]
    string name          = 3;  // comment[resource name]
    int32 status         = 4;  // comment[ref: AudienceStatus]
    string description   = 5;  // comment[description]
    repeated AudienceDimension dimensions = 6; // Union field
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceInfo?hl=en
message AudienceInfo {
    string audience = 1; // The Audience resource name.
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AssetGroupSignal?hl=en
message AssetGroupSignal {
    string       resource_name = 1;
    string       asset_group   = 2;  //  The asset group which this asset group signal belongs to.
    AudienceInfo audience      = 3;  // The signal(audience criterion) to be used by the performance max campaign.
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/KeywordInfo?hl=en
message KeywordInfo {
    KeywordMatchType match_type = 1; // 前端填写，参看枚举 KeywordMatchType
    string text = 2; // The text of the keyword (at most 80 characters and 10 words).
    string resource_name = 3; // 后台用
}

message AdGroup {
    int64 inner_campaign_id       = 1;  // 前端填写，所属的campaign
    int64 inner_ad_group_id       = 2;  // 前端填写，inner_ad_group_id=0新增 inner_ad_group_id!=0为更新
    string ad_group_resource_name = 3;  // 后台自动生成，发布后才有
    string ad_group_name          = 4;  // 前端填写
    Status group_status           = 5;  // 后台自动填充 参看枚举 Status
    repeated string audience      = 6;  // 前端填写 APP_ENGAGEMENT时，需要有audience（其实是user_list）
    string create_time            = 7;
    string update_time            = 8;

    string campaign_resource_name = 9;  // 前端填写，父级的campaign_resource_name,

    // web-traffic新增
    repeated KeywordInfo keywords = 10; // 前端填写

    string creator = 11;
    string memo    = 12;  // 提示信息：发布失败是错误提示

    // display 新增
    int32  bidding_strategy_type = 13;  // 参看枚举 BiddingStrategyType
    double target_cpa            = 14;
    double cpc_bid               = 15;
    double cpm_bid               = 16;

    string audience_signal = 17; // 这里其实是ad_group_criterion audience, 取名audience_signal是为了和上面区分
}

message AppInfo {
    Platform app_platform = 1;  // 前端填写，参看枚举 Platform
    string app_id         = 2;  // 前端填写
}

message Campaign {
    string game_code                     = 1;   // 前端填写
    string account_id                    = 2;   // 前端填写
    string strategy                      = 3;   // 前端填写
    string campaign_name                 = 4;   // 前端填写
    int64 inner_campaign_id              = 5;   // 前端填写 inner_campaign_id=0新增 inner_campaign_id!=0为更新
    string campaign_resource_name        = 6;   // 发布后才有
    Status campaign_status               = 7;   // 后台自动填充 参看枚举 Status
    CampaignObjective objective          = 8;   // 前端填写，参看枚举 CampaignObjective
    CampaignType type                    = 9;   // 前端填写，参看枚举 CampaignType
    CampaignSubType sub_type             = 10;  // 前端填写，参看枚举 CampaignSubType
    AppInfo app_info                     = 11;  // 前端填写
    Budget budget                        = 12;  // 前端填写
    repeated string location             = 13;  // 前端填写
    repeated string language             = 14;  // 前端填写
    FocusOn focus_on                     = 15;  // 前端填写，参看枚举 FocusOn
    repeated string track_install_volume = 16;  // 前端填写
    TargetUsers target_users             = 17;  // 前端填写，参看枚举 TargetUsers
    repeated string important_actions    = 18;  // 前端填写
    double target_cpa                    = 19;  // 前端填写
    double target_cpr                    = 20;  // 前端填写
    double target_cpi                    = 21;  // 前端填写
    uint32 target_roas                   = 22;  // 前端填写 百分比整数, 1-100000
    string start_date                    = 23;  // 前端填写 日期格式 2022-04-08
    string end_date                      = 24;  // 前端填写 日期格式 2022-04-08
    string create_time                   = 25;
    string update_time                   = 26;
    string criterion_resource_name       = 27;  // 发布后才有-后台用
    double daily_budget                  = 28;  // 后台内部用
    double bid_amount                    = 29;  // 后台内部用
    PublishStatus publish_status         = 30;  // 发布状态, 参考枚举PublishStatus
    string creator                       = 31;  // 创建人, 多个用户使用'|'分隔
    int32 notify_cnt                     = 32;  // 通知次数

    // web traffic 新增
    NetworkSettings       network_settings                      = 33;
    double                maximum_cpc_bid_limit                 = 34;
    TargetImpressionShare target_impression_share               = 35;
    string                tracking_url_template                 = 36;
    string                final_url_suffix                      = 37;
    repeated              CustomParameter url_custom_parameters = 38;
    string                business_website                      = 39; // 草稿状态search campaign,前端使用
    repeated CustomerConversionGoal conversion_goals            = 40; // 选择的conversion goals

    string memo = 41;  // 提示信息：发布失败是错误提示

    // display 新增
    repeated                   DeviceInfo devices            = 42;  // device
    OperatingSystemVersionInfo operating_system_version_info = 43;  // operating system version
}

// 保存campaign, POST, /api/v1/google_advertise/SaveCampaign
message SaveCampaignReq {
    Campaign campaign = 1;  // campaign数据, 当 inner_campaign_id != 0 为更新
}

message SaveCampaignRsp {
    aix.Result result       = 1;
    int64 inner_campaign_id = 2;  // 返回创建成功或本次更新的内部campaign_id
}

// 拉取campaign, POST, /api/v1/google_advertise/GetCampaign
message GetCampaignReq {
    string account_id             = 1;  // 前端填写
    string campaign_resource_name = 2;  // 前端填写，发布后的resource_name
    int64 inner_campaign_id       = 3;  // 前端填写，内部id
}

message GetCampaignRsp {
    aix.Result result           = 1;
    repeated Campaign campaigns = 2;  // 返回的campaign列表
}

message AdGroupTree {
    AdGroup ad_group = 1;  // ad_group信息
    repeated AdGroupAd   ad_group_ads = 2; // 子级ad_group_ad信息
}

message CampaignTree {
    Campaign campaign = 1;  // campaign信息
    repeated AdGroupTree ad_groups = 2; // 子级ad_group信息
}

// 拉取campaign树形结构简要信息(status,name), POST, /api/v1/google_advertise/GetCampaignTreeSummary
message GetCampaignTreeSummaryReq {
    string account_id             = 1;  // 前端填写
    string campaign_resource_name = 2;  // 有resource_name，一定要填，优先用resource_name
    int64 inner_campaign_id       = 3;  // 前端填写，内部id
}

message GetCampaignTreeSummaryRsp {
    aix.Result result           = 1;
    CampaignTree campaign_tree  = 2;  // 返回的campaign树形结构信息
}

// 拉取campaign树形结构简要信息(status,name), POST, /api/v1/google_advertise/GetCampaignTreeDetail
message GetCampaignTreeDetailReq {
    string account_id             = 1;  // 前端填写
    string campaign_resource_name = 2;  // 有resource_name，一定要填，优先用resource_name
    int64 inner_campaign_id       = 3;  // 前端填写，内部id
}

message GetCampaignTreeDetailRsp {
    aix.Result result           = 1;
    CampaignTree campaign_tree  = 2;  // 返回的campaign树形结构信息
}

// 批量拉取campaign树形结构简要信息(status,name), POST, /api/v1/google_advertise/BatchGetCampaignTreeDetail
message BatchGetCampaignTreeDetailReq {
    message Campaign {
        string account_id             = 1;  // 前端填写
        string campaign_resource_name = 2;  // 有resource_name，一定要填，优先用resource_name
        int64 inner_campaign_id       = 3;  // 前端填写，内部id
    }
    
    repeated Campaign campaigns = 1;
}

message BatchGetCampaignTreeDetailRsp {
    aix.Result result           = 1;
    repeated CampaignTree campaign_trees  = 2;  // 返回的campaign树形结构信息
}

// 拉取ad_group树形结构详情, POST, /api/v1/google_advertise/GetAdGroupTreeDetail
message GetAdGroupTreeDetailReq {
    string account_id             = 1;  // 前端填写
    string ad_group_resource_name = 2;  // 有resource_name，一定要填，优先用resource_name
    int64 inner_ad_group_id       = 3;  // 前端填写，内部id
}

message GetAdGroupTreeDetailRsp {
    aix.Result result           = 1;
    AdGroupTree ad_group_tree   = 2;  // 返回的ad_group树形结构信息
}

// 创建campaign树形结构草稿, POST, /api/v1/google_advertise/CreateCampaignTree
message CreateCampaignTreeReq {
    CampaignTree campaign_tree  = 1;  // campaign树形结构信息
}

message CreateCampaignTreeRsp {
    aix.Result result           = 1;
    CampaignTree campaign_tree  = 2; // 创建成果的campaign树形结构
}

// 创建ad_group树形结构草稿, POST, /api/v1/google_advertise/CreateAdGroupTree
message CreateAdGroupTreeReq {
    string account_id = 1; // 必填
    string      campaign_resource_name = 2;  // 有resource_name，一定要填，优先用resource_name
    int64       inner_campaign_id      = 3;  // 前端填写，内部id
    AdGroupTree ad_group_tree          = 4;  // ad_group树形结构信息
}

message CreateAdGroupTreeRsp {
    aix.Result result           = 1;
    AdGroupTree ad_group_tree   = 2; // 创建成果的ad_group树形结构信息
}

message PublishedItem {
    int64  inner_campaign_id      = 1;
    string campaign_resource_name = 2;
    int64  inner_ad_group_id      = 3;
    string ad_group_resource_name = 4;
    int64  inner_ad_id            = 5;
    string ad_resource_name       = 6;
    Status status                 = 7;  // 参照枚举 Status
}

// 发布campaign, POST, /api/v1/google_advertise/PublishCampaign
message PublishCampaignReq {
    int64  inner_campaign_id = 1;  // 内部campaign_id
    int64  inner_ad_id       = 2;  // 内部ad_id
    Status status            = 3;  // 不填，默认STATUS_PAUSED
}

message PublishCampaignRsp {
    aix.Result result       = 1;
    string ad_resource_name = 2;  // 根据传入的inner_ad_id匹配到的resource_name
    repeated PublishedItem items = 3; // 本次新发布的数据
}

// 删除campaign草稿, POST, /api/v1/google_advertise/DeleteCampaign
message DeleteCampaignReq {
    int64 inner_campaign_id = 1;  // 内部campaign_id
}

message DeleteCampaignRsp {
    aix.Result result = 1;
}

// 更新已发布campaign数据, POST, /api/v1/google_advertise/UpdatePublishedCampaign
message UpdatePublishedCampaignReq {
    Campaign campaign = 1;  // campaign数据, 更新后的全量数据
}

message UpdatePublishedCampaignRsp {
    aix.Result result       = 1;
    int64 inner_campaign_id = 2;  // 返回本次更新的内部campaign_id
}

// 保存ad_group, POST, /api/v1/google_advertise/SaveAdGroup
message SaveAdGroupReq {
    string  account_id = 1;
    AdGroup ad_group   = 2;  // ad_group数据, 当 inner_ad_group_id != 0 为更新
}

message SaveAdGroupRsp {
    aix.Result result       = 1;
    int64 inner_ad_group_id = 2;  // 返回创建成功或本次更新的内部ad_group_id
}

// 拉取ad_group, POST, /api/v1/google_advertise/GetAdGroup
message GetAdGroupReq {
    string account_id             = 1;  // 前端填写
    string ad_group_resource_name = 2;  // 前端填写，发布后的resource_name
    int64 inner_ad_group_id       = 3;  // 前端填写，内部id
}

message GetAdGroupRsp {
    aix.Result result          = 1;
    repeated AdGroup ad_groups = 2;  // 返回的ad_group列表
}

// 删除ad_group草稿, POST, /api/v1/google_advertise/DeleteAdGroup
message DeleteAdGroupReq {
    int64 inner_ad_group_id = 1;  // 内部ad_group_id
}

message DeleteAdGroupRsp {
    aix.Result result = 1;
}

// 发布ad_group, POST, /api/v1/google_advertise/PublishAdGroup
message PublishAdGroupReq {
    int64  inner_ad_group_id = 1;  // 内部ad_group_id
    Status status            = 2;  // 不填，默认STATUS_PAUSED
}

message PublishAdGroupRsp {
    aix.Result result       = 1;
    repeated PublishedItem items = 2; // 本次新发布的数据
}

// 保存ad_group_ad, POST, /api/v1/google_advertise/SaveAdGroupAd
message SaveAdGroupAdReq {
    string    account_id = 1;
    AdGroupAd ad         = 2;  // ad数据, 当 inner_ad_id != 0 为更新
}

message SaveAdGroupAdRsp {
    aix.Result result = 1;
    int64 inner_ad_id = 2;  // 返回创建成功或本次更新的内部ad_id
}

// 拉取ad_group_ad, POST, /api/v1/google_advertise/GetAdGroupAd
message GetAdGroupAdReq {
    string account_id       = 1;  // 前端填写
    string ad_resource_name = 2;  // 前端填写，发布后的resource_name
    int64 inner_ad_id       = 3;  // 前端填写，内部id

    string ad_group_resource_name = 4;
    int64 inner_ad_group_id       = 5;
}

message GetAdGroupAdRsp {
    aix.Result result      = 1;
    repeated AdGroupAd ads = 2;  // 返回的ad列表
}

// 删除ad_group_ad草稿, POST, /api/v1/google_advertise/DeleteAdGroupAd
message DeleteAdGroupAdReq {
    int64 inner_ad_id = 1;  // 内部ad_id
}

message DeleteAdGroupAdRsp {
    aix.Result result = 1;
}

// 发布ad, POST, /api/v1/google_advertise/PublishAd
message PublishAdReq {
    int64  inner_ad_id = 1;  // 内部ad_id
}

message PublishAdRsp {
    aix.Result result       = 1;
    repeated PublishedItem items = 2; // 本次新发布的数据
}

// 更新已发布ad数据, POST, /api/v1/google_advertise/UpdatePublishedAdGroupAd
message UpdatePublishedAdGroupAdReq {
    string account_id = 1;
    AdGroupAd ad = 2;  // ad数据，更新后的全量数据
}

message UpdatePublishedAdGroupAdRsp {
    aix.Result result = 1;
    int64 inner_ad_id = 2;  // 返回本次更新的内部ad_id
}

message CampaignStatus {
    int64  inner_campaign_id      = 1;
    string campaign_resource_name = 2;
    Status campaign_status        = 3;  // 参照枚举 Status
    string account_id             = 4;
}

// 批量拉取已发布campaign详情, POST, /api/v1/google_advertise/GetPublishedCampaigns
message GetPublishedCampaignsReq {
    string account_id                       = 1;  // 前端填写
    repeated string campaign_resource_names = 2;  // 前端填写，发布后的resource_name列表
}

message GetPublishedCampaignsRsp {
    aix.Result result           = 1;
    repeated Campaign campaigns = 2;  // 已发布campaign详情列表
}

// 批量拉取已发布ad_group详情, POST, /api/v1/google_advertise/GetPublishedAdGroups
message GetPublishedAdGroupsReq {
    string account_id                       = 1;  // 前端填写
    repeated string ad_group_resource_names = 2;  // 前端填写，发布后的resource_name列表
}

message GetPublishedAdGroupsRsp {
    aix.Result result          = 1;
    repeated AdGroup ad_groups = 2;  // 已发布ad_group详情列表
}

// 批量拉取已发布ad详情, POST, /api/v1/google_advertise/GetPublishedAds
message GetPublishedAdsReq {
    string account_id                 = 1;  // 前端填写
    repeated string ad_resource_names = 2;  // 前端填写，发布后的resource_name列表
}

message GetPublishedAdsRsp {
    aix.Result result      = 1;
    repeated AdGroupAd ads = 2;  // 已发布ad详情列表
}

// 更新已发布campaign状态, POST, /api/v1/google_advertise/ChangePublishedCampaignStatus
message ChangePublishedCampaignStatusReq {
    string   rtx                      = 1;  // 操作人
    repeated CampaignStatus campaigns = 2;
}

message ChangePublishedCampaignStatusRsp {
    aix.Result result                 = 1;
    repeated CampaignStatus campaigns = 2;  // 返回修改成功的campaign状态
}

message AdGroupStatus {
    int64 inner_ad_group_id       = 1;
    string ad_group_resource_name = 2;
    Status group_status           = 3;  // 参看枚举 Status
    string account_id             = 4;
}
// 更新已发布ad_group状态, POST, /api/v1/google_advertise/ChangePublishedAdGroupStatus
message ChangePublishedAdGroupStatusReq {
    string   rtx                     = 1;  // 操作人
    repeated AdGroupStatus ad_groups = 2;
}

message ChangePublishedAdGroupStatusRsp {
    aix.Result result                = 1;
    repeated AdGroupStatus ad_groups = 2;  // 返回修改成功的ad_group状态
}

message AdStatus {
    int64 inner_ad_id       = 1;
    string ad_resource_name = 2;
    Status ad_status        = 3;  // 参照枚举 Status
    string account_id             = 4;
}

// 更新已发布ad状态, POST, /api/v1/google_advertise/ChangePublishedAdGroupAdStatus
message ChangePublishedAdGroupAdStatusReq {
    string   rtx          = 1;  // 操作人
    repeated AdStatus ads = 2;
}

message ChangePublishedAdGroupAdStatusRsp {
    aix.Result result     = 1;
    repeated AdStatus ads = 2;  // 返回修改成功的ad状态
}

// 拉取campaign下的子级ad_group, POST, /api/v1/google_advertise/GetAdGroupsByCampaign
message GetAdGroupsByCampaignReq {
    string account_id             = 1;
    int64  inner_campaign_id      = 2;
    string campaign_resource_name = 3;
    CampaignType campaign_type    = 4;
}

message GetAdGroupsByCampaignRsp {
    aix.Result result     = 1;
    repeated AdGroup ad_groups = 2;  // ad_group详情列表
}

// 拉取ad_group下的子级ad, POST, /api/v1/google_advertise/GetAdsByAdGroup
message GetAdsByAdGroupReq {
    string account_id             = 1;
    int64  inner_ad_group_id      = 2;
    string ad_group_resource_name = 3;
}

message GetAdsByAdGroupRsp {
    aix.Result result      = 1;
    repeated AdGroupAd ads = 2;  // ad详情列表
}

message SqlCondition {
    string   column       = 1;
    string   operator     = 2; // 目前只支持: in, =, like, or_in; 条件之间是 and
    repeated string value = 3;
}

// 拉取草稿数据, POST, /api/v1/google_advertise/GetDrafts
message GetDraftsReq {
    repeated SqlCondition conditions = 1;
    int32    offset                  = 2;  // 拉取偏移
    int32    limit                   = 3;  // 拉取条数
    string   advertise_type          = 4;  // 类型：campaign, ad_group, ad_group_ad
}

// 草稿数据
message DraftView {
    string region                 = 1;
    string country                = 2;
    string os                     = 3;
    string date                   = 4;
    string campaign_goal          = 5;
    string custom_filed           = 6;
    string cost_type              = 7;
    string game_code              = 8;
    string account_id             = 9;
    int64  inner_campaign_id      = 10;
    string campaign_name          = 11;
    string campaign_resource_name = 12;
    double daily_budget           = 13;
    double bid_amount             = 14;
    string campaign_status        = 15;  // 参照枚举 Status
    string campaign_create_time   = 16;
    int64  inner_ad_group_id      = 17;
    string ad_group_name          = 18;
    string ad_group_resource_name = 19;
    string group_create_time      = 20;
    string group_status           = 21;  // 参照枚举 Status
    int64  inner_ad_id            = 22;
    string ad_name                = 23;
    string ad_resource_name       = 24;
    string ad_create_time         = 25;
    string ad_status              = 26;  // 参照枚举 Status
    string campaign_creator       = 27;
    string ad_group_creator       = 28;
    string ad_creator             = 29;
    string aix_campaign_type      = 30;
}

message GetDraftsRsp {
    aix.Result result           = 1;
    repeated   DraftView drafts = 2;  // 草稿列表
}

// 批量拉取已发布campaign概要信息, POST, /api/v1/google_advertise/GetPublishedCampaignsSummary
message GetPublishedCampaignsSummaryReq {
    string account_id                       = 1;  // 前端填写
    repeated string campaign_resource_names = 2;  // 前端填写，发布后的resource_name列表
}

message GetPublishedCampaignsSummaryRsp {
    aix.Result result           = 1;
    repeated Campaign campaigns = 2;  // 已发布campaign概要信息列表
}

// 批量拉取已发布ad_group概要信息, POST, /api/v1/google_advertise/GetPublishedAdGroupsSummary
message GetPublishedAdGroupsSummaryReq {
    string account_id                       = 1;  // 前端填写
    repeated string ad_group_resource_names = 2;  // 前端填写，发布后的resource_name列表
}

message GetPublishedAdGroupsSummaryRsp {
    aix.Result result          = 1;
    repeated AdGroup ad_groups = 2;  // 已发布ad_group概要信息列表
}

// 批量拉取已发布ad_group_ad概要信息, POST, /api/v1/google_advertise/GetPublishedAdGroupAdsSummary
message GetPublishedAdGroupAdsSummaryReq {
    string account_id                 = 1;  // 前端填写
    repeated string ad_resource_names = 2;  // 前端填写，发布后的resource_name列表
}

message GetPublishedAdGroupAdsSummaryRsp {
    aix.Result result      = 1;
    repeated AdGroupAd ads = 2;  // 已发布ad详情列表
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/CustomerConversionGoal]
message CustomerConversionGoal {
    string resource_name = 1;  // resource_name
    int32  category      = 2;  // 类型: 参看枚举ConversionActionCategory]
    int32  origin        = 3;  // 来源: 参看枚举ConversionOrigin]
    bool   biddable      = 4;  // biddable = true 表示账号默认
}


// 拉取customer_conversion_goal, POST, /api/v1/google_advertise/GetCustomerConversionGoal
message GetCustomerConversionGoalReq {
    string account_id                 = 1;  // 前端填写
}

message GetCustomerConversionGoalRsp {
    aix.Result result      = 1;
   repeated CustomerConversionGoal customer_conversion_goals = 2;
}

// 更新 ad group, POST, /api/v1/google_advertise/UpdatePublishedAdGroup
message UpdatePublishedAdGroupReq {
    string  account_id = 1;  // 前端填写
    AdGroup ad_group   = 2;  // ad_group信息, 更新后的全量数据
}

message UpdatePublishedAdGroupRsp {
    aix.Result result      = 1;
}

// 保存不校验草稿campaign, POST, /api/v1/google_advertise/SaveCampaignDraft
message SaveCampaignDraftReq {
    Campaign campaign = 1;  // campaign数据, 当 inner_campaign_id != 0 为更新
}

message SaveCampaignDraftRsp {
    aix.Result result       = 1;
    int64 inner_campaign_id = 2;  // 返回创建成功或本次更新的内部campaign_id
}

// 保存不校验草稿ad_group, POST, /api/v1/google_advertise/SaveAdGroupDraft
message SaveAdGroupDraftReq {
    string  account_id = 1;
    AdGroup ad_group   = 2;  // ad_group数据, 当 inner_ad_group_id != 0 为更新
}

message SaveAdGroupDraftRsp {
    aix.Result result       = 1;
    int64 inner_ad_group_id = 2;  // 返回创建成功或本次更新的内部ad_group_id
}

// 保存不校验草稿ad_group_ad, POST, /api/v1/google_advertise/SaveAdGroupAdDraft
message SaveAdGroupAdDraftReq {
    string    account_id = 1;
    AdGroupAd ad         = 2;  // ad数据, 当 inner_ad_id != 0 为更新
}

message SaveAdGroupAdDraftRsp {
    aix.Result result = 1;
    int64 inner_ad_id = 2;  // 返回创建成功或本次更新的内部ad_id
}

// 发布ad链路数据, POST, /api/v1/google_advertise/PublishAdLink
message PublishAdLinkReq {
    int64  inner_ad_id = 1;
    Status status      = 2;  // 发布状态，只能是 STATUS_PAUSED 或者 STATUS_ENABLE
}

message PublishAdLinkRsp {
    aix.Result result = 1;
    string campaign_resource_name = 2;
    string ad_group_resource_name = 3;
    string ad_resource_name       = 4;
    string failed_level           = 5;  // 发布失败时，标识是哪层失败
}

// 取消发布中状态, POST, /api/v1/google_advertise/CancelPublishing
message CancelPublishingReq {
    int64 inner_campaign_id = 1; // 优先使用inner_campaign_id
    int64 inner_ad_group_id = 2; // 其次使用inner_ad_group_id
    int64 inner_ad_id       = 3; // 最后使用inner_ad_id
}

message CancelPublishingRsp {
    aix.Result result = 1;
}


// 更新已发布campaign budget, POST, /api/v1/google_advertise/UpdatePublishedCampaignBudget
message UpdatePublishedCampaignBudgetReq {
    string account_id             = 1;  // 前端填写
    string campaign_resource_name = 2;
    double budget_amount          = 3;  // 前端填写 金额美元，小数 > 0
}

message UpdatePublishedCampaignBudgetRsp {
    aix.Result result      = 1;
}

// 更新已发布campaign 指定字段, POST, /api/v1/google_advertise/UpdatePublishedCampaignFields
message UpdatePublishedCampaignFieldsReq {
    message UpdateCampaign {
        double bid_amount = 1;  // 设置bid金额，小数
    }
    string      account_id             = 1;  // 前端填写
    string      campaign_resource_name = 2;
    UpdateCampaign update_campaign     = 3;  // 支持修改的campaign字段结构
    repeated    string fields          = 4;  // 指定修改哪些字段（或的关系），如：["bid_amount"]
}

message UpdatePublishedCampaignFieldsRsp {
    aix.Result result      = 1;
}

message StatusStatistic {
    string status = 1; // 状态
    int32  num    = 2; // 个数
}

message CampaignStatusStatistics {
    int64    inner_campaign_id                   = 1;
    string   campaign_resource_name              = 2;
    repeated StatusStatistic ad_group_statistics = 3;
    repeated StatusStatistic ad_statistics       = 4;
}

message AdGroupStatusStatistics {
    int64    inner_ad_group_id                   = 1;
    string   ad_group_resource_name              = 2;
    repeated StatusStatistic ad_statistics       = 3;
}

// 获取campaign子级状态统计, POST, /api/v1/google_advertise/GetCampaignStatusStatistics
message GetCampaignStatusStatisticsReq {
    repeated int64 inner_campaign_id       = 1; // 内部campaign id
    repeated string campaign_resource_name = 2; // 已发布campaign resource_name
}

message GetCampaignStatusStatisticsRsp {
    aix.Result result      = 1;
    repeated CampaignStatusStatistics statistics = 2;
}

// 获取ad_group子级状态统计, POST, /api/v1/google_advertise/GetAdGroupStatusStatistics
message GetAdGroupStatusStatisticsReq {
    repeated int64 inner_ad_group_id       = 1; // 内部ad_group id
    repeated string ad_group_resource_name = 2; // 已发布ad_group resource_name
}

message GetAdGroupStatusStatisticsRsp {
    aix.Result result      = 1;
    repeated AdGroupStatusStatistics statistics = 2;
}

// 获取sitelink素材列表, POST, /api/v1/google_advertise/GetSitelinks
message GetSitelinksReq {
    string account_id = 1;  // 前端填写
}

message GetSitelinksRsp {
    aix.Result result          = 1;
    repeated   Asset sitelinks = 2;  // 拉取到的sitelink列表
}

// 创建sitelink素材, POST, /api/v1/google_advertise/CreateSitelink
message CreateSitelinkReq {
    string account_id = 1;  // 前端填写
    Asset  sitelink   = 2;
}

message CreateSitelinkRsp {
    aix.Result result = 1;
    string resource_name = 2; // 创建成功的resource_name
}

// 获取custom_audience列表, POST, /api/v1/google_advertise/GetCustomAudiences
message GetCustomAudiencesReq {
    string account_id = 1;  // 前端填写
}

message GetCustomAudiencesRsp {
    aix.Result result          = 1;
    repeated   CustomAudience custom_audiences = 2;  // 拉取到的custom_audience列表
}

// 创建custom_audience, POST, /api/v1/google_advertise/CreateCustomAudience
message CreateCustomAudienceReq {
    string account_id = 1;  // 前端填写
    CustomAudience  custom_audience   = 2;
}

message CreateCustomAudienceRsp {
    aix.Result result = 1;
    string resource_name = 2; // 创建成功的resource_name
}

// 拉取audience列表(audience signal), POST, /api/v1/google_advertise/GetAudiences
message GetAudiencesReq {
    string account_id = 1;  // 前端填写
}

message GetAudiencesRsp {
    aix.Result result = 1;
    repeated Audience  audiences = 2;
}

// 创建audience(audience signal), POST, /api/v1/google_advertise/CreateAudience
message CreateAudienceReq {
    string account_id = 1;  // 前端填写
    Audience audience = 2;
}

message CreateAudienceRsp {
    aix.Result result = 1;
    string resource_name = 2; // 创建成功的resource_name
}

message AudienceOrEvent {
    message TDInfo {
        string id   = 1;
        string name = 2;
    }

    string account_id    = 1;
    string game_code     = 2;
    string audience_type = 3;
    string created_by    = 4;
    string data_source   = 5;
    string os            = 6;
    string status        = 7;
    string name          = 8;

    string google_action_name            = 9;
    string google_userlist_resource_name = 10;
    TDInfo td_info                       = 11;

    // event的app_id
    string app_id = 12;
}

// 拉取audience/event, POST, /api/v1/google_advertise/GetAudiencesOrEvents
message GetAudiencesOrEventsReq {
    string account_id    = 1;
    string game_code     = 2;
    string audience_type = 3;
    string app_id        = 4; // 拉取event时候，传入app_id过滤
}

message GetAudiencesOrEventsRsp {
    aix.Result result = 1;
    repeated AudienceOrEvent data = 2; // audience/event 列表
}

message OperatingSystemVersionItem {
    string value    = 1;  // 即OperatingSystemVersionConstant id
    string platform = 2;  // iOS/Android
    string version  = 3;  // eg: 15.0
    bool   greater  = 4;  // GREATER_THAN_EQUALS_TO为true
}

message OperatingSystemVersion {
    repeated OperatingSystemVersionItem ios     = 1;
    repeated OperatingSystemVersionItem android = 2;
}

// 拉取operating_system_version配置, POST, /api/v1/google_advertise/GetOperatingSystemVersion
message GetOperatingSystemVersionReq {
}

message GetOperatingSystemVersionRsp {
    aix.Result result = 1;
    OperatingSystemVersion operating_system_version = 2; 
}

message CopyCampaignReq {
    string      account_id              = 1;  // 渠道账号id
    int64       inner_campaign_id       = 2;
    string      campaign_resource_name  = 3;
    string      copy_type               = 4;  // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyCampaignRsp {
    aix.Result  result                  = 1;
    int64       inner_campaign_id       = 2;  // 生成复制后的草稿 inner_campaign_id
}

message CopyAdGroupReq {
    string      account_id              = 1;  // 渠道账号id
    int64       inner_ad_group_id       = 2;
    string      ad_group_resource_name  = 3;
    string      copy_type               = 4;  // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyAdGroupRsp {
    aix.Result  result                  = 1;
    int64       inner_ad_group_id       = 2;  // 生成复制后的草稿 inner_ad_group_id
}

message CopyAdGroupAdReq {
    string      account_id              = 1;  // 渠道账号id
    int64       inner_ad_id             = 2;
    string      ad_resource_name        = 3;
}
  
message CopyAdGroupAdRsp {
    aix.Result  result                  = 1;
    int64       inner_ad_id             = 2; //  生成复制后的草稿 inner_ad_id
}

// 更新campaign数据(草稿/已发布), POST, /api/v1/google_advertise/UpdateCampaign
message UpdateCampaignReq {
    Campaign campaign = 1;  // campaign数据
}

message UpdateCampaignRsp {
    aix.Result result       = 1;
}

// 更新ad_group数据(草稿/已发布), POST, /api/v1/google_advertise/UpdateAdGroup
message UpdateAdGroupReq {
    string account_id = 1;
    AdGroup ad_group  = 2;  // ad_group数据
}

message UpdateAdGroupRsp {
    aix.Result result       = 1;
}

// 更新ad数据(草稿/已发布), POST, /api/v1/google_advertise/UpdateAd
message UpdateAdReq {
    string    account_id = 1;
    AdGroupAd ad         = 2;  // ad数据
}

message UpdateAdRsp {
    aix.Result result       = 1;
}

// https://developers.google.com/google-ads/api/docs/keyword-planning/generate-keyword-ideas?hl=zh-cn#python
message GenerateKeywordIdeasReq {
    repeated string keywords = 1;  // A Keyword or phrase to generate ideas from, for example, cars.
    string   page_token      = 2;  // Token of the page to retrieve. If not specified, the first page of results will be returned. To request next page of results use the value obtained from next_page_token in the previous response
    int32    page_size       = 3;  // Number of results to retrieve in a single page. A maximum of 10,000 results may be returned, if the page_size exceeds this, it is ignored.
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/MonthOfYearEnum.MonthOfYear?hl=en
enum MonthOfYear {
    MONTHOFYEAR_UNSPECIFIED = 0;
    MONTHOFYEAR_UNKNOWN     = 1;
    MONTHOFYEAR_JANUARY     = 2;
    MONTHOFYEAR_FEBRUARY    = 3;
    MONTHOFYEAR_MARCH       = 4;
    MONTHOFYEAR_APRIL       = 5;
    MONTHOFYEAR_MAY         = 6;
    MONTHOFYEAR_JUNE        = 7;
    MONTHOFYEAR_JULY        = 8;
    MONTHOFYEAR_AUGUST      = 9;
    MONTHOFYEAR_SEPTEMBER   = 10;
    MONTHOFYEAR_OCTOBER     = 11;
    MONTHOFYEAR_NOVEMBER    = 12;
    MONTHOFYEAR_DECEMBER    = 13;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/MonthlySearchVolume?hl=en
message MonthlySearchVolume {
    int32 month            = 1; // 参看枚举 MonthOfYear
    int64 year             = 2;
    int64 monthly_searches = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/KeywordPlanCompetitionLevelEnum.KeywordPlanCompetitionLevel?hl=en
enum KeywordPlanCompetitionLevel {
    KEYWORDPLANCOMPETITIONLEVEL_UNSPECIFIED = 0;
    KEYWORDPLANCOMPETITIONLEVEL_UNKNOWN     = 1;
    KEYWORDPLANCOMPETITIONLEVEL_LOW         = 2;
    KEYWORDPLANCOMPETITIONLEVEL_MEDIUM      = 3;
    KEYWORDPLANCOMPETITIONLEVEL_HIGH        = 4;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/KeywordPlanHistoricalMetrics?hl=en
message KeywordPlanHistoricalMetrics {
    repeated MonthlySearchVolume monthly_search_volumes = 1;  // Approximate number of searches on this query for the past twelve months.
    int32    competition                                = 2;  // 参看枚举 KeywordPlanCompetitionLevel The competition level for the query.
    int64    avg_monthly_searches                       = 3;  // Approximate number of monthly searches on this query, averaged for the past 12 months.
    int64    competition_index                          = 4;  // The competition index for the query in the range [0, 100]
    int64    low_top_of_page_bid_micros                 = 5;  // Top of page bid low range (20th percentile) in micros for the keyword.
    int64    high_top_of_page_bid_micros                = 6;  // Top of page bid high range (80th percentile) in micros for the keyword.
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/GenerateKeywordIdeaResult?hl=en
message GenerateKeywordIdeaResult {
    KeywordPlanHistoricalMetrics keyword_idea_metrics = 1; // The historical metrics for the keyword.
    string text = 2; // Text of the keyword idea.
}

// comment[response]
message GenerateKeywordIdeasRsp {
    aix.Result   result                        = 1;  // comment[返回结果]; default[xxx]
    repeated GenerateKeywordIdeaResult results = 2;
    string   next_page_token                   = 3;
    int64    total_size                        = 4;
}

// 变更历史
message ChangeHistory {
    string   resource_name          = 1;   // 变更resource_name
    string   objective              = 2;   // 变更对象
    string   operator               = 3;   // 变更人
    string   change_date_time       = 4;   // 变更时间
    string   campaign_resource_name = 5;   // 变更所属的campaign
    string   adgroup_resource_name  = 6;   // 变更所属的adgroup
    string   ad_resource_name       = 7;   // 变更所属的ad
    string   change_summary         = 8;   // 变更简介
    string   change_type            = 9;   // 变更类型
    repeated string changes         = 10;  // 变更详情
}

// 拉取广告变更历史, POST, /api/v1/google_advertise/GetChangeHistory
message GetChangeHistoryReq {
    string   account_id             = 1;
    string   campaign_resource_name = 2;   // 变更所属的campaign
    string   adgroup_resource_name  = 3;   // 变更所属的adgroup
    string   ad_resource_name       = 4;   // 变更所属的ad
    repeated string   change_types  = 5;   // 变更类型
    repeated string   object_types  = 6;   // 变更对象
    string   start_time             = 7;   // 变更开始时间: YYYY-MM-dd HH:mm:ss (注意要以account的时区)
    string   end_time               = 8;   // 变更结束时间: YYYY-MM-dd HH:mm:ss (注意要以account的时区)
    int32    page                   = 9;   // 分页数，从1开始
    int32    page_size              = 10;  // 每条条数，最多1000条
}

message GetChangeHistoryRsp {
    aix.Result result             = 1;
    repeated   ChangeHistory list = 2;  // 变更记录列表
    int32      total              = 3;  // 总数
}

message GetMediaImageVideoReq {
    string account_id             = 1;
    repeated string asset_resource_names = 2;  // 素材的resource_name列表
}

// 图片视频信息
message ImageVideo {
    string id              = 1;  // 图片视频resource_name
    int32  type            = 2;  // 1-视频，2-图片
    string name            = 3;  // 名称
    int32  width           = 4;  // 宽
    int32  height          = 5;  // 高
    string yotube_video_id = 6;
    string thumbnail_url   = 7;  // 缩略图
}

message GetMediaImageVideoRsp {
    aix.Result result     = 1;
    repeated ImageVideo assets = 2; // 素材信息
}

// 增加游戏账号, POST, /api/v1/google_advertise/AddGameAccounts
message AddGameAccountsReq {
    string   game_code            = 1;
    repeated string   account_ids = 2;
}

message AddGameAccountsRsp {
    aix.Result result     = 1;
}

// 更新已发布campaign国家地区, POST, /api/v1/google_advertise/UpdatePublishedCampaignLocations
message UpdatePublishedCampaignLocationsReq {
    string   game_code              = 1;
    string   account_id             = 2;
    string   campaign_id            = 3;
    string   campaign_resource_name = 4;  // 优先用campaign_resource_name， 没有就用account_id+campaign_id
    repeated string   includes      = 5;  // 包括的国家地区, 前端传的格式：geoTargetConstants/9208326
    repeated string   excludes      = 6;  // 排除的国家地区, 前端传的格式：geoTargetConstants/9208326
}

message UpdatePublishedCampaignLocationsRsp {
    aix.Result result     = 1;
}