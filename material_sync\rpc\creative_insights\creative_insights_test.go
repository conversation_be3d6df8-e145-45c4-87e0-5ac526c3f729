package creative_insights

import (
	"context"
	"testing"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func TestAddMapping(t *testing.T) {
	var core zapcore.Core
	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	log.Logger = logger.Sugar()

	req_id := uuid.New().String()
	req := &AddMappingReq{}
	req.ReqID = req_id
	var video Video
	video.GameCode = "test"
	video.StorageType = 5
	video.StorageURL = "https://www.youtube.com/watch?v=p0V0LIP31q4"
	video.VideoID = "p0V0LIP31q4"
	req.VideoList = append(req.VideoList, video)
	ctx := context.Background()
	target := "http://************:8080"
	t.Logf("req: %+v", req)

	rsp, err := AddMapping(ctx, target, req)
	if err != nil {
		t.Log(err.Error())
		return
	}

	t.Logf("get rsp: %+v", rsp)
}
