package service

import (
	"bytes"
	"fmt"
	"path/filepath"
	"strings"

	"github.com/xuri/excelize/v2"
)

const (
	sheetName = "Sheet1"
)

// Sheet excel sheet
type Sheet struct {
	Name       string          // sheet名
	Titles     []string        // 标题, 即第一行
	Datas      [][]interface{} // 内容, 从第二行开始
	mergeCells []mergeCell
}

// WithMergeCell 合并单元格
// startCell:左上角坐标 endCell:右下角坐标
// 如：startCell:"A1" endCell:"B2" 表示 A1,B1,A2,B2合并为一个单元格，结果只会保留左上角的内容
func (s *Sheet) WithMergeCell(startCell, endCell string) *Sheet {
	s.mergeCells = append(s.mergeCells, mergeCell{
		StartCell: startCell,
		EndCell:   endCell,
	})
	return s
}

type mergeCell struct {
	StartCell string
	EndCell   string
}

// NewExcelBufferWithSheets 生成多sheet excel
func NewExcelBufferWithSheets(sheets []*Sheet) (*bytes.Buffer, error) {
	err := checkSheets(sheets)
	if err != nil {
		return nil, err
	}

	// 生成excel数据
	f, err := newExcelWithSheets(sheets)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	fileBuf, err := f.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("excelize WriteToBuffer failed, err:%v", err)
	}
	return fileBuf, nil
}

// NewExcelBufferWithRows 按行生成一个excel buffer
func NewExcelBufferWithRows(titles []string, datas [][]interface{}) (*bytes.Buffer, error) {
	if len(titles) <= 0 {
		return nil, fmt.Errorf("invalid params")
	}

	// 生成excel数据
	sheetIndex, f, err := newExcelWithRows(titles, datas)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	// 输出buffer数据
	f.SetActiveSheet(sheetIndex)
	fileBuf, err := f.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("excelize WriteToBuffer failed, err:%v", err)
	}

	return fileBuf, nil
}

// 按行生成excel数据
func newExcelWithRows(titles []string, datas [][]interface{}) (int, *excelize.File, error) {
	f := excelize.NewFile()
	sheetIndex := f.NewSheet(sheetName)

	// 填充标题
	err := f.SetSheetRow(sheetName, "A1", &titles)
	if err != nil {
		return 0, nil, fmt.Errorf("excelize title SetSheetRow failed, err:%v", err)
	}

	// 按行填入数据
	for i, row := range datas {
		axis := fmt.Sprintf("A%d", i+2)
		err := f.SetSheetRow(sheetName, axis, &row)
		if err != nil {
			return 0, nil, fmt.Errorf("excelize data axis:%v SetSheetRow failed, err:%v", axis, err)
		}
	}

	setSheetStype(f, sheetName, len(titles), len(datas)+1)

	return sheetIndex, f, nil
}

func setSheetStype(f *excelize.File, sheetName string, columnNum, rowNum int) error {
	colName, err := excelize.ColumnNumberToName(columnNum)
	if err != nil {
		return err
	}
	f.SetColWidth(sheetName, "A", colName, 15)

	style, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			WrapText: true,
		},
	})
	if err != nil {
		return err
	}
	f.SetRowStyle(sheetName, 1, rowNum, style)
	return nil
}

func checkSheets(sheets []*Sheet) error {
	if len(sheets) <= 0 {
		return fmt.Errorf("sheets length 0")
	}

	// 判断sheet名不能重复
	seen := make(map[string]struct{}, len(sheets))
	for idx, sheet := range sheets {
		name := sheet.Name
		if name == "" {
			return fmt.Errorf("sheet[%d] name empty", idx)
		}

		if _, ok := seen[name]; ok {
			return fmt.Errorf("sheet[%d] name:%v duplicate", idx, name)
		}

		seen[name] = struct{}{}
	}

	return nil
}

// 按行生成excel 多sheet数据
func newExcelWithSheets(sheets []*Sheet) (*excelize.File, error) {
	f := excelize.NewFile()

	for idx, sheet := range sheets {
		var err error
		// excel创建后默认会有一个Sheet1, 所以这里第一个sheet改名，后面的sheet增加
		if idx == 0 {
			f.SetSheetName(sheetName, sheet.Name)
		} else {
			f.NewSheet(sheet.Name)
		}
		// 填充标题
		err = f.SetSheetRow(sheet.Name, "A1", &sheet.Titles)
		if err != nil {
			return nil, fmt.Errorf("%s SetSheetRow titles error:%v", sheet.Name, err)
		}
		// 按行填入数据
		for i, row := range sheet.Datas {
			axis := fmt.Sprintf("A%d", i+2)
			err = f.SetSheetRow(sheet.Name, axis, &row)
			if err != nil {
				return nil, fmt.Errorf("%s data axis:%v SetSheetRow failed, err:%v", sheet.Name, axis, err)
			}
		}

		for _, mergeCell := range sheet.mergeCells {
			err = f.MergeCell(sheet.Name, mergeCell.StartCell, mergeCell.EndCell)
			if err != nil {
				return nil, fmt.Errorf("%s mergeCell:%+v err:%v", sheet.Name, mergeCell, err)
			}
		}
		setSheetStype(f, sheet.Name, len(sheet.Titles), len(sheet.Datas))
	}
	return f, nil
}

func removeFileExtension(name string) string {
	ext := filepath.Ext(name)
	if ext != "" {
		name = strings.TrimSuffix(name, ext)
	}

	return name
}
