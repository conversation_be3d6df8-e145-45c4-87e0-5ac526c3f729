// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: material_sync/material_sync.proto

package material_sync

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 内部接口 触发定时任务, POST, /api/v1/material_sync/cron_trigger
type CronTriggerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CronName string `protobuf:"bytes,1,opt,name=cron_name,json=cronName,proto3" json:"cron_name,omitempty"` // 定时任务名字
	GameCode string `protobuf:"bytes,2,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Date     string `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"` // 日期格式：20231201
}

func (x *CronTriggerReq) Reset() {
	*x = CronTriggerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronTriggerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronTriggerReq) ProtoMessage() {}

func (x *CronTriggerReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronTriggerReq.ProtoReflect.Descriptor instead.
func (*CronTriggerReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{0}
}

func (x *CronTriggerReq) GetCronName() string {
	if x != nil {
		return x.CronName
	}
	return ""
}

func (x *CronTriggerReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CronTriggerReq) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

type CronTriggerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *CronTriggerRsp) Reset() {
	*x = CronTriggerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronTriggerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronTriggerRsp) ProtoMessage() {}

func (x *CronTriggerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronTriggerRsp.ProtoReflect.Descriptor instead.
func (*CronTriggerRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{1}
}

func (x *CronTriggerRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 编辑素材机器标签和封面, POST, /api/v1/material_sync/set_material_label
type SetMaterialLabelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId               string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                                             // 素材id
	AlgorithmThumbnailUrl string `protobuf:"bytes,2,opt,name=algorithm_thumbnail_url,json=algorithmThumbnailUrl,proto3" json:"algorithm_thumbnail_url,omitempty"` // 新封面
	FirstLevelLabels      string `protobuf:"bytes,3,opt,name=first_level_labels,json=firstLevelLabels,proto3" json:"first_level_labels,omitempty"`                // 机器一级标签
	SecondLevelLabels     string `protobuf:"bytes,4,opt,name=second_level_labels,json=secondLevelLabels,proto3" json:"second_level_labels,omitempty"`             // 机器二级标签
	DepotName             string `protobuf:"bytes,5,opt,name=depot_name,json=depotName,proto3" json:"depot_name,omitempty"`                                       // 游戏代码
	CosUrl                string `protobuf:"bytes,6,opt,name=cos_url,json=cosUrl,proto3" json:"cos_url,omitempty"`                                                // cos url
}

func (x *SetMaterialLabelReq) Reset() {
	*x = SetMaterialLabelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMaterialLabelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaterialLabelReq) ProtoMessage() {}

func (x *SetMaterialLabelReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaterialLabelReq.ProtoReflect.Descriptor instead.
func (*SetMaterialLabelReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{2}
}

func (x *SetMaterialLabelReq) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *SetMaterialLabelReq) GetAlgorithmThumbnailUrl() string {
	if x != nil {
		return x.AlgorithmThumbnailUrl
	}
	return ""
}

func (x *SetMaterialLabelReq) GetFirstLevelLabels() string {
	if x != nil {
		return x.FirstLevelLabels
	}
	return ""
}

func (x *SetMaterialLabelReq) GetSecondLevelLabels() string {
	if x != nil {
		return x.SecondLevelLabels
	}
	return ""
}

func (x *SetMaterialLabelReq) GetDepotName() string {
	if x != nil {
		return x.DepotName
	}
	return ""
}

func (x *SetMaterialLabelReq) GetCosUrl() string {
	if x != nil {
		return x.CosUrl
	}
	return ""
}

type SetRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // 素材id
	SetRes  uint32 `protobuf:"varint,2,opt,name=set_res,json=setRes,proto3" json:"set_res,omitempty"`   // 设置结果 0-失败 1-成功
}

func (x *SetRes) Reset() {
	*x = SetRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRes) ProtoMessage() {}

func (x *SetRes) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRes.ProtoReflect.Descriptor instead.
func (*SetRes) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{3}
}

func (x *SetRes) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *SetRes) GetSetRes() uint32 {
	if x != nil {
		return x.SetRes
	}
	return 0
}

type SetMaterialLabelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result     *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                             // 返回结果
	SetResList *SetRes     `protobuf:"bytes,2,opt,name=set_res_list,json=setResList,proto3" json:"set_res_list,omitempty"` // 设置结果列表
}

func (x *SetMaterialLabelRsp) Reset() {
	*x = SetMaterialLabelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMaterialLabelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaterialLabelRsp) ProtoMessage() {}

func (x *SetMaterialLabelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaterialLabelRsp.ProtoReflect.Descriptor instead.
func (*SetMaterialLabelRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{4}
}

func (x *SetMaterialLabelRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SetMaterialLabelRsp) GetSetResList() *SetRes {
	if x != nil {
		return x.SetResList
	}
	return nil
}

// 自测接口, POST, /api/v1/material_sync/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{5}
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{6}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 自测接口, POST, /api/v1/material_sync/verify_asset
type VerifyAssetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`
}

func (x *VerifyAssetReq) Reset() {
	*x = VerifyAssetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAssetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAssetReq) ProtoMessage() {}

func (x *VerifyAssetReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAssetReq.ProtoReflect.Descriptor instead.
func (*VerifyAssetReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{7}
}

func (x *VerifyAssetReq) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type VerifyAssetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result           *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                               // 返回结果
	FullPathName     string      `protobuf:"bytes,2,opt,name=full_path_name,json=fullPathName,proto3" json:"full_path_name,omitempty"`             // ...
	YoutubeId        string      `protobuf:"bytes,3,opt,name=youtube_id,json=youtubeId,proto3" json:"youtube_id,omitempty"`                        // ...
	ChannelAccountId string      `protobuf:"bytes,4,opt,name=channel_account_id,json=channelAccountId,proto3" json:"channel_account_id,omitempty"` // ...
	ChannelAssetId   string      `protobuf:"bytes,5,opt,name=channel_asset_id,json=channelAssetId,proto3" json:"channel_asset_id,omitempty"`       // ...
	AssetName        string      `protobuf:"bytes,6,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`                        // ...
}

func (x *VerifyAssetRsp) Reset() {
	*x = VerifyAssetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyAssetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAssetRsp) ProtoMessage() {}

func (x *VerifyAssetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAssetRsp.ProtoReflect.Descriptor instead.
func (*VerifyAssetRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{8}
}

func (x *VerifyAssetRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *VerifyAssetRsp) GetFullPathName() string {
	if x != nil {
		return x.FullPathName
	}
	return ""
}

func (x *VerifyAssetRsp) GetYoutubeId() string {
	if x != nil {
		return x.YoutubeId
	}
	return ""
}

func (x *VerifyAssetRsp) GetChannelAccountId() string {
	if x != nil {
		return x.ChannelAccountId
	}
	return ""
}

func (x *VerifyAssetRsp) GetChannelAssetId() string {
	if x != nil {
		return x.ChannelAssetId
	}
	return ""
}

func (x *VerifyAssetRsp) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

// 自测接口, POST, /api/v1/material_sync/sync_creative_recommend_manual
type SyncCreativeRecommendManualReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncCreativeRecommendManualReq) Reset() {
	*x = SyncCreativeRecommendManualReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncCreativeRecommendManualReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCreativeRecommendManualReq) ProtoMessage() {}

func (x *SyncCreativeRecommendManualReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCreativeRecommendManualReq.ProtoReflect.Descriptor instead.
func (*SyncCreativeRecommendManualReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{9}
}

type SyncCreativeRecommendManualRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SyncCreativeRecommendManualRsp) Reset() {
	*x = SyncCreativeRecommendManualRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncCreativeRecommendManualRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCreativeRecommendManualRsp) ProtoMessage() {}

func (x *SyncCreativeRecommendManualRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCreativeRecommendManualRsp.ProtoReflect.Descriptor instead.
func (*SyncCreativeRecommendManualRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{10}
}

func (x *SyncCreativeRecommendManualRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 自测接口, POST, /api/v1/material_sync/rectify_creative_upload_manual
type RectifyCreativeUploadManualReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RectifyCreativeUploadManualReq) Reset() {
	*x = RectifyCreativeUploadManualReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RectifyCreativeUploadManualReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RectifyCreativeUploadManualReq) ProtoMessage() {}

func (x *RectifyCreativeUploadManualReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RectifyCreativeUploadManualReq.ProtoReflect.Descriptor instead.
func (*RectifyCreativeUploadManualReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{11}
}

type RectifyCreativeUploadManualRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *RectifyCreativeUploadManualRsp) Reset() {
	*x = RectifyCreativeUploadManualRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RectifyCreativeUploadManualRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RectifyCreativeUploadManualRsp) ProtoMessage() {}

func (x *RectifyCreativeUploadManualRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RectifyCreativeUploadManualRsp.ProtoReflect.Descriptor instead.
func (*RectifyCreativeUploadManualRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{12}
}

func (x *RectifyCreativeUploadManualRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 非实体素材
type VirtualAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId      string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                // 素材ID
	AssetName    string `protobuf:"bytes,2,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`          // 素材名称
	AssetType    int32  `protobuf:"varint,3,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`         // 素材类型. 1-youtube video id; 2-素材编号
	AssetVersion string `protobuf:"bytes,4,opt,name=asset_version,json=assetVersion,proto3" json:"asset_version,omitempty"` // 素材版本, 弃用
	AssetTheme   string `protobuf:"bytes,5,opt,name=asset_theme,json=assetTheme,proto3" json:"asset_theme,omitempty"`       // 素材主题, 弃用
	LabelName    string `protobuf:"bytes,6,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`          // 标签名
	FirstLabel   string `protobuf:"bytes,7,opt,name=first_label,json=firstLabel,proto3" json:"first_label,omitempty"`       // 一级标签
	SecondLabel  string `protobuf:"bytes,8,opt,name=second_label,json=secondLabel,proto3" json:"second_label,omitempty"`    // 二级标签
}

func (x *VirtualAsset) Reset() {
	*x = VirtualAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VirtualAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirtualAsset) ProtoMessage() {}

func (x *VirtualAsset) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirtualAsset.ProtoReflect.Descriptor instead.
func (*VirtualAsset) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{13}
}

func (x *VirtualAsset) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *VirtualAsset) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *VirtualAsset) GetAssetType() int32 {
	if x != nil {
		return x.AssetType
	}
	return 0
}

func (x *VirtualAsset) GetAssetVersion() string {
	if x != nil {
		return x.AssetVersion
	}
	return ""
}

func (x *VirtualAsset) GetAssetTheme() string {
	if x != nil {
		return x.AssetTheme
	}
	return ""
}

func (x *VirtualAsset) GetLabelName() string {
	if x != nil {
		return x.LabelName
	}
	return ""
}

func (x *VirtualAsset) GetFirstLabel() string {
	if x != nil {
		return x.FirstLabel
	}
	return ""
}

func (x *VirtualAsset) GetSecondLabel() string {
	if x != nil {
		return x.SecondLabel
	}
	return ""
}

// 上传非实体素材, POST, /api/v1/material_sync/upload_virtual_assets
type UploadVirtualAssetsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets          []*VirtualAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`                                             // 虚拟素材列表
	UserName        string          `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`                         // 上传用户名
	DigAssetVersion int32           `protobuf:"varint,3,opt,name=dig_asset_version,json=digAssetVersion,proto3" json:"dig_asset_version,omitempty"` // 是否挖掘素材版本. 0-否, 1-是
	MapHistoryType  int32           `protobuf:"varint,4,opt,name=map_history_type,json=mapHistoryType,proto3" json:"map_history_type,omitempty"`    // 映射历史广告素材方式. 1-使用新插入的虚拟标签进行映射; 2-直接使用上传的虚拟标签进行映射. 默认使用1.
}

func (x *UploadVirtualAssetsReq) Reset() {
	*x = UploadVirtualAssetsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadVirtualAssetsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadVirtualAssetsReq) ProtoMessage() {}

func (x *UploadVirtualAssetsReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadVirtualAssetsReq.ProtoReflect.Descriptor instead.
func (*UploadVirtualAssetsReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{14}
}

func (x *UploadVirtualAssetsReq) GetAssets() []*VirtualAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *UploadVirtualAssetsReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UploadVirtualAssetsReq) GetDigAssetVersion() int32 {
	if x != nil {
		return x.DigAssetVersion
	}
	return 0
}

func (x *UploadVirtualAssetsReq) GetMapHistoryType() int32 {
	if x != nil {
		return x.MapHistoryType
	}
	return 0
}

type UploadVirtualAssetsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *UploadVirtualAssetsRsp) Reset() {
	*x = UploadVirtualAssetsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadVirtualAssetsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadVirtualAssetsRsp) ProtoMessage() {}

func (x *UploadVirtualAssetsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadVirtualAssetsRsp.ProtoReflect.Descriptor instead.
func (*UploadVirtualAssetsRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{15}
}

func (x *UploadVirtualAssetsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 设置素材映射, POST, /api/v1/material_sync/set_asset_map
type SetAssetMapReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqId          string `protobuf:"bytes,1,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`                              // 请求ID
	VideoId        string `protobuf:"bytes,2,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty"`                        // 视频ID
	MappingAssetId string `protobuf:"bytes,3,opt,name=mapping_asset_id,json=mappingAssetId,proto3" json:"mapping_asset_id,omitempty"` // 映射到的素材ID, 为空表示没有映射到
	StorageType    int32  `protobuf:"varint,4,opt,name=storage_type,json=storageType,proto3" json:"storage_type,omitempty"`           // 存储类型, 1-cos，2-arthub，3-google_driver，4-web_http，5-youtube_url
	ErrorMsg       string `protobuf:"bytes,5,opt,name=error_msg,json=errorMsg,proto3" json:"error_msg,omitempty"`                     // 没有映射到素材ID时返回的错误信息
}

func (x *SetAssetMapReq) Reset() {
	*x = SetAssetMapReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAssetMapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAssetMapReq) ProtoMessage() {}

func (x *SetAssetMapReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAssetMapReq.ProtoReflect.Descriptor instead.
func (*SetAssetMapReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{16}
}

func (x *SetAssetMapReq) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *SetAssetMapReq) GetVideoId() string {
	if x != nil {
		return x.VideoId
	}
	return ""
}

func (x *SetAssetMapReq) GetMappingAssetId() string {
	if x != nil {
		return x.MappingAssetId
	}
	return ""
}

func (x *SetAssetMapReq) GetStorageType() int32 {
	if x != nil {
		return x.StorageType
	}
	return 0
}

func (x *SetAssetMapReq) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type SetAssetMapRsq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SetAssetMapRsq) Reset() {
	*x = SetAssetMapRsq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAssetMapRsq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAssetMapRsq) ProtoMessage() {}

func (x *SetAssetMapRsq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAssetMapRsq.ProtoReflect.Descriptor instead.
func (*SetAssetMapRsq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{17}
}

func (x *SetAssetMapRsq) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 自测接口, POST, /api/v1/material_sync/start_media_content_map
type StartMediaContentMapReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StartMediaContentMapReq) Reset() {
	*x = StartMediaContentMapReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartMediaContentMapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartMediaContentMapReq) ProtoMessage() {}

func (x *StartMediaContentMapReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartMediaContentMapReq.ProtoReflect.Descriptor instead.
func (*StartMediaContentMapReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{18}
}

type StartMediaContentMapRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *StartMediaContentMapRsp) Reset() {
	*x = StartMediaContentMapRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartMediaContentMapRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartMediaContentMapRsp) ProtoMessage() {}

func (x *StartMediaContentMapRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartMediaContentMapRsp.ProtoReflect.Descriptor instead.
func (*StartMediaContentMapRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{19}
}

func (x *StartMediaContentMapRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 同步游戏历史上线数据, POST, /api/v1/material_sync/sync_overview_online_status_history
type SyncOverviewOnlineStatusHistoryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // game code
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 开始时间
	EndTime   string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 结束时间
}

func (x *SyncOverviewOnlineStatusHistoryReq) Reset() {
	*x = SyncOverviewOnlineStatusHistoryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncOverviewOnlineStatusHistoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOverviewOnlineStatusHistoryReq) ProtoMessage() {}

func (x *SyncOverviewOnlineStatusHistoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOverviewOnlineStatusHistoryReq.ProtoReflect.Descriptor instead.
func (*SyncOverviewOnlineStatusHistoryReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{20}
}

func (x *SyncOverviewOnlineStatusHistoryReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *SyncOverviewOnlineStatusHistoryReq) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *SyncOverviewOnlineStatusHistoryReq) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type SyncOverviewOnlineStatusHistoryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SyncOverviewOnlineStatusHistoryRsp) Reset() {
	*x = SyncOverviewOnlineStatusHistoryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncOverviewOnlineStatusHistoryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncOverviewOnlineStatusHistoryRsp) ProtoMessage() {}

func (x *SyncOverviewOnlineStatusHistoryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncOverviewOnlineStatusHistoryRsp.ProtoReflect.Descriptor instead.
func (*SyncOverviewOnlineStatusHistoryRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{21}
}

func (x *SyncOverviewOnlineStatusHistoryRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 通知接口，将某个标签规则应用到aix library, POST, /api/v1/material_sync/notify_apply_label_rule_to_aix
type NotifyApplyLabelRuleToAixReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // game code
	RuleId   int64  `protobuf:"varint,2,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`
}

func (x *NotifyApplyLabelRuleToAixReq) Reset() {
	*x = NotifyApplyLabelRuleToAixReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyApplyLabelRuleToAixReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyApplyLabelRuleToAixReq) ProtoMessage() {}

func (x *NotifyApplyLabelRuleToAixReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyApplyLabelRuleToAixReq.ProtoReflect.Descriptor instead.
func (*NotifyApplyLabelRuleToAixReq) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{22}
}

func (x *NotifyApplyLabelRuleToAixReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *NotifyApplyLabelRuleToAixReq) GetRuleId() int64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

type NotifyApplyLabelRuleToAixRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *NotifyApplyLabelRuleToAixRsp) Reset() {
	*x = NotifyApplyLabelRuleToAixRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_sync_material_sync_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotifyApplyLabelRuleToAixRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotifyApplyLabelRuleToAixRsp) ProtoMessage() {}

func (x *NotifyApplyLabelRuleToAixRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_sync_material_sync_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotifyApplyLabelRuleToAixRsp.ProtoReflect.Descriptor instead.
func (*NotifyApplyLabelRuleToAixRsp) Descriptor() ([]byte, []int) {
	return file_material_sync_material_sync_proto_rawDescGZIP(), []int{23}
}

func (x *NotifyApplyLabelRuleToAixRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_material_sync_material_sync_proto protoreflect.FileDescriptor

var file_material_sync_material_sync_proto_rawDesc = []byte{
	0x0a, 0x21, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2f,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x5e, 0x0a, 0x0e, 0x43, 0x72, 0x6f, 0x6e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65,
	0x22, 0x35, 0x0a, 0x0e, 0x43, 0x72, 0x6f, 0x6e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52,
	0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xfe, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x6c,
	0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69,
	0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x61, 0x6c, 0x67,
	0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x55,
	0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x6f, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x6f, 0x73, 0x55, 0x72, 0x6c, 0x22, 0x3c, 0x0a, 0x06, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x22, 0x73, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x52,
	0x0a, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x0a, 0x0a, 0x08, 0x53,
	0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71, 0x22, 0x2f, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2b, 0x0a, 0x0e, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0xf1, 0x01, 0x0a, 0x0e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x24, 0x0a,
	0x0e, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x20, 0x0a, 0x1e, 0x53, 0x79, 0x6e,
	0x63, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x22, 0x45, 0x0a, 0x1e, 0x53,
	0x79, 0x6e, 0x63, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x20, 0x0a, 0x1e, 0x52, 0x65, 0x63, 0x74, 0x69, 0x66, 0x79, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x52, 0x65, 0x71, 0x22, 0x45, 0x0a, 0x1e, 0x52, 0x65, 0x63, 0x74, 0x69, 0x66, 0x79, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x90, 0x02, 0x0a, 0x0c,
	0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0xc0,
	0x01, 0x0a, 0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61,
	0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x64,
	0x69, 0x67, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x64, 0x69, 0x67, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x3d, 0x0a, 0x16, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x56, 0x69, 0x72, 0x74, 0x75,
	0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69,
	0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0xac, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x71, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22,
	0x35, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x73,
	0x71, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65,
	0x71, 0x22, 0x3e, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x7b, 0x0a, 0x22, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65,
	0x77, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x49,
	0x0a, 0x22, 0x53, 0x79, 0x6e, 0x63, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x54, 0x0a, 0x1c, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c,
	0x65, 0x54, 0x6f, 0x41, 0x69, 0x78, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x22,
	0x43, 0x0a, 0x1c, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x6f, 0x41, 0x69, 0x78, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x42, 0x3c, 0x5a, 0x3a, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74,
	0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_material_sync_material_sync_proto_rawDescOnce sync.Once
	file_material_sync_material_sync_proto_rawDescData = file_material_sync_material_sync_proto_rawDesc
)

func file_material_sync_material_sync_proto_rawDescGZIP() []byte {
	file_material_sync_material_sync_proto_rawDescOnce.Do(func() {
		file_material_sync_material_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_material_sync_material_sync_proto_rawDescData)
	})
	return file_material_sync_material_sync_proto_rawDescData
}

var file_material_sync_material_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_material_sync_material_sync_proto_goTypes = []interface{}{
	(*CronTriggerReq)(nil),                     // 0: material_sync.CronTriggerReq
	(*CronTriggerRsp)(nil),                     // 1: material_sync.CronTriggerRsp
	(*SetMaterialLabelReq)(nil),                // 2: material_sync.SetMaterialLabelReq
	(*SetRes)(nil),                             // 3: material_sync.SetRes
	(*SetMaterialLabelRsp)(nil),                // 4: material_sync.SetMaterialLabelRsp
	(*SayHiReq)(nil),                           // 5: material_sync.SayHiReq
	(*SayHiRsp)(nil),                           // 6: material_sync.SayHiRsp
	(*VerifyAssetReq)(nil),                     // 7: material_sync.VerifyAssetReq
	(*VerifyAssetRsp)(nil),                     // 8: material_sync.VerifyAssetRsp
	(*SyncCreativeRecommendManualReq)(nil),     // 9: material_sync.SyncCreativeRecommendManualReq
	(*SyncCreativeRecommendManualRsp)(nil),     // 10: material_sync.SyncCreativeRecommendManualRsp
	(*RectifyCreativeUploadManualReq)(nil),     // 11: material_sync.RectifyCreativeUploadManualReq
	(*RectifyCreativeUploadManualRsp)(nil),     // 12: material_sync.RectifyCreativeUploadManualRsp
	(*VirtualAsset)(nil),                       // 13: material_sync.VirtualAsset
	(*UploadVirtualAssetsReq)(nil),             // 14: material_sync.UploadVirtualAssetsReq
	(*UploadVirtualAssetsRsp)(nil),             // 15: material_sync.UploadVirtualAssetsRsp
	(*SetAssetMapReq)(nil),                     // 16: material_sync.SetAssetMapReq
	(*SetAssetMapRsq)(nil),                     // 17: material_sync.SetAssetMapRsq
	(*StartMediaContentMapReq)(nil),            // 18: material_sync.StartMediaContentMapReq
	(*StartMediaContentMapRsp)(nil),            // 19: material_sync.StartMediaContentMapRsp
	(*SyncOverviewOnlineStatusHistoryReq)(nil), // 20: material_sync.SyncOverviewOnlineStatusHistoryReq
	(*SyncOverviewOnlineStatusHistoryRsp)(nil), // 21: material_sync.SyncOverviewOnlineStatusHistoryRsp
	(*NotifyApplyLabelRuleToAixReq)(nil),       // 22: material_sync.NotifyApplyLabelRuleToAixReq
	(*NotifyApplyLabelRuleToAixRsp)(nil),       // 23: material_sync.NotifyApplyLabelRuleToAixRsp
	(*aix.Result)(nil),                         // 24: aix.Result
}
var file_material_sync_material_sync_proto_depIdxs = []int32{
	24, // 0: material_sync.CronTriggerRsp.result:type_name -> aix.Result
	24, // 1: material_sync.SetMaterialLabelRsp.result:type_name -> aix.Result
	3,  // 2: material_sync.SetMaterialLabelRsp.set_res_list:type_name -> material_sync.SetRes
	24, // 3: material_sync.SayHiRsp.result:type_name -> aix.Result
	24, // 4: material_sync.VerifyAssetRsp.result:type_name -> aix.Result
	24, // 5: material_sync.SyncCreativeRecommendManualRsp.result:type_name -> aix.Result
	24, // 6: material_sync.RectifyCreativeUploadManualRsp.result:type_name -> aix.Result
	13, // 7: material_sync.UploadVirtualAssetsReq.assets:type_name -> material_sync.VirtualAsset
	24, // 8: material_sync.UploadVirtualAssetsRsp.result:type_name -> aix.Result
	24, // 9: material_sync.SetAssetMapRsq.result:type_name -> aix.Result
	24, // 10: material_sync.StartMediaContentMapRsp.result:type_name -> aix.Result
	24, // 11: material_sync.SyncOverviewOnlineStatusHistoryRsp.result:type_name -> aix.Result
	24, // 12: material_sync.NotifyApplyLabelRuleToAixRsp.result:type_name -> aix.Result
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_material_sync_material_sync_proto_init() }
func file_material_sync_material_sync_proto_init() {
	if File_material_sync_material_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_material_sync_material_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CronTriggerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CronTriggerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMaterialLabelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMaterialLabelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAssetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyAssetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncCreativeRecommendManualReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncCreativeRecommendManualRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RectifyCreativeUploadManualReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RectifyCreativeUploadManualRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VirtualAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadVirtualAssetsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadVirtualAssetsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAssetMapReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAssetMapRsq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartMediaContentMapReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartMediaContentMapRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncOverviewOnlineStatusHistoryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncOverviewOnlineStatusHistoryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyApplyLabelRuleToAixReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_sync_material_sync_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotifyApplyLabelRuleToAixRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_material_sync_material_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_material_sync_material_sync_proto_goTypes,
		DependencyIndexes: file_material_sync_material_sync_proto_depIdxs,
		MessageInfos:      file_material_sync_material_sync_proto_msgTypes,
	}.Build()
	File_material_sync_material_sync_proto = out.File
	file_material_sync_material_sync_proto_rawDesc = nil
	file_material_sync_material_sync_proto_goTypes = nil
	file_material_sync_material_sync_proto_depIdxs = nil
}
