package data

import (
	"context"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// QueryPageCreativeOverviewColumns 分页查overview表指定列
func QueryPageCreativeOverviewColumns(ctx context.Context,
	gameCode string, offset, limit int, columns []string) ([]*model.CreativeOverview, error) {
	table := model.GetCreativeOverviewTableName(gameCode)
	db := postgresql.GetDBWithContext(ctx)

	var founds []*model.CreativeOverview
	query := db.Model(&model.CreativeOverview{}).Table(table)
	query.Column(columns...)
	query.Order("asset_id")
	query.Offset(offset).Limit(limit)
	err := query.Select(&founds)
	if err != nil {
		return nil, err
	}

	return founds, nil
}
