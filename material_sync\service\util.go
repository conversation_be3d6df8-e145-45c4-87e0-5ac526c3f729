package service

import "strings"

// 分割换行，并去掉空格
func splitLineAndTrim(s string) []string {
	if s == "" {
		return nil
	}

	// 先按换行分割
	s = strings.ReplaceAll(s, "\r\n", "\n")
	lines := strings.Split(s, "\n")
	var result []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// 再按英文逗号分割
			items := strings.Split(line, ",")
			for _, item := range items {
				if strings.TrimSpace(item) != "" {
					result = append(result, strings.TrimSpace(item))
				}
			}
		}
	}

	return result
}

// 分割，并去掉空字符串
func splitAndTrim(s string, sep string) (results []string) {
	s = strings.TrimSpace(s)
	if s == "" {
		return nil
	}
	if sep == "" {
		results = append(results, s)
		return
	}

	for _, item := range strings.Split(s, sep) {
		item = strings.TrimSpace(item)
		if item != "" {
			results = append(results, item)
		}
	}
	return
}
