# 后面接口文档在这里维护

[https://ptc.coding.intlgame.com/p/aix-backend/d/creative/git/tree/master/protos/material_display/material_display.proto.md](https://ptc.coding.intlgame.com/p/aix-backend/d/creative/git/tree/master/protos/material_display/material_display.proto.md)



# 表结构更新
1.[arthub和google素材鉴权信息更新到tb_arthub_depot表]()  

```
CREATE TABLE IF NOT EXISTS arthub_sync.tb_arthub_depot (
    depot_id text COLLATE pg_catalog."default",
    depot_name text COLLATE pg_catalog."default",
    public_token text COLLATE pg_catalog."default",
    game_code text COLLATE pg_catalog."default",
    game_name text COLLATE pg_catalog."default",
    update_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    type integer DEFAULT 0,
    arthub_code text COLLATE pg_catalog."default",
    google_service_account text COLLATE pg_catalog."default",
    CONSTRAINT tb_arthub_depot_pkey PRIMARY KEY (depot_id)
);
```

2.[目录表和素材overview、details表字段类型由uint64更新到string]() 

例：
```
ALTER TABLE arthub_sync.tb_creative_details_gas ALTER COLUMN  asset_id TYPE TEXT;
```
3.[overview表新增preview_url, asset_status字段，类型分别为string, smallint；之前的旧数据需更新asset_status为1（非删除状态）]()
    
例:
```
ALTER TABLE arthub_sync.tb_creative_overview_gas
    ADD COLUMN preview_url text,
    ADD COLUMN asset_status smallint,
    ADD COLUMN online_status integer,
    ADD COLUMN online_date text,
    ADD COLUMN online_status_update_time text;
```

```
UPDATE arthub_sync.tb_creative_overview_gas
    SET asset_status = 1;
```
4.[media_directroy相关表，name字段添加唯一约束]()
```
ALTER TABLE arthub_sync.tb_creative_media_directory_gas
    ADD CONSTRAINT tb_creative_media_directory_gas_unique_name UNIQUE(name, parent_name);
```