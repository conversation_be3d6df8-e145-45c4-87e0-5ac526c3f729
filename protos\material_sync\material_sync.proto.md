**Generated by protoc-gen-md. DO NOT EDIT.**
[TOC]
# 编辑素材机器标签和封面, POST, /api/v1/material_sync/set_material_label
test curl:
```shell
curl 'http://target/api/v1/material_sync/set_material_label' -H 'Content-Type:application/json' -d '{"algorithm_thumbnail_url":"","asset_id":"","cos_url":"","depot_name":"","first_level_labels":"","second_level_labels":""}'
```
#### set material label req
parameter explain:
```yaml
SetMaterialLabelReq: # 编辑素材机器标签和封面, POST, /api/v1/material_sync/set_material_label
  asset_id: "" # 素材id
  algorithm_thumbnail_url: "" # 新封面
  first_level_labels: "" # 机器一级标签
  second_level_labels: "" # 机器二级标签
  depot_name: "" # 游戏代码
  cos_url: "" # cos url

```
data example:
```json
{
    "algorithm_thumbnail_url": "",
    "asset_id": "",
    "cos_url": "",
    "depot_name": "",
    "first_level_labels": "",
    "second_level_labels": ""
}
```
#### set material label rsp
parameter explain:
```yaml
SetMaterialLabelRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  set_res_list: # 设置结果列表
    asset_id: "" # 素材id
    set_res: 0 # 设置结果 0-失败 1-成功

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "set_res_list": {
        "asset_id": "",
        "set_res": 0
    }
}
```
# 自测接口, POST, /api/v1/material_sync/say_hi
test curl:
```shell
curl 'http://target/api/v1/material_sync/say_hi' -H 'Content-Type:application/json' -d '{}'
```
#### say hi req
parameter explain:
```yaml
SayHiReq: # 自测接口, POST, /api/v1/material_sync/say_hi

```
data example:
```json
{}
```
#### say hi rsp
parameter explain:
```yaml
SayHiRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 自测接口, POST, /api/v1/material_sync/verify_asset
test curl:
```shell
curl 'http://target/api/v1/material_sync/verify_asset' -H 'Content-Type:application/json' -d '{"asset_id":""}'
```
#### verify asset req
parameter explain:
```yaml
VerifyAssetReq: # 自测接口, POST, /api/v1/material_sync/verify_asset
  asset_id: "" # 

```
data example:
```json
{
    "asset_id": ""
}
```
#### verify asset rsp
parameter explain:
```yaml
VerifyAssetRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  full_path_name: "" # ...
  youtube_id: "" # ...
  channel_account_id: "" # ...
  channel_asset_id: "" # ...
  asset_name: "" # ...

```
data example:
```json
{
    "asset_name": "",
    "channel_account_id": "",
    "channel_asset_id": "",
    "full_path_name": "",
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "youtube_id": ""
}
```
# 自测接口, POST, /api/v1/material_sync/sync_creative_recommend_manual
test curl:
```shell
curl 'http://target/api/v1/material_sync/sync_creative_recommend_manual' -H 'Content-Type:application/json' -d '{}'
```
#### sync creative recommend manual req
parameter explain:
```yaml
SyncCreativeRecommendManualReq: # 自测接口, POST, /api/v1/material_sync/sync_creative_recommend_manual

```
data example:
```json
{}
```
#### sync creative recommend manual rsp
parameter explain:
```yaml
SyncCreativeRecommendManualRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 自测接口, POST, /api/v1/material_sync/rectify_creative_upload_manual
test curl:
```shell
curl 'http://target/api/v1/material_sync/rectify_creative_upload_manual' -H 'Content-Type:application/json' -d '{}'
```
#### rectify creative upload manual req
parameter explain:
```yaml
RectifyCreativeUploadManualReq: # 自测接口, POST, /api/v1/material_sync/rectify_creative_upload_manual

```
data example:
```json
{}
```
#### rectify creative upload manual rsp
parameter explain:
```yaml
RectifyCreativeUploadManualRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 上传非实体素材, POST, /api/v1/material_sync/upload_virtual_assets
test curl:
```shell
curl 'http://target/api/v1/material_sync/upload_virtual_assets' -H 'Content-Type:application/json' -d '{"assets":[{"asset_id":"","asset_name":"","asset_theme":"","asset_type":0,"asset_version":"","first_label":"","label_name":"","second_label":""}],"dig_asset_version":0,"map_history_type":0,"user_name":""}'
```
#### upload virtual assets req
parameter explain:
```yaml
UploadVirtualAssetsReq: # 上传非实体素材, POST, /api/v1/material_sync/upload_virtual_assets
  assets: # 虚拟素材列表
  - asset_id: "" # 素材ID
    asset_name: "" # 素材名称
    asset_type: 0 # 素材类型. 1-youtube video id; 2-素材编号
    asset_version: "" # 素材版本, 弃用
    asset_theme: "" # 素材主题, 弃用
    label_name: "" # 标签名
    first_label: "" # 一级标签
    second_label: "" # 二级标签
  user_name: "" # 上传用户名
  dig_asset_version: 0 # 是否挖掘素材版本. 0-否, 1-是
  map_history_type: 0 # 映射历史广告素材方式. 1-使用新插入的虚拟标签进行映射; 2-直接使用上传的虚拟标签进行映射. 默认使用1.

```
data example:
```json
{
    "assets": [
        {
            "asset_id": "",
            "asset_name": "",
            "asset_theme": "",
            "asset_type": 0,
            "asset_version": "",
            "first_label": "",
            "label_name": "",
            "second_label": ""
        }
    ],
    "dig_asset_version": 0,
    "map_history_type": 0,
    "user_name": ""
}
```
#### upload virtual assets rsp
parameter explain:
```yaml
UploadVirtualAssetsRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 设置素材映射, POST, /api/v1/material_sync/set_asset_map
test curl:
```shell
curl 'http://target/api/v1/material_sync/set_asset_map' -H 'Content-Type:application/json' -d '{"error_msg":"","mapping_asset_id":"","req_id":"","storage_type":0,"video_id":""}'
```
#### set asset map req
parameter explain:
```yaml
SetAssetMapReq: # 设置素材映射, POST, /api/v1/material_sync/set_asset_map
  req_id: "" # 请求ID
  video_id: "" # 视频ID
  mapping_asset_id: "" # 映射到的素材ID, 为空表示没有映射到
  storage_type: 0 # 存储类型, 1-cos，2-arthub，3-google_driver，4-web_http，5-youtube_url
  error_msg: "" # 没有映射到素材ID时返回的错误信息

```
data example:
```json
{
    "error_msg": "",
    "mapping_asset_id": "",
    "req_id": "",
    "storage_type": 0,
    "video_id": ""
}
```
# 自测接口, POST, /api/v1/material_sync/start_media_content_map
test curl:
```shell
curl 'http://target/api/v1/material_sync/start_media_content_map' -H 'Content-Type:application/json' -d '{}'
```
#### start media content map req
parameter explain:
```yaml
StartMediaContentMapReq: # 自测接口, POST, /api/v1/material_sync/start_media_content_map

```
data example:
```json
{}
```
#### start media content map rsp
parameter explain:
```yaml
StartMediaContentMapRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
