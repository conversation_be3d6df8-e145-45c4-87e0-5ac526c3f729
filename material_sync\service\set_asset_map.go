package service

import (
	"context"
	"fmt"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// SetAssetMap 设置素材映射, 由算法端调用
func SetAssetMap(ctx *gin.Context, req *material_sync.SetAssetMapReq, rsp *material_sync.SetAssetMapRsq) error {
	var record pgmodel.CreativeRecommendMediaContentMap
	record.MediaID = req.GetVideoId()
	record.AixAssetId = req.GetMappingAssetId()
	record.StorageType = req.GetStorageType()
	author := "material_sync.set_asset_map"
	record.CreateBy = author
	record.UpdateBy = author
	time_now := time.Now().Format("2006-01-02 15:04:05")
	record.CreateTime = time_now
	record.UpdateTime = time_now

	game_code := ctx.Request.Header.Get("game")
	pg_query := pgdb.GetDBWithContext(ctx).Model(&record).Table(pgmodel.GetCreativeRecommendMediaContentMapTableName(game_code))
	pg_query.OnConflict("(media_id) do update")
	pg_query.Set("aix_asset_id=excluded.aix_asset_id")
	pg_query.Set("storage_type=excluded.storage_type")
	pg_query.Set("update_by=excluded.update_by")
	pg_query.Set("update_time=excluded.update_time")
	_, err := pg_query.Insert()
	if err != nil {
		return fmt.Errorf("insert asset map failed: %s", err)
	}

	go MapHistoryAsset(game_code, record)

	return nil
}

// MapHistoryAsset 映射历史素材信息
func MapHistoryAsset(game_code string, record pgmodel.CreativeRecommendMediaContentMap) {
	if len(record.AixAssetId) == 0 || len(record.MediaID) == 0 || record.StorageType != 5 {
		return
	}

	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "MapHistoryAsset start for game code: %s", game_code)

	st := time.Now()

	min_date, err := getGoogleAssetMinDate(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getGoogleAssetMinDate failed: %s", err)
		return
	}

	max_date, err := getGoogleAssetMaxDate(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getGoogleAssetMaxDate failed: %s", err)
		return
	}

	for min_date.Before(max_date) {
		err = MapHistoryAssetForDate(ctx, game_code, record, min_date)
		if err != nil {
			log.ErrorContextf(ctx, "MapHistoryAssetForDate failed: %s", err)
			break
		}

		min_date = min_date.AddDate(0, 0, 1)
	}

	cost := time.Since(st)
	log.InfoContextf(ctx, "MapHistoryAsset end for game code: %s, cost: %v", game_code, cost)
}

// MapHistoryAssetForDate 映射一天的历史数据
func MapHistoryAssetForDate(ctx context.Context, game_code string, content_map pgmodel.CreativeRecommendMediaContentMap, date time.Time) error {
	ad_assets, err := realTimeAssetsForDateAndYoutubeId(ctx, game_code, date, content_map.MediaID)
	if err != nil {
		return fmt.Errorf("realTimeAssetsForDateAndYoutubeId failed: %s", err)
	}

	log.DebugContextf(ctx, "get google real time asset info number: %d", len(ad_assets))

	if len(ad_assets) == 0 {
		return nil
	}

	err = upsertAssetMapsFromGoogleRealTimeAssets(ctx, game_code, content_map.AixAssetId, ad_assets)
	if err != nil {
		return fmt.Errorf("upsertAssetMapsFromGoogleRealTimeAssets failed: %s", err)
	}

	err = upsertChannelAssetLabelFromGoogleRealTimeAssets(ctx, game_code, content_map.AixAssetId, ad_assets)
	if err != nil {
		return fmt.Errorf("upsertChannelAssetLabelFromGoogleRealTimeAssets failed: %s", err)
	}

	return nil
}

// upsertAssetMapsFromGoogleRealTimeAssets 通过广告端数据更新素材映射表
func upsertAssetMapsFromGoogleRealTimeAssets(ctx context.Context, game_code, aix_asset_id string, ad_assets []pgmodel.GoogleRealtimeAssetInfo) error {
	asset_maps := make([]pgmodel.CreativeRecommendAssetMap, 0, len(ad_assets))
	for _, ad_asset := range ad_assets {
		var asset_map pgmodel.CreativeRecommendAssetMap
		asset_map.AixAssetId = aix_asset_id
		asset_map.ChannelType = 1 // Google
		asset_map.ChannelAccountID = ad_asset.AccountId
		asset_map.ChannelAssetId = ad_asset.AssetId
		asset_map.MapSource = 3 // 通过内容映射

		asset_maps = append(asset_maps, asset_map)
	}

	author := "material_sync.set_asset_map"
	err := upsertAssetMaps(ctx, game_code, author, asset_maps)
	if err != nil {
		return fmt.Errorf("upsertAssetMaps failed: %s", err)
	}

	return nil
}

// upsertChannelAssetLabelFromGoogleRealTimeAssets 通过广告端数据更新广告素材标签
func upsertChannelAssetLabelFromGoogleRealTimeAssets(ctx context.Context, game_code string, aix_asset_id string, ad_assets []pgmodel.GoogleRealtimeAssetInfo) error {
	table_name := pgmodel.GetAssetLabelTableName(game_code)
	var asset_labels []pgmodel.AssetLabel
	pg_query := pgdb.GetDBWithContext(ctx).Model(&asset_labels).Table(table_name)
	pg_query.Column("asset_id", "label_name", "first_label", "second_label")
	pg_query.Where("asset_id=?", aix_asset_id)

	err := pg_query.Select()
	if err != nil {
		return fmt.Errorf("select asset labels failed: %s", err)
	}

	log.DebugContextf(ctx, "get asset labels number: %d", len(asset_labels))

	if len(asset_labels) == 0 {
		return nil
	}

	var channel_asset_labels []pgmodel.ChannelAssetLabel
	for _, ad_asset := range ad_assets {
		for _, asset_label := range asset_labels {
			var channel_asset_label pgmodel.ChannelAssetLabel
			channel_asset_label.ChannelType = 1 // Google
			channel_asset_label.ChannelAccountID = ad_asset.AccountId
			channel_asset_label.ChannelAssetId = ad_asset.AssetId
			channel_asset_label.LabelName = asset_label.LabelName
			channel_asset_label.FirstLabel = asset_label.FirstLabel
			channel_asset_label.SecondLabel = asset_label.SecondLabel

			channel_asset_labels = append(channel_asset_labels, channel_asset_label)
		}
	}

	author := "material_sync.set_asset_map"
	err = upsertChannelAssetLabelsWithAuthor(ctx, game_code, author, channel_asset_labels)
	if err != nil {
		return fmt.Errorf("upsertChannelAssetLabels failed: %s", err)
	}

	return nil
}

// realTimeAssetsForDateAndYoutubeId 通过时间和youtube_id获取广告数据
func realTimeAssetsForDateAndYoutubeId(ctx context.Context, game_code string, date time.Time, youtube_id string) ([]pgmodel.GoogleRealtimeAssetInfo, error) {
	table_name := pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code)
	var ad_assets []pgmodel.GoogleRealtimeAssetInfo
	pg_query := pgdb.GetDBWithContext(ctx).Model(&ad_assets).Table(table_name)
	ad_asset_columns := []string{"account_id", "asset_id"}
	pg_query.Column(ad_asset_columns...)
	pg_query.Where("dtstatdate=?", date.Format("********"))
	pg_query.Where("youtube_id=?", youtube_id)
	pg_query.Group(ad_asset_columns...)

	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select google real time asset info failed: %s", err)
	}

	return ad_assets, nil
}

// getGoogleAssetMaxDate 获取google素材最大时间
func getGoogleAssetMaxDate(ctx context.Context, game_code string) (time.Time, error) {
	return getGoogleAssetDateWithFunc(ctx, game_code, "max(dtstatdate)")
}

// getGoogleAssetMinDate 获取google素材最小时间
func getGoogleAssetMinDate(ctx context.Context, game_code string) (time.Time, error) {
	return getGoogleAssetDateWithFunc(ctx, game_code, "min(dtstatdate)")
}

// getGoogleAssetDateWithFunc 获取google素材指定计算方法的日期
func getGoogleAssetDateWithFunc(ctx context.Context, game_code, column string) (time.Time, error) {
	table_name := pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{}).Table(table_name)

	var date_str string
	err := pg_query.ColumnExpr(column).Select(&date_str)
	if err != nil {
		return time.Time{}, fmt.Errorf("get %s of google asset failed: %s", column, err)
	}

	date, err := time.Parse("********", date_str)
	if err != nil {
		return time.Time{}, fmt.Errorf("parse time failed: %s", err)
	}

	return date, nil
}
