syntax = "proto3";

package google_sdk;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/google_sdk";
import "google_sdk/common.proto";

// url[http://127.0.0.1:6027/api/campaign/create_campaign_budget]
// comment[create campaign budget]
message CreateCampaignBudgetReq {
    string customer_id                          = 1;  // comment[customer id]; default[**********]
    CreateCampaignBudget create_campaign_budget = 2;  // comment[生成campaign budget时的参数]; default[xxx]
}
message CreateCampaignBudget {
    string name         = 1;  // comment[campaign budget name]; default[test_006]
    int64 amount_micros = 2;  // comment[amount micros，注意是微美元]; default[10000]
}
// comment[response]
message CreateCampaignBudgetRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;  // comment[campaign budget resource name]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/get_campaign_budgets]
// comment[get campaign budgets]
message GetCampaignBudgetsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
}
message CampaignBudget {
    string resource_name = 1;  // comment[campaign budget resource name]; default[xxx]
    int32 status         = 2;  // comment[status]; default[xxx]
    string name          = 3;  // comment[name]; default[xxx]
    int64 amount_micros  = 4;  // comment[campaign budget amount micros]
}
// comment[response]
message GetCampaignBudgetsRsp {
    Result result                            = 1;  // comment[返回结果]; default[xxx]
    repeated CampaignBudget campaign_budgets = 2;  // comment[campaign budget list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/get_campaign_budget]
// comment[get campaign budget]
message GetCampaignBudgetReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[campaign budget resource name]
}
// comment[response]
message GetCampaignBudgetRsp {
    Result result                  = 1;  // comment[返回结果]
    CampaignBudget campaign_budget = 2;  // comment[campaign budget]
}

// url[http://127.0.0.1:6027/api/campaign/update_campaign_budget]
// comment[update campaign budget]
message UpdateCampaignBudgetReq {
    string customer_id                          = 1;  // comment[customer id]; default[**********]
    UpdateCampaignBudget update_campaign_budget = 2;  // comment[update campaign parameters]
}
message UpdateCampaignBudget {
    string resource_name = 1;  // comment[campaign budget resource name]; default[customers/2797796711/campaigns/16898854968]
    int64 amount_micros  = 2;  // comment[amount micros，注意是微美元]; default[10000]
}
// comment[response]
message UpdateCampaignBudgetRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/create_campaign]
// comment[create campaign]
message CreateCampaignReq {
    string customer_id             = 1;  // comment[customer id]; default[**********]
    CreateCampaign create_campaign = 2;  // comment[create campaign parameters]; default[xxx]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AdvertisingChannelSubTypeEnum.AdvertisingChannelSubType]
enum AdvertisingChannelSubType {
    ADVERTISINGCHANNELSUBTYPE_UNSPECIFIED                       = 0;
    ADVERTISINGCHANNELSUBTYPE_UNKNOWN                           = 1;
    ADVERTISINGCHANNELSUBTYPE_SEARCH_MOBILE_APP                 = 2;
    ADVERTISINGCHANNELSUBTYPE_DISPLAY_MOBILE_APP                = 3;
    ADVERTISINGCHANNELSUBTYPE_SEARCH_EXPRESS                    = 4;
    ADVERTISINGCHANNELSUBTYPE_DISPLAY_EXPRESS                   = 5;
    ADVERTISINGCHANNELSUBTYPE_SHOPPING_SMART_ADS                = 6;
    ADVERTISINGCHANNELSUBTYPE_DISPLAY_GMAIL_AD                  = 7;
    ADVERTISINGCHANNELSUBTYPE_DISPLAY_SMART_CAMPAIGN            = 8;
    ADVERTISINGCHANNELSUBTYPE_VIDEO_OUTSTREAM                   = 9;
    ADVERTISINGCHANNELSUBTYPE_VIDEO_ACTION                      = 10;
    ADVERTISINGCHANNELSUBTYPE_VIDEO_NON_SKIPPABLE               = 11;
    ADVERTISINGCHANNELSUBTYPE_APP_CAMPAIGN                      = 12;
    ADVERTISINGCHANNELSUBTYPE_APP_CAMPAIGN_FOR_ENGAGEMENT       = 13;
    ADVERTISINGCHANNELSUBTYPE_LOCAL_CAMPAIGN                    = 14;
    ADVERTISINGCHANNELSUBTYPE_SHOPPING_COMPARISON_LISTING_ADS   = 15;
    ADVERTISINGCHANNELSUBTYPE_SMART_CAMPAIGN                    = 16;
    ADVERTISINGCHANNELSUBTYPE_VIDEO_SEQUENCE                    = 17;
    ADVERTISINGCHANNELSUBTYPE_APP_CAMPAIGN_FOR_PRE_REGISTRATION = 18;
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/CampaignStatusEnum.CampaignStatus]
enum CampaignStatus {
    CAMPAIGNSTATUS_UNSPECIFIED = 0;
    CAMPAIGNSTATUS_UNKNOWN     = 1;
    CAMPAIGNSTATUS_ENABLED     = 2;
    CAMPAIGNSTATUS_PAUSED      = 3;
    CAMPAIGNSTATUS_REMOVED     = 4;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v12/CampaignPrimaryStatusEnum.CampaignPrimaryStatus?hl=en]
enum CampaignPrimaryStatus {
    CAMPAIGNPRIMARYSTATUS_UNSPECIFIED   = 0;
    CAMPAIGNPRIMARYSTATUS_UNKNOWN       = 1;
    CAMPAIGNPRIMARYSTATUS_ELIGIBLE      = 2;
    CAMPAIGNPRIMARYSTATUS_PAUSED        = 3;
    CAMPAIGNPRIMARYSTATUS_REMOVED       = 4;
    CAMPAIGNPRIMARYSTATUS_ENDED         = 5;
    CAMPAIGNPRIMARYSTATUS_PENDING       = 6;
    CAMPAIGNPRIMARYSTATUS_MISCONFIGURED = 7;
    CAMPAIGNPRIMARYSTATUS_LIMITED       = 8;
    CAMPAIGNPRIMARYSTATUS_LEARNING      = 9;
    CAMPAIGNPRIMARYSTATUS_NOT_ELIGIBLE  = 10;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v13/CampaignPrimaryStatusReasonEnum.CampaignPrimaryStatusReason]
enum CampaignPrimaryStatusReason {
    CAMPAIGNPRIMARYSTATUSREASON_UNSPECIFIED                             = 0;
    CAMPAIGNPRIMARYSTATUSREASON_UNKNOWN                                 = 1;
    CAMPAIGNPRIMARYSTATUSREASON_CAMPAIGN_REMOVED                        = 2;
    CAMPAIGNPRIMARYSTATUSREASON_CAMPAIGN_PAUSED                         = 3;
    CAMPAIGNPRIMARYSTATUSREASON_CAMPAIGN_PENDING                        = 4;
    CAMPAIGNPRIMARYSTATUSREASON_CAMPAIGN_ENDED                          = 5;
    CAMPAIGNPRIMARYSTATUSREASON_CAMPAIGN_DRAFT                          = 6;
    CAMPAIGNPRIMARYSTATUSREASON_BIDDING_STRATEGY_MISCONFIGURED          = 7;
    CAMPAIGNPRIMARYSTATUSREASON_BIDDING_STRATEGY_LIMITED                = 8;
    CAMPAIGNPRIMARYSTATUSREASON_BIDDING_STRATEGY_LEARNING               = 9;
    CAMPAIGNPRIMARYSTATUSREASON_BIDDING_STRATEGY_CONSTRAINED            = 10;
    CAMPAIGNPRIMARYSTATUSREASON_BUDGET_CONSTRAINED                      = 11;
    CAMPAIGNPRIMARYSTATUSREASON_BUDGET_MISCONFIGURED                    = 12;
    CAMPAIGNPRIMARYSTATUSREASON_SEARCH_VOLUME_LIMITED                   = 13;
    CAMPAIGNPRIMARYSTATUSREASON_AD_GROUPS_PAUSED                        = 14;
    CAMPAIGNPRIMARYSTATUSREASON_NO_AD_GROUPS                            = 15;
    CAMPAIGNPRIMARYSTATUSREASON_KEYWORDS_PAUSED                         = 16;
    CAMPAIGNPRIMARYSTATUSREASON_NO_KEYWORDS                             = 17;
    CAMPAIGNPRIMARYSTATUSREASON_AD_GROUP_ADS_PAUSED                     = 18;
    CAMPAIGNPRIMARYSTATUSREASON_NO_AD_GROUP_ADS                         = 19;
    CAMPAIGNPRIMARYSTATUSREASON_HAS_ADS_LIMITED_BY_POLICY               = 20;
    CAMPAIGNPRIMARYSTATUSREASON_HAS_ADS_DISAPPROVED                     = 21;
    CAMPAIGNPRIMARYSTATUSREASON_MOST_ADS_UNDER_REVIEW                   = 22;
    CAMPAIGNPRIMARYSTATUSREASON_MISSING_LEAD_FORM_EXTENSION             = 23;
    CAMPAIGNPRIMARYSTATUSREASON_MISSING_CALL_EXTENSION                  = 24;
    CAMPAIGNPRIMARYSTATUSREASON_LEAD_FORM_EXTENSION_UNDER_REVIEW        = 25;
    CAMPAIGNPRIMARYSTATUSREASON_LEAD_FORM_EXTENSION_DISAPPROVED         = 26;
    CAMPAIGNPRIMARYSTATUSREASON_CALL_EXTENSION_UNDER_REVIEW             = 27;
    CAMPAIGNPRIMARYSTATUSREASON_CALL_EXTENSION_DISAPPROVED              = 28;
    CAMPAIGNPRIMARYSTATUSREASON_NO_MOBILE_APPLICATION_AD_GROUP_CRITERIA = 29;
    CAMPAIGNPRIMARYSTATUSREASON_CAMPAIGN_GROUP_PAUSED                   = 30;
    CAMPAIGNPRIMARYSTATUSREASON_CAMPAIGN_GROUP_ALL_GROUP_BUDGETS_ENDED  = 31;
    CAMPAIGNPRIMARYSTATUSREASON_APP_NOT_RELEASED                        = 32;
    CAMPAIGNPRIMARYSTATUSREASON_APP_PARTIALLY_RELEASED                  = 33;
}

message AppCampaignSetting {
    string app_id                    = 1;  // comment[app id]; default["com.google.android.apps.adwords"]
    int32 app_store                  = 2;  // comment[reference enum AppCampaignAppStore]; default[3]
    int32 bidding_strategy_goal_type = 3;  // comment[reference enum AppCampaignBiddingStrategyGoalType]; default[2]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AppCampaignAppStoreEnum.AppCampaignAppStore]
enum AppCampaignAppStore {
    APPCAMPAIGNAPPSTORE_UNSPECIFIED      = 0;
    APPCAMPAIGNAPPSTORE_UNKNOWN          = 1;
    APPCAMPAIGNAPPSTORE_APPLE_APP_STORE  = 2;
    APPCAMPAIGNAPPSTORE_GOOGLE_APP_STORE = 3;
}
message TargetCpa {
    int64 target_cpa_micros = 1;  // comment[target cpa micros]; default[10000]
}
message TargetRoas {
    double target_roas = 1;  // comment[target roas]; default[0.01]
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/ManualCpc?hl=en
message ManualCpc {
    bool enhanced_cpc_enabled = 1;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/ManualCpm?hl=en
message ManualCpm {
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v11/MaximizeConversions?hl=en]
message MaximizeConversions {
   int64 target_cpa_micros = 1;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/MaximizeConversionValue]
message MaximizeConversionValue {
    double target_roas = 1;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/TargetSpend]
message TargetSpend {
    int64 cpc_bid_ceiling_micros = 1;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/TargetImpressionShare]
message TargetImpressionShare {
    int32 location                 = 1;  // comment[ref: TargetImpressionShareLocation];
    int64 location_fraction_micros = 2;
    int64 cpc_bid_ceiling_micros   = 3;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/TargetImpressionShareLocationEnum.TargetImpressionShareLocation]
enum TargetImpressionShareLocation {
    TARGETIMPRESSIONSHARELOCATION_UNSPECIFIED          = 0;
    TARGETIMPRESSIONSHARELOCATION_UNKNOWN              = 1;
    TARGETIMPRESSIONSHARELOCATION_ANYWHERE_ON_PAGE     = 2;
    TARGETIMPRESSIONSHARELOCATION_TOP_OF_PAGE          = 3;
    TARGETIMPRESSIONSHARELOCATION_ABSOLUTE_TOP_OF_PAGE = 4;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/Campaign.NetworkSettings]
message NetworkSettings {
    bool target_google_search          = 1;
    bool target_search_network         = 2;
    bool target_content_network        = 3;
    bool target_partner_search_network = 4;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/CustomParameter]
message CustomParameter {
    string key   = 1;
    string value = 2;
}

message SelectiveOptimization {
    repeated string conversion_actions = 1;  // comment[conversion action resource name]; default[customers/2797796711/conversionActions/894134818]
}
enum CampaignOptimizationGoalType {
    CAMPAIGNOPTIMIZATIONGOALTYPE_UNSPECIFIED          = 0;
    CAMPAIGNOPTIMIZATIONGOALTYPE_UNKNOWN              = 1;
    CAMPAIGNOPTIMIZATIONGOALTYPE_CALL_CLICKS          = 2;
    CAMPAIGNOPTIMIZATIONGOALTYPE_DRIVING_DIRECTIONS   = 3;
    CAMPAIGNOPTIMIZATIONGOALTYPE_APP_PRE_REGISTRATION = 4;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v18/AdvertisingChannelTypeEnum.AdvertisingChannelType]
enum AdvertisingChannelType {
    ADVERTISINGCHANNELTYPE_UNSPECIFIED     = 0;
    ADVERTISINGCHANNELTYPE_UNKNOWN         = 1;
    ADVERTISINGCHANNELTYPE_SEARCH          = 2;
    ADVERTISINGCHANNELTYPE_DISPLAY         = 3;
    ADVERTISINGCHANNELTYPE_SHOPPING        = 4;
    ADVERTISINGCHANNELTYPE_HOTEL           = 5;
    ADVERTISINGCHANNELTYPE_VIDEO           = 6;
    ADVERTISINGCHANNELTYPE_MULTI_CHANNEL   = 7;
    ADVERTISINGCHANNELTYPE_LOCAL           = 8;
    ADVERTISINGCHANNELTYPE_SMART           = 9;
    ADVERTISINGCHANNELTYPE_PERFORMANCE_MAX = 10;
    ADVERTISINGCHANNELTYPE_LOCAL_SERVICES  = 11;
    // deprecated v18
    // ADVERTISINGCHANNELTYPE_DISCOVERY       = 12;
    // deprecated v18
    ADVERTISINGCHANNELTYPE_TRAVEL          = 13;
    ADVERTISINGCHANNELTYPE_DEMAND_GEN      = 14;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AssetTypeEnum.AssetType?hl=en
enum AssetType {
    ASSETTYPE_UNSPECIFIED        = 0;
    ASSETTYPE_UNKNOWN            = 1;
    ASSETTYPE_YOUTUBE_VIDEO      = 2;
    ASSETTYPE_MEDIA_BUNDLE       = 3;
    ASSETTYPE_IMAGE              = 4;
    ASSETTYPE_TEXT               = 5;
    ASSETTYPE_LEAD_FORM          = 6;
    ASSETTYPE_BOOK_ON_GOOGLE     = 7;
    ASSETTYPE_PROMOTION          = 8;
    ASSETTYPE_CALLOUT            = 9;
    ASSETTYPE_STRUCTURED_SNIPPET = 10;
    ASSETTYPE_SITELINK           = 11;
    ASSETTYPE_PAGE_FEED          = 12;
    ASSETTYPE_DYNAMIC_EDUCATION  = 13;
    ASSETTYPE_MOBILE_APP         = 14;
    ASSETTYPE_HOTEL_CALLOUT      = 15;
    ASSETTYPE_CALL               = 16;
    ASSETTYPE_PRICE              = 17;
    ASSETTYPE_CALL_TO_ACTION     = 18;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/MimeTypeEnum.MimeType?hl=en
enum MimeType {
    MIMETYPE_UNSPECIFIED  = 0;
    MIMETYPE_UNKNOWN      = 1;
    MIMETYPE_IMAGE_JPEG   = 2;
    MIMETYPE_IMAGE_GIF    = 3;
    MIMETYPE_IMAGE_PNG    = 4;
    MIMETYPE_FLASH        = 5;
    MIMETYPE_TEXT_HTML    = 6;
    MIMETYPE_PDF          = 7;
    MIMETYPE_MSWORD       = 8;
    MIMETYPE_MSEXCEL      = 9;
    MIMETYPE_RTF          = 10;
    MIMETYPE_AUDIO_WAV    = 11;
    MIMETYPE_AUDIO_MP3    = 12;
    MIMETYPE_HTML5_AD_ZIP = 13;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/TextAsset?hl=en
message TextAsset {
    string text = 1;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/ImageDimension?hl=en
message ImageDimension {
int64  height_pixels = 1;
int64  width_pixels  = 2;
string url           = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/ImageAsset?hl=en
message ImageAsset {
    int32          mime_type = 1;  // // comment[ref enum MimeType];
    ImageDimension full_size = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/YoutubeVideoAsset?hl=en
message YoutubeVideoAsset {
    string youtube_video_id    = 1;
    string youtube_video_title = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/SitelinkAsset?hl=en
message SitelinkAsset {
    string link_text    = 1;
    string description1 = 2;
    string description2 = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CallToActionTypeEnum.CallToActionType?hl=en
enum CallToActionType {
    CALLTOACTIONTYPE_UNSPECIFIED = 0;
    CALLTOACTIONTYPE_UNKNOWN     = 1;
    CALLTOACTIONTYPE_LEARN_MORE  = 2;
    CALLTOACTIONTYPE_GET_QUOTE   = 3;
    CALLTOACTIONTYPE_APPLY_NOW   = 4;
    CALLTOACTIONTYPE_SIGN_UP     = 5;
    CALLTOACTIONTYPE_CONTACT_US  = 6;
    CALLTOACTIONTYPE_SUBSCRIBE   = 7;
    CALLTOACTIONTYPE_DOWNLOAD    = 8;
    CALLTOACTIONTYPE_BOOK_NOW    = 9;
    CALLTOACTIONTYPE_SHOP_NOW    = 10;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CallToActionAsset?hl=en
message CallToActionAsset {
    int32 call_to_action = 1; // 枚举CallToActionType
}



// https://developers.google.com/google-ads/api/reference/rpc/v10/Asset?hl=en
message Asset {
    int64  id            = 1;
    string resource_name = 2;
    int32  type          = 3;  // comment[ref enum AssetType];
    string name          = 4;

    repeated string final_urls                     = 5;
    string   tracking_url_template                 = 6;
    string   final_url_suffix                      = 7;
    repeated CustomParameter url_custom_parameters = 8;
    repeated string final_mobile_urls              = 9;

    TextAsset         text_asset           = 10;
    ImageAsset        image_asset          = 11;
    YoutubeVideoAsset youtube_video_asset  = 12;
    SitelinkAsset     sitelink_asset       = 13;
    CallToActionAsset call_to_action_asset = 14;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AssetFieldTypeEnum.AssetFieldType?hl=en
enum AssetFieldType {
    ASSETFIELDTYPE_UNSPECIFIED              = 0;
    ASSETFIELDTYPE_UNKNOWN                  = 1;
    ASSETFIELDTYPE_HEADLINE                 = 2;
    ASSETFIELDTYPE_DESCRIPTION              = 3;
    ASSETFIELDTYPE_MANDATORY_AD_TEXT        = 4;
    ASSETFIELDTYPE_MARKETING_IMAGE          = 5;
    ASSETFIELDTYPE_MEDIA_BUNDLE             = 6;
    ASSETFIELDTYPE_YOUTUBE_VIDEO            = 7;
    ASSETFIELDTYPE_BOOK_ON_GOOGLE           = 8;
    ASSETFIELDTYPE_LEAD_FORM                = 9;
    ASSETFIELDTYPE_PROMOTION                = 10;
    ASSETFIELDTYPE_CALLOUT                  = 11;
    ASSETFIELDTYPE_STRUCTURED_SNIPPET       = 12;
    ASSETFIELDTYPE_SITELINK                 = 13;
    ASSETFIELDTYPE_MOBILE_APP               = 14;
    ASSETFIELDTYPE_HOTEL_CALLOUT            = 15;
    ASSETFIELDTYPE_CALL                     = 16;
    ASSETFIELDTYPE_PRICE                    = 24;
    ASSETFIELDTYPE_LONG_HEADLINE            = 17;
    ASSETFIELDTYPE_BUSINESS_NAME            = 18;
    ASSETFIELDTYPE_SQUARE_MARKETING_IMAGE   = 19;
    ASSETFIELDTYPE_PORTRAIT_MARKETING_IMAGE = 20;
    ASSETFIELDTYPE_LOGO                     = 21;
    ASSETFIELDTYPE_LANDSCAPE_LOGO           = 22;
    ASSETFIELDTYPE_VIDEO                    = 23;
    ASSETFIELDTYPE_CALL_TO_ACTION_SELECTION = 25;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AssetLinkStatusEnum.AssetLinkStatus?hl=en
enum AssetLinkStatus {
    ASSETLINKSTATUS_UNSPECIFIED = 0;
    ASSETLINKSTATUS_UNKNOWN     = 1;
    ASSETLINKSTATUS_ENABLED     = 2;
    ASSETLINKSTATUS_REMOVED     = 3;
    ASSETLINKSTATUS_PAUSED      = 4;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CampaignAsset?hl=en
message CampaignAsset {
    string resource_name = 1;  // The resource name of the campaign asset
    int32  field_type    = 2;  // AssetFieldType
    int32  status        = 3;  // AssetLinkStatus
    string campaign      = 4;  // The campaign to which the asset is linked.
    string asset         = 5;  // The asset which is linked to the campaign.

    Asset asset_instance = 6;  // 具体的asset实例
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceStatusEnum.CustomAudienceStatus?hl=en
enum CustomAudienceStatus {
    CUSTOMAUDIENCESTATUS_UNSPECIFIED = 0;
    CUSTOMAUDIENCESTATUS_UNKNOWN     = 1;
    CUSTOMAUDIENCESTATUS_ENABLED     = 2;
    CUSTOMAUDIENCESTATUS_REMOVED     = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceTypeEnum.CustomAudienceType?hl=en
enum CustomAudienceType {
    CUSTOMAUDIENCETYPE_UNSPECIFIED     = 0;
    CUSTOMAUDIENCETYPE_UNKNOWN         = 1;
    CUSTOMAUDIENCETYPE_AUTO            = 2;
    CUSTOMAUDIENCETYPE_INTEREST        = 3;
    CUSTOMAUDIENCETYPE_PURCHASE_INTENT = 4;
    CUSTOMAUDIENCETYPE_SEARCH          = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceMemberTypeEnum.CustomAudienceMemberType?hl=en
enum CustomAudienceMemberType {
    CUSTOMAUDIENCEMEMBERTYPE_UNSPECIFIED    = 0;
    CUSTOMAUDIENCEMEMBERTYPE_UNKNOWN        = 1;
    CUSTOMAUDIENCEMEMBERTYPE_KEYWORD        = 2;
    CUSTOMAUDIENCEMEMBERTYPE_URL            = 3;
    CUSTOMAUDIENCEMEMBERTYPE_PLACE_CATEGORY = 4;
    CUSTOMAUDIENCEMEMBERTYPE_APP            = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceMember?hl=en
message CustomAudienceMember {
    int32  member_type    = 1;  // enum CustomAudienceMemberType
    string keyword        = 2;
    string url            = 3;
    int64  place_category = 4;
    string app            = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudience?hl=en
message CustomAudience {
    string   resource_name                = 1;  // The resource name of the custom audience
    int64    id                           = 2;  // ID of the custom audience.
    int32    status                       = 3;  // enum CustomAudienceStatus
    string   name                         = 4;
    int32    type                         = 5;  // enum CustomAudienceType, Type of the custom audience. ("INTEREST" OR "PURCHASE_INTENT" is not allowed for newly created custom audience but kept for existing audiences)
    string   description                  = 6;
    repeated CustomAudienceMember members = 7;
}

message OptimizationGoalSetting {
    repeated int32 optimization_goal_types = 1;  // comment[ref: CampaignOptimizationGoalType]; default[0]
}
message CreateCampaign {
    string                  name                                  = 1;   // comment[name]; default[campaign_test_005]
    string                  campaign_budget                       = 2;   // comment[campaign budget]; default[customers/**********/campaignBudgets/10676911331]
    int32                   advertising_channel_sub_type          = 3;   // comment[ref: AdvertisingChannelSubType]; default[12]
    int32                   status                                = 4;   // comment[ref enum CampaignStatus]; default[3]
    AppCampaignSetting      app_campaign_setting                  = 5;   // comment[app campaign setting]; default[xxx]
    TargetCpa               target_cpa                            = 6;   // comment[target cpa];
    string                  start_date                            = 7;   // comment[start date]; default[20220512]
    string                  end_date                              = 8;   // comment[end date]; default[20220612]
    SelectiveOptimization   selective_optimization                = 9;   // comment[selective optimization];
    TargetRoas              target_roas                           = 10;  // comment[ref: TargetRoas];
    OptimizationGoalSetting optimization_goal_setting             = 11;  // comment[ref: OptimizationGoalSetting]
    MaximizeConversions     maximize_conversions                  = 13;  // comment[ref: BiddingStrategyType]
    int32                   advertising_channel_type              = 14;  // comment[ref: AdvertisingChannelType];
    MaximizeConversionValue maximize_conversion_value             = 15;  // comment[ref: MaximizeConversionValue]
    TargetSpend             target_spend                          = 16;  // comment[ref: TargetSpend]
    TargetImpressionShare   target_impression_share               = 17;  // comment[ref: TargetImpressionShare]
    NetworkSettings         network_settings                      = 18;  // comment[ref: NetworkSettings]
    string                  tracking_url_template                 = 19;  // comment[ref: tracking_url_template]
    string                  final_url_suffix                      = 20;  // comment[ref: final_url_suffix]
    repeated                CustomParameter url_custom_parameters = 21;  // comment[ref: CustomParameter]
    ManualCpc               manual_cpc                            = 22;
    ManualCpm               manual_cpm                            = 23;
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AppCampaignBiddingStrategyGoalTypeEnum.AppCampaignBiddingStrategyGoalType]
enum AppCampaignBiddingStrategyGoalType {
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_UNSPECIFIED                                        = 0;
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_UNKNOWN                                            = 1;
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_OPTIMIZE_INSTALLS_TARGET_INSTALL_COST              = 2;
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_OPTIMIZE_IN_APP_CONVERSIONS_TARGET_INSTALL_COST    = 3;
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_OPTIMIZE_IN_APP_CONVERSIONS_TARGET_CONVERSION_COST = 4;
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_OPTIMIZE_RETURN_ON_ADVERTISING_SPEND               = 5;
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_OPTIMIZE_PRE_REGISTRATION_CONVERSION_VOLUME        = 6;
    APPCAMPAIGNBIDDINGSTRATEGYGOALTYPE_OPTIMIZE_INSTALLS_WITHOUT_TARGET_INSTALL_COST      = 7;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/BiddingStrategyTypeEnum.BiddingStrategyType?hl=en]
enum BiddingStrategyType {
    BIDDINGSTRATEGYTYPE_UNSPECIFIED               = 0;
    BIDDINGSTRATEGYTYPE_UNKNOWN                   = 1;
    BIDDINGSTRATEGYTYPE_COMMISSION                = 16;
    BIDDINGSTRATEGYTYPE_ENHANCED_CPC              = 2;
    BIDDINGSTRATEGYTYPE_INVALID                   = 17;
    BIDDINGSTRATEGYTYPE_MANUAL_CPC                = 3;
    BIDDINGSTRATEGYTYPE_MANUAL_CPM                = 4;
    BIDDINGSTRATEGYTYPE_MANUAL_CPV                = 13;
    BIDDINGSTRATEGYTYPE_MAXIMIZE_CONVERSIONS      = 10;
    BIDDINGSTRATEGYTYPE_MAXIMIZE_CONVERSION_VALUE = 11;
    BIDDINGSTRATEGYTYPE_PAGE_ONE_PROMOTED         = 5;
    BIDDINGSTRATEGYTYPE_PERCENT_CPC               = 12;
    BIDDINGSTRATEGYTYPE_TARGET_CPA                = 6;
    BIDDINGSTRATEGYTYPE_TARGET_CPM                = 14;
    BIDDINGSTRATEGYTYPE_TARGET_IMPRESSION_SHARE   = 15;
    BIDDINGSTRATEGYTYPE_TARGET_OUTRANK_SHARE      = 7;
    BIDDINGSTRATEGYTYPE_TARGET_ROAS               = 8;
    BIDDINGSTRATEGYTYPE_TARGET_SPEND              = 9;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/DeviceEnum.Device?hl=en
enum Device {
    DEVICE_UNSPECIFIED  = 0;
    DEVICE_UNKNOWN      = 1;
    DEVICE_MOBILE       = 2;
    DEVICE_TABLET       = 3;
    DEVICE_DESKTOP      = 4;
    DEVICE_CONNECTED_TV = 6;
    DEVICE_OTHER        = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v18/Campaign.DemandGenCampaignSettings
message DemandGenCampaignSettings {
    // Immutable. Specifies whether this campaign uses upgraded targeting options. 
    // When this field is set to true, you can use location and language targeting at the ad group level as opposed to the standard campaign-level targeting. 
    // This field defaults to true, and can only be set when creating a campaign.
    bool upgraded_targeting = 1;
}

// comment[response]
message CreateCampaignRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;  // comment[campaign resource name]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/update_campaign]
// comment[update campaign]
message UpdateCampaignReq {
    string customer_id             = 1;  // comment[customer id]; default[**********]
    UpdateCampaign update_campaign = 2;  // comment[update campaign parameters]
}
message UpdateCampaign {
    string                  resource_name                         = 1;   // comment[campaign resource name]; default[customers/2797796711/campaigns/16898854968]
    int32                   status                                = 2;   // comment[ref: CampaignStatus]; default[4]
    string                  end_date                              = 3;   // comment[end date]; default[20220910]
    TargetCpa               target_cpa                            = 4;   // comment[target cpa]
    TargetRoas              target_roas                           = 5;   // comment[target roas]
    int32                   advertising_channel_type              = 6;   // comment[ref: AdvertisingChannelType];
    MaximizeConversionValue maximize_conversion_value             = 7;   // comment[ref: MaximizeConversionValue]
    TargetSpend             target_spend                          = 8;   // comment[ref: TargetSpend]
    TargetImpressionShare   target_impression_share               = 9;   // comment[ref: TargetImpressionShare]
    NetworkSettings         network_settings                      = 10;  // comment[ref: NetworkSettings]
    string                  tracking_url_template                 = 11;  // comment[ref: tracking_url_template]
    string                  final_url_suffix                      = 12;  // comment[ref: final_url_suffix]
    repeated                CustomParameter url_custom_parameters = 13;  // comment[ref: CustomParameter]
    MaximizeConversions     maximize_conversions                  = 14;  // comment[ref: MaximizeConversions]
    repeated                string field_masks                    = 15;  // comment[field_masks]
    string                  start_date                            = 16;  // comment[start date]
    AppCampaignSetting      app_campaign_setting                  = 17;  // comment[ref: AppCampaignSetting]
    ManualCpm               manual_cpm                            = 18;  // comment[ref: ManualCpm]
    ManualCpc               manual_cpc                            = 19;  // comment[ref: ManualCpc]
    string                  name                                  = 20;  // comment[campaign name]
}
// comment[response]
message UpdateCampaignRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/get_campaigns]
// comment[get campaign list]
message GetCampaignsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
}
message Campaign {
    string                  name                                  = 1;   // comment[name]; default[campaign_test_005]
    string                  campaign_budget                       = 2;   // comment[campaign budget]; default[customers/**********/campaignBudgets/10676911331]
    int32                   advertising_channel_sub_type          = 3;   // comment[ref: AdvertisingChannelSubType]; default[12]
    int32                   status                                = 4;   // comment[ref enum CampaignStatus]; default[3]
    AppCampaignSetting      app_campaign_setting                  = 5;   // comment[app campaign setting]; default[xxx]
    TargetCpa               target_cpa                            = 6;   // comment[target cpa];
    string                  start_date                            = 7;   // comment[start date]; default[20220512]
    string                  end_date                              = 8;   // comment[end date]; default[20220612]
    SelectiveOptimization   selective_optimization                = 9;   // comment[selective optimization];
    TargetRoas              target_roas                           = 10;  // comment[ref: TargetRoas];
    OptimizationGoalSetting optimization_goal_setting             = 11;  // comment[ref: OptimizationGoalSetting]
    string                  resource_name                         = 12;  // comment[resource name]; default[xxx]
    int32                   bidding_strategy_type                 = 13;  // comment[ref: BiddingStrategyType]
    int32                   advertising_channel_type              = 14;  // comment[ref: AdvertisingChannelType];
    MaximizeConversionValue maximize_conversion_value             = 15;  // comment[ref: MaximizeConversionValue]
    TargetSpend             target_spend                          = 16;  // comment[ref: TargetSpend]
    TargetImpressionShare   target_impression_share               = 17;  // comment[ref: TargetImpressionShare]
    NetworkSettings         network_settings                      = 18;  // comment[ref: NetworkSettings]
    string                  tracking_url_template                 = 19;  // comment[ref: tracking_url_template]
    string                  final_url_suffix                      = 20;  // comment[ref: final_url_suffix]
    repeated                CustomParameter url_custom_parameters = 21;  // comment[ref: CustomParameter]
    MaximizeConversions     maximize_conversions                  = 22;  // comment[ref: MaximizeConversions]
    DemandGenCampaignSettings demand_gen_campaign_settings        = 23;
}
// comment[response]
message GetCampaignsRsp {
    Result result               = 1;  // comment[返回结果]; default[xxx]
    repeated Campaign campaigns = 2;  // comment[campaign info list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/get_campaign]
// comment[get campaign]
message GetCampaignReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[campaign resource name]
}

// comment[response]
message GetCampaignRsp {
    Result result                  = 1;  // comment[返回结果]; default[xxx]
    Campaign campaign              = 2;  // comment[campaign info list]; default[xxx]
    CampaignBudget campaign_budget = 3;  // comment[campaign budget]
}

// url[http://127.0.0.1:6027/api/campaign/batch_get_campaign]
// comment[batch get campaign]
message BatchGetCampaignReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    repeated string resource_names = 2;  // comment[campaign resource name lit]
}

message CampaignWithBudget {
    Campaign campaign              = 1;  // comment[campaign info list]; default[xxx]
    CampaignBudget campaign_budget = 2;  // comment[campaign budget]
}

// comment[response]
message BatchGetCampaignRsp {
    Result result                  = 1;  // comment[返回结果]; default[xxx]
   repeated CampaignWithBudget  campaigns = 2; // comment[campaign list]
}

// deprecated 迁移到统一campaign criterion 修改接口：modify_campaign_criterion
// url[http://127.0.0.1:6027/api/campaign/create_campaign_criterion]
// comment[create campaign criterion]
message CreateCampaignCriterionReq {
    string customer_id                                = 1;  // comment[customer id]; default[**********]
    CreateCampaignCriterion create_campaign_criterion = 2;  // comment[create campaign criterion parameters]; default[xxx]
}
// campaign criterion
message CreateCampaignCriterion {
    string campaign              = 1;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
    repeated string location_ids = 2;  // comment[location id list]; default[21137]
    repeated string language_ids = 3;  // comment[language id list]; default[1001]
}
// comment[response]
message CreateCampaignCriterionRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;  // comment[campaign criterion resource name]; default[xxx]
}

// deprecated 迁移到统一campaign criterion 修改接口：modify_campaign_criterion
// url[http://127.0.0.1:6027/api/campaign/update_campaign_criterion]
// comment[update campaign criterion]
message UpdateCampaignCriterionReq {
    string customer_id                                = 1;  // comment[customer id]; default[**********]
    UpdateCampaignCriterion update_campaign_criterion = 2;  // comment[create campaign criterion parameters]; default[xxx]
}
// campaign criterion
message UpdateCampaignCriterion {
    string   campaign                   = 1;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
    repeated string remove_language_ids = 2;  // comment[the remove language id list]; default[]
    repeated string add_language_ids    = 3;  // comment[the add language id list]; default[]
    repeated string remove_location_ids = 4;  // comment[the remove location id list]; default[]
    repeated string add_location_ids    = 5;  // comment[the add location id list]; default[]
}
// comment[response]
message UpdateCampaignCriterionRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
}

message ModifyCampaignCriterions {
    string   campaign                              = 1;  // camapign resource_name
    repeated CampaignCriterion campaign_criterions = 2;  // comment[campaign criterion list]; default[xxx]
}

// 由于campaign criterion的创建和更新，都相当于修改
// 统一成modify_campaign_criterions接口来处理
// url[http://127.0.0.1:6027/api/campaign/modify_campaign_criterions]
// comment[modify campaign criterion]
message ModifyCampaignCriterionsReq {
    string                  customer_id                 = 1;  // comment[customer id]; default[**********]
    ModifyCampaignCriterions modify_campaign_criterions = 2;
}

// comment[response]
message ModifyCampaignCriterionsRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/get_campaign_criterions]
// comment[get campaign criterion list]
message GetCampaignCriterionsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
}
message Language {
    string language_constant = 1;  // comment[language id]; default[xxx]
}
message Location {
    string geo_target_constant = 1;  // comment[location id]; default[xxx]
}
message OperatingSystemVersionInfo {
    string operating_system_version_constant = 1;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CampaignCriterionStatusEnum.CampaignCriterionStatus?hl=en
enum CampaignCriterionStatus {
    CAMPAIGNCRITERIONSTATUS_UNSPECIFIED = 0;
    CAMPAIGNCRITERIONSTATUS_UNKNOWN     = 1;
    CAMPAIGNCRITERIONSTATUS_ENABLED     = 2;
    CAMPAIGNCRITERIONSTATUS_PAUSED      = 3;
    CampaignCriterionStatus_REMOVED     = 4;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CampaignCriterion?hl=en
message CampaignCriterion {
    string resource_name = 1;  // comment[resource name]; default[xxx]

    int32                      type                     = 2;   // 参看枚举 CriterionType
    int32                      status                   = 3;   // 状态参看枚举 CampaignCriterionStatus
    string                     criterion_id             = 4;   //
    string                     display_name             = 5;
    double                     bid_modifier             = 6;   // The modifier for the bids when the criterion matches. The modifier must be in the range: 0.1 - 10.0. Most targetable criteria types support modifiers. Use 0 to opt out of a Device type.
    bool                       has_bid_modifier         = 7;
    bool                       negative                 = 8;   // Whether to target (false) or exclude (true) the criterion.
    Language                   language                 = 9;   // comment[language]; default[xxx]
    Location                   location                 = 10;  // comment[location]; default[xxx]
    OperatingSystemVersionInfo operating_system_version = 11;
    int32                      device                   = 12;  // 参看枚举 Device
    string                     campaign_resource_name   = 13;  // criterion 属于哪个campaign
}
// comment[response]
message GetCampaignCriterionsRsp {
    Result result                                  = 1;  // comment[返回结果]; default[xxx]
    repeated CampaignCriterion campaign_criterions = 2;  // comment[campaign criterion list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/campaign/remove_campaign]
// comment[remove campaign]
message RemoveCampaignReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[resource name]
}
// comment[response]
message RemoveCampaignRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/create_ad_group]
// comment[create ad group]
message CreateAdGroupReq {
    string customer_id            = 1;  // comment[customer id]; default[**********]
    CreateAdGroup create_ad_group = 2;  // comment[create ad group parameters]; default[xxx]
}
message CreateAdGroup {
    string name     = 1;  // comment[name]; default[ad_group_test_002]
    string campaign = 2;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
    int32 status    = 3;  // comment[ref: AdGroupStatus]; default[3]

    int32 bidding_strategy_type = 4;  // comment[ref: BiddingStrategyType]
    int64 target_cpa_micros     = 5;
    int64 cpc_bid_micros        = 6;
    int64 cpm_bid_micros        = 7;
    bool use_audience_grouped   = 8; // If true, this ad group uses an Audience resource for audience targeting. If false, this ad group may use audience segment criteria instead.
}
// comment[response]
message CreateAdGroupRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;  // comment[ad group resource name]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_groups]
// comment[get ad group list]
message GetAdGroupsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
}
message AdGroup {
    string resource_name = 1;  // comment[resource name]; default[xxx]
    int32 status         = 2;  // comment[ref: AdGroupStatus]
    string name          = 3;  // comment[name]; default[xxx]
    string campaign      = 4;  // comment[campaign resource name]; default[xxx]

    int32 bidding_strategy_type = 5;  // comment[ref: BiddingStrategyType]
    int64 target_cpa_micros     = 6;
    int64 cpc_bid_micros        = 7;
    int64 cpm_bid_micros        = 8;
}
// comment[response]
message GetAdGroupsRsp {
    Result result              = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroup ad_groups = 2;  // comment[ad group list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_group]
// comment[get ad group]
message GetAdGroupReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
}
// comment[response]
message GetAdGroupRsp {
    Result result    = 1;  // comment[返回结果]; default[xxx]
    AdGroup ad_group = 2;  // comment[ad group]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/batch_get_ad_group]
// comment[batch get ad group]
message BatchGetAdGroupReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    repeated string resource_names = 2;  // comment[ad_group resource name list]; default[customers/**********/campaigns/16806285811]
}
// comment[response]
message BatchGetAdGroupRsp {
    Result result    = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroup ad_groups = 2;  // comment[ad group list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/update_ad_group]
// comment[update ad group]
message UpdateAdGroupReq {
    string customer_id            = 1;  // comment[customer id]; default[**********]
    UpdateAdGroup update_ad_group = 2;  // comment[update ad group parameters]
}
enum AdGroupStatus {
    ADGROUPSTATUS_UNSPECIFIED = 0;
    ADGROUPSTATUS_UNKNOWN     = 1;
    ADGROUPSTATUS_ENABLED     = 2;
    ADGROUPSTATUS_PAUSED      = 3;
    ADGROUPSTATUS_REMOVED     = 4;
}
message UpdateAdGroup {
    string resource_name = 1;  // comment[ad group resource name]; default[customers/2797796711/adGroups/136114422855]
    int32 status         = 2;  // comment[ref: AdGroupStatus]; default[4]

    int32 bidding_strategy_type = 3;  // comment[ref: BiddingStrategyType]
    int64 target_cpa_micros     = 4;
    int64 cpc_bid_micros        = 5;
    int64 cpm_bid_micros        = 6;
    string ad_group_name        = 7;
}
// comment[response]
message UpdateAdGroupRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/remove_ad_group]
// comment[remove ad group]
message RemoveAdGroupReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[resource name]
}
// comment[response]
message RemoveAdGroupRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/create_ad_group_criterion]
// comment[create ad group criterion]
message CreateAdGroupCriterionReq {
    string customer_id                               = 1;  // comment[customer id]; default[**********]
    CreateAdGroupCriterion create_ad_group_criterion = 2;  // comment[create ad group criterion parameters]; default[xxx]
}
message AdGroupCriterionUserList {
    string user_list = 1;  // comment[user list resource name]; default[xxx]
}
message CreateAdGroupCriterion {
    string name                        = 1;  // comment[name]; default[xxx]
    string ad_group                    = 2;  // comment[ad group resource name]; default[xxx]
    AdGroupCriterionUserList user_list = 3;  // commnet[ref: AdGroupCriterionUserList]
}
// comment[response]
message CreateAdGroupCriterionRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;  // comment[ad group criterion resource name]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_group_criterions]
// comment[get ad group criterion list]
message GetAdGroupCriterionsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string ad_group    = 2;  // comment[ad group resource name]; default[customers/**********/campaigns/16806285811]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AdGroupCriterionStatusEnum.AdGroupCriterionStatus]
enum AdGroupCriterionStatus {
    ADGROUPCRITERIONSTATUS_UNSPECIFIED = 0;
    ADGROUPCRITERIONSTATUS_UNKNOWN     = 1;
    ADGROUPCRITERIONSTATUS_ENABLED     = 2;
    ADGROUPCRITERIONSTATUS_PAUSED      = 3;
    ADGROUPCRITERIONSTATUS_REMOVED     = 4;
}

enum AdGroupCriterionApprovalStatus {    
    ADGROUPCRITERIONAPPROVALSTATUS_UNSPECIFIED    = 0;
    ADGROUPCRITERIONAPPROVALSTATUS_UNKNOWN        = 1;
    ADGROUPCRITERIONAPPROVALSTATUS_APPROVED       = 2;
    ADGROUPCRITERIONAPPROVALSTATUS_DISAPPROVED    = 3;
    ADGROUPCRITERIONAPPROVALSTATUS_PENDING_REVIEW = 4;
    ADGROUPCRITERIONAPPROVALSTATUS_UNDER_REVIEW   = 5;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/KeywordMatchTypeEnum.KeywordMatchType]
enum KeywordMatchType {
    KEYWORDMATCHTYPE_UNSPECIFIED = 0;
    KEYWORDMATCHTYPE_UNKNOWN     = 1;
    KEYWORDMATCHTYPE_EXACT       = 2;
    KEYWORDMATCHTYPE_PHRASE      = 3;
    KEYWORDMATCHTYPE_BROAD       = 4;
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/KeywordInfo]
message KeywordInfo {
    int32  match_type = 1; // comment[ref: KeywordMatchType]
    string text       = 2;
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/CriterionTypeEnum.CriterionType]
enum CriterionType {
    CRITERIONTYPE_UNSPECIFIED              = 0;
    CRITERIONTYPE_UNKNOWN                  = 1;
    CRITERIONTYPE_KEYWORD                  = 2;
    CRITERIONTYPE_PLACEMENT                = 3;
    CRITERIONTYPE_MOBILE_APP_CATEGORY      = 4;
    CRITERIONTYPE_MOBILE_APPLICATION       = 5;
    CRITERIONTYPE_DEVICE                   = 6;
    CRITERIONTYPE_LOCATION                 = 7;
    CRITERIONTYPE_LISTING_GROUP            = 8;
    CRITERIONTYPE_AD_SCHEDULE              = 9;
    CRITERIONTYPE_AGE_RANGE                = 10;
    CRITERIONTYPE_GENDER                   = 11;
    CRITERIONTYPE_INCOME_RANGE             = 12;
    CRITERIONTYPE_PARENTAL_STATUS          = 13;
    CRITERIONTYPE_YOUTUBE_VIDEO            = 14;
    CRITERIONTYPE_YOUTUBE_CHANNEL          = 15;
    CRITERIONTYPE_USER_LIST                = 16;
    CRITERIONTYPE_PROXIMITY                = 17;
    CRITERIONTYPE_TOPIC                    = 18;
    CRITERIONTYPE_LISTING_SCOPE            = 19;
    CRITERIONTYPE_LANGUAGE                 = 20;
    CRITERIONTYPE_IP_BLOCK                 = 21;
    CRITERIONTYPE_CONTENT_LABEL            = 22;
    CRITERIONTYPE_CARRIER                  = 23;
    CRITERIONTYPE_USER_INTEREST            = 24;
    CRITERIONTYPE_WEBPAGE                  = 25;
    CRITERIONTYPE_OPERATING_SYSTEM_VERSION = 26;
    CRITERIONTYPE_APP_PAYMENT_MODEL        = 27;
    CRITERIONTYPE_MOBILE_DEVICE            = 28;
    CRITERIONTYPE_CUSTOM_AFFINITY          = 29;
    CRITERIONTYPE_CUSTOM_INTENT            = 30;
    CRITERIONTYPE_LOCATION_GROUP           = 31;
    CRITERIONTYPE_CUSTOM_AUDIENCE          = 32;
    CRITERIONTYPE_COMBINED_AUDIENCE        = 33;
    CRITERIONTYPE_KEYWORD_THEME            = 34;
    CRITERIONTYPE_AUDIENCE                 = 35;
}

enum PlacementType {
    PLACEMENTTYPE_UNSPECIFIED         = 0;
    PLACEMENTTYPE_UNKNOWN             = 1;
    PLACEMENTTYPE_WEBSITE             = 2;
    PLACEMENTTYPE_MOBILE_APP_CATEGORY = 3;
    PLACEMENTTYPE_MOBILE_APPLICATION  = 4;
    PLACEMENTTYPE_YOUTUBE_VIDEO       = 5;
    PLACEMENTTYPE_YOUTUBE_CHANNEL     = 6;
    PLACEMENTTYPE_GOOGLE_PRODUCTS     = 7;
}

// ref: https://developers.google.com/google-ads/api/reference/rpc/v16/YouTubeChannelInfo
message YouTubeChannelInfo {
    string channel_id = 1; // The YouTube uploader channel id or the channel code of a YouTube channel.
}

// ref: https://developers.google.com/google-ads/api/reference/rpc/v16/YouTubeVideoInfo
message YouTubeVideoInfo {
    string video_id = 1; // YouTube video id as it appears on the YouTube watch page.
}

// ref:  https://developers.google.com/google-ads/api/reference/rpc/v16/PlacementInfo
message PlacementInfo {
    string url = 1; // URL of the placement. For example, "http://www.domain.com".
}

// ref: https://developers.google.com/google-ads/api/reference/rpc/v16/MobileApplicationInfo
message MobileApplicationInfo {
    string app_id = 1;  // A well formed app id for Google Ads API would thus be "1-476943146" for iOS and "2-com.labpixies.colordrips" for Android. 
    string name   = 2;  // Name of this mobile application.
}

// ref: https://developers.google.com/google-ads/api/reference/rpc/v16/MobileAppCategoryInfo
message MobileAppCategoryInfo {
    string mobile_app_category_constant = 1;  // The mobile app category constant resource name.
}



// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AdGroupCriterion]
message AdGroupCriterion {
    string                   resource_name = 1;  // comment[resource name]; default[xxx]
    bool                     negative      = 2;  // comment[negative]
    AdGroupCriterionUserList user_list     = 3;  // comment[ref: AdGroupCriterionUserList]
    int32                    status        = 4;  // comment[ref: AdGroupCriterionStatus]
    KeywordInfo              keyword       = 5;  // comment[ref: KeywordInfo]
    string                   ad_group      = 6;  // comment[ad_group]
    int32                    type          = 7;  // comment[ref: CriterionType]
    AudienceInfo             audience      = 8;  // comment[ref: AudienceInfo]
    YouTubeChannelInfo    youtube_channel     = 9;   // comment[ref: YouTubeChannelInfo]
    YouTubeVideoInfo      youtube_video       = 10;  // comment[ref: YouTubeVideoInfo]
    PlacementInfo         placement           = 11;  // comment[ref: PlacementInfo]
    MobileAppCategoryInfo mobile_app_category = 12;  // comment[ref: MobileAppCategoryInfo]
    MobileApplicationInfo mobile_application  = 13;  // comment[ref: MobileApplicationInfo]
    string                display_name        = 14;
}
// comment[response]
message GetAdGroupCriterionsRsp {
    Result result                                 = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupCriterion ad_group_criterions = 2;  // comment[ad group criterion list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_group_all_criterions]
// comment[get ad group all criterion list]
message GetAdGroupAllCriterionsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string ad_group    = 2;  // comment[ad group resource name]; default[customers/**********/campaigns/16806285811]
}

message GetAdGroupAllCriterionsRsp {
    Result result                                 = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupCriterion ad_group_criterions = 2;  // comment[ad group criterion list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_groups_criterions]
// comment[get ad groups criterion list]
message GetAdGroupsCriterionsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    repeated string ad_groups    = 2;  // comment[ad groups resource name list];
}
// comment[response]
message GetAdGroupsCriterionsRsp {
    Result result                                 = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupCriterion ad_group_criterions = 2;  // comment[ad group criterion list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_group_criterions_by_campaign]
// comment[get ad group criterion list]
message GetAdGroupCriterionsByCampaignReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
}

// comment[response]
message GetAdGroupCriterionsByCampaignRsp {
    Result result                                 = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupCriterion ad_group_criterions = 2;  // comment[ad group criterion list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/create_ad_group_ad]
// comment[create ad group ad]
message CreateAdGroupAdReq {
    string customer_id                 = 1;  // comment[customer id]; default[**********]
    CreateAdGroupAd create_ad_group_ad = 2;  // comment[create ad group ad parameters]; default[xxx]
}
message CreateAdGroupAd {
    string ad_group                     = 1;  // comment[ad group resource name]; default[customers/**********/adGroups/134018102374]
    int32  advertising_channel_sub_type = 2;  // comment[ref: AdvertisingChannelSubType]; default[12]
    Ad     ad                           = 3;  // comment[ref: AdGroupAd]
    int32  status                       = 4;  // comment[ref: AdGroupAdStatus]; default[3]
    int32  advertising_channel_type     = 5;  // comment[ref: AdvertisingChannelType]
}
// comment[response]
message CreateAdGroupAdRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;  // comment[resource name]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ads]
// comment[get ad group ad list]
message GetAdGroupAdsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string ad_group    = 2;  // comment[ad group resource name]; default[customers/**********/adGroups/134018102374]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AdTextAsset#text]
message AdTextAsset {
    string text = 1;  // comment[Asset text]; default[hellow world]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AdImageAsset]
message AdImageAsset {
    string asset = 1;  // comment[The Asset resource name of this image]
}
message AdVideoAsset {
    string asset = 1;  // comment[The Asset resource name of this video]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AppAdInfo]
message AppAd {
    repeated AdTextAsset headlines       = 1;  // comment[List of text assets for headlines, at least two text assets]
    repeated AdTextAsset descriptions    = 2;  // comment[List of text assets for descriptions, at least two text assets]
    repeated AdImageAsset images         = 3;  // comment[List of image assets that may be displayed with the ad]
    repeated AdVideoAsset youtube_videos = 4;  // comment[List of YouTube video assets that may be displayed with the ad]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AppEngagementAdInfo]
message AppEngagementAd {
    repeated AdTextAsset headlines    = 1;  // comment[List of text assets for headlines, at least two text assets]
    repeated AdTextAsset descriptions = 2;  // comment[List of text assets for descriptions, at least two text assets]
    repeated AdImageAsset images      = 3;  // comment[List of image assets that may be displayed with the ad]
    repeated AdVideoAsset videos      = 4;  // comment[List of YouTube video assets that may be displayed with the ad]
}
message AppPreRegistrationAd {
    repeated AdTextAsset headlines       = 1;  // comment[List of text assets for headlines, at least two text assets]
    repeated AdTextAsset descriptions    = 2;  // comment[List of text assets for descriptions, at least two text assets]
    repeated AdImageAsset images         = 3;  // comment[List of image assets that may be displayed with the ad]
    repeated AdVideoAsset youtube_videos = 4;  // comment[List of YouTube video assets that may be displayed with the ad]
}
enum AppUrlOperatingSystemType {
    APPURLOPERATINGSYSTEMTYPE_UNSPECIFIED = 0;
    APPURLOPERATINGSYSTEMTYPE_UNKNOWN     = 1;
    APPURLOPERATINGSYSTEMTYPE_IOS         = 2;
    APPURLOPERATINGSYSTEMTYPE_ANDROID     = 3;
}
message FinalAppUrl {
    int32 os_type = 1;  // comment[ref: AppUrlOperatingSystemType]
    string url    = 2;  // comment[url]
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ResponsiveSearchAdInfo]
message ResponsiveSearchAdInfo {
    repeated AdTextAsset headlines    = 1;  // comment[List of text assets for headlines]
    repeated AdTextAsset descriptions = 2;  // comment[List of text assets for descriptions]
    string   path1                    = 3;
    string   path2                    = 4;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ResponsiveDisplayAdInfo?hl=en]
message ResponsiveDisplayAdInfo {
    repeated    AdImageAsset marketing_images        = 1;
    repeated    AdImageAsset square_marketing_images = 2;
    repeated    AdImageAsset logo_images             = 3;
    repeated    AdImageAsset square_logo_images      = 4;
    repeated    AdTextAsset headlines                = 5;
    AdTextAsset long_headline                        = 6;
    repeated    AdTextAsset descriptions             = 7;
    repeated    AdVideoAsset youtube_videos          = 8;
    string      business_name                        = 9;
    string      call_to_action_text                  = 10;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/DiscoveryMultiAssetAdInfo
message DiscoveryMultiAssetAdInfo {
    repeated AdImageAsset marketing_images          = 1;
    repeated AdImageAsset square_marketing_images   = 2;
    repeated AdImageAsset portrait_marketing_images = 3;
    repeated AdImageAsset logo_images               = 4;
    repeated AdTextAsset headlines                  = 5;
    repeated AdTextAsset descriptions               = 6;
    string   business_name                          = 7;
    string   call_to_action_text                    = 8;
}

// https://developers.google.com/google-ads/api/reference/rpc/v18/DemandGenMultiAssetAdInfo
message DemandGenMultiAssetAdInfo {
    repeated AdImageAsset marketing_images          = 1;
    repeated AdImageAsset square_marketing_images   = 2;
    repeated AdImageAsset portrait_marketing_images = 3;
    repeated AdImageAsset logo_images               = 4;
    repeated AdTextAsset headlines                  = 5;
    repeated AdTextAsset descriptions               = 6;
    string   business_name                          = 7;
    string   call_to_action_text                    = 8;
}

// https://developers.google.com/google-ads/api/reference/rpc/v14/ExpandedTextAdInfo
message ExpandedTextAdInfo {
    string headline_part1 = 1;
    string headline_part2 = 2;
    string headline_part3 = 3;
    string description    = 4;
    string description2   = 5;
    string path1          = 6;
    string path2          = 7;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v18/Ad]
message Ad {
    string                 resource_name                         = 1;   // comment[resource name]; default[xxx]
    string                 name                                  = 2;   // comment[name]; default[xxx]
    AppAd                  app_ad                                = 3;   // comment[ref: AppAdInfo]
    AppEngagementAd        app_engagement_ad                     = 4;   // comment[ref: AppEngagementAd]
    AppPreRegistrationAd   app_pre_registration_ad               = 5;   // comment[ref: AppPreRegistrationAd]
    int32                  type                                  = 6;   // comment[ref: AdType]; default[13]
    repeated               FinalAppUrl final_app_urls            = 7;   // comment[required when create app engagement ad]
    repeated               string final_urls                     = 8;   // comment[final_urls]
    ResponsiveSearchAdInfo responsive_search_ad                  = 9;   // comment[ref: ResponsiveSearchAdInfo]
    string                 tracking_url_template                 = 10;  // comment[tracking_url_template]
    string                 final_url_suffix                      = 11;  // comment[final_url_suffix]
    repeated               CustomParameter url_custom_parameters = 12;  // comment[ref: CustomParameter]
    repeated               string final_mobile_urls              = 13;  // comment[ref: final_mobile_urls]

    // display 新增
    ResponsiveDisplayAdInfo responsive_display_ad = 14; // comment[ref: ResponsiveDisplayAdInfo]
    // deprecated v8 discovery 新增
    // DiscoveryMultiAssetAdInfo discovery_multi_asset_ad = 15; // comment[ref: DiscoveryMultiAssetAdInfo]
    // display 新增
    ExpandedTextAdInfo expanded_text_ad = 16; // comment[ref: ExpandedTextAd]
    
    // v18 discovery_multi_asset_ad -> demand_gen_multi_asset_ad
    DemandGenMultiAssetAdInfo demand_gen_multi_asset_ad = 17; // comment[ref: DemandGenMultiAssetAdInfo]
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/AppAdInfo]
message AppAdInfo {
    repeated AdTextAsset headlines       = 1;  // comment[List of text assets for headlines]
    repeated AdTextAsset descriptions    = 2;  // comment[List of text assets for descriptions]
    repeated AdImageAsset images         = 3;  // comment[List of image assets that may be displayed with the ad]
    repeated AdVideoAsset youtube_videos = 4;  // comment[List of YouTube video assets that may be displayed with the ad]
}
enum AdType {
    ADTYPE_UNSPECIFIED                      = 0;
    ADTYPE_UNKNOWN                          = 1;
    ADTYPE_TEXT_AD                          = 2;
    ADTYPE_EXPANDED_TEXT_AD                 = 3;
    ADTYPE_EXPANDED_DYNAMIC_SEARCH_AD       = 7;
    ADTYPE_HOTEL_AD                         = 8;
    ADTYPE_SHOPPING_SMART_AD                = 9;
    ADTYPE_SHOPPING_PRODUCT_AD              = 10;
    ADTYPE_VIDEO_AD                         = 12;
    ADTYPE_GMAIL_AD                         = 13;
    ADTYPE_IMAGE_AD                         = 14;
    ADTYPE_RESPONSIVE_SEARCH_AD             = 15;
    ADTYPE_LEGACY_RESPONSIVE_DISPLAY_AD     = 16;
    ADTYPE_APP_AD                           = 17;
    ADTYPE_LEGACY_APP_INSTALL_AD            = 18;
    ADTYPE_RESPONSIVE_DISPLAY_AD            = 19;
    ADTYPE_LOCAL_AD                         = 20;
    ADTYPE_HTML5_UPLOAD_AD                  = 21;
    ADTYPE_DYNAMIC_HTML5_AD                 = 22;
    ADTYPE_APP_ENGAGEMENT_AD                = 23;
    ADTYPE_SHOPPING_COMPARISON_LISTING_AD   = 24;
    ADTYPE_VIDEO_BUMPER_AD                  = 25;
    ADTYPE_VIDEO_NON_SKIPPABLE_IN_STREAM_AD = 26;
    ADTYPE_VIDEO_OUTSTREAM_AD               = 27;
    ADTYPE_VIDEO_TRUEVIEW_IN_STREAM_AD      = 29;
    ADTYPE_VIDEO_RESPONSIVE_AD              = 30;
    ADTYPE_SMART_CAMPAIGN_AD                = 31;
    ADTYPE_CALL_AD                          = 32;
    ADTYPE_APP_PRE_REGISTRATION_AD          = 33;
    ADTYPE_IN_FEED_VIDEO_AD                 = 34;
    // deprecated v18
    // ADTYPE_DISCOVERY_MULTI_ASSET_AD         = 35;
    // ADTYPE_DISCOVERY_CAROUSEL_AD            = 36;
    // deprecated v18
    ADTYPE_DEMAND_GEN_MULTI_ASSET_AD        = 40;
    ADTYPE_DEMAND_GEN_CAROUSEL_AD           = 41;
    ADTYPE_TRAVEL_AD                        = 37;
    ADTYPE_DEMAND_GEN_VIDEO_RESPONSIVE_AD   = 42;
    ADTYPE_DEMAND_GEN_PRODUCT_AD            = 39;
}
message AdGroupAd {
    Ad ad                = 1;  // comment[ad group ad info]; default[xxx]
    int32 status         = 2;  // comment[ref: AdGroupAdStatus]
    string resource_name = 3;  // comment[ad group ad resource name]
    string group_resource_name = 4; // comment[ad_group resource name]
}
// comment[response]
message GetAdGroupAdsRsp {
    Result result                   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAd ad_group_ads = 2;  // comment[ad group ad list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ads_by_campaign]
// comment[get ad group ad list]
message GetAdGroupAdsByCampaignReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource name]
}

// comment[response]
message GetAdGroupAdsByCampaignRsp {
    Result result                   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAd ad_group_ads = 2;  // comment[ad group ad list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ad]
// comment[get ad group ad]
message GetAdGroupAdReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[ad group ad resource name]; default[]
}
// comment[response]
message GetAdGroupAdRsp {
    Result result         = 1;  // comment[返回结果]
    AdGroupAd ad_group_ad = 2;  // comment[ad group ad]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/batch_get_ad_group_ad]
// comment[batch get ad group ad]
message BatchGetAdGroupAdReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    repeated string resource_names = 2;  // comment[ad group ad resource name list]; default[]
}
// comment[response]
message BatchGetAdGroupAdRsp {
    Result result         = 1;  // comment[返回结果]
    repeated AdGroupAd ad_group_ads = 2;  // comment[ad group ad list]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/update_ad_group_ad]
// comment[update ad group ad]
message UpdateAdGroupAdReq {
    string customer_id                 = 1;  // comment[customer id]; default[**********]
    UpdateAdGroupAd update_ad_group_ad = 2;  // comment[update ad group ad parameters]
}
enum AdGroupAdStatus {
    ADGROUPADSTATUS_UNSPECIFIED = 0;
    ADGROUPADSTATUS_UNKNOWN     = 1;
    ADGROUPADSTATUS_ENABLED     = 2;
    ADGROUPADSTATUS_PAUSED      = 3;
    ADGROUPADSTATUS_REMOVED     = 4;
}

message UpdateAdGroupAd {
    string resource_name = 1;  // comment[ad group ad resource name]
    int32 status         = 2;  // comment[ref: AdGroupAdStatus]; default[3]
}
// comment[response]
message UpdateAdGroupAdRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/remove_ad_group_ad]
// comment[remove ad group ad]
message RemoveAdGroupAdReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[resource name]
}
// comment[response]
message RemoveAdGroupAdRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/conversion/create_conversion_action]
// comment[create conversion action]
message CreateConversionActionReq {
    string customer_id                              = 1;  // comment[customer id]; default[**********]
    CreateConversionAction create_conversion_action = 2;  // comment[create conversion action]; default[xxx]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ConversionActionTypeEnum.ConversionActionType]
enum ConversionActionType {
    CONVERSIONACTIONTYPE_UNSPECIFIED                                       = 0;
    CONVERSIONACTIONTYPE_UNKNOWN                                           = 1;
    CONVERSIONACTIONTYPE_AD_CALL                                           = 2;
    CONVERSIONACTIONTYPE_CLICK_TO_CALL                                     = 3;
    CONVERSIONACTIONTYPE_GOOGLE_PLAY_DOWNLOAD                              = 4;
    CONVERSIONACTIONTYPE_GOOGLE_PLAY_IN_APP_PURCHASE                       = 5;
    CONVERSIONACTIONTYPE_UPLOAD_CALLS                                      = 6;
    CONVERSIONACTIONTYPE_UPLOAD_CLICKS                                     = 7;
    CONVERSIONACTIONTYPE_WEBPAGE                                           = 8;
    CONVERSIONACTIONTYPE_WEBSITE_CALL                                      = 9;
    CONVERSIONACTIONTYPE_STORE_SALES_DIRECT_UPLOAD                         = 10;
    CONVERSIONACTIONTYPE_STORE_SALES                                       = 11;
    CONVERSIONACTIONTYPE_FIREBASE_ANDROID_FIRST_OPEN                       = 12;
    CONVERSIONACTIONTYPE_FIREBASE_ANDROID_IN_APP_PURCHASE                  = 13;
    CONVERSIONACTIONTYPE_FIREBASE_ANDROID_CUSTOM                           = 14;
    CONVERSIONACTIONTYPE_FIREBASE_IOS_FIRST_OPEN                           = 15;
    CONVERSIONACTIONTYPE_FIREBASE_IOS_IN_APP_PURCHASE                      = 16;
    CONVERSIONACTIONTYPE_FIREBASE_IOS_CUSTOM                               = 17;
    CONVERSIONACTIONTYPE_THIRD_PARTY_APP_ANALYTICS_ANDROID_FIRST_OPEN      = 18;
    CONVERSIONACTIONTYPE_THIRD_PARTY_APP_ANALYTICS_ANDROID_IN_APP_PURCHASE = 19;
    CONVERSIONACTIONTYPE_THIRD_PARTY_APP_ANALYTICS_ANDROID_CUSTOM          = 20;
    CONVERSIONACTIONTYPE_THIRD_PARTY_APP_ANALYTICS_IOS_FIRST_OPEN          = 21;
    CONVERSIONACTIONTYPE_THIRD_PARTY_APP_ANALYTICS_IOS_IN_APP_PURCHASE     = 22;
    CONVERSIONACTIONTYPE_THIRD_PARTY_APP_ANALYTICS_IOS_CUSTOM              = 23;
    CONVERSIONACTIONTYPE_ANDROID_APP_PRE_REGISTRATION                      = 24;
    CONVERSIONACTIONTYPE_ANDROID_INSTALLS_ALL_OTHER_APPS                   = 25;
    CONVERSIONACTIONTYPE_FLOODLIGHT_ACTION                                 = 26;
    CONVERSIONACTIONTYPE_FLOODLIGHT_TRANSACTION                            = 27;
    CONVERSIONACTIONTYPE_GOOGLE_HOSTED                                     = 28;
    CONVERSIONACTIONTYPE_LEAD_FORM_SUBMIT                                  = 29;
    CONVERSIONACTIONTYPE_SALESFORCE                                        = 30;
    CONVERSIONACTIONTYPE_SEARCH_ADS_360                                    = 31;
    CONVERSIONACTIONTYPE_SMART_CAMPAIGN_AD_CLICKS_TO_CALL                  = 32;
    CONVERSIONACTIONTYPE_SMART_CAMPAIGN_MAP_CLICKS_TO_CALL                 = 33;
    CONVERSIONACTIONTYPE_SMART_CAMPAIGN_MAP_DIRECTIONS                     = 34;
    CONVERSIONACTIONTYPE_SMART_CAMPAIGN_TRACKED_CALLS                      = 35;
    CONVERSIONACTIONTYPE_STORE_VISITS                                      = 36;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ConversionActionCategoryEnum.ConversionActionCategory]
enum ConversionActionCategory {
    CONVERSIONACTIONCATEGORY_UNSPECIFIED      = 0;
    CONVERSIONACTIONCATEGORY_UNKNOWN          = 1;
    CONVERSIONACTIONCATEGORY_DEFAULT          = 2;
    CONVERSIONACTIONCATEGORY_PAGE_VIEW        = 3;
    CONVERSIONACTIONCATEGORY_PURCHASE         = 4;
    CONVERSIONACTIONCATEGORY_SIGNUP           = 5;
    CONVERSIONACTIONCATEGORY_DOWNLOAD         = 7;
    CONVERSIONACTIONCATEGORY_ADD_TO_CART      = 8;
    CONVERSIONACTIONCATEGORY_BEGIN_CHECKOUT   = 9;
    CONVERSIONACTIONCATEGORY_SUBSCRIBE_PAID   = 10;
    CONVERSIONACTIONCATEGORY_PHONE_CALL_LEAD  = 11;
    CONVERSIONACTIONCATEGORY_IMPORTED_LEAD    = 12;
    CONVERSIONACTIONCATEGORY_SUBMIT_LEAD_FORM = 13;
    CONVERSIONACTIONCATEGORY_BOOK_APPOINTMENT = 14;
    CONVERSIONACTIONCATEGORY_REQUEST_QUOTE    = 15;
    CONVERSIONACTIONCATEGORY_GET_DIRECTIONS   = 16;
    CONVERSIONACTIONCATEGORY_OUTBOUND_CLICK   = 17;
    CONVERSIONACTIONCATEGORY_CONTACT          = 18;
    CONVERSIONACTIONCATEGORY_ENGAGEMENT       = 19;
    CONVERSIONACTIONCATEGORY_STORE_VISIT      = 20;
    CONVERSIONACTIONCATEGORY_STORE_SALE       = 21;
    CONVERSIONACTIONCATEGORY_QUALIFIED_LEAD   = 22;
    CONVERSIONACTIONCATEGORY_CONVERTED_LEAD   = 23;
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ConversionActionStatusEnum.ConversionActionStatus]
enum ConversionActionStatus {
    CONVERSIONACTIONSTATUS_UNSPECIFIED = 0;
    CONVERSIONACTIONSTATUS_UNKNOWN     = 1;
    CONVERSIONACTIONSTATUS_ENABLED     = 2;
    CONVERSIONACTIONSTATUS_REMOVED     = 3;
    CONVERSIONACTIONSTATUS_HIDDEN      = 4;
}
message ConversionActionValueSettings {
    double default_value          = 1;  // comment[defautl value]; default[15.0]
    bool always_use_default_value = 2;  // comment[always use default value]; default[true]
}
message CreateConversionAction {
    string name                                  = 1;  // comment[name]; default[test create conversion action]
    int32 type                                   = 2;  // comment[ref: ConversionActionType]; default[13]
    int32 category                               = 3;  // comment[ref: ConversionActionCategory]; default[5]
    int32 status                                 = 4;  // comment[ref: ConversionActionStatus]; default[0]
    int64 view_through_lookback_window_days      = 5;  // comment[view through lookback window days]; default[15]
    ConversionActionValueSettings value_settings = 6;  // comment[value settings]
}
// comment[response]
message CreateConversionActionRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;  // comment[resource name]; default[xxx]
}

// url[http://127.0.0.1:6027/api/conversion/get_conversion_actions]
// comment[get conversion action list]
message GetConversionActionsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    repeated string app_id_list = 2; // comment[app_id list]
}
// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/ConversionOriginEnum.ConversionOrigin]
enum ConversionOrigin {
    CONVERSIONORIGIN_UNSPECIFIED    = 0;
    CONVERSIONORIGIN_UNKNOWN        = 1;
    CONVERSIONORIGIN_WEBSITE        = 2;
    CONVERSIONORIGIN_GOOGLE_HOSTED  = 3;
    CONVERSIONORIGIN_APP            = 4;
    CONVERSIONORIGIN_CALL_FROM_ADS  = 5;
    CONVERSIONORIGIN_STORE          = 6;
    CONVERSIONORIGIN_YOUTUBE_HOSTED = 7;
}

// ThirdPartyAppAnalyticsSettings
message ThirdPartyAppAnalyticsSettings {
    string event_name       = 1;
    string provider_name    = 2;
}

// FirebaseSettings
message FirebaseSettings {
    string event_name       = 1;
    string project_id       = 2;
}

// comment[conversion action]
message ConversionAction {
    string app_id         = 1;  // comment[app id];
    int32 category        = 2;  // comment[ref: CreateConversionActionCategory]
    string name           = 3;  // comment[name]
    int32 origin          = 4;  // comment[ref: ConversionOrigin]
    string owner_customer = 5;  // comment[owner customer resource name]
    string resource_name  = 6;  // comment[resource name]
    int32 status          = 7;  // comment[ref: ConversionActionStatus]
    int32 type            = 8;  // comment[ref: ConversionActionType]
    FirebaseSettings    firebase_settings   = 9; // comment[ref: FirebaseSettings]
    ThirdPartyAppAnalyticsSettings third_party_app_analytics_settings   = 10; // comment[ref: ThirdPartyAppAnalyticsSettings]

    int64 id = 11; // The ID of the conversion action.
}
// comment[response]
message GetConversionActionsRsp {
    Result result                                = 1;  // comment[返回结果]; default[xxx]
    repeated ConversionAction conversion_actions = 2;  // comment[conversion action list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/conversion/get_conversion_actions_page]
// comment[get conversion action list by page]
message GetConversionActionsPageReq {
    string   customer_id                = 1;  // comment[customer id]; default[**********]
    repeated string app_id_list         = 2;  // comment[app_id list]
    int64    start_conversion_action_id = 3;  // comment[start with > start_conversion_action_id]
    int32    limit                      = 4;
}
// comment[response]
message GetConversionActionsPageRsp {
    Result result                                = 1;  // comment[返回结果]; default[xxx]
    repeated ConversionAction conversion_actions = 2;  // comment[conversion action list]; default[xxx]
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceStatusEnum.AudienceStatus?hl=en
enum AudienceStatus {
    AUDIENCESTATUS_UNSPECIFIED = 0;
    AUDIENCESTATUS_UNKNOWN     = 1;
    AUDIENCESTATUS_ENABLED     = 2;
    AUDIENCESTATUS_REMOVED     = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AgeSegment?hl=en
message AgeSegment {
    int32 min_age = 1; // Minimum age to include. A minimum age must be specified and must be at least 18. Allowed values are 18, 25, 35, 45, 55, and 65.
    int32 max_age = 2; // Maximum age to include. A maximum age need not be specified. If specified, max_age must be greater than min_age, and allowed values are 24, 34, 44, 54, and 64.
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AgeDimension?hl=en
message AgeDimension {
    repeated AgeSegment  age_ranges = 1;
    bool     include_undetermined   = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/GenderTypeEnum.GenderType?hl=en
enum GenderType {
    GENDERTYPE_UNSPECIFIED  = 0;
    GENDERTYPE_UNKNOWN      = 1;
    GENDERTYPE_MALE         = 10;
    GENDERTYPE_FEMALE       = 11;
    GENDERTYPE_UNDETERMINED = 20;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/GenderDimension?hl=en
message GenderDimension {
    repeated int32 genders        = 1;  // enum GenderType
    bool     include_undetermined = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/UserListSegment?hl=en
message UserListSegment {
    string user_list = 1; // The user list resource name
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/UserInterestSegment?hl=en
message UserInterestSegment {
    string user_interest_category = 1; // The user interest resource name
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomAudienceSegment?hl=en
message CustomAudienceSegment {
    string custom_audience = 1; // The custom audience resource name
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceSegment?hl=en
message AudienceSegment {
    UserListSegment       user_list       = 1;
    UserInterestSegment   user_interest   = 2;
    CustomAudienceSegment custom_audience = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceSegmentDimension?hl=en
message AudienceSegmentDimension {
    repeated AudienceSegment segments = 1; // Union field
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceDimension?hl=en
message AudienceDimension {
    AgeDimension             age               = 1;
    GenderDimension          gender            = 2;
    AudienceSegmentDimension audience_segments = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/Audience?hl=en
message Audience {
    int64 id             = 1;  // comment[id]
    string resource_name = 2;  // comment[resource name]
    string name          = 3;  // comment[resource name]
    int32 status         = 4;  // comment[ref: AudienceStatus]
    string description   = 5;  // comment[description]
    repeated AudienceDimension dimensions = 6; // Union field
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AudienceInfo?hl=en
message AudienceInfo {
    string audience = 1;
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/AssetGroupSignal?hl=en
message AssetGroupSignal {
    string       resource_name = 1;
    string       asset_group   = 2;  //  The asset group which this asset group signal belongs to.
    AudienceInfo audience      = 3;  // The signal(audience criterion) to be used by the performance max campaign.
}

// url[http://127.0.0.1:6027/api/audience/get_user_lists]
// comment[get user lists]
message GetUserListsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
}
enum UserListType {
    USERLISTTYPE_UNSPECIFIED          = 0;
    USERLISTTYPE_UNKNOWN              = 1;
    USERLISTTYPE_REMARKETING          = 2;
    USERLISTTYPE_LOGICAL              = 3;
    USERLISTTYPE_EXTERNAL_REMARKETING = 4;
    USERLISTTYPE_RULE_BASED           = 5;
    USERLISTTYPE_SIMILAR              = 6;
    USERLISTTYPE_CRM_BASED            = 7;
}

message CrmBasedUserList {
    string app_id = 1;  // comment[A string that uniquely identifies a mobile application from which the data was collected]
}

message LogicalUserListOperandInfo {
    string user_list = 1; // Resource name of a user list as an operand.
}

enum UserListLogicalRuleOperator {
    USERLISTLOGICALRULEOPERATOR_UNSPECIFIED = 0;  // Not specified.
    USERLISTLOGICALRULEOPERATOR_UNKNOWN     = 1;  // Used for return value only. Represents value unknown in this version.
    USERLISTLOGICALRULEOPERATOR_ALL         = 2;  // And - all of the operands.
    USERLISTLOGICALRULEOPERATOR_ANY         = 3;  // Or - at least one of the operands.
    USERLISTLOGICALRULEOPERATOR_NONE        = 4;  // Not - none of the operands.
}

message UserListLogicalRuleInfo {
    int32    operator                                 = 1;  // comment[ref: UserListLogicalRuleOperator]
    repeated LogicalUserListOperandInfo rule_operands = 2;  // The list of operands of the rule.
}
message LogicalUserListInfo {
    // Logical list rules that define this user list. The rules are defined as a logical operator (ALL/ANY/NONE) and a list of user lists. All the rules are ANDed when they are evaluated.
    repeated UserListLogicalRuleInfo rules = 1;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/UserListAccessStatusEnum.UserListAccessStatus]
enum UserListAccessStatus {
    USERLISTACCESSSTATUS_UNSPECIFIED = 0;
    USERLISTACCESSSTATUS_UNKNOWN     = 1;
    USERLISTACCESSSTATUS_ENABLED     = 2;
    USERLISTACCESSSTATUS_DISABLED    = 3;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v12/UserListMembershipStatusEnum.UserListMembershipStatus?hl=en] 
enum UserListMembershipStatus {
    UserListMembershipStatus_UNSPECIFIED = 0;
    UserListMembershipStatus_UNKNOWN     = 1;
    UserListMembershipStatus_OPEN        = 2;
    UserListMembershipStatus_CLOSED      = 3;
}

message UserList {
    string id                            = 1;  // comment[id];
    string name                          = 2;  // comment[name];
    string resource_name                 = 3;  // comment[resource name];
    int32 type                           = 4;  // comment[ref: UserListType]
    string size_for_display              = 5;  // comment[Estimated number of users in this user list, on the Google Display Network]
    string size_for_search               = 6;  // comment[Estimated number of users in this user list in the google.com domain]
    CrmBasedUserList crm_based_user_list = 7;  // comment[ref: CrmBasedUserList]
    int32 account_user_list_status       = 8;  // comment[ref: UserListAccessStatus]
    int32 membership_status              = 9;  // comment[ref: UserListMembershipStatus]
    LogicalUserListInfo logical_user_list = 10; // type = USERLISTTYPE_LOGICAL 有效
}

message GetUserListsRsp {
    Result result                = 1;  // comment[返回结果]; default[xxx]
    repeated UserList user_lists = 2;  // comment[ref: UserList]
}

// url[http://127.0.0.1:6027/api/audience/get_user_lists_page]
// comment[get user lists by page]
message GetUserListsPageReq {
    string customer_id        = 1;  // comment[customer id]; default[**********]
    int64  start_user_list_id = 2;  // comment[start with > start_user_list_id]
    int32  limit              = 3;
}

message GetUserListsPageRsp {
    Result result                = 1;  // comment[返回结果]; default[xxx]
    repeated UserList user_lists = 2;  // comment[ref: UserList]
}

// url[http://127.0.0.1:6027/api/audience/get_audiences]
// comment[get audience lists]
message GetAudiencesReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
}

message GetAudiencesRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
    repeated Audience audiences = 2;
}

// url[http://127.0.0.1:6027/api/audience/create_audience]
// comment[create audience]
message CreateAudienceReq {
    string   customer_id = 1;  // comment[customer id]; default[**********]
    Audience audience    = 2;
}

message CreateAudienceRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2; // 创建成功的audience resource name
}

// url[http://127.0.0.1:6027/api/ad/update_ad]
// comment[update ad]
message UpdateAdReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    UpdateAd update_ad = 2;  // comment[update ad group ad parameters]
}
message UpdateAd {
    string                 resource_name                         = 1;   // comment[resource name]; default[xxx]
    string                 name                                  = 2;   // comment[name]; default[xxx]
    AppAd                  app_ad                                = 3;   // comment[ref: AppAdInfo]
    AppEngagementAd        app_engagement_ad                     = 4;   // comment[ref: AppEngagementAd]
    AppPreRegistrationAd   add_pre_registration                  = 5;   // comment[ref: AppPreRegistrationAd]
    int32                  type                                  = 6;   // comment[ref: AdType]; default[13]
    repeated               FinalAppUrl final_app_urls            = 7;   // comment[required when create app engagement ad]
    int32                  advertising_channel_sub_type          = 8;   // comment[ref: AdvertisingChannelSubType]; default[12]
    int32                  advertising_channel_type              = 9;   // comment[ref: AdvertisingChannelType];
    repeated               string final_urls                     = 10;  // comment[final_urls]
    ResponsiveSearchAdInfo responsive_search_ad                  = 11;  // comment[ref: ResponsiveSearchAdInfo]
    string                 tracking_url_template                 = 12;  // comment[tracking_url_template]
    string                 final_url_suffix                      = 13;  // comment[final_url_suffix]
    repeated               CustomParameter url_custom_parameters = 14;  // comment[ref: CustomParameter]
    repeated               string final_mobile_urls              = 15;  // comment[ref: final_mobile_urls]
    repeated               string field_masks                    = 16;  // comment[field_masks]
    ResponsiveDisplayAdInfo responsive_display_ad                = 17;  // comment[ref: ResponsiveDisplayAdInfo]

    // deprecated v18
    // DiscoveryMultiAssetAdInfo discovery_multi_asset_ad           = 18;  // comment[ref: DiscoveryMultiAssetAdInfo]
    // deprecated v18

    // v18 discovery_multi_asset_ad -> demand_gen_multi_asset_ad
    DemandGenMultiAssetAdInfo demand_gen_multi_asset_ad = 19; // comment[ref: DemandGenMultiAssetAdInfo]
}
// comment[response]
message UpdateAdRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}


// url[http://127.0.0.1:8080/api/account/get_account_info]
// comment[get account info]
message GetAccountInfoReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
}

// https://developers.google.com/google-ads/api/reference/rpc/v10/CustomerStatusEnum.CustomerStatus?hl=en
enum CustomerStatus {
    CUSTOMERSTATUS_UNSPECIFIED = 0;
    CUSTOMERSTATUS_UNKNOWN     = 1;
    CUSTOMERSTATUS_ENABLED     = 2;
    CUSTOMERSTATUS_CANCELED    = 3;
    CUSTOMERSTATUS_SUSPENDED   = 4;
    CUSTOMERSTATUS_CLOSED      = 5;
}
message AccountInfo {
    string customer_id           = 1;
    string descriptive_name      = 2;
    string currency_code         = 3;
    string time_zone             = 4;
    string tracking_url_template = 5;
    string auto_tagging_enabled  = 6;
    int32  status                = 7;  // comment[ref CustomerStatus]
}

// comment[response]
message GetAccountInfoRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
    AccountInfo account_info = 2;
}


// url[http://127.0.0.1:8080/api/account/get_mcc_accounts]
// comment[get account info by mcc]
message GetMccAccountsReq {
    string mcc_id = 1;  // comment[mcc id]
}

// comment[response]
message GetMccAccountsRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
    repeated AccountInfo accounts = 2;
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/CampaignConversionGoal]
message CampaignConversionGoal {
    string resource_name = 1;
    string campaign      = 2;
    int32  category      = 3;  // comment[ref: ConversionActionCategory]
    int32  origin        = 4;  // comment[ref: ConversionOrigin]
    bool   biddable      = 5;
}

// url[http://127.0.0.1:6027/api/campaign/create_campaign_conversion_goals]
// comment[create campaign conversion goals]
message CreateCampaignConversionGoalsReq {
    string   customer_id                                      = 1;
    string   campaign_resource_name                           = 2;
    repeated CampaignConversionGoal campaign_conversion_goals = 3;  // campaign_conversion_goals列表
}

// comment[create campaign conversion goals]
message CreateCampaignConversionGoalsRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
}

// comment[ref: https://developers.google.com/google-ads/api/reference/rpc/v10/CustomerConversionGoal]
message CustomerConversionGoal {
    string resource_name = 1;  // customers/{customer_id}/customerConversionGoals/{category}~{origin}
    int32  category      = 2;  // comment[ref: ConversionActionCategory]
    int32  origin        = 3;  // comment[ref: ConversionOrigin]
    bool   biddable      = 4;
}

// url[http://127.0.0.1:8080/api/conversion_goal/get_customer_conversion_goal]
// comment[create campaign conversion goals]
message GetCustomerConversionGoalReq {
    string   customer_id                                      = 1;
}

// comment[create campaign conversion goals]
message GetCustomerConversionGoalRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    repeated CustomerConversionGoal customer_conversion_goals = 2;
}

// url[http://127.0.0.1:8080/api/ad_group/create_ad_group_criterions]
message CreateAdGroupCriterionsReq {
    string   customer_id                          = 1;
    repeated AdGroupCriterion ad_group_criterions = 2;  // ad_group_criterions 列表
}

// comment[create campaign conversion goals]
message CreateAdGroupCriterionsRsp {
    Result   result                = 1;  // comment[返回结果]; default[xxx]
    repeated string resource_names = 2;  // comment[创建成功的resource_names列表]; default[xxx]
}

// url[http://127.0.0.1:8080/api/campaign/get_campaign_conversion_goal]
message GetCampaignConversionGoalReq {
    string customer_id            = 1;
    string campaign_resource_name = 2;
}

message GetCampaignConversionGoalRsp {
    Result   result                                           = 1;  // comment[返回结果]; default[xxx]
    repeated CampaignConversionGoal campaign_conversion_goals = 2;  // campaign_conversion_goals列表
}

// url[http://127.0.0.1:8080/api/ad_group/update_ad_group_criterions]
message UpdateAdGroupCriterionsReq {
    string   customer_id                                 = 1;
    repeated AdGroupCriterion create_ad_group_criterions = 2;  // 待新增 ad_group_criterions 列表
    repeated AdGroupCriterion remove_ad_group_criterions = 3;  // 待删除 ad_group_criterions 列表 每一项必须包含resource_name
}

message UpdateAdGroupCriterionsRsp {
    Result result = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:8080/api/campaign/get_campaign_status]
// comment[get campaign status name]
message GetCampaignStatusReq {
    string customer_id            = 1;
    string campaign_resource_name = 2;
}

message GetCampaignStatusRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    Campaign campaign = 2;  // campaign status, name
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_groups_status]
// comment[get ad group list: status, name]
message GetAdGroupsStatusReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
}
// comment[response]
message GetAdGroupsStatusRsp {
    Result result              = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroup ad_groups = 2;  // comment[ad group list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ads_status]
// comment[get ad group ad list: status]
message GetAdGroupAdsStatusReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string ad_group    = 2;  // comment[ad group resource name]; default[customers/**********/adGroups/134018102374]
}

// comment[response]
message GetAdGroupAdsStatusRsp {
    Result result                   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAd ad_group_ads = 2;  // comment[ad group ad list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ads_status_by_campaign]
// comment[get ad group ad list: status]
message GetAdGroupAdsStatusByCampaignReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource name];
}

// comment[response]
message GetAdGroupAdsStatusByCampaignRsp {
    Result result                   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAd ad_group_ads = 2;  // comment[ad group ad list]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/update_campaign_status]
// comment[update campaign status]
message UpdateCampaignStatusReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[campaign resource name]; default[customers/**********/campaigns/16806285811]
    int32  status        = 3;  // comment[ref enum CampaignStatus];
}

// comment[response]
message UpdateCampaignStatusRsp {
    Result result                   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/update_ad_group_status]
// comment[update ad_group status]
message UpdateAdGroupStatusReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[ad group resource name]; default[customers/**********/adGroups/134018102374]
    int32  status        = 3;  // comment[ref enum AdGroupStatus];
}

// comment[response]
message UpdateAdGroupStatusRsp {
    Result result                   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/update_ad_group_ad_status]
// comment[update ad_group_ad status]
message UpdateAdGroupAdStatusReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[ad group ad resource name]
    int32  status        = 3;  // comment[ref enum AdGroupAdStatus];
}

// comment[response]
message UpdateAdGroupAdStatusRsp {
    Result result                   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ad_campaign_summary]
// comment[查ad_group_ad的父级campaign简要信息]
message GetAdGroupAdCampaignSummaryReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[ad group ad resource name]
}

// comment[response]
message GetAdGroupAdCampaignSummaryRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    Campaign campaign = 2;
}

// url[http://127.0.0.1:6027/api/asset/get_sitelinks]
// comment[查sitelink列表]
message GetSitelinksReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
}

// comment[response]
message GetSitelinksRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated Asset assets = 2;
}

// url[http://127.0.0.1:6027/api/asset/get_sitelinks_by_campaign]
// comment[通过campaign查sitelink列表]
message GetSitelinksByCampaignReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign = 2; // comment[campaign resource name];
}

// comment[response]
message GetSitelinksByCampaignRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated Asset assets = 2;
}

// url[http://127.0.0.1:6027/api/asset/get_assets]
// comment[查指定的asset列表]
message GetAssetsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    repeated string resource_names = 2;
}

message GetAssetsRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated Asset assets = 2;
}

enum AssetGroupAssetFieldType {
    ASSET_GROUP_ASSET_FIELD_TYPE_UNSPECIFIED = 0;
    ASSET_GROUP_ASSET_FIELD_TYPE_UNKNOWN = 1;
    ASSET_GROUP_ASSET_FIELD_TYPE_HEADLINE = 2;
    ASSET_GROUP_ASSET_FIELD_TYPE_DESCRIPTION = 3;
    ASSET_GROUP_ASSET_FIELD_TYPE_MANDATORY_AD_TEXT = 4;
    ASSET_GROUP_ASSET_FIELD_TYPE_MARKETING_IMAGE = 5;
    ASSET_GROUP_ASSET_FIELD_TYPE_MEDIA_BUNDLE = 6;
    ASSET_GROUP_ASSET_FIELD_TYPE_YOUTUBE_VIDEO = 7;
    ASSET_GROUP_ASSET_FIELD_TYPE_BOOK_ON_GOOGLE = 8;
    ASSET_GROUP_ASSET_FIELD_TYPE_LEAD_FORM = 9;
    ASSET_GROUP_ASSET_FIELD_TYPE_PROMOTION = 10;
    ASSET_GROUP_ASSET_FIELD_TYPE_CALLOUT = 11;
    ASSET_GROUP_ASSET_FIELD_TYPE_STRUCTURED_SNIPPET = 12;
    ASSET_GROUP_ASSET_FIELD_TYPE_SITELINK = 13;
    ASSET_GROUP_ASSET_FIELD_TYPE_MOBILE_APP = 14;
    ASSET_GROUP_ASSET_FIELD_TYPE_HOTEL_CALLOUT = 15;
    ASSET_GROUP_ASSET_FIELD_TYPE_CALL = 16;
    ASSET_GROUP_ASSET_FIELD_TYPE_PRICE = 24;
    ASSET_GROUP_ASSET_FIELD_TYPE_LONG_HEADLINE = 17;
    ASSET_GROUP_ASSET_FIELD_TYPE_BUSINESS_NAME = 18;
    ASSET_GROUP_ASSET_FIELD_TYPE_SQUARE_MARKETING_IMAGE = 19;
    ASSET_GROUP_ASSET_FIELD_TYPE_PORTRAIT_MARKETING_IMAGE = 20;
    ASSET_GROUP_ASSET_FIELD_TYPE_LOGO = 21;
    ASSET_GROUP_ASSET_FIELD_TYPE_LANDSCAPE_LOGO = 22;
    ASSET_GROUP_ASSET_FIELD_TYPE_VIDEO = 23;
    ASSET_GROUP_ASSET_FIELD_TYPE_CALL_TO_ACTION_SELECTION = 25;
}

enum AssetPerformanceLabel {
    ASSET_PERFORMANCE_LABEL_UNSPECIFIED = 0;
    ASSET_PERFORMANCE_LABEL_UNKNOWN = 1;
    ASSET_PERFORMANCE_LABEL_PENDING = 2;
    ASSET_PERFORMANCE_LABEL_LEARNING = 3;
    ASSET_PERFORMANCE_LABEL_LOW = 4;
    ASSET_PERFORMANCE_LABEL_GOOD = 5;
    ASSET_PERFORMANCE_LABEL_BEST = 6;
}


message AssetGroupAsset {
    string asset = 1; // asset resource name
    string asset_group = 2; // asset group resource name
    int32 field_type = 3; // asset group asset field type, AssetGroupAssetFieldType
    int32 performance_label = 4; // performance label, AssetPerformanceLabel
    // PolicySummary policy_summary = 5; // PolicySummary
    string resource_name = 6; // resource name
    int32 status=  7; // asset group asset status, AssetLinkStatus
    Asset asset_instance = 8; // asset instance
}

enum AssetGroupStatus {
    ASSET_GROUP_STATUS_UNSPECIFIED = 0;
    ASSET_GROUP_STATUS_UNKNOWN = 1;
    ASSET_GROUP_STATUS_ENABLED = 2;
    ASSET_GROUP_STATUS_PAUSED = 3;
    ASSET_GROUP_STATUS_REMOVED = 4;
}

message AssetGroup {
    string campaign = 1; // campaign resource name
    repeated string final_mobile_urls = 2; // final mobile url list
    repeated string final_urls = 3; // final url list
    int64 id = 4; // ID
    string name = 5; // name
    string path1 = 6; // path1
    string path2 = 7; // path2
    string resource_name = 8; // resource name
    AssetGroupStatus status = 9; // AssetGroupStatus
}

// url[http://127.0.0.1:6027/api/asset_group/create_asset_group]
// comment[创建asset group]
message CreateAssetGroupReq {
    string customer_id  = 1;
    AssetGroup asset_group = 2;
    repeated AssetGroupAsset asset_group_assets = 3;
}

// comment[response]
message CreateAssetGroupRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2; // asset group resource name
}

// url[http://127.0.0.1:6027/api/asset_group/update_asset_group]
// comment[更新asset group]
message UpdateAssetGroupReq {
    string customer_id  = 1;
    AssetGroup asset_group = 2;
    repeated AssetGroupAsset create_asset_group_assets = 3;
    repeated string remove_asset_group_assets = 4; // remove asset group asset resource name
    repeated AssetGroupAsset update_asset_group_assets = 5;
    repeated string asset_group_field_masks = 6;  // comment[field_masks]
}

// comment[response]
message UpdateAssetGroupRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/asset_group/update_asset_group_name]
// comment[更新asset group]
message UpdateAssetGroupNameReq {
    string customer_id  = 1;
    AssetGroup asset_group = 2;
}

// comment[response]
message UpdateAssetGroupNameRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/asset_group/update_asset_group_status]
// comment[更新asset group status]
message UpdateAssetGroupStatusReq {
    string customer_id  = 1;
    AssetGroup asset_group = 2;
}

// comment[response]
message UpdateAssetGroupStatusRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/asset_group/get_asset_groups_by_campaign]
// comment[获取asset group]
message GetAssetGroupsByCampaignReq {
    string customer_id  = 1;
    string campaign = 2; // campaign resource name
}

// comment[response]
message GetAssetGroupsByCampaignRsp {
    Result   result = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroup asset_groups = 2;
}

// url[http://127.0.0.1:6027/api/asset_group/get_asset_group]
// comment[获取asset group]
message GetAssetGroupReq {
    string customer_id  = 1;
    string asset_group = 2; // asset group resource name
}

// comment[response]
message GetAssetGroupRsp {
    Result   result = 1;  // comment[返回结果]; default[xxx]
    AssetGroup asset_group = 2;
}

// url[http://127.0.0.1:6027/api/asset_group/batch_get_asset_group]
// comment[批量获取asset group]
message BatchGetAssetGroupReq {
    string customer_id  = 1;
    repeated string asset_groups = 2; // asset group resource name
}

// comment[response]
message BatchGetAssetGroupRsp {
    Result   result = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroup asset_groups = 2;
}

// url[http://127.0.0.1:6027/api/asset_group_asset/get_asset_group_assets_by_asset_group]
// comment[获取asset group assets]
message GetAssetGroupAssetsByAssetGroupReq {
    string customer_id  = 1;
    string asset_group = 2; // asset group resource name
}

// comment[response]
message GetAssetGroupAssetsByAssetGroupRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroupAsset asset_group_assets = 2;
    AssetGroup asset_group_instance = 3; // asset group信息
}

// url[http://127.0.0.1:6027/api/asset_group_asset/get_asset_group_assets_by_campaign]
// comment[获取asset group assets]
message GetAssetGroupAssetsByCampaignReq {
    string customer_id  = 1;
    string campaign = 2; // asset group resource name
}

message AssetGroupAssetWithAssetGroup {
    AssetGroup asset_group = 1;
    AssetGroupAsset asset_group_asset = 2;
}

// comment[response]
message GetAssetGroupAssetsByCampaignRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroupAssetWithAssetGroup asset_group_assets = 2;
}

// url[http://127.0.0.1:6027/api/asset/create_assets]
// comment[创建asset]
message CreateAssetsReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    repeated Asset  assets = 2;  // comment[asset list req]
}

// comment[response]
message CreateAssetsRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    repeated string resource_names = 2;  // comment[创建成功的asset resource name]
}


// url[http://127.0.0.1:6027/api/campaign_asset/get_campaign_sitelinks]
// comment[get campaign sitelink]
message GetCampaignSitelinksReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource anme]
}

// comment[response]
message GetCampaignSitelinksRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    repeated CampaignAsset campaign_assets = 2;  // comment[campaign asset list]
}

// url[http://127.0.0.1:6027/api/campaign_asset/modify_campaign_asset]
// comment[asset targeting to campaing, or remove from campaign ]
message ModifyCampaignAssetReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    string campaign    = 2;  // comment[campaign resource name]
    repeated CampaignAsset remove_campaign_assets = 3; // 从campaign中移除的asset
    repeated CampaignAsset add_campaign_assets    = 4; // 加入到campaign的asset
}

// comment[response]
message ModifyCampaignAssetRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/custom_audience/get_custom_audiences]
// comment[get custom_audience list]
message GetCustomAudiencesReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
}

// comment[response]
message GetCustomAudiencesRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    repeated CustomAudience custom_audiences = 2;
}

// url[http://127.0.0.1:6027/api/custom_audience/create_custom_audience]
// comment[create custom_audience]
message CreateCustomAudienceReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    CustomAudience custom_audience = 2;
}

// comment[response]
message CreateCustomAudienceRsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
    string resource_name = 2;
}

// url[http://127.0.0.1:6027/api/asset_group/remove_asset_group]
// comment[删除asset group]
message RemoveAssetGroupReq {
    string customer_id  = 1;
    string resource_name = 2;
}

// comment[response]
message RemoveAssetGroupRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/asset_group_signal/get_asset_group_signal_by_asset_group]
// comment[拉取asset_group下的asset_group_signal]
message GetAssetGroupSignalByAssetGroupReq {
    string customer_id = 1;
    string asset_group = 2;
}

// comment[response]
message GetAssetGroupSignalByAssetGroupRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroupSignal signals = 2;
}


// url[http://127.0.0.1:6027/api/asset_group_signal/get_asset_group_signal_by_campaign]
// comment[拉取campaign下的所有asset_group_signal]
message GetAssetGroupSignalByCampaignReq {
    string customer_id = 1;
    string campaign = 2;
}

// comment[response]
message GetAssetGroupSignalByCampaignRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroupSignal signals = 2;
}


// url[http://127.0.0.1:6027/api/asset_group_signal/modify_asset_group_signal]
// comment[修改asset_group下的audience signal]
message ModifyAssetGroupSignalReq {
    string customer_id = 1;
    string asset_group = 2;
    string remove_asset_group_signal = 3;  // 移除的asset_group_signal resource name
    string add_audience              = 4;  // 绑定的audience
}

// comment[response]
message ModifyAssetGroupSignalRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
}

// url[http://127.0.0.1:6027/api/asset_group/get_asset_group_campaign_summary]
// comment[查ad_group_ad的父级campaign简要信息]
message GetAssetGroupCampaignSummaryReq {
    string customer_id   = 1;  // comment[customer id]; default[**********]
    string resource_name = 2;  // comment[ad group ad resource name]
}

// comment[response]
message GetAssetGroupCampaignSummaryRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    Campaign campaign = 2;
}

// campaign层级属性数据
message CampaignAttribute {    
    int64                   campaign_id                  = 1;
    string                  resource_name                = 2;   // comment[resource name]
    string                  name                         = 3;   // comment[name]
    CampaignBudget          campaign_budget              = 4;   // comment[ref: CampaignBudget]
    int32                   advertising_channel_type     = 5;   // comment[ref: AdvertisingChannelType];
    int32                   advertising_channel_sub_type = 6;   // comment[ref: AdvertisingChannelSubType]
    int32                   status                       = 7;   // comment[ref: CampaignStatus]
    int32                   bidding_strategy_type        = 8;   // comment[ref: BiddingStrategyType]
    TargetCpa               target_cpa                   = 9;   // comment[target cpa];
    TargetRoas              target_roas                  = 10;  // comment[ref: TargetRoas];
    MaximizeConversionValue maximize_conversion_value    = 11;  // comment[ref: MaximizeConversionValue]
    TargetSpend             target_spend                 = 12;  // comment[ref: TargetSpend]
    MaximizeConversions     maximize_conversions         = 13;  // comment[ref: MaximizeConversions]
    TargetImpressionShare   target_impression_share      = 14;  // comment[ref: TargetImpressionShare]
    int32                   primary_status               = 15;  // comment[ref: CampaignPrimaryStatus]
    repeated       int32    primary_status_reasons       = 16;  // comment[ref: CampaignPrimaryStatusReason]
    string                  start_date                   = 17;   // comment[start date]
    string                  end_date                     = 18;   // comment[end date]
    
    string                  tracking_url_template                 = 19;  // comment[ref: tracking_url_template]
    string                  final_url_suffix                      = 20;  // comment[ref: final_url_suffix]
    repeated                CustomParameter url_custom_parameters = 21;  // comment[ref: CustomParameter]
}

// ad_group层级属性数据
message AdGroupAttribute {    
    int64  ad_group_id            = 1;
    string resource_name          = 2;  // comment[resource name]
    string name                   = 3;  // comment[name]
    int32  status                 = 4;  // comment[ref: AdGroupStatus]
    int64  campaign_id            = 5;  // comment[belongs to which campaign]
    string campaign_resource_name = 6;  // comment[belongs to which campaign]
    string campaign_name = 7;  // comment[belongs to which campaign]
    int32  campaign_primary_status = 8;  // comment[ref: CampaignPrimaryStatus]
}

enum PolicyReviewStatus {
    POLICYREVIEWSTATUS_UNSPECIFIED        = 0;
    POLICYREVIEWSTATUS_UNKNOWN            = 1;
    POLICYREVIEWSTATUS_REVIEW_IN_PROGRESS = 2;
    POLICYREVIEWSTATUS_REVIEWED           = 3;
    POLICYREVIEWSTATUS_UNDER_APPEAL       = 4;
    POLICYREVIEWSTATUS_ELIGIBLE_MAY_SERVE = 5;
}

enum PolicyApprovalStatus {
    POLICYAPPROVALSTATUS_UNSPECIFIED           = 0;
    POLICYAPPROVALSTATUS_UNKNOWN               = 1;
    POLICYAPPROVALSTATUS_DISAPPROVED           = 2;
    POLICYAPPROVALSTATUS_APPROVED_LIMITED      = 3;
    POLICYAPPROVALSTATUS_APPROVED              = 4;
    POLICYAPPROVALSTATUS_AREA_OF_INTEREST_ONLY = 5;
}

// https://developers.google.com/google-ads/api/reference/rpc/v13/PolicyTopicEntryTypeEnum.PolicyTopicEntryType
enum PolicyTopicEntryType {
    POLICYTOPICENTRYTYPE_UNSPECIFIED           = 0;
    POLICYTOPICENTRYTYPE_UNKNOWN               = 1;
    POLICYTOPICENTRYTYPE_PROHIBITED            = 2;
    POLICYTOPICENTRYTYPE_LIMITED               = 4;
    POLICYTOPICENTRYTYPE_FULLY_LIMITED         = 8;
    POLICYTOPICENTRYTYPE_DESCRIPTIVE           = 5;
    POLICYTOPICENTRYTYPE_BROADENING            = 6;
    POLICYTOPICENTRYTYPE_AREA_OF_INTEREST_ONLY = 7;
}

// https://developers.google.com/google-ads/api/reference/rpc/v13/PolicyTopicEntry
message PolicyTopicEntry {
    int32  type  = 1;  // comment[ref: PolicyTopicEntryType]
    string topic = 2;
}
        
// https://developers.google.com/google-ads/api/reference/rpc/v12/AdGroupAdPolicySummary?hl=en
message AdGroupAdPolicySummary {
    int32 review_status   = 1;  // comment[ref: PolicyReviewStatus]
    int32 approval_status = 2;  // comment[ref: PolicyApprovalStatus]
    repeated PolicyTopicEntry policy_topic_entrys = 3; // comment[ref: PolicyTopicEntry]
}

// https://developers.google.com/google-ads/api/reference/rpc/v13/AdGroupAdAssetPolicySummary?hl=en
message AdGroupAdAssetPolicySummary {
    int32 review_status   = 1;  // comment[ref: PolicyReviewStatus]
    int32 approval_status = 2;  // comment[ref: PolicyApprovalStatus]
    repeated PolicyTopicEntry policy_topic_entrys = 3; // comment[ref: PolicyTopicEntry]
}

// ad_group_ad层级属性数据
message AdGroupAdAttribute {    
    int64  ad_id                  = 1;   // ad_group_ad.ad.id
    string resource_name          = 2;   // the resource name of the ad. Ad group ad resource names have the form: customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}
    int32  status                 = 3;   // comment[ref: AdGroupAdStatus]
    int64  campaign_id            = 4;   // comment[belongs to which campaign]
    string campaign_resource_name = 5;   // comment[belongs to which campaign]
    string campaign_name          = 6;
    int32  campaign_status        = 7;   // comment[ref: CampaignStatus]
    int64  ad_group_id            = 8;   // comment[belongs to which ad_group]
    string ad_group_resource_name = 9;   // comment[belongs to which ad_group]
    string ad_group_name          = 10;
    int32  ad_group_status        = 11;  // comment[ref: AdGroupStatus]
    
    AdGroupAdPolicySummary policy_summary = 12;

    string   tracking_url_template                 = 13;  // comment[tracking_url_template]
    string   final_url_suffix                      = 14;  // comment[final_url_suffix]
    repeated CustomParameter url_custom_parameters = 15;  // comment[ref: CustomParameter]
    repeated string   final_urls                   = 16;
    repeated string   final_mobile_urls            = 17;
}

// asset_group层级属性数据
message AssetGroupAttribute {    
    int64  asset_group_id         = 1;  // asset_group.id
    string resource_name          = 2;  // The resource name of the asset group
    string name                   = 3;  // comment[name]
    int32  status                 = 4;  // comment[ref: AssetGroupStatus]
    int64  campaign_id            = 5;  // comment[belongs to which campaign]
    string campaign_resource_name = 6;  // comment[belongs to which campaign]
    string campaign_name          = 7;
    int32  campaign_primary_status = 8;  // comment[ref: CampaignPrimaryStatus]

    repeated string   final_urls        = 9;
    repeated string   final_mobile_urls = 10;
}

// keyword 层级属性数据
message KeywordAttribute {    
    int64  criterion_id            = 1;   // keyword 是ad_group_criterion
    string resource_name           = 2;   // The resource name of the asset group
    int32  status                  = 3;   // comment[ref: AdGroupCriterionStatus]
    int32  approval_status         = 4;   // comment[ref: AdGroupCriterionStatus]
    
    repeated string disapproval_reasons = 5;   // 
    
    bool   negative                = 6;   // Whether to target (false) or exclude (true) the criterion.
    string text                    = 7;   //  
    int32  match_type              = 8;   // comment[ref: KeywordMatchType]
    int64  campaign_id             = 9;   // comment[belongs to which campaign]
    string campaign_resource_name  = 10;  // comment[belongs to which campaign]
    string campaign_name           = 11;
    int32  campaign_status         = 12;  // comment[ref: CampaignStatus]
    int32  campaign_primary_status = 13;  // comment[ref: CampaignPrimaryStatus]
    int64  ad_group_id             = 14;  // comment[belongs to which ad_group]
    string ad_group_resource_name  = 15;  // comment[belongs to which ad_group]
    string ad_group_name           = 16;
    int32  ad_group_status         = 17;  // comment[ref: AdGroupStatus]
}


// asset 属性数据
message AssetAttribute {    
    string                      asset_view_resource_name = 1;   // customers/4528022864/adGroupAdAssetViews/153207135972~654839498129~78445336361~DESCRIPTION
    int64                       asset_id                 = 2;
    string                      asset_resource_name      = 3;   // customers/4528022864/assets/78445336361
    AdGroupAdAssetPolicySummary policy_summary           = 4;
    int32                       field_type               = 5;   // comment[ref AssetFieldType]
    bool                        enabled                  = 6;
    Asset                       asset                    = 7;
    int64                       campaign_id              = 8;   // comment[belongs to which campaign]
    string                      campaign_resource_name   = 9;   // comment[belongs to which campaign]
    string                      campaign_name            = 10;
    int32                       campaign_status          = 11;  // comment[ref: CampaignStatus]
    int32                       campaign_primary_status  = 12;  // comment[ref: CampaignPrimaryStatus]
    int64                       ad_group_id              = 13;  // comment[belongs to which ad_group]
    string                      ad_group_resource_name   = 14;  // comment[belongs to which ad_group]
    string                      ad_group_name            = 15;
    int32                       ad_group_status          = 16;  // comment[ref: AdGroupStatus]
    int64                       ad_id                    = 17;  // ad_group_ad.ad.id
    string                      ad_resource_name         = 18;  // the resource name of the ad. Ad group ad resource names have the form: customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}
    int32                       ad_status                = 19;
}

// url[http://127.0.0.1:6027/api/campaign/sync_campaign_attribute]
// comment[同步campaign的属性数据]
message SyncCampaignAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    int64  start_campaign_id = 2;  // comment[start with > start_campaign_id]
    int32  limit             = 3;
}

// comment[response]
message SyncCampaignAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated CampaignAttribute campaigns = 2;
}

// url[http://127.0.0.1:6027/api/campaign/get_campaign_attribute]
// comment[同步campaign的属性数据]
message GetCampaignAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string campaign          = 2;  // resource_name
}

// comment[response]
message GetCampaignAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    CampaignAttribute campaign = 2;
}

// url[http://127.0.0.1:6027/api/ad_group/sync_ad_group_attribute]
// comment[同步ad_group的属性数据]
message SyncAdGroupAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    int64  start_ad_group_id = 2;  // comment[start with > start_ad_group_id]
    int32  limit             = 3;
}

// comment[response]
message SyncAdGroupAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAttribute ad_groups = 2;
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_group_attribute]
// comment[同步ad_group的属性数据]
message GetAdGroupAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string ad_group          = 2;  // resource anme
}

// comment[response]
message GetAdGroupAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    AdGroupAttribute ad_group = 2;
}

// url[http://127.0.0.1:6027/api/ad_group/get_ad_group_attributes_by_campaign]
// comment[根据campaign拉取ad_group的属性数据]
message GetAdGroupAttributesByCampaignReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string campaign          = 2;  // resource anme
}

// comment[response]
message GetAdGroupAttributesByCampaignRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAttribute ad_groups = 2;
}

// url[http://127.0.0.1:6027/api/ad_group_ad/sync_ad_group_ad_attribute]
// comment[同步ad_group_ad的属性数据]
message SyncAdGroupAdAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    int64  start_ad_id = 2;  // comment[start with > start_ad_id]
    int32  limit             = 3;
}

// comment[response]
message SyncAdGroupAdAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAdAttribute ad_group_ads = 2;
}

// url[http://127.0.0.1:6027/api/ad_group_ad/sync_ad_group_ad_attribute_page_token]
// comment[同步ad_group_ad的属性数据, 用page_token的方式]
message SyncAdGroupAdAttributePageTokenReq {
    string customer_id = 1;  // comment[customer id]; default[**********]
    int64  start_ad_id = 2;  // comment[start with > start_ad_id]
    int32  page_size   = 3;  // 每页条数
    string page_token  = 4;
}

// comment[response]
message SyncAdGroupAdAttributePageTokenRsp {
    Result   result                          = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAdAttribute ad_group_ads = 2;
    string   next_page_token                 = 3;
    int32    total_count                     = 4;
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ad_attribute]
// comment[同步ad_group_ad的属性数据]
message GetAdGroupAdAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string ad_resource_name  = 2;  // resource_name
}

// comment[response]
message GetAdGroupAdAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    AdGroupAdAttribute ad = 2;
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ad_attributes_by_campaign]
// comment[同步campaign下的ad_group_ad的属性数据]
message GetAdGroupAdAttributesByCampaignReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string campaign  = 2;  // resource_name
}

// comment[response]
message GetAdGroupAdAttributesByCampaignRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAdAttribute ads = 2;
}

// url[http://127.0.0.1:6027/api/ad_group_ad/get_ad_group_ad_attributes_by_adgroup]
// comment[同步adgroup下的ad_group_ad的属性数据]
message GetAdGroupAdAttributesByAdgroupReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string ad_group  = 2;  // resource_name
}

// comment[response]
message GetAdGroupAdAttributesByAdgroupRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AdGroupAdAttribute ads = 2;
}

// url[http://127.0.0.1:6027/api/asset_group/sync_asset_group_attribute]
// comment[同步asset_group的属性数据]
message SyncAssetGroupAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    int64  start_asset_group_id = 2;  // comment[start with > start_asset_group_id]
    int32  limit             = 3;
}

// comment[response]
message SyncAssetGroupAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroupAttribute asset_groups = 2;
}

// url[http://127.0.0.1:6027/api/asset_group/get_asset_group_attribute]
// comment[同步asset_group的属性数据]
message GetAssetGroupAttributeReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string asset_group       = 2;  // resource_name
}

// comment[response]
message GetAssetGroupAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    AssetGroupAttribute asset_group = 2;
}


// url[http://127.0.0.1:6027/api/asset_group/get_asset_group_attributes_by_campaign]
// comment[拉取campaign下的asset_group的属性数据]
message GetAssetGroupAttributesByCampaignReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    string campaign       = 2;  // resource_name
}

// comment[response]
message GetAssetGroupAttributesByCampaignRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AssetGroupAttribute asset_groups = 2;
}

// url[http://127.0.0.1:6027/api/ad_group/sync_keyword_attribute]
// comment[同步keyword的属性数据]
message SyncKeywordAttributeReq {
    string customer_id        = 1;  // comment[customer id]; default[**********]
    repeated string campaigns = 2;  // comment[campaign list]
}

// comment[response]
message SyncKeywordAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated KeywordAttribute keywords = 2;
}

// url[http://127.0.0.1:6027/api/asset/sync_asset_attribute]
// comment[同步keyword的属性数据]
message SyncAssetAttributeReq {
    string customer_id        = 1;  // comment[customer id]; default[**********]
    repeated string adgroups  = 2;  // comment[campaign list]
}

// comment[response]
message SyncAssetAttributeRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated AssetAttribute assets = 2;
}

// url[http://127.0.0.1:6027/api/campaign/get_campaigns_location]
// comment[拉取一批campaign的location]
message GetCampaignsLocationReq {
    string customer_id       = 1;  // comment[customer id]; default[**********]
    repeated string campaign_resource_names = 2;
}

// comment[response]
message GetCampaignsLocationRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated CampaignCriterion critertions = 2;
}

// url[http://127.0.0.1:6027/api/operating_system/get_operating_system_version_constants_page]
// comment[拉取operating_system_version 分页]
message GetOperatingSystemVersionConstantsPageReq {
    string customer_id                                = 1;  // comment[customer id]; default[**********]
    int64  start_operating_system_version_constant_id = 2;  // comment[start with > start_operating_system_version_constants_id]
    int32  limit                                      = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/OperatingSystemVersionOperatorTypeEnum.OperatingSystemVersionOperatorType?hl=en
enum OperatingSystemVersionOperatorType {
    OPERATINGSYSTEMVERSIONOPERATORTYPE_UNSPECIFIED            = 0;
    OPERATINGSYSTEMVERSIONOPERATORTYPE_UNKNOWN                = 1;
    OPERATINGSYSTEMVERSIONOPERATORTYPE_EQUALS_TO              = 2;
    OPERATINGSYSTEMVERSIONOPERATORTYPE_GREATER_THAN_EQUALS_TO = 4;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/OperatingSystemVersionConstant?hl=en
message OperatingSystemVersionConstant {
    int64  id               = 1;
    string resource_name    = 2;  // Operating system version constant resource names have the form: operatingSystemVersionConstants/{criterion_id}
    string name             = 3;  // Name of the operating system.
    int32  os_major_version = 4;
    int32  os_minor_version = 5;
    int32  operator_type    = 6;  // 参看枚举 OperatingSystemVersionOperatorType Determines whether this constant represents a single version or a range of versions
}

// comment[response]
message GetOperatingSystemVersionConstantsPageRsp {
    Result   result   = 1;  // comment[返回结果]; default[xxx]
    repeated OperatingSystemVersionConstant lists = 2;
}

// url[http://127.0.0.1:6027/api/keyword_plan/generate_keyword_ideas]
message GenerateKeywordIdeasReq {
    string   customer_id     = 1;  // comment[customer id]; default[**********]
    repeated string keywords = 2;  // A Keyword or phrase to generate ideas from, for example, cars
    string   page_token      = 3;  // Token of the page to retrieve. If not specified, the first page of results will be returned, next page of results use the value obtained from next_page_token in the previous response
    int32    page_size       = 4;  // Number of results to retrieve in a single page. A maximum of 10,000 results may be returned,
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/MonthOfYearEnum.MonthOfYear?hl=en
enum MonthOfYear {
    MONTHOFYEAR_UNSPECIFIED = 0;
    MONTHOFYEAR_UNKNOWN     = 1;
    MONTHOFYEAR_JANUARY     = 2;
    MONTHOFYEAR_FEBRUARY    = 3;
    MONTHOFYEAR_MARCH       = 4;
    MONTHOFYEAR_APRIL       = 5;
    MONTHOFYEAR_MAY         = 6;
    MONTHOFYEAR_JUNE        = 7;
    MONTHOFYEAR_JULY        = 8;
    MONTHOFYEAR_AUGUST      = 9;
    MONTHOFYEAR_SEPTEMBER   = 10;
    MONTHOFYEAR_OCTOBER     = 11;
    MONTHOFYEAR_NOVEMBER    = 12;
    MONTHOFYEAR_DECEMBER    = 13;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/MonthlySearchVolume?hl=en
message MonthlySearchVolume {
    int32 month            = 1; // 参看枚举 MonthOfYear
    int64 year             = 2;
    int64 monthly_searches = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/KeywordPlanCompetitionLevelEnum.KeywordPlanCompetitionLevel?hl=en
enum KeywordPlanCompetitionLevel {
    KEYWORDPLANCOMPETITIONLEVEL_UNSPECIFIED = 0;
    KEYWORDPLANCOMPETITIONLEVEL_UNKNOWN     = 1;
    KEYWORDPLANCOMPETITIONLEVEL_LOW         = 2;
    KEYWORDPLANCOMPETITIONLEVEL_MEDIUM      = 3;
    KEYWORDPLANCOMPETITIONLEVEL_HIGH        = 4;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/KeywordPlanHistoricalMetrics?hl=en
message KeywordPlanHistoricalMetrics {
    repeated MonthlySearchVolume monthly_search_volumes = 1;  // Approximate number of searches on this query for the past twelve months.
    int32    competition                                = 2;  // 参看枚举 KeywordPlanCompetitionLevel The competition level for the query.
    int64    avg_monthly_searches                       = 3;  // Approximate number of monthly searches on this query, averaged for the past 12 months.
    int64    competition_index                          = 4;  // The competition index for the query in the range [0, 100]
    int64    low_top_of_page_bid_micros                 = 5;  // Top of page bid low range (20th percentile) in micros for the keyword.
    int64    high_top_of_page_bid_micros                = 6;  // Top of page bid high range (80th percentile) in micros for the keyword.
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/GenerateKeywordIdeaResult?hl=en
message GenerateKeywordIdeaResult {
    KeywordPlanHistoricalMetrics keyword_idea_metrics = 1; // The historical metrics for the keyword.
    string text = 2; // Text of the keyword idea.
}

// comment[response]
message GenerateKeywordIdeasRsp {
    Result   result                            = 1;  // comment[返回结果]; default[xxx]
    repeated GenerateKeywordIdeaResult results = 2;
    string   next_page_token                   = 3;
    int64    total_size                        = 4;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/ChangeEventResourceTypeEnum.ChangeEventResourceType
enum ChangeEventResourceType {
    CHANGEEVENTRESOURCETYPE_UNSPECIFIED           = 0;
    CHANGEEVENTRESOURCETYPE_UNKNOWN               = 1;
    CHANGEEVENTRESOURCETYPE_AD                    = 2;
    CHANGEEVENTRESOURCETYPE_AD_GROUP              = 3;
    CHANGEEVENTRESOURCETYPE_AD_GROUP_CRITERION    = 4;
    CHANGEEVENTRESOURCETYPE_CAMPAIGN              = 5;
    CHANGEEVENTRESOURCETYPE_CAMPAIGN_BUDGET       = 6;
    CHANGEEVENTRESOURCETYPE_AD_GROUP_BID_MODIFIER = 7;
    CHANGEEVENTRESOURCETYPE_CAMPAIGN_CRITERION    = 8;
    CHANGEEVENTRESOURCETYPE_FEED                  = 9;
    CHANGEEVENTRESOURCETYPE_FEED_ITEM             = 10;
    CHANGEEVENTRESOURCETYPE_CAMPAIGN_FEED         = 11;
    CHANGEEVENTRESOURCETYPE_AD_GROUP_FEED         = 12;
    CHANGEEVENTRESOURCETYPE_AD_GROUP_AD           = 13;
    CHANGEEVENTRESOURCETYPE_ASSET                 = 14;
    CHANGEEVENTRESOURCETYPE_CUSTOMER_ASSET        = 15;
    CHANGEEVENTRESOURCETYPE_CAMPAIGN_ASSET        = 16;
    CHANGEEVENTRESOURCETYPE_AD_GROUP_ASSET        = 17;
    CHANGEEVENTRESOURCETYPE_ASSET_SET             = 18;
    CHANGEEVENTRESOURCETYPE_ASSET_SET_ASSET       = 19;
    CHANGEEVENTRESOURCETYPE_CAMPAIGN_ASSET_SET    = 20;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/ChangeClientTypeEnum.ChangeClientType
enum ChangeClientType {
    CHANGECLIENTTYPE_UNSPECIFIED                = 0;
    CHANGECLIENTTYPE_UNKNOWN                    = 1;
    CHANGECLIENTTYPE_GOOGLE_ADS_WEB_CLIENT      = 2;
    CHANGECLIENTTYPE_GOOGLE_ADS_AUTOMATED_RULE  = 3;
    CHANGECLIENTTYPE_GOOGLE_ADS_SCRIPTS         = 4;
    CHANGECLIENTTYPE_GOOGLE_ADS_BULK_UPLOAD     = 5;
    CHANGECLIENTTYPE_GOOGLE_ADS_API             = 6;
    CHANGECLIENTTYPE_GOOGLE_ADS_EDITOR          = 7;
    CHANGECLIENTTYPE_GOOGLE_ADS_MOBILE_APP      = 8;
    CHANGECLIENTTYPE_GOOGLE_ADS_RECOMMENDATIONS = 9;
    CHANGECLIENTTYPE_SEARCH_ADS_360_SYNC        = 10;
    CHANGECLIENTTYPE_SEARCH_ADS_360_POST        = 11;
    CHANGECLIENTTYPE_INTERNAL_TOOL              = 12;
    CHANGECLIENTTYPE_OTHER                      = 13;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/ChangeEvent.ChangedResource
message ChangedResource {
    Campaign          campaign           = 1;  // Set if change_resource_type == CAMPAIGN.
    CampaignBudget    campaign_budget    = 2;  // Set if change_resource_type == CAMPAIGN_BUDGET.
    CampaignCriterion campaign_criterion = 3;  // Set if change_resource_type == CAMPAIGN_CRITERION.
    AdGroup           ad_group           = 4;  // Set if change_resource_type == AD_GROUP.
    AdGroupAd         ad_group_ad        = 5;  // Set if change_resource_type == AD_GROUP_AD.
    Ad                ad                 = 6;  // Set if change_resource_type == AD.
    AdGroupCriterion  ad_group_criterion = 7;  // Set if change_resource_type == AD_GROUP_CRITERION
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/ResourceChangeOperationEnum.ResourceChangeOperation
enum ResourceChangeOperation {
    ResourceChangeOperation_UNSPECIFIED = 0;
    ResourceChangeOperation_UNKNOWN     = 1;
    ResourceChangeOperation_CREATE      = 2;
    ResourceChangeOperation_UPDATE      = 3;
    ResourceChangeOperation_REMOVE      = 4;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/ChangeEvent
message ChangeEvent {
    string          resource_name             = 1;   // Output only. The resource name of the change event. Change event resource names have the form: customers/{customer_id}/changeEvents/{timestamp_micros}~{command_index}~{mutate_index}
    string          command_resource_name     = 2;   // have the form: customers/{customer_id}/changeEvents/{timestamp_micros}~{command_index}
    string          change_date_time          = 3;
    int32           change_resource_type      = 4;   // ChangeEventResourceType The type of the changed resource. This dictates what resource will be set in old_resource and new_resource.
    string          change_resource_name      = 5;   // The Simply resource this change occurred on.
    int32           client_type               = 6;   // ChangeClientType Where the change was made through.
    string          user_email                = 7;   // The email of the user who made this change. 
    ChangedResource old_resource              = 8;   // The old resource before the change. Only changed fields will be populated.
    ChangedResource new_resource              = 9;   // The new resource after the change. Only changed fields will be populated.
    int32           resource_change_operation = 10;   // ResourceChangeOperation The operation on the changed resource.
    repeated        string changed_fields     = 11;  // A list of fields that are changed in the returned resource.
    string          campaign                  = 12;  // The Campaign affected by this change.
    string          ad_group                  = 13;  // The AdGroup affected by this change.
}

// url[http://127.0.0.1:8080/api/change_event/sync_change_event_page]
message SyncChangeEventPageReq {
    string customer_id = 1;  // comment[customer id]
    string start_time  = 2;  // 开始时间 >= start_time 可以是日期YYYY-MM-DD，或者时间 YYYY-MM-DD HH:MM:SS
    string end_time    = 3;  // 结束时间 <= end_time 
    int32  max_limit   = 4;  // 返回期望总条数，比page_size 大
    // deprecated v18
    //int32  page_size   = 5;  // 分页条数, 从max_limit中每页返回的条数
    // deprecated v18
    string page_token  = 6;  // 分页token，第一页为空，后续为rsp的next_page_token
}

message SyncChangeEventPageRsp {
    Result   result                    = 1;  // comment[返回结果]; default[xxx]
    repeated ChangeEvent change_events = 2;
    string   next_page_token           = 3; // 不为空，为下一页token
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/LanguageConstant
message LanguageConstant {
    string resource_name = 1;  // The resource name of the language constant. Language constant resource names have the form: languageConstants/{criterion_id}
    int64  id            = 2;  // The ID of the language constant.
    string code          = 3;  // The language code, for example, "en_US", "en_AU", "es", "fr", etc.
    string name          = 4;  // The full name of the language in English, for example, "English (US)", "Spanish", etc.
    bool   targetable    = 5;  //  Whether the language is targetable.
}

// url[http://127.0.0.1:8080/api/language_constant/sync_language_constant_page]
message SyncLanguageConstantPageReq {
    string customer_id = 1;  // comment[customer id]
    int64  start_id    = 2;  // > start_id
    int32  limit       = 3;
}

message SyncLanguageConstantPageRsp {
    Result   result                     = 1;  // comment[返回结果]; default[xxx]
    repeated LanguageConstant languages = 2;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/GeoTargetConstantStatusEnum.GeoTargetConstantStatus
enum GeoTargetConstantStatus {
    GEOTARGETCONSTANTSTATUS_UNSPECIFIED     = 0;
    GEOTARGETCONSTANTSTATUS_UNKNOWN         = 1;
    GEOTARGETCONSTANTSTATUS_ENABLED         = 2;
    GEOTARGETCONSTANTSTATUS_REMOVAL_PLANNED = 3;
}

// https://developers.google.com/google-ads/api/reference/rpc/v12/GeoTargetConstant
message GeoTargetConstant {
    string resource_name     = 1;  // Geo target constant resource names have the form: geoTargetConstants/{geo_target_constant_id}
    int32  status            = 2;  // GeoTargetConstantStatus, Geo target constant status
    int64  id                = 3;  // The ID of the geo target constant.
    string name              = 4;  // Geo target constant English name.
    string country_code      = 5;  // The ISO-3166-1 alpha-2 country code that is associated with the target.
    string target_type       = 6;  // Geo target constant target type.
    string canonical_name    = 7;  // The fully qualified English name, consisting of the target's name and that of its parent and country.
    string parent_geo_target = 8;  // The resource name of the parent geo target constant. Geo target constant resource names have the form: eoTargetConstants/{parent_geo_target_constant_id}
}

// url[http://127.0.0.1:8080/api/geo_target_constant/sync_geo_target_constant_page]
message SyncGeoTargetConstantPageReq {
    string customer_id = 1;  // comment[customer id]
    int64  start_id    = 2;  // > start_id
    int32  limit       = 3;
}

message SyncGeoTargetConstantPageRsp {
    Result   result                        = 1;  // comment[返回结果]; default[xxx]
    repeated GeoTargetConstant geo_targets = 2;
}

// v2版本的campaign criterion的创建和更新，
// 处理真正意思上的negative campaign criterion, 其实negative表示的是排除
// url[http://127.0.0.1:6027/api/campaign/modify_campaign_criterions_v2]
// comment[modify campaign criterion]
message ModifyCampaignCriterionsV2Req {
    string   customer_id                         = 1;  // comment[customer id]; default[**********]
    string   campaign_resource_name              = 2;
    repeated CampaignCriterion remove_criterions = 3; // 需要删除的那些criterion, 需要resource_name
    repeated CampaignCriterion add_criterions    = 4; // 需要include、exclude那些criterion, 用negative=true表示排除
}

// comment[response]
message ModifyCampaignCriterionsV2Rsp {
    Result result        = 1;  // comment[返回结果]; default[xxx]
}