package service

import (
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
)

// GetDepotInfo ...
func GetDepotInfo(ctx *gin.Context, req *material_display.GetDepotTokenReq) (*material_display.GetDepotTokenRsp, error) {
	gameCodes := req.GetGameCodeList()
	if len(gameCodes) == 0 {
		return &material_display.GetDepotTokenRsp{}, nil
	}
	models := []*model.ArthubDepot{}
	err := postgresql.GetDBWithContext(ctx).Model(&models).
		WhereIn("game_code IN (?)", gameCodes).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "fail to get depot info, game_code items: %v, err: %v", gameCodes, err)
		return nil, err
	}

	items := []*material_display.DepotInfo{}
	if len(models) > 0 {
		for idx := range models {
			if models[idx] != nil {
				item := singleDepotInfoToProto(models[idx])
				items = append(items, item)
			}
		}
	}
	resp := material_display.GetDepotTokenRsp{}
	resp.Items = items
	return &resp, nil
}

// ListDepotInfo ...
func ListDepotInfo(ctx *gin.Context, req *material_display.ListDepotReq) (*material_display.ListDepotRsp, error) {
	models := []*model.ArthubDepot{}
	offset := req.GetOffset()
	limit := req.GetLimit()
	if limit <= 0 {
		limit = 10
	}

	resp := material_display.ListDepotRsp{}
	total, err := postgresql.GetDBWithContext(ctx).Model((*model.ArthubDepot)(nil)).Count()
	if err != nil {
		log.ErrorContextf(ctx, "fail to count depot info, err: %v", err)
		return &resp, err
	}
	if limit > uint64(total) {
		limit = uint64(total)
	}
	if offset >= uint64(total) {
		resp.Total = uint64(total)
		resp.Items = make([]*material_display.ListDepotInfo, 0)
	}

	err = postgresql.GetDBWithContext(ctx).Model((*model.ArthubDepot)(nil)).
		Offset(int(offset)).
		Limit(int(limit)).
		Order("depot_id").
		Select(&models)
	if err != nil {
		log.ErrorContextf(ctx, "fail to get depot info, err: %v", err)
		return &resp, err
	}

	items := []*material_display.ListDepotInfo{}
	if len(models) > 0 {
		for idx := range models {
			if models[idx] != nil {
				item := material_display.ListDepotInfo{
					DepotId:   models[idx].DepotId,
					DepotName: models[idx].DepotName,
					GameCode:  models[idx].GameCode,
					GameName:  models[idx].GameName,
				}
				items = append(items, &item)
			}
		}
	}
	resp.Items = items
	resp.Total = uint64(total)
	return &resp, nil
}
