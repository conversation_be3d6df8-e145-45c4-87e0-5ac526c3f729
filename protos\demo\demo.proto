syntax = "proto3";

package arthub_data_sync;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/demo";

import "aix/aix_common_message.proto";

// 内部测试接口, POST, /api/v1/arthub_data_sync/say_hi
message SayHiReq {
    string arthub_code  = 1;  // arthub code
    string token        = 2;  // token
    repeated uint64 ids = 3;  // 素材列表
}

message SayHiRsp {
    aix.Result result = 1;  // 返回结果
}