// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: asset_media_manager/asset_media_manager.proto

package asset_media_manager

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 内部接口 触发定时任务, POST, /api/v1/asset_media_manager/cron_trigger
type CronTriggerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CronName string `protobuf:"bytes,1,opt,name=cron_name,json=cronName,proto3" json:"cron_name,omitempty"` // 定时任务名字
}

func (x *CronTriggerReq) Reset() {
	*x = CronTriggerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronTriggerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronTriggerReq) ProtoMessage() {}

func (x *CronTriggerReq) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronTriggerReq.ProtoReflect.Descriptor instead.
func (*CronTriggerReq) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{0}
}

func (x *CronTriggerReq) GetCronName() string {
	if x != nil {
		return x.CronName
	}
	return ""
}

type CronTriggerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *CronTriggerRsp) Reset() {
	*x = CronTriggerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronTriggerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronTriggerRsp) ProtoMessage() {}

func (x *CronTriggerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronTriggerRsp.ProtoReflect.Descriptor instead.
func (*CronTriggerRsp) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{1}
}

func (x *CronTriggerRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// ResourceName 结构
type ResourceName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceName      string `protobuf:"bytes,1,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`
	CoverResourceName string `protobuf:"bytes,2,opt,name=cover_resource_name,json=coverResourceName,proto3" json:"cover_resource_name,omitempty"`
	ImageMaterialId   string `protobuf:"bytes,3,opt,name=image_material_id,json=imageMaterialId,proto3" json:"image_material_id,omitempty"` // 在tiktok中即UI页面上的Image ID, 如7159153087693127682
	VideoMaterialId   string `protobuf:"bytes,4,opt,name=video_material_id,json=videoMaterialId,proto3" json:"video_material_id,omitempty"` // 在tiktok中即UI页面上的Video Material ID, 如7159153118533828609
}

func (x *ResourceName) Reset() {
	*x = ResourceName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceName) ProtoMessage() {}

func (x *ResourceName) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceName.ProtoReflect.Descriptor instead.
func (*ResourceName) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{2}
}

func (x *ResourceName) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *ResourceName) GetCoverResourceName() string {
	if x != nil {
		return x.CoverResourceName
	}
	return ""
}

func (x *ResourceName) GetImageMaterialId() string {
	if x != nil {
		return x.ImageMaterialId
	}
	return ""
}

func (x *ResourceName) GetVideoMaterialId() string {
	if x != nil {
		return x.VideoMaterialId
	}
	return ""
}

// 已上传到渠道的素材-更新渠道素材名字, POST, /api/v1/asset_media_manager/update_uploaded_media_asset_name
type UpdateUploadedMediaAssetNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识game_code
	AssetId   string `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`       // 必填 asset id
	AssetName string `protobuf:"bytes,3,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"` // 必填 asset name
}

func (x *UpdateUploadedMediaAssetNameReq) Reset() {
	*x = UpdateUploadedMediaAssetNameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUploadedMediaAssetNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUploadedMediaAssetNameReq) ProtoMessage() {}

func (x *UpdateUploadedMediaAssetNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUploadedMediaAssetNameReq.ProtoReflect.Descriptor instead.
func (*UpdateUploadedMediaAssetNameReq) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUploadedMediaAssetNameReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *UpdateUploadedMediaAssetNameReq) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *UpdateUploadedMediaAssetNameReq) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

type UpdateUploadedMediaAssetNameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *UpdateUploadedMediaAssetNameRsp) Reset() {
	*x = UpdateUploadedMediaAssetNameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUploadedMediaAssetNameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUploadedMediaAssetNameRsp) ProtoMessage() {}

func (x *UpdateUploadedMediaAssetNameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUploadedMediaAssetNameRsp.ProtoReflect.Descriptor instead.
func (*UpdateUploadedMediaAssetNameRsp) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateUploadedMediaAssetNameRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// slack交互命令获取某个游戏上传统计报表, POST, /api/v1/asset_media_manager/slack_cmd_upload_statistics
type SlackCmdUploadStatisticsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识game_code
	GameName  string `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`    // 必填 游戏名称
	Studio    string `protobuf:"bytes,3,opt,name=studio,proto3" json:"studio,omitempty"`                        // 必填 游戏工作室
	ChannelId string `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"` // 必填 slack channel id
	ThreadId  string `protobuf:"bytes,5,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`    // 必填 slack thread id
}

func (x *SlackCmdUploadStatisticsReq) Reset() {
	*x = SlackCmdUploadStatisticsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlackCmdUploadStatisticsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlackCmdUploadStatisticsReq) ProtoMessage() {}

func (x *SlackCmdUploadStatisticsReq) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlackCmdUploadStatisticsReq.ProtoReflect.Descriptor instead.
func (*SlackCmdUploadStatisticsReq) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{5}
}

func (x *SlackCmdUploadStatisticsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *SlackCmdUploadStatisticsReq) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

func (x *SlackCmdUploadStatisticsReq) GetStudio() string {
	if x != nil {
		return x.Studio
	}
	return ""
}

func (x *SlackCmdUploadStatisticsReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *SlackCmdUploadStatisticsReq) GetThreadId() string {
	if x != nil {
		return x.ThreadId
	}
	return ""
}

type SlackCmdUploadStatisticsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SlackCmdUploadStatisticsRsp) Reset() {
	*x = SlackCmdUploadStatisticsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlackCmdUploadStatisticsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlackCmdUploadStatisticsRsp) ProtoMessage() {}

func (x *SlackCmdUploadStatisticsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlackCmdUploadStatisticsRsp.ProtoReflect.Descriptor instead.
func (*SlackCmdUploadStatisticsRsp) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{6}
}

func (x *SlackCmdUploadStatisticsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type Game struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 必填 游戏标识game_code
	GameName string `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"` // 必填 游戏名称
}

func (x *Game) Reset() {
	*x = Game{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Game) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Game) ProtoMessage() {}

func (x *Game) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Game.ProtoReflect.Descriptor instead.
func (*Game) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{7}
}

func (x *Game) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *Game) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

// slack交互命令统计至今为止studio下游戏素材上传渠道成功记录数, POST, /api/v1/asset_media_manager/slack_cmd_get_summary
type SlackCmdGetSummaryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Games      []*Game `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`                             // 必填 游戏列表
	Studio     string  `protobuf:"bytes,2,opt,name=studio,proto3" json:"studio,omitempty"`                           // 必填 游戏工作室
	StudioName string  `protobuf:"bytes,3,opt,name=studio_name,json=studioName,proto3" json:"studio_name,omitempty"` // 必填 游戏工作室名称
	ChannelId  string  `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`    // 必填 slack channel id
	ThreadId   string  `protobuf:"bytes,5,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`       // 必填 slack thread id
}

func (x *SlackCmdGetSummaryReq) Reset() {
	*x = SlackCmdGetSummaryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlackCmdGetSummaryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlackCmdGetSummaryReq) ProtoMessage() {}

func (x *SlackCmdGetSummaryReq) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlackCmdGetSummaryReq.ProtoReflect.Descriptor instead.
func (*SlackCmdGetSummaryReq) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{8}
}

func (x *SlackCmdGetSummaryReq) GetGames() []*Game {
	if x != nil {
		return x.Games
	}
	return nil
}

func (x *SlackCmdGetSummaryReq) GetStudio() string {
	if x != nil {
		return x.Studio
	}
	return ""
}

func (x *SlackCmdGetSummaryReq) GetStudioName() string {
	if x != nil {
		return x.StudioName
	}
	return ""
}

func (x *SlackCmdGetSummaryReq) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *SlackCmdGetSummaryReq) GetThreadId() string {
	if x != nil {
		return x.ThreadId
	}
	return ""
}

type SlackCmdGetSummaryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SlackCmdGetSummaryRsp) Reset() {
	*x = SlackCmdGetSummaryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlackCmdGetSummaryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlackCmdGetSummaryRsp) ProtoMessage() {}

func (x *SlackCmdGetSummaryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_asset_media_manager_asset_media_manager_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlackCmdGetSummaryRsp.ProtoReflect.Descriptor instead.
func (*SlackCmdGetSummaryRsp) Descriptor() ([]byte, []int) {
	return file_asset_media_manager_asset_media_manager_proto_rawDescGZIP(), []int{9}
}

func (x *SlackCmdGetSummaryRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_asset_media_manager_asset_media_manager_proto protoreflect.FileDescriptor

var file_asset_media_manager_asset_media_manager_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x2d, 0x0a, 0x0e, 0x43, 0x72, 0x6f, 0x6e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x35, 0x0a, 0x0e, 0x43, 0x72, 0x6f, 0x6e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xbb, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a,
	0x0a, 0x11, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x69,
	0x64, 0x65, 0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x22, 0x78, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x46, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x65, 0x64, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x1b, 0x53, 0x6c, 0x61,
	0x63, 0x6b, 0x43, 0x6d, 0x64, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x68, 0x72,
	0x65, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x68,
	0x72, 0x65, 0x61, 0x64, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x1b, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x43,
	0x6d, 0x64, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x40, 0x0a, 0x04, 0x47, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbd, 0x01, 0x0a,
	0x15, 0x53, 0x6c, 0x61, 0x63, 0x6b, 0x43, 0x6d, 0x64, 0x47, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x05, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x61, 0x6d, 0x65,
	0x52, 0x05, 0x67, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x75, 0x64, 0x69,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x69, 0x6f, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x15,
	0x53, 0x6c, 0x61, 0x63, 0x6b, 0x43, 0x6d, 0x64, 0x47, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x42, 0x5a, 0x40, 0x65, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_asset_media_manager_asset_media_manager_proto_rawDescOnce sync.Once
	file_asset_media_manager_asset_media_manager_proto_rawDescData = file_asset_media_manager_asset_media_manager_proto_rawDesc
)

func file_asset_media_manager_asset_media_manager_proto_rawDescGZIP() []byte {
	file_asset_media_manager_asset_media_manager_proto_rawDescOnce.Do(func() {
		file_asset_media_manager_asset_media_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_asset_media_manager_asset_media_manager_proto_rawDescData)
	})
	return file_asset_media_manager_asset_media_manager_proto_rawDescData
}

var file_asset_media_manager_asset_media_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_asset_media_manager_asset_media_manager_proto_goTypes = []interface{}{
	(*CronTriggerReq)(nil),                  // 0: asset_media_manager.CronTriggerReq
	(*CronTriggerRsp)(nil),                  // 1: asset_media_manager.CronTriggerRsp
	(*ResourceName)(nil),                    // 2: asset_media_manager.ResourceName
	(*UpdateUploadedMediaAssetNameReq)(nil), // 3: asset_media_manager.UpdateUploadedMediaAssetNameReq
	(*UpdateUploadedMediaAssetNameRsp)(nil), // 4: asset_media_manager.UpdateUploadedMediaAssetNameRsp
	(*SlackCmdUploadStatisticsReq)(nil),     // 5: asset_media_manager.SlackCmdUploadStatisticsReq
	(*SlackCmdUploadStatisticsRsp)(nil),     // 6: asset_media_manager.SlackCmdUploadStatisticsRsp
	(*Game)(nil),                            // 7: asset_media_manager.Game
	(*SlackCmdGetSummaryReq)(nil),           // 8: asset_media_manager.SlackCmdGetSummaryReq
	(*SlackCmdGetSummaryRsp)(nil),           // 9: asset_media_manager.SlackCmdGetSummaryRsp
	(*aix.Result)(nil),                      // 10: aix.Result
}
var file_asset_media_manager_asset_media_manager_proto_depIdxs = []int32{
	10, // 0: asset_media_manager.CronTriggerRsp.result:type_name -> aix.Result
	10, // 1: asset_media_manager.UpdateUploadedMediaAssetNameRsp.result:type_name -> aix.Result
	10, // 2: asset_media_manager.SlackCmdUploadStatisticsRsp.result:type_name -> aix.Result
	7,  // 3: asset_media_manager.SlackCmdGetSummaryReq.games:type_name -> asset_media_manager.Game
	10, // 4: asset_media_manager.SlackCmdGetSummaryRsp.result:type_name -> aix.Result
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_asset_media_manager_asset_media_manager_proto_init() }
func file_asset_media_manager_asset_media_manager_proto_init() {
	if File_asset_media_manager_asset_media_manager_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_asset_media_manager_asset_media_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CronTriggerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CronTriggerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUploadedMediaAssetNameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUploadedMediaAssetNameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlackCmdUploadStatisticsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlackCmdUploadStatisticsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Game); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlackCmdGetSummaryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_asset_media_manager_asset_media_manager_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlackCmdGetSummaryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_asset_media_manager_asset_media_manager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_asset_media_manager_asset_media_manager_proto_goTypes,
		DependencyIndexes: file_asset_media_manager_asset_media_manager_proto_depIdxs,
		MessageInfos:      file_asset_media_manager_asset_media_manager_proto_msgTypes,
	}.Build()
	File_asset_media_manager_asset_media_manager_proto = out.File
	file_asset_media_manager_asset_media_manager_proto_rawDesc = nil
	file_asset_media_manager_asset_media_manager_proto_goTypes = nil
	file_asset_media_manager_asset_media_manager_proto_depIdxs = nil
}
