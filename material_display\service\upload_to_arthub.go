package service

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/spf13/cast"
)

// 针对用户上传的素材，每十分钟同步一次素材详情信息
// 该素材信息包括overview 和detail信息
const MAX_QUERY_DETAIL_COUNT = 288 // 默认288， 5分钟同步一次

// 用户上传素材到arthub后，实时同步数据到db
func UploadToArthub(ctx *gin.Context, req *pb.UploadToArthubReq) (*pb.UploadToArthubRsp, error) {
	rsp := pb.UploadToArthubRsp{}
	succeedIds, fialedIds, err := syncAssetInfo(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "fail to sync asset information to db, err: %v", err)
		return &rsp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "fail to sync information to db")
	}
	if len(succeedIds) > 0 {
		for idx := range succeedIds {
			rsp.SucceedAssetIds = append(rsp.SucceedAssetIds, succeedIds[idx])
		}
	}
	if len(fialedIds) > 0 {
		for idx := range fialedIds {
			rsp.FailedAssetIds = append(rsp.FailedAssetIds, fialedIds[idx])
		}
		return &rsp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_SUCC), pbAix.AixCommErrCode_AIX_COMM_SUCC.String())
	}

	return &rsp, nil
}

// 返回同步成功和失败的assetId list
// 用户上传素材到arthub后，实时同步数据到db
func syncAssetInfo(ctx *gin.Context, req *pb.UploadToArthubReq) ([]string, []string, error) {
	assetlist := req.GetAssetIdList()
	gamecode := req.GetGameCode()

	db := postgresql.GetDBWithContext(ctx)
	result, err := db.Exec(`SELECT COUNT(*) FROM  pg_class WHERE relname = ?`,
		fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gamecode))
	if err != nil {
		log.ErrorContextf(ctx, "fail to check gamecode in postgresql, gamecode: %v, err: %v", gamecode, err)
		return nil, nil, fmt.Errorf("fail to check gamecode in postgresql")
	}
	if result.RowsReturned() < 1 {
		return nil, nil, fmt.Errorf("resource db not exist")
	}

	// 获取根节点信息
	depot, err := data.GetDepot(gamecode)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get depot information, gamecode: %v, err: %v", gamecode, err)
		return nil, nil, fmt.Errorf("fail to get arthub token information")
	}

	var (
		failedIds  = []string{}
		succeedIds = []string{}
	)

	switch depot.Type {
	case utils.GAME_DEPOT_TYPE_ARTHUB:
		var assetModelList = []assetItem{}
		assetModelList, succeedIds, failedIds, err = getAssetByArthub(ctx, req.GetAssetIdList(),
			depot, req.GetIsMeta(), convertUploadToArthubReqMetaToMap(req.GetMetas()))
		err = syncAssetInfoToDb(ctx, &depot, assetModelList, req.GetAixUploader())
		if err != nil {
			log.ErrorContextf(ctx, "fail to sync data to db, game_code: %v, assetModelList: %v, err: %v",
				depot.GameCode, assetModelList, err)
			return nil, nil, fmt.Errorf("fail to sync information to db")
		}
	case utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER:
		// 过滤素材信息，若数据库已存在，则无需插入db，只需要更新media目录
		assetOverviewSliceExisted := []model.CreativeOverview{}
		tablename := fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", depot.GameCode)
		err = postgresql.GetDBWithContext(ctx).Model(&assetOverviewSliceExisted).
			Table(tablename).
			WhereIn("asset_id IN (?)", assetlist).
			Select()
		if err != nil {
			log.ErrorContextf(ctx, "fail to get assset overview from table %q, asset_ids: %v,"+
				" err: %v", tablename, assetlist, err)
			return nil, nil, err
		}
		log.DebugContextf(ctx, "syncAssetInfo assetOverviewSliceExisted length: %d,"+
			" assetOverviewSliceExisted: %v", len(assetOverviewSliceExisted), assetOverviewSliceExisted)
		assetModelList, assetDirectorySyncInfoList, err := getGoogleAssetInfoList(ctx, &depot, assetlist, assetOverviewSliceExisted,
			req.GetIsMeta(), convertUploadToArthubReqMetaToMap(req.GetMetas()))
		if err != nil {
			log.ErrorContextf(ctx, "fail to get google asset info list, assetList: %v, err: %v", assetlist, err)
			return nil, nil, err
		}
		log.DebugContextf(ctx, "syncAssetInfo getGoogleAssetInfoList, assetModelList: %v, "+
			"assetDirectorySyncInfoList: %v", assetModelList, assetDirectorySyncInfoList)
		err = syncAssetInfoToDb(ctx, &depot, assetModelList, req.GetAixUploader())
		if err != nil {
			log.ErrorContextf(ctx, "fail to sync google data to db, game_code: %v, "+
				"assetModelList: %v, assetDirectorySyncInfoList: %v, err: %v", depot.GameCode,
				assetModelList, assetDirectorySyncInfoList, err)
			return nil, nil, fmt.Errorf("fail to sync information to db")
		}
		// 更新db 素材 previewUrl信息
		for _, item := range assetModelList {
			_, err = postgresql.GetDBWithContext(ctx).Model((*model.CreativeOverview)(nil)).
				Table(fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", gamecode)).
				Where("asset_id = ?", item.assetOverview.ID).
				Set("preview_url = ?", item.assetOverview.PreviewUrl).
				Update()
			if err != nil {
				log.ErrorContextf(ctx, "fail to update google asset preview url, id: %v, previewUrl: %v, err: %v",
					item.assetOverview.ID, item.assetOverview.PreviewUrl, err)
				return nil, nil, err
			}
		}

		for _, assetIDReq := range assetlist {
			flag := false
			for _, assetModel := range assetModelList {
				if assetModel.assetOverview.ID == assetIDReq {
					flag = true
					break
				}
			}
			if flag {
				succeedIds = append(succeedIds, assetIDReq)
			} else {
				failedIds = append(failedIds, assetIDReq)
			}
		}

	default:
		log.ErrorContextf(ctx, "depot type error, type: %v", depot.Type)
		return nil, nil, fmt.Errorf("depot type error")
	}

	return succeedIds, failedIds, nil
}

// 通过arthub获取素材信息
func getAssetByArthub(ctx *gin.Context, assetlist []string, depot model.ArthubDepot, isMeta bool,
	metaMap map[string]*pb.UploadToArthubReqMeta) ([]assetItem, []string, []string, error) {
	assetIds := []uint64{}
	for idx := range assetlist {
		if assetlist[idx] != "" {
			assetIds = append(assetIds, cast.ToUint64(assetlist[idx]))
		}
	}

	if len(assetIds) == 0 {
		log.Logger.Debug("syncDbInUploadToChannel asset id list is 0")
		return nil, nil, nil, nil
	}
	// 获取素材信息
	assetInfoList, failedAssetInfoIdsMap, err := arthub.AssetInfoGet(ctx, depot.ArthubCode, depot.PublicToken, assetIds)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get asset information, gamecode: %v, assetIds: %v, err: %v", depot.GameCode, assetIds, err)
		return nil, nil, nil, fmt.Errorf("fail to get asset information")
	}
	log.DebugContextf(ctx, "syncDbInUploadToChannel assetInfoList length: %d", len(assetInfoList))

	// 获取素材详情
	// 仅需获取素材信息成功的部分素材详情信息
	assetIdsSucceed := []uint64{}
	for idx := range assetInfoList {
		assetIdsSucceed = append(assetIdsSucceed, assetInfoList[idx].ID)
	}
	assetDetailInfo, err := arthub.AssetDetailInfoGet(ctx, depot.ArthubCode, depot.PublicToken, assetIdsSucceed)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get asset detail information, gamecode: %v, assetIds: %v, "+
			"err: %v", depot.GameCode, assetIds, err)
		return nil, nil, nil, fmt.Errorf("fail to get asset details information")
	}

	// 获取素材标签信息
	tagInfoMap := make(map[uint64][]arthub.TagInfo)
	// 20240718 arthub这个接口404， 暂时不调用
	// for idx := range assetInfoList {
	// 	tagInfo, err := arthub.AssetTagInfoGet(ctx, depot.ArthubCode, depot.PublicToken, assetInfoList[idx].ID)
	// 	if err != nil {
	// 		log.ErrorContextf(ctx, "fail to get tag information, game_code: %v, public_token: %v,"+
	// 			" asset_id: %v, err: %v", depot.GameCode, depot.PublicToken, assetInfoList[idx].ID, err)
	// 	} else {
	// 		tagInfoMap[assetInfoList[idx].ID] = tagInfo
	// 	}
	// }

	assetIDS := []string{}
	for idx := range assetInfoList {
		assetIDS = append(assetIDS, cast.ToString(assetInfoList[idx].ID))
	}

	// 过滤素材信息，若数据库已存在，则无需插入db，只需要更新media目录
	assetOverviewSliceExisted := []model.CreativeOverview{}
	tablename := fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", depot.GameCode)
	err = postgresql.GetDBWithContext(ctx).Model(&assetOverviewSliceExisted).
		Table(tablename).
		WhereIn("asset_id IN (?)", assetIDS).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "fail to get assset details from table %q, asset_ids: %v,"+
			" err: %v", tablename, assetIDS, err)
		return nil, nil, nil, err
	}

	assetModelList, _ := formatArthubAsset(ctx, assetInfoList, assetDetailInfo, tagInfoMap, assetOverviewSliceExisted, isMeta,
		metaMap)
	var (
		failedAssetInfoIds = []string{}
		successedInfoIds   = []string{}
	)
	for failedAssetId := range failedAssetInfoIdsMap {
		failedAssetInfoIds = append(failedAssetInfoIds, strconv.FormatUint(failedAssetId, 10))
	}
	for _, asset := range assetInfoList {
		successedInfoIds = append(successedInfoIds, strconv.FormatUint(asset.ID, 10))
	}
	return assetModelList, successedInfoIds, failedAssetInfoIds, nil
}

func convertUploadToArthubReqMetaToMap(metas []*pb.UploadToArthubReqMeta) map[string]*pb.UploadToArthubReqMeta {
	if len(metas) == 0 {
		return nil
	}
	m := make(map[string]*pb.UploadToArthubReqMeta)
	for idx := range metas {
		m[metas[idx].AssetId] = metas[idx]
	}
	return m
}

func syncAssetInfoToDb(ctx *gin.Context, depot *model.ArthubDepot, assetModelList []assetItem, aix_uploader string) error {
	tx, err := postgresql.GetDBWithContext(ctx).Begin()
	if err != nil {
		return err
	}
	assitIds := []string{}
	for idx := range assetModelList {
		assitIds = append(assitIds, assetModelList[idx].assetOverview.ID)
	}

	// 过滤素材信息，若数据库已存在，则无需插入db，只需要更新media目录
	assetOverviewSliceExisted := []model.CreativeOverview{}
	tablename := fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", depot.GameCode)
	err = tx.Model(&assetOverviewSliceExisted).
		Table(tablename).
		WhereIn("asset_id IN (?)", assitIds).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "fail to get assset details from table %q, asset_ids: %v,"+
			" err: %v", tablename, assitIds, err)
		return err
	}
	log.DebugContextf(ctx, "syncAssetInfoToDb assetOverviewSliceExisted: %v,"+
		" assetOverviewSliceExisted: %v", len(assetOverviewSliceExisted), assetOverviewSliceExisted)

	assetDetailSlice, assetOverviewSlice, assetDirectorySyncInfoSlice := formatAssetDetailToArthub(ctx, depot.Type,
		assetOverviewSliceExisted, assetModelList, aix_uploader)
	// 插入素材详情信息
	if len(assetDetailSlice) > 0 {
		tablename = fmt.Sprintf("%s.tb_creative_details_%s", "arthub_sync", depot.GameCode)
		_, err = tx.Model(&assetDetailSlice).
			Table(tablename).
			Insert()
		if err != nil {
			log.ErrorContextf(ctx, "fail to insert asset detail information, assetDetailSlice: %v, err: %v", assetDetailSlice, err)
			tx.Rollback()
			return err
		}
	}

	// 插入素材overview信息
	log.DebugContextf(ctx, "syncArthubAssetInfoToDb assetOverviewSlice length: %d, assetOverviewSlice: %v", len(assetOverviewSlice), assetOverviewSlice)
	if len(assetOverviewSlice) > 0 {
		tablename = fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", depot.GameCode)
		_, err = tx.Model(&assetOverviewSlice).
			Table(tablename).
			Insert()
		if err != nil {
			log.ErrorContextf(ctx, "fail to insert asset detail information, assetDetailSlice: %v, err: %v", assetDetailSlice, err)
			tx.Rollback()
			return err
		}
	}

	// 同步arthub 目录数据
	directoryUpdateMap := make(map[string]mediaDirectoryUpdate) // arthub目录需要更新的数据，key 为目录arthub 目录id
	if len(assetDirectorySyncInfoSlice) > 0 {
		for _, item := range assetDirectorySyncInfoSlice {
			fullPathIdSlice := strings.Split(item.FullPathId, ",")
			for idx := range fullPathIdSlice {
				if fullPathIdSlice[idx] == "" {
					continue
				}
				directoryId := fullPathIdSlice[idx]
				obj, ok := directoryUpdateMap[directoryId]
				objNew := mediaDirectoryUpdate{}
				if ok {
					if directoryId == item.ParentId {
						objNew.DirectChildCount = obj.DirectChildCount + 1
					}
					objNew.TotalLeafCount = obj.TotalLeafCount + 1
				} else {
					if directoryId == item.ParentId {
						objNew.DirectChildCount = 1
					}
					objNew.TotalLeafCount = 1
				}
				directoryUpdateMap[directoryId] = objNew // 更新arthub目录表信息
			}
		}
	}

	// 更新arthub 目录数量
	tablename = fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", depot.GameCode)
	for directoryId, directoryUpdateInfo := range directoryUpdateMap {
		mediaDirectoryDb := pgmodel.CreativeDirectory{}
		sql := fmt.Sprintf("SELECT * FROM %s WHERE id = ?", tablename)
		_, err = tx.QueryOne(&mediaDirectoryDb, sql, directoryId)
		if err == pg.ErrNoRows {
			continue
		}
		if err != nil {
			tx.Rollback()
			log.ErrorContextf(ctx, "fail to get asset info, sql: %v, err: %v", sql, err)
			return err
		}
		directChildCount := mediaDirectoryDb.DirectChildCount + directoryUpdateInfo.DirectChildCount
		directDirectoryCount := mediaDirectoryDb.DirectDirectoryCount + directoryUpdateInfo.DirectDirectoryCount
		totalLeafCount := mediaDirectoryDb.TotalLeafCount + directoryUpdateInfo.TotalLeafCount
		sql = fmt.Sprintf(`UPDATE %s.tb_creative_directory_%s
				SET direct_child_count = ?,
				direct_directory_count = ?,
				total_leaf_count = ? WHERE id = ?`,
			"arthub_sync", depot.GameCode)
		_, err = tx.Exec(sql, directChildCount, directDirectoryCount, totalLeafCount, directoryId)
		if err != nil {
			log.ErrorContextf(ctx, "fail to update arthub directory information by id,"+
				" id: %d, mediaDirectoryUpdate: %+v, err: %v\n", directoryId, directoryUpdateInfo, err)
			tx.Rollback()
			return err
		}
	}

	err = tx.Commit()
	if err != nil {
		log.ErrorContextf(ctx, "fail to commit info, err: %v", err)
	}
	return err
}

// assetDetailSliceExisted: 数据库里已经存在的素材信息，需要过滤掉
// assetInfoList: 从arthub获取的素材信息
// assetDetailInfo： 从arthub获取的素材详情信息
// tagInfoMap： 从arthub获取的标签信息， key为asset_id
// 返回值： 返回需要同步到db的素材overview和detail信息，以及需要同步的arthub目录表结构
func formatAssetDetailToArthub(ctx *gin.Context, depotType int, assetOverviewSliceExisted []model.CreativeOverview,
	assetModelList []assetItem, aix_uploader string) ([]model.CreativeAssetDetails, []model.CreativeOverview, []assetDirectorySyncInfo) {
	assetMap := map[string]*assetItem{}

	// 过滤已同步过的数据
	for idx := range assetModelList {
		if len(assetOverviewSliceExisted) > 0 {
			existed := false
			for idxExisted := range assetOverviewSliceExisted {
				if assetOverviewSliceExisted[idxExisted].AssetID == assetModelList[idx].assetOverview.ID {
					existed = true
					break
				}
			}
			if !existed {
				assetMap[assetModelList[idx].assetOverview.ID] = &assetModelList[idx]
			}
		} else {
			assetMap[assetModelList[idx].assetOverview.ID] = &assetModelList[idx]
		}
	}

	assetDetails := []model.CreativeAssetDetails{}
	assetOverviews := []model.CreativeOverview{}
	assetDirectorySyncSlice := []assetDirectorySyncInfo{}

	for _, assetModel := range assetModelList {
		asset, ok := assetMap[assetModel.assetDetail.ID]
		if !ok {
			log.InfoContextf(ctx, "asset not exist, asset_id: %v", assetModel.assetDetail.ID)
			continue
		}

		assetDetailObject := model.CreativeAssetDetails{
			AssetID:           assetModel.assetDetail.ID,
			Size:              asset.assetOverview.Capacity,
			Format:            asset.assetOverview.FileFormat,
			UpdateDate:        asset.assetOverview.UpdatedDate,
			Creator:           asset.assetOverview.Creator,
			Updater:           asset.assetOverview.Author,
			ManualFirstLabel:  assetModel.assetDetail.Tag,
			ManualSecondLabel: "",
			Width:             uint32(assetModel.assetDetail.Width),
			High:              uint32(assetModel.assetDetail.High),
			Duration:          assetModel.assetDetail.Duration,
			FrameRate:         assetModel.assetDetail.FrameRate,
			AspectRatio:       assetModel.assetDetail.AspectRatio,
			BitRate:           assetModel.assetDetail.BitRate,
			CompressionFormat: assetModel.assetDetail.CompressionFormat,
			RobotFirstLabel:   "",
			RobotSecondLabel:  "",
			Cover:             "",
			RobotLabelTryCnts: 0,
		}

		var (
			nodeId     string
			nodeName   string
			fullPathId string
		)
		length := len(asset.assetOverview.FullPathID)
		if length > 0 {
			nodeId = asset.assetOverview.FullPathID[length-1]
		}
		length = len(asset.assetOverview.FullPathName)
		if length > 0 {
			nodeName = asset.assetOverview.FullPathName[length-1]
		}
		for _, id := range asset.assetOverview.FullPathID {
			fullPathId = fullPathId + id + ","
		}
		fullPathId = fullPathId[:len(fullPathId)-1]

		// {"mp4", "wmv", "avi", "mov", "flv", "mkv"}; 1
		// {"psd", "tga", "dds", "tif", "tiff", "jpg", "jpeg", "png", "bmp", "gif"}; 2
		var formatType int
		switch strings.ToLower(asset.assetOverview.FileFormat) {
		case "mp4", "wmv", "avi", "mov", "flv", "mkv", "mpeg", "3gp", "mpg":
			formatType = 1
		case "psd", "tga", "dds", "tif", "tiff", "jpg", "jpeg", "png", "bmp", "gif":
			formatType = 2
		}
		var assetStatus uint8
		if depotType == utils.GAME_DEPOT_TYPE_ARTHUB {
			switch strings.ToLower(assetModel.assetOverview.AssetStatus) {
			case "normal":
				assetStatus = arthub.ARTHUB_ASSET_STATUS_NORMAL
			case "recycle":
				assetStatus = arthub.ARTHUB_ASSET_STATUS_RECYCLE
			case "deleted":
				assetStatus = arthub.ARTHUB_ASSET_STATUS_DELETED
			default:
			}
		} else {
			assetStatus = arthub.ARTHUB_ASSET_STATUS_NORMAL
		}
		assetOverviewObject := model.CreativeOverview{
			NodeID:             cast.ToString(nodeId),
			NodeName:           nodeName,
			AssetID:            asset.assetOverview.ID,
			AssetName:          asset.assetOverview.Name,
			AssetStatus:        assetStatus,
			Status:             0,
			CreateDate:         asset.assetOverview.CreatedDate,
			UplineDate:         asset.assetOverview.UpdatedDate,
			OfflineDate:        "",
			OnlineDays:         0,
			FullPathName:       strings.Join(asset.assetOverview.FullPathName, ","),
			FullPathID:         fullPathId,
			UploadState:        0,
			UploadEnable:       0,
			UploadToChannel:    "",
			OriginURL:          "",
			MediaDirectoryID:   "",
			MediaDirectoryName: "",
			FormatType:         formatType,
			QueryDetail:        MAX_QUERY_DETAIL_COUNT, // 默认144， 没十分钟同步一次
			AixUploader:        aix_uploader,
			FileFormat:         asset.assetOverview.FileFormat,
			UpdatedDate:        asset.assetOverview.UpdatedDate,
		}

		assetDirectorySyncObject := assetDirectorySyncInfo{
			AssetId:      asset.assetOverview.ID,
			FullPathName: strings.Join(asset.assetOverview.FullPathName, ","),
			FullPathId:   fullPathId,
			ParentId:     nodeId,
			ParentName:   nodeName,
		}
		assetDirectorySyncSlice = append(assetDirectorySyncSlice, assetDirectorySyncObject)
		assetDetails = append(assetDetails, assetDetailObject)
		assetOverviews = append(assetOverviews, assetOverviewObject)
	}
	return assetDetails, assetOverviews, assetDirectorySyncSlice
}

type assetDirectorySyncInfo struct {
	AssetId      string // 素材id
	FullPathName string // 素材arthub全路径
	FullPathId   string // 素材arthub全目录id
	ParentId     string // 素材arthub所在目录ID
	ParentName   string // 素材arthub所在目录名称
}

// 格式化arthub信息
type assetItem struct {
	assetOverview AssetOverview
	assetDetail   AssetDetail
}

type AssetOverview struct {
	ID           string   `json:"id"`
	Name         string   `json:"name"`
	Author       string   `json:"author"`
	Creator      string   `json:"creator"`
	Capacity     uint64   `json:"capacity"`
	CreatedDate  string   `json:"created_date"`
	UpdatedDate  string   `json:"updated_date"`
	FileFormat   string   `json:"file_format"`
	FullPathName []string `json:"full_path_name"`
	FullPathID   []string `json:"full_path_id"`
	FormatType   int      `json:"format_type"`
	PreviewUrl   string   `json:"preview_url"`
	AssetStatus  string   `json:"asset_status"`
}

type AssetDetail struct {
	ID                string `json:"id"`
	Width             uint64 `json:"width"`
	High              uint64 `json:"high"`
	Duration          string `json:"duration"`
	FrameRate         string `json:"frameRate"`
	AspectRatio       string `json:"aspectRatio"`
	BitRate           string `json:"bitRate"`
	CompressionFormat string `json:"compressionFormat"`
	Tag               string `json:"tag"`
}

func formatArthubAsset(ctx *gin.Context, assetlist []arthub.AssetInfo, assetDetailInfo []arthub.MetaResult,
	tagInfoMap map[uint64][]arthub.TagInfo, assetOverviewSliceExisted []model.CreativeOverview, isMeta bool,
	metaMap map[string]*pb.UploadToArthubReqMeta) ([]assetItem, []assetDirectorySyncInfo) {
	assets := []assetItem{}
	assetMap := map[uint64]*arthub.AssetInfo{}

	// 过滤已同步过的数据
	for idx := range assetlist {
		if len(assetOverviewSliceExisted) > 0 {
			existed := false
			for idxExisted := range assetOverviewSliceExisted {
				if assetOverviewSliceExisted[idxExisted].AssetID == cast.ToString(&assetlist[idx].ID) {
					existed = true
					break
				}
			}
			if !existed {
				assetMap[assetlist[idx].ID] = &assetlist[idx]
			}
		} else {
			assetMap[assetlist[idx].ID] = &assetlist[idx]
		}
	}
	var (
		assetDirectorySyncSlice = []assetDirectorySyncInfo{}
	)
	for _, item := range assetlist {
		for _, detail := range assetDetailInfo {
			var (
				width             uint64
				high              uint64
				duration          string
				frameRate         string
				aspectRatio       string
				BitRate           string
				compressionFormat string
			)
			for _, val := range detail.MetaVal {
				if val.Key == "width" {
					width, _ = strconv.ParseUint(val.Value, 10, 64)
				} else if val.Key == "height" {
					high, _ = strconv.ParseUint(val.Value, 10, 64)
				} else if val.Key == "duration" {
					duration = val.Value
				} else if val.Key == "frame_rate" {
					frameRate = val.Value
				} else if val.Key == "aspect" {
					aspectRatio = val.Value
				} else if val.Key == "video_bit_rate" {
					BitRate = val.Value
				} else if val.Key == "video_codec" {
					compressionFormat = val.Value
				}
			}
			if isMeta {
				meta, isExist := metaMap[strconv.FormatUint(item.ID, 10)]
				if isExist && meta != nil {
					width = meta.Width
					high = meta.High
					duration = meta.Duration
				}
			}
			asset, ok := assetMap[detail.ID]
			if !ok {
				log.InfoContextf(ctx, "asset not exist, asset_id: %v", detail.ID)
				continue
			}
			tags, ok := tagInfoMap[detail.ID]
			if !ok {
				log.InfoContextf(ctx, "asset tag not exist, asset_id: %v", detail.ID)
			}
			tag := ""
			if len(tags) > 0 {
				tagNameSlice := []string{}
				for idx := range tags {
					tagNameSlice = append(tagNameSlice, tags[idx].TagName)
				}
				if len(tagNameSlice) > 0 {
					tag = strings.Join(tagNameSlice, ",")
				}
			}

			var (
				nodeId     string
				nodeName   string
				fullPathId string
			)
			length := len(asset.FullPathID)
			if length > 0 {
				nodeId = cast.ToString(asset.FullPathID[length-1])
			}
			length = len(asset.FullPathName)
			if length > 0 {
				nodeName = asset.FullPathName[length-1]
			}
			for _, id := range asset.FullPathID {
				fullPathId = fullPathId + cast.ToString(id) + ","
			}
			fullPathId = fullPathId[:len(fullPathId)-1]

			// {"mp4", "wmv", "avi", "mov", "flv", "mkv"}; 1
			// {"psd", "tga", "dds", "tif", "tiff", "jpg", "jpeg", "png", "bmp", "gif"}; 2
			var formatType int
			switch asset.FileFormat {
			case "mp4", "wmv", "avi", "mov", "flv", "mkv":
				formatType = 1
			case "psd", "tga", "dds", "tif", "tiff", "jpg", "jpeg", "png", "bmp", "gif":
				formatType = 2
			}

			assetDirectorySyncObj := assetDirectorySyncInfo{
				AssetId:      cast.ToString(asset.ID),
				FullPathName: strings.Join(asset.FullPathName, ","),
				FullPathId:   fullPathId,
				ParentId:     nodeId,
				ParentName:   nodeName,
			}
			var itemFullPathID []string
			for _, id := range asset.FullPathID {
				itemFullPathID = append(itemFullPathID, cast.ToString(id))
			}
			assetOverviewObj := AssetOverview{
				ID:           cast.ToString(item.ID),
				Name:         item.Name,
				Author:       item.Author,
				Creator:      item.Creator,
				Capacity:     item.Capacity,
				CreatedDate:  item.CreatedDate,
				UpdatedDate:  item.UpdatedDate,
				FileFormat:   item.FileFormat,
				FullPathName: item.FullPathName,
				FullPathID:   itemFullPathID,
				FormatType:   formatType,
				AssetStatus:  item.Status,
				PreviewUrl:   "", // arthub素材封面有效期为为10min，故暂时不写入db
			}
			assetDetailObj := AssetDetail{
				ID:                cast.ToString(detail.ID),
				Width:             width,
				High:              high,
				Duration:          duration,
				FrameRate:         frameRate,
				AspectRatio:       aspectRatio,
				BitRate:           BitRate,
				CompressionFormat: compressionFormat,
				Tag:               tag,
			}
			assetObj := assetItem{
				assetOverview: assetOverviewObj,
				assetDetail:   assetDetailObj,
			}
			assetDirectorySyncSlice = append(assetDirectorySyncSlice, assetDirectorySyncObj)
			assets = append(assets, assetObj)
		}
	}
	return assets, assetDirectorySyncSlice
}

func getGoogleAssetInfoList(ctx *gin.Context, depot *model.ArthubDepot, assetIDList []string,
	assetOverviewSliceExisted []model.CreativeOverview, isMeta bool,
	metaMap map[string]*pb.UploadToArthubReqMeta) ([]assetItem, []assetDirectorySyncInfo, error) {
	var (
		assets             = []assetItem{}
		assetDirectorySync = []assetDirectorySyncInfo{}
		assetsID           = []string{}
	)

	// 过滤已同步过的数据
	for idx := range assetIDList {
		if len(assetOverviewSliceExisted) > 0 {
			existed := false
			for idxExisted := range assetOverviewSliceExisted {
				if assetOverviewSliceExisted[idxExisted].AssetID == assetIDList[idx] {
					existed = true
					break
				}
			}
			if !existed {
				assetsID = append(assetsID, assetIDList[idx])
			}
		} else {
			assetsID = append(assetsID, assetIDList[idx])
		}
	}
	for _, assetID := range assetsID {
		//srv, err := google.NewGoogleService([]byte(depot.GoogleServiceAccount))
		//if err != nil {
		//	log.ErrorContextf(ctx, "fail to create google service, depot: %v", *depot)
		//	return nil, nil, err
		//}
		srv, err := cache.GoogleServiceCache{}.Get(ctx, depot.GameCode)
		if err != nil {
			log.ErrorContextf(ctx, "cache.GoogleServiceCache fail to get google service, gameCode: %v, err: %v", depot.GameCode, err)
			return nil, nil, err
		}

		f, err := srv.GetFileInfo(assetID)
		if err != nil {
			log.ErrorContextf(ctx, "fail to get google file info, fileID: %v, err: %v", assetID, err)
			continue
		}
		var (
			parentID = ""
		)
		if len(f.Parents) > 0 {
			parentID = f.Parents[0]
		}
		fullPathID, fullPathName, parentID, parentName, err := srv.GetFullPathInfo(parentID, depot.DepotId)
		if err != nil {
			log.ErrorContextf(ctx, "fail to get google file full path info, fileID: %v, err: %v", assetID, err)
			continue
		}
		var (
			creator string
		)
		if f != nil && len(f.Owners) > 0 && f.Owners[0] != nil {
			creator = f.Owners[0].DisplayName
		}

		assetDirectorySyncObj := assetDirectorySyncInfo{
			AssetId:      assetID,
			FullPathName: fullPathName,
			FullPathId:   fullPathID,
			ParentId:     parentID,
			ParentName:   parentName,
		}
		format := f.FileExtension
		assetOverviewObj := AssetOverview{
			ID:           assetID,
			Name:         strings.TrimSuffix(f.Name, "."+format),
			Author:       creator,
			Creator:      creator,
			Capacity:     uint64(f.Size),
			CreatedDate:  fromRFC3339ToCSTLayout(f.CreatedTime),
			UpdatedDate:  fromRFC3339ToCSTLayout(f.ModifiedTime),
			FileFormat:   format,
			FullPathName: strings.Split(fullPathName, ","),
			FullPathID:   strings.Split(fullPathID, ","),
			FormatType:   0,
			PreviewUrl:   f.ThumbnailLink,
		}
		var (
			width    uint64
			height   uint64
			duration string
		)
		if f.ImageMediaMetadata != nil {
			width = uint64(f.ImageMediaMetadata.Width)
			height = uint64(f.ImageMediaMetadata.Height)
		} else if f.VideoMediaMetadata != nil {
			width = uint64(f.VideoMediaMetadata.Width)
			height = uint64(f.VideoMediaMetadata.Height)
			duration = cast.ToString(f.VideoMediaMetadata.DurationMillis / 1000)
		}
		if isMeta {
			meta, isExist := metaMap[assetID]
			if isExist && meta != nil {
				width = meta.Width
				height = meta.High
				duration = meta.Duration
			}
		}
		assetDetailObj := AssetDetail{
			ID:                assetID,
			Width:             width,
			High:              height,
			Duration:          duration,
			FrameRate:         "",
			AspectRatio:       "",
			BitRate:           "",
			CompressionFormat: "",
			Tag:               "",
		}
		assetItemObj := assetItem{
			assetOverview: assetOverviewObj,
			assetDetail:   assetDetailObj,
		}
		assets = append(assets, assetItemObj)
		assetDirectorySync = append(assetDirectorySync, assetDirectorySyncObj)
	}

	return assets, assetDirectorySync, nil
}

// fromRFC3339ToCSTLayout convert rfc3339 value to China standard time layout
// 2020-11-08T08:18:46+08:00 => 2020-11-08 08:18:46
func fromRFC3339ToCSTLayout(value string) string {
	ts, err := time.Parse(time.RFC3339, value)
	if err != nil {
		return value
	}
	cst, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return value
	}
	return ts.In(cst).Format("2006-01-02 15:04:05")
}
