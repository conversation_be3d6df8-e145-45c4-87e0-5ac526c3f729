syntax = "proto3";

package google_drive_sync;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/google_drive_sync";

import "aix/aix_common_message.proto";

message SayHiReq {
    string msg = 1;
}

message SayHiRsp {
    aix.Result result = 1;
    string     msg    = 2;
}

// 手动同步素材status, POST, /api/v1/google_drive_sync/sync_material
message SyncMaterialReq  {
    string game_code = 1;  // game_code
    string node_id   = 2;  // 节点ID
    int32 node_type  = 3;  // 节点类型, 1-google drive dir
}

message SyncMaterialRsp {
    aix.Result result = 1;
}