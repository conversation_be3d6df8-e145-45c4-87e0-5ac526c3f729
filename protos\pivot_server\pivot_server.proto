syntax = "proto3";

package pivot_server;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/pivot_server";

import "aix/aix_common_message.proto";

// where
message AnalysisPivotWhere {
    string name             = 1;   // 字段名
    double upper            = 2;   // 最大值，type为2时适用
    double lower            = 3;   // 最小值，type为2时适用
    repeated string in_list = 4;   // 列表值，type为1时适用, string类型
    int32 in_list_type      = 5;   // 列表值类型, 1表示字符串, 2表示整型, 3表示浮点型
    string like             = 6;   // 模糊匹配值，type为3时适用
    int32 type              = 7;   // 1-in多个值任一匹配 2-where的range范围内查找 3-like模糊匹配 4-having的range范围查询,5-not in多个值任一匹配
    int32 is_upper_equal    = 8;   // 最大值是否可等于，-1没有上限 0不等于 1等于，type为2时适用
    int32 is_lower_equal    = 9;   // 最小值是否可等于，-1没有下限 0不等于 1等于，type为2时适用
    int32 data_type         = 10;  // 筛选字段类型, 1表示字符串, 2表示整型, 3表示浮点型
}
// group
message AnalysisPivotGroup {
    string country_code = 1;  //国家代码
    string show_network = 2;  //渠道
    string asset_name   = 3;  //素材名称
    string game_code    = 4;  //游戏代码
}

message OrderBy {
    string by    = 1;  // 字段名
    string order = 2;  // 'desc' 'asc'
}

// analysis_pivot列表请求, POST, /api/v1/pivot_server/analysis_pivot_list
message AnalysisPivotListReq {
    repeated string group             = 1;  // 聚合字段
    repeated AnalysisPivotWhere where = 2;  // 筛选条件
    uint32 pageIndex                  = 3;  // 起始偏移
    uint32 pageSize                   = 4;  // 拉取数量
    repeated string metric            = 5;  // 需要的字段
    repeated OrderBy orderby          = 6;  // order by
    int32 need_agg_num                = 7;  // 是否需要聚合数量, 0-否, 1-是
}
// analysis_pivot元数据
message AnalysisPivotMeta {
    // 需要重新计算的字段
    double ctr               = 1;
    double cvr               = 2;
    double ipm               = 3;
    double cpi               = 4;
    double daily_impressions = 5;
    double impression_change = 6;
    double click_change      = 7;

    // clickhouse表中字段
    string game_code           = 8;
    int64 dtstatdate           = 9;
    string mcc_id              = 10;
    string account_id          = 11;
    string asset_id            = 12;
    string asset_name          = 13;
    string asset_status        = 14;
    string asset_serial_id     = 15;
    string asset_project       = 16;
    string asset_format        = 17;
    string asset_size          = 18;
    string asset_language      = 19;
    string asset_organization  = 20;
    string asset_custom_name   = 21;
    string asset_play          = 22;
    string asset_perform       = 23;
    string asset_stage         = 24;
    string asset_delivery_date = 25;
    string creative_id         = 26;
    string creative_name       = 27;
    string creative_status     = 28;
    string ad_id               = 29;
    string ad_name             = 30;
    string ad_status           = 31;
    string adgroup_id          = 32;
    string adgroup_name        = 33;
    string adgroup_status      = 34;
    string campaign_id         = 35;
    string campaign_name       = 36;
    string campaign_status     = 37;
    string campaign_type       = 38;
    string parse_network       = 39;
    string parse_region        = 40;
    string parse_platform      = 41;
    string parse_date          = 42;
    string parse_campaign_goal = 43;
    string parse_custom_field  = 44;
    string parse_cost_type     = 45;
    string parse_cost_region   = 46;
    string online_date         = 47;
    string offline_date        = 48;
    int64 online_days          = 49;
    string country_code        = 50;
    string country_name_ch     = 51;
    string country_name_en     = 52;
    string ua_region           = 53;
    string language            = 54;
    string network             = 55;
    string asset_url           = 56;
    string start_date          = 57;
    string end_date            = 58;
    double impressions         = 59;
    double clicks              = 60;
    double conversions         = 61;
    double registers           = 62;
    double installs            = 63;
    double spend               = 64;
    string machine_lables      = 65;
    string human_lables        = 66;
    string asset_type          = 67;
    string youtube_id          = 68;
    double watch_time          = 69;
    double video_played_25     = 70;
    double video_played_50     = 71;
    double video_played_75     = 72;
    double video_played_100    = 73;
    double video_duration      = 74;
    double num_of_ads          = 75;
    double d1_pay_users        = 76;
    double d2_pay_users        = 77;
    double d3_pay_users        = 78;
    double d7_pay_users        = 79;
    double d1_pay_rate         = 80;
    double d2_pay_rate         = 81;
    double d3_pay_rate         = 82;
    double d7_pay_rate         = 83;
    double d1_pay_amount       = 84;
    double d2_pay_amount       = 85;
    double d3_pay_amount       = 86;
    double d7_pay_amount       = 87;
    double d1_roas             = 88;
    double d2_roas             = 89;
    double d3_roas             = 90;
    double d7_roas             = 91;
    double d1_ltv              = 92;
    double d2_ltv              = 93;
    double d3_ltv              = 94;
    double d7_ltv              = 95;
    double r1                  = 96;
    double r2                  = 97;
    double r3                  = 98;
    double r7                  = 99;
    int64 agg_num              = 100;  // 聚合数量
}

message AnalysisPivotListRsp {
    aix.Result result                 = 1;  // 返回结果
    repeated AnalysisPivotMeta pivots = 2;  // 列表
    uint32 total                      = 3;  // 总数
}

// 获取creative pivot表筛选项信息, POST, /api/v1/pivot_server/get_filter_info
message GetFilterInfoReq {}

message GetFilterInfoRsp {
    aix.Result result = 1;  // 返回结果
    int64 dtstatdate  = 2;  // 最新数据时间
}

// 插入mock数据, POST, /api/v1/pivot_server/insert_mock
message InsertMockReq {
    int32 number = 1;  // 插入mock数据数量
}

// 插入mock数据-返回结构
message InsertMockRsp {
    aix.Result result = 1;  // 返回结果
}

// 测试语句
message SayHiReq {
}

message SayHiRsp {
    aix.Result result = 1;  // 返回结果
}

// 请求google实时统计数据信息, POST, /api/v1/pivot_server/query_google_realtime_asset_info
message QueryGoogleRealtimeAssetInfoReq {
    repeated string group             = 1;  // 聚合字段
    repeated AnalysisPivotWhere where = 2;  // 筛选条件
    uint32 pageIndex                  = 3;  // 起始偏移
    uint32 pageSize                   = 4;  // 拉取数量
    repeated string metric            = 5;  // 需要的字段
    repeated OrderBy orderby          = 6;  // order by
    int32 need_agg_num                = 7;  // 是否需要聚合数量, 0-否, 1-是
}

// google实时统计数据结构体
message GoogleRealtimeAssetInfo {
    int64 agg_num = 1;
    // 需要重新计算的字段
    double ctr               = 2;
    double cvr               = 3;
    double ipm               = 4;
    double cpi               = 5;
    double daily_impressions = 6;
    double impression_change = 7;
    double click_change      = 8;

    string dtstatdate                            = 9;
    string hour                                  = 10;
    string game_code                             = 11;
    string customer_id                           = 12;
    string asset_id                              = 13;
    string asset_name                            = 14;
    string asset_image_size                      = 15;
    string asset_img_height                      = 16;
    string asset_img_url                         = 17;
    string asset_img_width                       = 18;
    string asset_mime_type                       = 19;
    string asset_resource_name                   = 20;
    string asset_type                            = 21;
    string asset_video_id                        = 22;
    string asset_video_title                     = 23;
    string ad_group_id                           = 24;
    string ad_group_name                         = 25;
    string ad_group_resource_name                = 26;
    string ad_group_status                       = 27;
    string ad_status                             = 28;
    string ad_group_ad_resource_name             = 29;
    string ad_id                                 = 30;
    string ad_resource_name                      = 31;
    string headline_list_str                     = 32;
    string description_list_str                  = 33;
    string image_list_str                        = 34;
    string video_list_str                        = 35;
    string campaign_id                           = 36;
    string campaign_name                         = 37;
    string campaign_resource_name                = 38;
    string campaign_budget_resource_name         = 39;
    string campaign_status                       = 40;
    string campaign_serving_status               = 41;
    string campaign_advertising_channel_type     = 42;
    string campaign_advertising_channel_sub_type = 43;
    string campaign_cpa                          = 44;
    string campaign_roas                         = 45;
    string campaign_app_id                       = 46;
    string campaign_app_store                    = 47;
    string campaign_bidding_strategy_goal_type   = 48;
    string campaign_start_date                   = 49;
    string campaign_end_date                     = 50;
    string campaign_conversion_actions           = 51;
    double clicks                                = 52;
    double conversions                           = 53;
    double cost_micros                           = 54;
    double impressions                           = 55;
    string country_list                          = 56;
    string main_country                          = 57;
    string account_id                            = 58;
    string asset_serial_id                       = 59;
    string asset_project                         = 60;
    string asset_format                          = 61;
    string asset_size                            = 62;
    string asset_language                        = 63;
    string asset_organization                    = 64;
    string asset_custom_name                     = 65;
    string asset_play                            = 66;
    string asset_perform                         = 67;
    string asset_stage                           = 68;
    string asset_delivery_date                   = 69;
    string campaign_type                         = 70;
    string parse_network                         = 71;
    string parse_region                          = 72;
    string parse_platform                        = 73;
    string parse_date                            = 74;
    string parse_campaign_goal                   = 75;
    string parse_custom_field                    = 76;
    string parse_cost_type                       = 77;
    string parse_cost_region                     = 78;
    string country_code                          = 79;
    string language                              = 80;
    string network                               = 81;
    string asset_url                             = 82;
    double spend                                 = 83;
    string youtube_id                            = 84;
    string conversion_action_name                = 85;
    double installs                              = 86;
    double in_app_actions                        = 87;
    string dtstattime                            = 88;
}

message QueryGoogleRealtimeAssetInfoRsp {
    aix.Result result                          = 1;  // 返回结果
    repeated GoogleRealtimeAssetInfo info_list = 2;  // 返回结果
    uint32 total                               = 3;  // 总数
    string sql                                 = 4;  // 查询sql
}