package creative_insights

import (
	"context"
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/httphelper"
)

// Video
type Video struct {
	GameCode    string `json:"game_code"`    // 必填，所属业务,如:hok/pubgm
	VideoID     string `json:"video_id"`     // 必填，视频id，全局唯一的值
	StorageType int    `json:"storage_type"` // 必填，存储类型，int类型，取值1-cos，2-arthub，3-google_driver，4-web_http，5-youtube_url
	StorageURL  string `json:"storage_url"`  // 必填,能够下载到视频的地址或者fileid，其中arthub和google drive 填写视频fileID
}

// AddMappingReq 添加视频映射请求
type AddMappingReq struct {
	ReqID     string  `json:"req_id"`     // uuid
	VideoList []Video `json:"video_list"` // 批量调用，暂定100个
}

// AddMappingRsp 添加视频映射请求返回结果
type AddMappingRsp struct {
	RetCode string `json:"ret_code"` // 返回码
	Message string `json:"message"`  // 返回信息
}

// AddMapping 添加视频映射
func AddMapping(ctx context.Context, target string, req *AddMappingReq) (*AddMappingRsp, error) {
	client := httphelper.NewRequest(ctx)
	client.SetHeader("Content-Type", "application/json")
	client.SetBody(req).SetResult(AddMappingRsp{})
	url := fmt.Sprintf("%s/api/v1/creative_insights/add_mapping", target)
	client_rsp, err := client.Post(url)
	if err != nil {
		return nil, fmt.Errorf("add mapping failed: %v", err)
	}

	if !client_rsp.IsSuccess() {
		return nil, fmt.Errorf("get node meta value by meta key failed, status code: %s", client_rsp.Status())
	}

	rsp, ok := client_rsp.Result().(*AddMappingRsp)
	if !ok {
		return nil, fmt.Errorf("assert client rsp to asset detail failed")
	}

	return rsp, nil
}
