-- 标签规则结构化ck外表(
CREATE TABLE ieg_ads_bi.tb_asset_rule_labels on cluster default_cluster
(
    `game_code` String,
    `rule` String,
    `type` Int32,
    `label_name` String,
    `first_label` String,
    `second_label` String,
    `create_time` String,
    `update_time` String,
    `create_by` String,
    `update_by` String
)
ENGINE = PostgreSQL('10.190.24.134:5432', 'UA-Intelli-PRODUCTION', 'tb_asset_rule_labels', 'ua_td', 'uatd2021666', 'arthub_sync');