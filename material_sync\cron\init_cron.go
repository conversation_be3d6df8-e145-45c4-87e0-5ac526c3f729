package cron

import (
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/cronhelper"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/service"
)

// InitCron ...
func InitCron() {
	// 海外环境没有clickhouse，这里定时任务需要注意

	// 切换到common包cron处理
	// 定时同步每天的渠道素材信息(包括impression_date)到总表
	cronhelper.RegisterSafeCron("SaveAllChannelAssets", service.SaveAllChannelAssets)
	// 自动生成标签规则
	cronhelper.RegisterSafeCron("AutoGenLableRule", service.AutoGenLableRule)

	// 20231220
	// 标签规则打标定时任务处理
	cronhelper.RegisterSafeCron("CronLabelRuleTask", service.CronLabelRuleTask)

	// 20240308
	// 将标签规则结构化到ck外表(arthub_sync.tb_asset_rule_labels)
	cronhelper.RegisterSafeCron("SyncRuleLabels", service.SyncRuleLabels)

	// 20241227 注释掉 旧标签体系的定时任务
	// cronhelper.RegisterSafeCron("SyncCreativeRecommendCandidate", service.SyncCreativeRecommendCandidate)
	// cronhelper.RegisterSafeCron("SyncChannelAssetLabelToCk", service.SyncChannelAssetLabelToCk)
	// cronhelper.RegisterSafeCron("SyncMediaContentMap", service.SyncMediaContentMap)
	// cronhelper.RegisterSafeCron("SyncAssetMapTask", service.SyncAssetMapTask)
	// cronhelper.RegisterSafeCron("SyncAssetInfoTotal", service.SyncAssetInfoTotal)
	// cronhelper.RegisterSafeCron("SyncImpressionDate", service.SyncImpressionDate)
	// cronhelper.RegisterSafeCron("CalculateKeywordFrequency", calculateKeywordFrequency)
	// cronhelper.RegisterSafeCron("NotifyUnusedMaterial", notifyUnusedMaterial)
	// cronhelper.RegisterSafeCron("SyncCreativeAnalysisPivot", syncCreativeAnalysisPivot)
	// cronhelper.RegisterSafeCron("PushMaterial", pushMaterial)
	// cronhelper.RegisterSafeCron("MaterialMetrics", materialMetrics)
	//
	// // 20231221 定时任务迁移到配置文件控制
	// cronhelper.RegisterSafeCron("SyncOnlineInfoTotal", service.SyncOnlineInfoTotal)
	//
	// // 禁用clickhouse, 就不处理
	// if !conf.GetBizConf().DisabelClickhouse {
	// 	go service.SyncImpressionDateHistory()
	// }

	cronhelper.StartExistCron()
}
