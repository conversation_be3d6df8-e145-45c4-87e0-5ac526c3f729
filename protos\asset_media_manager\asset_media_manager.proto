syntax = "proto3";

package asset_media_manager;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/asset_media_manager";
import "aix/aix_common_message.proto";

// 内部接口 触发定时任务, POST, /api/v1/asset_media_manager/cron_trigger
message CronTriggerReq {
    string cron_name = 1;  // 定时任务名字
}

message CronTriggerRsp {
    aix.Result result = 1;  // 返回结果
}

// ResourceName 结构
message ResourceName {
    string resource_name       = 1;
    string cover_resource_name = 2;
    string image_material_id   = 3;  // 在tiktok中即UI页面上的Image ID, 如7159153087693127682
    string video_material_id   = 4;  // 在tiktok中即UI页面上的Video Material ID, 如7159153118533828609
}

// 已上传到渠道的素材-更新渠道素材名字, POST, /api/v1/asset_media_manager/update_uploaded_media_asset_name
message UpdateUploadedMediaAssetNameReq {
    string game_code  = 1;  // 必填 游戏标识game_code
    string asset_id   = 2;  // 必填 asset id
    string asset_name = 3;  // 必填 asset name
}

message UpdateUploadedMediaAssetNameRsp {
    aix.Result result = 1;  // 返回结果
}

// slack交互命令获取某个游戏上传统计报表, POST, /api/v1/asset_media_manager/slack_cmd_upload_statistics
message SlackCmdUploadStatisticsReq {
    string game_code  = 1;  // 必填 游戏标识game_code
    string game_name  = 2;  // 必填 游戏名称
    string studio     = 3;  // 必填 游戏工作室
    string channel_id = 4;  // 必填 slack channel id
    string thread_id  = 5;  // 必填 slack thread id
}

message SlackCmdUploadStatisticsRsp {
    aix.Result result = 1;  // 返回结果
}

message Game {
    string game_code  = 1;  // 必填 游戏标识game_code
    string game_name  = 2;  // 必填 游戏名称
}

// slack交互命令统计至今为止studio下游戏素材上传渠道成功记录数, POST, /api/v1/asset_media_manager/slack_cmd_get_summary
message SlackCmdGetSummaryReq {
    repeated Game games  = 1;  // 必填 游戏列表
    string   studio      = 2;  // 必填 游戏工作室
    string   studio_name = 3;  // 必填 游戏工作室名称
    string   channel_id  = 4;  // 必填 slack channel id
    string   thread_id   = 5;  // 必填 slack thread id
}

message SlackCmdGetSummaryRsp {
    aix.Result result = 1;  // 返回结果
}