# Go结构体依赖分析器 - 使用指南

## 🎯 功能概述

这个JavaScript工具能够分析Go结构体的依赖关系，递归查找所有相关的结构体定义，直到不再依赖其他结构体为止。

### ✨ 主要特性

- 🔍 **递归依赖分析**: 自动分析结构体的所有依赖关系
- 📦 **跨包支持**: 支持分析跨包的结构体引用（如 `aix.Result`）
- 🚀 **高性能**: 使用缓存机制避免重复解析
- 🔄 **循环依赖检测**: 防止无限递归
- 📝 **完整输出**: 返回所有相关结构体的完整定义

## 📁 项目文件结构

```
.
├── struct-analyzer.js    # 主要分析器代码
├── quick-analyze.js      # 快速分析工具
├── test-analyzer.js      # 测试套件
├── package.json         # NPM配置
├── README.md           # 详细文档
├── USAGE.md            # 使用指南（本文件）
└── protos/             # Go protobuf文件
    ├── aix/
    │   └── aix_common_message.pb.go
    └── material_display/
        └── material_display.pb.go
```

## 🚀 快速开始

### 1. 命令行使用

```bash
# 基本用法
node struct-analyzer.js GetMaterialInfoRsp

# 快速分析（推荐）
node quick-analyze.js GetMaterialInfoRsp

# 使用npm脚本
npm run quick GetMaterialInfoRsp
npm run analyze MaterialExt
npm test
```

### 2. 作为模块使用

```javascript
const { analyzeStruct, GoStructAnalyzer } = require('./struct-analyzer.js');

// 简单分析
const dependencies = analyzeStruct('GetMaterialInfoRsp', '.');

// 高级用法
const analyzer = new GoStructAnalyzer();
const result = analyzer.analyzeStructDependencies('GetMaterialInfoRsp', 'material_display', '.');

// 遍历结果
for (const [structName, structDef] of result) {
    console.log(`结构体: ${structName}`);
    console.log(`字段数量: ${structDef.fields.length}`);
}
```

## 📊 示例分析结果

### 输入: `GetMaterialInfoRsp`

```bash
node quick-analyze.js GetMaterialInfoRsp
```

### 输出:

```
🔍 正在分析结构体: GetMaterialInfoRsp
============================================================
✅ 分析完成! 耗时: 5ms
📊 找到 7 个相关结构体

📋 依赖关系:
----------------------------------------
├── GetMaterialInfoRsp
├── Result
├── MaterialExt
├── Universal
├── Label
├── Statistics
└── Video

============================================================
📝 完整结构体定义:
============================================================

// GetMaterialInfoRsp
type GetMaterialInfoRsp struct {
    Result          *aix.Result
    MaterialExt     *MaterialExt
}

// Result (来自aix包)
type Result struct {
    ErrorCode       uint32
    ErrorMessage    string
}

// MaterialExt
type MaterialExt struct {
    AssetId         string
    Universal       *Universal
    Label           *Label
    Statistics      *Statistics
    Video           *Video
}

// Universal
type Universal struct {
    Format          string
    Size            uint64
    CreateTime      string
    Updater         string
    UpdateTime      string
    Creator         string
    Cover           string
    FormatType      int32
}

// Label
type Label struct {
    ManualFirstLabel []string
    ManualSecondLabel []string
    RobotFirstLabel []string
    RobotSecondLabel []string
}

// Statistics
type Statistics struct {
    TakeUsers       uint64
    TakeCount       uint64
}

// Video
type Video struct {
    Width           uint32
    High            uint32
    Duration        uint32
    FrameRate       string
    AspectRatio     string
    BitRate         string
    CompressionFormat string
}
```

## 🔧 支持的结构体

当前支持分析以下结构体（以及它们的所有依赖）：

### Material Display 包
- `GetMaterialInfoRsp` - 获取素材信息响应
- `MaterialExt` - 素材扩展信息
- `MaterialMeta` - 素材元数据
- `Universal` - 通用信息
- `Label` - 标签信息
- `Statistics` - 统计信息
- `Video` - 视频信息
- `MaterialListReq` - 素材列表请求
- `MaterialListRsp` - 素材列表响应

### AIX 包
- `Result` - 通用结果结构体

## 📈 依赖关系图

```
GetMaterialInfoRsp
├── aix.Result (跨包引用)
│   ├── ErrorCode (uint32)
│   └── ErrorMessage (string)
└── MaterialExt
    ├── AssetId (string)
    ├── Universal
    │   ├── Format (string)
    │   ├── Size (uint64)
    │   ├── CreateTime (string)
    │   ├── Updater (string)
    │   ├── UpdateTime (string)
    │   ├── Creator (string)
    │   ├── Cover (string)
    │   └── FormatType (int32)
    ├── Label
    │   ├── ManualFirstLabel ([]string)
    │   ├── ManualSecondLabel ([]string)
    │   ├── RobotFirstLabel ([]string)
    │   └── RobotSecondLabel ([]string)
    ├── Statistics
    │   ├── TakeUsers (uint64)
    │   └── TakeCount (uint64)
    └── Video
        ├── Width (uint32)
        ├── High (uint32)
        ├── Duration (uint32)
        ├── FrameRate (string)
        ├── AspectRatio (string)
        ├── BitRate (string)
        └── CompressionFormat (string)
```

## 🛠️ 高级用法

### 自定义包路径

```javascript
const analyzer = new GoStructAnalyzer();

// 添加新的包路径
analyzer.packagePaths.set('custom_package', '/path/to/custom.pb.go');

// 分析自定义包中的结构体
const result = analyzer.analyzeStructDependencies('CustomStruct', 'custom_package', '.');
```

### 过滤字段

```javascript
const dependencies = analyzeStruct('GetMaterialInfoRsp', '.');

for (const [structName, structDef] of dependencies) {
    // 只显示业务字段
    const businessFields = structDef.fields.filter(field => 
        !['state', 'sizeCache', 'unknownFields'].includes(field.name)
    );
    
    console.log(`${structName} 业务字段:`, businessFields.map(f => f.name));
}
```

## 🧪 测试

运行完整测试套件：

```bash
npm test
```

测试包括：
- ✅ GetMaterialInfoRsp结构体分析
- ✅ MaterialExt结构体分析  
- ✅ 不存在结构体的错误处理
- ✅ 直接使用分析器类
- ✅ 跨包引用解析
- ✅ 循环依赖检测

## 🔍 故障排除

### 常见问题

1. **找不到结构体**
   ```
   未找到结构体定义: material_display.StructName
   ```
   - 检查结构体名称是否正确
   - 确保Go文件路径配置正确

2. **文件不存在**
   ```
   文件不存在: /path/to/file.go
   ```
   - 检查项目根目录是否正确
   - 确保proto文件已生成

3. **解析错误**
   - 检查Go文件语法是否正确
   - 确保是protobuf生成的标准格式

### 调试模式

```javascript
const analyzer = new GoStructAnalyzer();
analyzer.debug = true; // 启用调试输出
```

## 📝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License
