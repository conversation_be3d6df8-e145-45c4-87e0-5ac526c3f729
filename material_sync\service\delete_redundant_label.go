package service

import (
	"context"
	"strings"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// DeleteRedundantLabelForGameCode 删除多余的标签
func DeleteRedundantLabelForGameCode(game_code string) {
	deleteRedundantLabelGeneral(game_code)

	switch game_code {
	case "pubgm":
		DeleteRedundantLabelForPUBGM()
	}
}

// deleteRedundantLabelGeneral 删除冗余标签
func deleteRedundantLabelGeneral(gameCode string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "deleteRedundantLabelGeneral start")

	st := time.Now()

	redundant_labels, err := getRedundantLabelsGeneral(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "getRedundantPubgmLabels failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get redundant labels number: %d", len(redundant_labels))

	if len(redundant_labels) == 0 {
		return
	}

	table_name := pgmodel.GetChannelAssetLabelTableName(gameCode)
	pg_query := pgdb.GetDBWithContext(ctx).Model(&redundant_labels).Table(table_name)

	_, err = pg_query.Delete()
	if err != nil {
		log.ErrorContextf(ctx, "delete redundant labels failed: %s", err)
		return
	}

	cost := time.Since(st)
	log.InfoContextf(ctx, "deleteRedundantLabelGeneral end, cost: %v", cost)
}

// getRedundantLabelsGeneral 获取应当是唯一的标签数据
func getRedundantLabelsGeneral(ctx context.Context, gameCode string) ([]pgmodel.ChannelAssetLabel, error) {
	sql := `select
    channel_type,
    channel_account_id,
    channel_asset_id,
    label_name,
    first_label,
    second_label
from
    (
        SELECT
            channel_type,
            channel_account_id,
            channel_asset_id,
            label_name,
            first_label,
            second_label,
            update_time,
            update_by,
            ROW_NUMBER() OVER(
                PARTITION BY channel_type,
                channel_account_id,
                channel_asset_id,
                label_name,
                first_label
                ORDER BY
                    update_time desc
            ) rn
        FROM
            "channel_asset"."channel_asset_label_${game_code}" AS "channel_asset_label"
        WHERE
            (
                (label_name='素材内容' and first_label = '内容编号')
            )
    ) t
where
    rn > 1;`
	sql = strings.ReplaceAll(sql, "${game_code}", gameCode)
	var records []pgmodel.ChannelAssetLabel
	pg_query := pgdb.GetDBWithContext(ctx)
	_, err := pg_query.Query(&records, sql)
	if err != nil {
		return nil, err
	}

	return records, nil
}

// DeleteRedundantLabelForPUBGM 删除pubgm中的多余标签
func DeleteRedundantLabelForPUBGM() {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "DeleteRedundantLabelForPUBGM start")

	st := time.Now()

	redundant_labels, err := getRedundantPubgmLabels(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getRedundantPubgmLabels failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get redundant labels number: %d", len(redundant_labels))

	if len(redundant_labels) == 0 {
		return
	}

	table_name := pgmodel.GetChannelAssetLabelTableName("pubgm")
	pg_query := pgdb.GetDBWithContext(ctx).Model(&redundant_labels).Table(table_name)

	_, err = pg_query.Delete()
	if err != nil {
		log.ErrorContextf(ctx, "delete redundant labels failed: %s", err)
		return
	}

	cost := time.Since(st)
	log.InfoContextf(ctx, "DeleteRedundantLabelForPUBGM end, cost: %v", cost)
}

// getPubgmShouldBeUniqueLabels 获取应当是唯一的标签数据
func getRedundantPubgmLabels(ctx context.Context) ([]pgmodel.ChannelAssetLabel, error) {
	sql := `select
    channel_type,
    channel_account_id,
    channel_asset_id,
    label_name,
    first_label,
    second_label
from
    (
        SELECT
            channel_type,
            channel_account_id,
            channel_asset_id,
            label_name,
            first_label,
            second_label,
            update_time,
            update_by,
            ROW_NUMBER() OVER(
                PARTITION BY channel_type,
                channel_account_id,
                channel_asset_id,
                label_name,
                first_label
                ORDER BY
                    update_time desc
            ) rn
        FROM
            "channel_asset"."channel_asset_label_pubgm" AS "channel_asset_label"
        WHERE
            (
                (label_name='Aix' and first_label = '素材版本')
                or (label_name='Aix' and first_label = '素材分类')
                or (label_name='Aix' and first_label = '素材主题')
                or (label_name='Aix' and first_label = '是否含有FPS第一视角')
                or (label_name='Aix' and first_label = '有无文案')
            )
    ) t
where
    rn > 1;`
	var records []pgmodel.ChannelAssetLabel
	pg_query := pgdb.GetDBWithContext(ctx)
	_, err := pg_query.Query(&records, sql)
	if err != nil {
		return nil, err
	}

	return records, nil
}
