package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	commonRepo "e.coding.intlgame.com/ptc/aix-backend/common/repo/common_config"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/rpc"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pbApplovin "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// AddUploadToChannelTask 增加素材上传渠道任务
func AddUploadToChannelTask(ctx *gin.Context, req *pb.AddUploadToChannelTaskReq, rsp *pb.AddUploadToChannelTaskRsp) error {
	creator := req.GetCreator()
	gameCode := req.GetGameCode()
	assetIDList := req.GetAssetIds()
	if gameCode == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code error")
	}
	// 校验下线日期格式
	if req.GetOfflineDate() != "" {
		if !utils.IsNumericDate(req.GetOfflineDate()) {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "offline_date err:%v", req.GetOfflineDate())
		}
	}
	assetIDList = funk.UniqString(assetIDList)
	if len(assetIDList) == 0 || len(assetIDList) > 2000 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "asset_ids empty or > 2000")
	}
	// 保存渠道的上传渠道目录
	var fbUploadChannelInfo *pb.UploadChannelInfo
	var ggUploadChannelInfo *pb.UploadChannelInfo
	var tkUploadChannelInfo *pb.UploadChannelInfo
	var twUploadChannelInfo *pb.UploadChannelInfo
	var unityUploadChannelInfo *pb.UploadChannelInfo
	var snapchatUploadChannelInfo *pb.UploadChannelInfo
	var applovinUploadChannelInfo *pb.UploadChannelInfo
	for _, toChannel := range req.GetToChannels() {
		if toChannel.GetChannel() == constant.MediaFacebook {
			fbUploadChannelInfo = toChannel
		}

		if toChannel.GetChannel() == constant.MediaGoogle {
			ggUploadChannelInfo = toChannel
		}

		if toChannel.GetChannel() == constant.MediaTikTok {
			tkUploadChannelInfo = toChannel
		}

		if toChannel.GetChannel() == constant.MediaTwitter {
			twUploadChannelInfo = toChannel
		}

		if toChannel.GetChannel() == constant.MediaUnity {
			unityUploadChannelInfo = toChannel
			if toChannel.GetLanguage() == "" {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "language not set")
			}
		}

		if toChannel.GetChannel() == constant.MediaSnapchat {
			snapchatUploadChannelInfo = toChannel
		}

		if toChannel.GetChannel() == constant.MediaApplovin {
			applovinUploadChannelInfo = toChannel
			// 只有一个账号
			if toChannel.GetAccounts() == "" {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "account not set")
			}
			if toChannel.GetLanguage() == "" {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "language not set")
			}
			if toChannel.GetCampaignId() == "" {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "campaign id not set")
			}
			if toChannel.GetCreativeSetName() == "" {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "creative set name not set")
			}
		}
	}
	if fbUploadChannelInfo == nil &&
		ggUploadChannelInfo == nil &&
		tkUploadChannelInfo == nil &&
		twUploadChannelInfo == nil &&
		unityUploadChannelInfo == nil &&
		snapchatUploadChannelInfo == nil &&
		applovinUploadChannelInfo == nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "to_channles param error")
	}

	// ******** 不再检查上传配置是否已经配置
	// 在上传任务处理时检查

	// 创建根目录
	root, err := buildMediaRootDirectory(ctx, gameCode)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"AddUploadToChannelTask buildMediaRootDirectory err:%v", err)
	}
	// ---- 构建上传子目录 -----
	if fbUploadChannelInfo.GetMediaPath() != "" {
		fbUploadChannelInfo.MediaDirectoryId, err = buildMediaSubDirectory(ctx, gameCode, root, fbUploadChannelInfo.GetMediaPath())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddUploadToChannelTask buildMediaSubDirectory fbDirectory err:%v", err)
		}
	}
	if ggUploadChannelInfo.GetMediaPath() != "" {
		ggUploadChannelInfo.MediaDirectoryId, err = buildMediaSubDirectory(ctx, gameCode, root, ggUploadChannelInfo.GetMediaPath())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddUploadToChannelTask buildMediaSubDirectory ggDirectory err:%v", err)
		}
	}
	if tkUploadChannelInfo.GetMediaPath() != "" {
		tkUploadChannelInfo.MediaDirectoryId, err = buildMediaSubDirectory(ctx, gameCode, root, tkUploadChannelInfo.GetMediaPath())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "AddUploadToChannelTask buildMediaSubDirectory tkDirectory err:%v", err)
		}
	}
	if twUploadChannelInfo.GetMediaPath() != "" {
		twUploadChannelInfo.MediaDirectoryId, err = buildMediaSubDirectory(ctx, gameCode, root, twUploadChannelInfo.GetMediaPath())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "AddUploadToChannelTask buildMediaSubDirectory twDirectory err:%v", err)
		}
	}
	if unityUploadChannelInfo.GetMediaPath() != "" {
		unityUploadChannelInfo.MediaDirectoryId, err = buildMediaSubDirectory(ctx, gameCode, root, unityUploadChannelInfo.GetMediaPath())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddUploadToChannelTask buildMediaSubDirectory unityDirectory err:%v", err)
		}
	}
	if snapchatUploadChannelInfo.GetMediaPath() != "" {
		snapchatUploadChannelInfo.MediaDirectoryId, err = buildMediaSubDirectory(ctx, gameCode, root, snapchatUploadChannelInfo.GetMediaPath())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddUploadToChannelTask buildMediaSubDirectory snapchatDirectory err:%v", err)
		}
	}
	if applovinUploadChannelInfo.GetMediaPath() != "" {
		applovinUploadChannelInfo.MediaDirectoryId, err = buildMediaSubDirectory(ctx, gameCode, root, applovinUploadChannelInfo.GetMediaPath())
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddUploadToChannelTask buildMediaSubDirectory applovinDirectory err:%v", err)
		}
	}

	// ---- 构建上传子目录 结束 -----

	// applovin渠道先创建creative set
	if applovinUploadChannelInfo != nil {
		req := &pbApplovin.CreateCreativeSetReq{
			GameCode:  gameCode,
			AccountId: applovinUploadChannelInfo.GetAccounts(),
			CreativeSet: &pbApplovin.CreativeSet{
				CampaignId:      applovinUploadChannelInfo.GetCampaignId(),
				CreativeSetName: applovinUploadChannelInfo.GetCreativeSetName(),
				Languages:       []string{applovinUploadChannelInfo.GetLanguage()},
				Countries:       applovinUploadChannelInfo.GetCountries(),
			},
		}

		creativeSet, err := rpc.ApplovinCreateCreativeSet(ctx, creator, req)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_INTERNAL_ERROR),
				"AddUploadToChannelTask rpc.ApplovinCreateCreativeSet err:%v", err)
		}
		// 记录创建的creative set id
		applovinUploadChannelInfo.CreativeSetId = creativeSet.GetCreativeSetId()
	}

	// 记录素材名，后面处理下线日期需要用到
	var assetNames []string
	// 由于前端一次可以提交上千个任务，这里分批处理(每次处理200个)
	chunks := funk.ChunkStrings(assetIDList, 200)
	for _, tmpAssetIDList := range chunks {
		log.DebugContextf(ctx, "AddUploadToChannelTask deal tmpAssetIDList:%v", tmpAssetIDList)
		// 查素材overview
		assetOverviewList, err := data.QueryCreativeOverviewByIDList(ctx, gameCode, tmpAssetIDList)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddUploadToChannelTask data.QueryCreativeOverviewByIDList err:%v", err)
		}
		assetOveviewMap := make(map[string]*model.CreativeOverview)
		for _, asset := range assetOverviewList {
			assetOveviewMap[asset.AssetID] = asset
		}

		// 查历史任务
		oldTasks, err := data.QueryUploadTasksByAssetIDList(ctx, gameCode, tmpAssetIDList)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"AddUploadToChannelTask data.QueryUploadTasksByAssetIDList err:%v", err)
		}
		oldTaskMap := make(map[string]*model.CreativeMediaUploadTask)
		for _, task := range oldTasks {
			key := fmt.Sprintf("%v_%v", task.AssetID, task.Channel)
			oldTaskMap[key] = task
		}

		// 保持前端传入的素材顺序先后生成任务
		for _, assetID := range tmpAssetIDList {
			asset := assetOveviewMap[assetID]
			if asset == nil {
				continue
			}

			var tasks []*model.CreativeMediaUploadTask
			assetNames = append(assetNames, asset.AssetName)
			// 上传到facebook
			if fbUploadChannelInfo.GetMediaDirectoryId() != "" {
				task := genCreativeMediaUploadTask(ctx, fbUploadChannelInfo, asset, oldTaskMap, req)
				if task != nil {
					tasks = append(tasks, task)
				}
			}

			// 上传到google
			if ggUploadChannelInfo.GetMediaDirectoryId() != "" {
				task := genCreativeMediaUploadTask(ctx, ggUploadChannelInfo, asset, oldTaskMap, req)
				if task != nil {
					tasks = append(tasks, task)
				}
			}

			// 上传到tiktok
			if tkUploadChannelInfo.GetMediaDirectoryId() != "" {
				task := genCreativeMediaUploadTask(ctx, tkUploadChannelInfo, asset, oldTaskMap, req)
				if task != nil {
					tasks = append(tasks, task)
				}
			}

			// 上传到twitter
			if twUploadChannelInfo.GetMediaDirectoryId() != "" {
				task := genCreativeMediaUploadTask(ctx, twUploadChannelInfo, asset, oldTaskMap, req)
				if task != nil {
					tasks = append(tasks, task)
				}
			}

			// 上传到unity
			if unityUploadChannelInfo.GetMediaDirectoryId() != "" {
				task := genCreativeMediaUploadTask(ctx, unityUploadChannelInfo, asset, oldTaskMap, req)
				if task != nil {
					tasks = append(tasks, task)
				}
			}

			// 上传到snapchat
			if snapchatUploadChannelInfo.GetMediaDirectoryId() != "" {
				task := genCreativeMediaUploadTask(ctx, snapchatUploadChannelInfo, asset, oldTaskMap, req)
				if task != nil {
					tasks = append(tasks, task)
				}
			}

			// 上传到applovin
			if applovinUploadChannelInfo.GetMediaDirectoryId() != "" {
				task := genCreativeMediaUploadTask(ctx, applovinUploadChannelInfo, asset, oldTaskMap, req)
				if task != nil {
					tasks = append(tasks, task)
				}
			}

			err = data.BatchUpsertMediaUploadTask(ctx, gameCode, tasks)
			if err != nil {
				return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
					"AddUploadToChannelTask data.BatchUpsertMediaUploadTask err:%v", err)
			}
		}
	}

	// 如果配置了下线日期，需要关联标签规则下线日期
	// 根据素材名抽取标签规则前缀处理
	offlineDate := req.GetOfflineDate()
	if offlineDate != "" {
		linkLabelRuleOfflineDate(ctx, gameCode, creator, offlineDate, assetNames)
	}
	return nil
}

// CancelUploadToChannelTask 取消素材上传渠道任务
func CancelUploadToChannelTask(ctx *gin.Context, req *pb.CancelUploadToChannelTaskReq, rsp *pb.CancelUploadToChannelTaskRsp) error {
	gameCode := req.GetGameCode()
	if gameCode == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code error")
	}
	var taskIDList []int64
	for _, taskID := range req.GetTaskIds() {
		taskIDList = append(taskIDList, cast.ToInt64(taskID))
	}
	if len(taskIDList) < 0 || len(taskIDList) > 200 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "task_ids error")
	}

	err := data.CancelUploadTaskByTaskIDList(ctx, gameCode, taskIDList)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"CancelUploadToChannelTask data.CancelUploadTaskByTaskIDList err:%v", err)
	}
	return nil
}

// ResumeUploadToChannelTask 重启素材上传渠道任务
func ResumeUploadToChannelTask(ctx *gin.Context,
	req *pb.ResumeUploadToChannelTaskReq, rsp *pb.ResumeUploadToChannelTaskRsp) error {
	gameCode := req.GetGameCode()
	if gameCode == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code error")
	}
	var taskIDList []int64
	for _, taskID := range req.GetTaskIds() {
		taskIDList = append(taskIDList, cast.ToInt64(taskID))
	}
	if len(taskIDList) < 0 || len(taskIDList) > 200 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "task_ids error")
	}

	err := data.ResumeUploadTaskByTaskIDList(ctx, gameCode, taskIDList)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"CancelUploadToChannelTask data.ResumeUploadTaskByTaskIDList err:%v", err)
	}
	return nil
}

// -------- 内部函数 -----------

// buildMediaRootDirectory 构建上传渠道跟目录，返回根目录信息
func buildMediaRootDirectory(ctx context.Context, gameCode string) (*model.CreativeMediaDirectory, error) {
	now := utils.GetNowStr()
	// ---  创建根目录 -----
	// 构建上传根目录
	root := &model.CreativeMediaDirectory{
		ID:                   gameCode,
		Name:                 gameCode,
		ParentID:             "", //arthubRootDirectory.ParentId,
		ParentName:           "", //arthubRootDirectory.ParentName,
		CreateDate:           now,
		UpdateDate:           now,
		DirectChildCount:     0,
		TotalLeafCount:       0,
		DirectDirectoryCount: 0,
		FullPathName:         "",
		FullPathID:           "",
		Type:                 "depot",
	}
	err := data.UpsertRootCreativeMediaDirectory(ctx, gameCode, root)
	if err != nil {
		return nil, errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"buildMediaRootDirectory data.UpsertRootCreativeMediaDirectory err:%v", err)
	}

	return root, nil
}

// buildMediaSubDirectory 构建上传渠道子目录，返回目录id
func buildMediaSubDirectory(ctx context.Context, gameCode string, root *model.CreativeMediaDirectory, mediaPath string) (string, error) {
	now := utils.GetNowStr()
	parentID := root.ID
	parentName := root.Name
	fullPathID := root.ID
	fullPathName := root.Name
	mediaDirectoryType := arthub.DIRECTORY_TYPR_PROJECT //一级目录

	// 需要支持多级上传子目录
	directories := strings.Split(strings.TrimSuffix(mediaPath, "/"), "/")
	for _, directoryName := range directories {
		if directoryName == "" {
			continue
		}
		mediaDirectory := &model.CreativeMediaDirectory{
			Name:                 directoryName,
			ParentID:             parentID,
			ParentName:           parentName,
			CreateDate:           now,
			UpdateDate:           now,
			DirectChildCount:     0,
			TotalLeafCount:       0,
			DirectDirectoryCount: 0,
			FullPathName:         fullPathName,
			FullPathID:           fullPathID,
			Type:                 mediaDirectoryType,
		}

		log.DebugContextf(ctx, "begin upsert mediaDirectory:%+v", mediaDirectory)
		err := data.UpsertCreativeMediaDirectory(ctx, gameCode, mediaDirectory)
		if err != nil {
			return "", errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
				"buildMediaSubDirectory data.UpsertCreativeMediaDirectory err:%v", err)
		}
		log.DebugContextf(ctx, "after upsert mediaDirectory:%+v", mediaDirectory)

		fullPathID = fmt.Sprintf("%s,%s", fullPathID, mediaDirectory.ID)
		fullPathName = fmt.Sprintf("%s,%s", fullPathName, mediaDirectory.Name)
		parentID = mediaDirectory.ID
		parentName = mediaDirectory.Name
		mediaDirectoryType = arthub.DIRECTORY_TYPR_DIRECTORY
	}

	return parentID, nil
}

// checkGameUploadSettingForGoogle google上传配置检查
func checkGameUploadSettingForGoogle(ctx context.Context, gameCode, mediaPath string) error {
	if len(mediaPath) == 0 { // 不需要检查
		return nil
	}

	// 使用google td配置
	var rows []*model.ServerConfig
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(&model.ServerConfig{})
	query.Where("server = ?", "google_advertise")
	query.Where("key = ?", "DEFAULT_CUSTOMER_ID")
	query.Where("second_key = ?", gameCode)

	err := query.Select(&rows)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"checkGameUploadSettingForGoogle select err:%v", err)
	}

	if len(rows) == 0 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "upload setting Google account not configured")
	}

	return nil
}

// checkGameUploadSettingForFacebook facebook上传配置检查
func checkGameUploadSettingForFacebook(ctx context.Context, gameCode, mediaPath string) error {
	if len(mediaPath) == 0 { // 不需要检查
		return nil
	}

	var hasFBToken, hasFBAcount bool

	// 使用facebook td配置
	var rows []*model.ServerConfig
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(&model.ServerConfig{})
	query.Where("server = ?", "facebook_advertise")
	query.WhereIn("key in (?)", []string{"ACCOUNTS", "ACCESS_TOKEN"})
	query.Where("second_key = ?", gameCode)

	err := query.Select(&rows)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB),
			"checkGameUploadSettingForFacebook select err:%v", err)
	}

	for _, row := range rows {
		if row.Key == "ACCESS_TOKEN" {
			// facebook的token
			hasFBToken = true
		} else if row.Key == "ACCOUNTS" {
			// facebook账号
			hasFBAcount = true
		}
	}

	if !hasFBToken {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "upload setting Facebook token not configured")
	}
	if !hasFBAcount {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "upload setting Facebook account not configured")
	}

	return nil
}

// checkGameUploadSettingForTiktok tiktok上传配置检查
func checkGameUploadSettingForTiktok(ctx context.Context, gameCode, mediaPath string) error {
	if len(mediaPath) == 0 { // 不需要检查
		return nil
	}

	var records []model.MediaAccount
	tableName := model.GetMediaAccountTableName()
	query := postgresql.GetDBWithContext(ctx).Model(&records).Table(tableName)
	query.Column("account_config")
	query.Where("game_code=?", gameCode)
	query.Where("media=?", constant.MediaNameTikTok)

	err := query.Select()
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "select TikTok account info failed: %s", err)
	}

	if len(records) == 0 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_CONFIG), "no account info find for %s", gameCode)
	}

	for _, record := range records {
		var account model.TikTokAccountConfig
		err := json.Unmarshal([]byte(record.AccountConfig), &account)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_JSON), "unmarshal account config failed: %s", err)
		}

		if len(account.Authorizations) == 0 {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_CONFIG), "no authorizations find for tiktok")
		}

		if len(account.Authorizations[0].AccessToken) == 0 { // 第一个token为默认token, 必填
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_CONFIG), "no access token find for tiktok")
		}
	}

	return nil
}

// checkGameUploadSettingForTwitter twitter上传配置检查
func checkGameUploadSettingForTwitter(ctx context.Context, gameCode, mediaPath string) error {
	if len(mediaPath) == 0 { // 不需要检查
		return nil
	}

	var records []model.MediaAccount
	tableName := model.GetMediaAccountTableName()
	query := postgresql.GetDBWithContext(ctx).Model(&records).Table(tableName)
	query.Column("account_config")
	query.Where("game_code=?", gameCode)
	query.Where("media=?", constant.MediaNameTwitter)

	err := query.Select()
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "select Twitter account info failed: %s", err)
	}

	if len(records) == 0 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_CONFIG), "no account info find for %s", gameCode)
	}

	for _, record := range records {
		var account model.TwitterAccountConfig
		err := json.Unmarshal([]byte(record.AccountConfig), &account)
		if err != nil {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_JSON), "unmarshal account config failed: %s", err)
		}

		if len(account.Authorizations) == 0 {
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_CONFIG), "no authorizations find for twitter")
		}

		firstAuth := account.Authorizations[0]
		if len(firstAuth.AccessToken) == 0 || len(firstAuth.TokenSecret) == 0 || len(firstAuth.ConsumerKey) == 0 || len(firstAuth.ConsumerSecret) == 0 { // 第一个token为默认token, 必填
			return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_CONFIG), "no access token find for twitter")
		}
	}

	return nil
}

// 检查unity上传配置
func checkGameUploadSettingForUnity(ctx context.Context, gameCode, mediaPath string) error {
	if len(mediaPath) == 0 { // 不需要检查
		return nil
	}

	accounts, err := commonRepo.QueryUnityMediaAccounts(ctx, gameCode)
	if err != nil {
		return err
	}
	if len(accounts) == 0 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_CONFIG), "no account info find for %s", gameCode)
	}

	return nil
}

// 关联标签规则下线日期
func linkLabelRuleOfflineDate(ctx context.Context,
	gameCode, creator, offlineDate string, assetNames []string) {
	depot, err := data.GetDepotWithContext(ctx, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "linkLabelRuleOfflineDate data.GetDepotWithContext err:%v", err)
		return
	}
	// 获取素材库的标签规则类型和分割符
	ruleType, _ := repo.GetDepotLabelRuleType(ctx, depot)
	splitReg, err := repo.GetDepotSerialSplitReg(ctx, depot)
	if err != nil {
		log.ErrorContextf(ctx, "linkLabelRuleOfflineDate repo.GetDepotSerialSplitReg err:%v", err)
		return
	}

	// 记录规则是否重复
	uniq := make(map[string]bool)
	var rules []*model.TbAssetLabelRule
	for _, assetName := range assetNames {
		// 抽取素材名规则前缀
		match := getRuleRegMatchName(gameCode, ruleType, splitReg, assetName)
		if match != "" {
			key := fmt.Sprintf("%v_%v", match, ruleType)
			if !uniq[key] {
				r := &model.TbAssetLabelRule{
					GameCode:    gameCode,
					Rule:        match,
					Type:        int32(ruleType),
					CreateUser:  fmt.Sprintf("upload_task_%v", creator),
					CreateTime:  time.Now(),
					UpdateUser:  fmt.Sprintf("upload_task_%v", creator),
					UpdateTime:  time.Now(),
					OfflineDate: offlineDate,
				}
				log.DebugContextf(ctx, "linkLabelRuleOfflineDate get rule:%+v", r)

				rules = append(rules, r)
			}
			uniq[key] = true
		}
	}

	// 只更新offline_date字段到数据库
	fields := []string{"offline_date", "update_user", "update_time"}
	err = repo.UpsertBatchLabelRuleByFields(ctx, rules, fields)
	if err != nil {
		log.ErrorContextf(ctx, "linkLabelRuleOfflineDate UpsertBatchLabelRuleByFields failed: %v", err)
	}

	return
}

// 生成上传素材任务, 手动上传任务正在运行忽略掉, 返回nil
func genCreativeMediaUploadTask(ctx context.Context,
	channelInfo *pb.UploadChannelInfo, asset *model.CreativeOverview,
	oldTaskMap map[string]*model.CreativeMediaUploadTask, req *pb.AddUploadToChannelTaskReq) *model.CreativeMediaUploadTask {
	// 标识是自动化提交的任务
	fromAutomatic := req.GetAutomaticSyncTaskRuleId() != 0

	oldAccounts := ""
	// 非applovin渠道才判断是否相同任务正在运行
	// applovin渠道每次提交的任务都是创建一个新的creative set
	if channelInfo.GetChannel() != constant.MediaApplovin {
		key := fmt.Sprintf("%v_%v", asset.AssetID, channelInfo.GetChannel())
		if t, ok := oldTaskMap[key]; ok {
			// 手动上传的，任务正在运行忽略掉
			if !fromAutomatic && t.Status == 1 {
				log.DebugContextf(ctx, "AddUploadToChannelTask channel:%v assetID:%v, task is running, task:%+v",
					channelInfo.GetChannel(), asset.AssetID, t)
				return nil
			}

			oldAccounts = t.Accounts
		}
	}

	// 上传账号从具体的渠道配置中取
	allAccounts := channelInfo.GetAccounts()
	// 兼容前端旧版本
	if allAccounts == "" {
		allAccounts = req.GetAccounts()
	}
	if fromAutomatic {
		// 自动化任务，将上传账号合并到一起
		allAccounts = contactUploadAccounts(oldAccounts, allAccounts)
	}

	now := data.GetNowStrOrUTC()
	task := &model.CreativeMediaUploadTask{
		AssetID:       asset.AssetID,
		Channel:       cast.ToInt32(channelInfo.GetChannel()),
		AssetName:     asset.AssetName,
		FormatType:    cast.ToInt32(asset.FormatType),
		DirectoryID:   channelInfo.GetMediaDirectoryId(),
		DirectoryName: channelInfo.GetMediaPath(),
		FullPathID:    asset.FullPathID,
		FullPathName:  asset.FullPathName,
		Status:        1, // 1-正在上传，2-上传成功，3-上传失败
		Retry:         0, // 重置重试次数
		ErrMemo:       "",
		ErrDetail:     "",
		CreateTime:    now,
		UpdateTime:    now,
		Country:       req.GetCountry(),
		Creator:       req.GetCreator(),
		Accounts:      allAccounts,
		NotifyDays:    cast.ToInt32(req.GetNotifyDays()),
		NotifyTryCnt:  0,
		Language:      channelInfo.GetLanguage(),
		SortNum:       0,

		AutomaticSyncTaskRuleID: req.GetAutomaticSyncTaskRuleId(),
	}

	// applovin渠道，特殊逻辑
	if channelInfo.GetChannel() == constant.MediaApplovin {
		// assetID 格式 {asset_id}_applovin_{creative_set_id}
		task.AssetID = model.PackApplovinUploadTaskAssetID(asset.AssetID, channelInfo.GetCreativeSetId())
		// 只有单个account
		task.Accounts = channelInfo.GetAccounts()
		// 记录额外信息， 处理上传任务时会用到
		task.ExtraInfo = model.UploadTaskExtraInfo{
			CampaignID:      channelInfo.GetCampaignId(),
			CreativeSetID:   channelInfo.GetCreativeSetId(),
			CreativeSetName: channelInfo.GetCreativeSetName(),
			Countries:       channelInfo.GetCountries(),
		}
	}

	// 目前提醒天数生效的话，只提醒一次
	if task.NotifyDays > 0 {
		task.NotifyTryCnt = 1
	}

	return task
}

func contactUploadAccounts(a1 string, a2 string) string {
	if a1 == "" {
		return a2
	}
	if a2 == "" {
		return a1
	}

	l1 := strings.Split(a1, ",")
	l2 := strings.Split(a2, ",")

	all := append(l1, l2...)
	all = funk.UniqString(all)
	return strings.Join(all, ",")
}
