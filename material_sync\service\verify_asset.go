package service

import (
	ckmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/clickhouse"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_sync"
	"github.com/gin-gonic/gin"
)

// SayHi 设置素材机器标签及封面信息
func VerifyAsset(ctx *gin.Context, req *pb.VerifyAssetReq, rsp *pb.VerifyAssetRsp) error {
	game_code := ctx.Request.Header.Get("game")

	pg_query := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendOnline{})
	table_name := pgmodel.GetCreativeRecommendOnlineTableName(game_code)
	pg_query.Table(table_name)
	pg_query.Where("asset_id=?", req.GetAssetId())
	pg_query.Limit(1)

	var online pgmodel.CreativeRecommendOnline
	err := pg_query.Select(&online)
	if err != nil {
		log.ErrorContextf(ctx, "select recommend online failed: %s", err)
		return err
	}

	pg_query = postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeOverview{})
	table_name = pgmodel.GetCreativeOverviewTableName(game_code)
	pg_query.Table(table_name)
	pg_query.Where("asset_id=?", online.AssetId)
	pg_query.Limit(1)
	var overview pgmodel.CreativeOverview
	err = pg_query.Select(&overview)
	if err != nil {
		log.ErrorContextf(ctx, "select creative overview failed: %s", err)
	}

	ck_query := clickhouse.GetGORMDB(ctx).Model(ckmodel.CreativeAnalysisPivotNew{})
	table_name = conf.GetBizConf().CreativeAnalysisPivotNew
	ck_query.Table(table_name)
	ck_query.Where("account_id=?", online.ChannelAccountID)
	ck_query.Where("asset_id=?", online.ChannelAssetId)
	ck_query.Limit(1)
	var pivot ckmodel.CreativeAnalysisPivotNew
	result := ck_query.Find(&pivot)
	if result.Error != nil {
		log.ErrorContextf(ctx, "select clickhouse pivot failed: %s", err)
	}

	rsp.FullPathName = overview.FullPathName
	rsp.YoutubeId = pivot.YoutubeId
	rsp.ChannelAccountId = pivot.AccountId
	rsp.ChannelAssetId = pivot.AssetId
	rsp.AssetName = overview.AssetName

	return nil
}
