package data

import (
	"context"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// UpsertCreativeLabels 插入更新游戏的一二级标签
func UpsertCreativeLabels(ctx context.Context, rows []*model.CreativeLabel) error {
	query := postgresql.GetDBWithContext(ctx).Model(&rows)
	query.OnConflict("(id) DO UPDATE")
	query.Set("name=EXCLUDED.name")
	query.Set("options=EXCLUDED.options")
	query.Set("updated_at=EXCLUDED.updated_at")
	query.Set("updater=EXCLUDED.updater")
	// 其他不改

	_, err := query.Insert()
	return err
}
