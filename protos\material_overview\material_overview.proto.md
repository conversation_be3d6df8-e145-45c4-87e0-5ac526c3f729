# 接口说明

## 目录:

|接口|请求方法|路径|
|--|--|--|
|[获取顶部汇总数据](#/api/v1/material_overview/general_data)|`POST`|`/api/v1/material_overview/general_data`|
|[较上周新增-下降数据](#/api/v1/material_overview/incr_decr_data)|`POST`|`/api/v1/material_overview/incr_decr_data`|
|[素材主题分析](#/api/v1/material_overview/asset_theme_analyze)|`POST`|`/api/v1/material_overview/asset_theme_analyze`|
|[素材观看时长分析](#/api/v1/material_overview/video_played_time_analysis)|`POST`|`/api/v1/material_overview/video_played_time_analysis`|
## 结构说明:

### <span id="/api/v1/material_overview/general_data">获取顶部汇总数据</span>

|项目|值|
|--|--|
|请求方法|`POST`|
|请求路径|`/api/v1/material_overview/general_data`|
|请求结构说明|[req](#/api/v1/material_overview/general_data_req)|
|返回结构说明|[rsp](#/api/v1/material_overview/general_data_rsp)|
#### <span id="/api/v1/material_overview/general_data_req">请求数据 Req</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{}
}

```

- 示例

```json
{}

```

#### <span id="/api/v1/material_overview/general_data_rsp">返回数据 Rsp</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "result":{
            "type":"object",
            "description":"",
            "properties":{
                "error_code":{
                    "type":"integer",
                    "description":""
                },
                "error_message":{
                    "type":"string",
                    "description":""
                }
            }
        },
        "iegg_total_material":{
            "type":"integer",
            "description":""
        },
        "online_num_list":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "date":{
                        "type":"integer",
                        "description":"日期，型如20220210"
                    },
                    "num":{
                        "type":"float",
                        "description":"浮点数据"
                    }
                }
            },
            "type":"array",
            "description":""
        },
        "impression_num_list":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "date":{
                        "type":"integer",
                        "description":"日期，型如20220210"
                    },
                    "num":{
                        "type":"float",
                        "description":"浮点数据"
                    }
                }
            },
            "type":"array",
            "description":""
        }
    }
}

```

- 示例

```json
{
    "online_num_list":[
        {
            "date":0,
            "num":0.0
        }
    ],
    "iegg_total_material":0,
    "result":{
        "error_message":"",
        "error_code":0
    },
    "impression_num_list":[
        {
            "date":0,
            "num":0.0
        }
    ]
}

```

### <span id="/api/v1/material_overview/incr_decr_data">较上周新增-下降数据</span>

|项目|值|
|--|--|
|请求方法|`POST`|
|请求路径|`/api/v1/material_overview/incr_decr_data`|
|请求结构说明|[req](#/api/v1/material_overview/incr_decr_data_req)|
|返回结构说明|[rsp](#/api/v1/material_overview/incr_decr_data_rsp)|
#### <span id="/api/v1/material_overview/incr_decr_data_req">请求数据 Req</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "country":{
            "items":{
                "type":"string",
                "description":"国家筛选列表，全球不用传"
            },
            "type":"array",
            "description":"国家筛选列表，全球不用传"
        }
    }
}

```

- 示例

```json
{
    "country":[
        ""
    ]
}

```

#### <span id="/api/v1/material_overview/incr_decr_data_rsp">返回数据 Rsp</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "result":{
            "type":"object",
            "description":"国家筛选列表，全球不用传",
            "properties":{
                "error_code":{
                    "type":"integer",
                    "description":""
                },
                "error_message":{
                    "type":"string",
                    "description":""
                }
            }
        },
        "iegg_total_material":{
            "type":"integer",
            "description":""
        },
        "incr_num":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "date":{
                        "type":"integer",
                        "description":"日期，型如20220210"
                    },
                    "num":{
                        "type":"float",
                        "description":"浮点数据"
                    }
                }
            },
            "type":"array",
            "description":""
        },
        "decr_num":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "date":{
                        "type":"integer",
                        "description":"日期，型如20220210"
                    },
                    "num":{
                        "type":"float",
                        "description":"浮点数据"
                    }
                }
            },
            "type":"array",
            "description":""
        },
        "country_list":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "country_code":{
                        "type":"string",
                        "description":"国家缩写，用于请求"
                    },
                    "en_name":{
                        "type":"string",
                        "description":"国家英文名"
                    },
                    "ch_name":{
                        "type":"string",
                        "description":"国家中文名"
                    }
                }
            },
            "type":"array",
            "description":""
        }
    }
}

```

- 示例

```json
{
    "country_list":[
        {
            "en_name":"",
            "ch_name":"",
            "country_code":""
        }
    ],
    "iegg_total_material":0,
    "decr_num":[
        {
            "date":0,
            "num":0.0
        }
    ],
    "result":{
        "error_message":"",
        "error_code":0
    },
    "incr_num":[
        {
            "date":0,
            "num":0.0
        }
    ]
}

```

### <span id="/api/v1/material_overview/asset_theme_analyze">素材主题分析</span>

|项目|值|
|--|--|
|请求方法|`POST`|
|请求路径|`/api/v1/material_overview/asset_theme_analyze`|
|请求结构说明|[req](#/api/v1/material_overview/asset_theme_analyze_req)|
|返回结构说明|[rsp](#/api/v1/material_overview/asset_theme_analyze_rsp)|
#### <span id="/api/v1/material_overview/asset_theme_analyze_req">请求数据 Req</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "label_type":{
            "type":"integer",
            "description":"请求的素材主题类型1-人工标签2-机器标签"
        }
    }
}

```

- 示例

```json
{
    "label_type":0
}

```

#### <span id="/api/v1/material_overview/asset_theme_analyze_rsp">返回数据 Rsp</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "result":{
            "type":"object",
            "description":"请求的素材主题类型1-人工标签2-机器标签",
            "properties":{
                "error_code":{
                    "type":"integer",
                    "description":""
                },
                "error_message":{
                    "type":"string",
                    "description":""
                }
            }
        },
        "label_list":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "name":{
                        "type":"string",
                        "description":"表横坐标名称"
                    },
                    "data_list":{
                        "items":{
                            "type":"object",
                            "description":"数据类型列表",
                            "properties":{
                                "name":{
                                    "type":"string",
                                    "description":"数据节点名称"
                                },
                                "data_type":{
                                    "type":"integer",
                                    "description":"数据类型0-整数1-float"
                                },
                                "int_data":{
                                    "type":"integer",
                                    "description":"int数据,data_type=0时有效"
                                },
                                "float_data":{
                                    "type":"float",
                                    "description":"float数据,data_type=1时有效"
                                }
                            }
                        },
                        "type":"array",
                        "description":"数据类型列表"
                    }
                }
            },
            "type":"array",
            "description":""
        }
    }
}

```

- 示例

```json
{
    "label_list":[
        {
            "data_list":[
                {
                    "int_data":0,
                    "name":"",
                    "data_type":0,
                    "float_data":0.0
                }
            ],
            "name":""
        }
    ],
    "result":{
        "error_message":"",
        "error_code":0
    }
}

```

### <span id="/api/v1/material_overview/video_played_time_analysis">素材观看时长分析</span>

|项目|值|
|--|--|
|请求方法|`POST`|
|请求路径|`/api/v1/material_overview/video_played_time_analysis`|
|请求结构说明|[req](#/api/v1/material_overview/video_played_time_analysis_req)|
|返回结构说明|[rsp](#/api/v1/material_overview/video_played_time_analysis_rsp)|
#### <span id="/api/v1/material_overview/video_played_time_analysis_req">请求数据 Req</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "country_code_list":{
            "items":{
                "type":"string",
                "description":"国家筛选列表，空列表表示全部国家"
            },
            "type":"array",
            "description":"国家筛选列表，空列表表示全部国家"
        },
        "language_list":{
            "items":{
                "type":"string",
                "description":"语言列表,空列表表示全部语言"
            },
            "type":"array",
            "description":"语言列表,空列表表示全部语言"
        },
        "need_data_list":{
            "items":{
                "type":"integer",
                "description":"需要的数据,1表示watch_count_distribution,2表示country_watch_count_distribution,3表示country_watch_duration"
            },
            "type":"array",
            "description":"需要的数据,1表示watch_count_distribution,2表示country_watch_count_distribution,3表示country_watch_duration"
        }
    }
}

```

- 示例

```json
{
    "country_code_list":[
        ""
    ],
    "language_list":[
        ""
    ],
    "need_data_list":[
        0
    ]
}

```

#### <span id="/api/v1/material_overview/video_played_time_analysis_rsp">返回数据 Rsp</span>

- 数据结构

```json
{
    "type":"object",
    "properties":{
        "result":{
            "type":"object",
            "description":"国家筛选列表，空列表表示全部国家",
            "properties":{
                "error_code":{
                    "type":"integer",
                    "description":""
                },
                "error_message":{
                    "type":"string",
                    "description":""
                }
            }
        },
        "watch_count_distribution":{
            "items":{
                "type":"object",
                "description":"语言列表,空列表表示全部语言",
                "properties":{
                    "name":{
                        "type":"string",
                        "description":"表横坐标名称"
                    },
                    "data_list":{
                        "items":{
                            "type":"object",
                            "description":"数据类型列表",
                            "properties":{
                                "name":{
                                    "type":"string",
                                    "description":"数据节点名称"
                                },
                                "data_type":{
                                    "type":"integer",
                                    "description":"数据类型0-整数1-float"
                                },
                                "int_data":{
                                    "type":"integer",
                                    "description":"int数据,data_type=0时有效"
                                },
                                "float_data":{
                                    "type":"float",
                                    "description":"float数据,data_type=1时有效"
                                }
                            }
                        },
                        "type":"array",
                        "description":"数据类型列表"
                    }
                }
            },
            "type":"array",
            "description":"语言列表,空列表表示全部语言"
        },
        "country_watch_count_distribution":{
            "items":{
                "type":"object",
                "description":"需要的数据,1表示watch_count_distribution,2表示country_watch_count_distribution,3表示country_watch_duration",
                "properties":{
                    "name":{
                        "type":"string",
                        "description":"表横坐标名称"
                    },
                    "data_list":{
                        "items":{
                            "type":"object",
                            "description":"数据类型列表",
                            "properties":{
                                "name":{
                                    "type":"string",
                                    "description":"数据节点名称"
                                },
                                "data_type":{
                                    "type":"integer",
                                    "description":"数据类型0-整数1-float"
                                },
                                "int_data":{
                                    "type":"integer",
                                    "description":"int数据,data_type=0时有效"
                                },
                                "float_data":{
                                    "type":"float",
                                    "description":"float数据,data_type=1时有效"
                                }
                            }
                        },
                        "type":"array",
                        "description":"数据类型列表"
                    }
                }
            },
            "type":"array",
            "description":"需要的数据,1表示watch_count_distribution,2表示country_watch_count_distribution,3表示country_watch_duration"
        },
        "country_watch_duration":{
            "items":{
                "type":"object",
                "description":"",
                "properties":{
                    "name":{
                        "type":"string",
                        "description":"表横坐标名称"
                    },
                    "data_list":{
                        "items":{
                            "type":"object",
                            "description":"数据类型列表",
                            "properties":{
                                "name":{
                                    "type":"string",
                                    "description":"数据节点名称"
                                },
                                "data_type":{
                                    "type":"integer",
                                    "description":"数据类型0-整数1-float"
                                },
                                "int_data":{
                                    "type":"integer",
                                    "description":"int数据,data_type=0时有效"
                                },
                                "float_data":{
                                    "type":"float",
                                    "description":"float数据,data_type=1时有效"
                                }
                            }
                        },
                        "type":"array",
                        "description":"数据类型列表"
                    }
                }
            },
            "type":"array",
            "description":""
        }
    }
}

```

- 示例

```json
{
    "watch_count_distribution":[
        {
            "data_list":[
                {
                    "int_data":0,
                    "name":"",
                    "data_type":0,
                    "float_data":0.0
                }
            ],
            "name":""
        }
    ],
    "country_watch_count_distribution":[
        {
            "data_list":[
                {
                    "int_data":0,
                    "name":"",
                    "data_type":0,
                    "float_data":0.0
                }
            ],
            "name":""
        }
    ],
    "country_watch_duration":[
        {
            "data_list":[
                {
                    "int_data":0,
                    "name":"",
                    "data_type":0,
                    "float_data":0.0
                }
            ],
            "name":""
        }
    ],
    "result":{
        "error_message":"",
        "error_code":0
    }
}

```

