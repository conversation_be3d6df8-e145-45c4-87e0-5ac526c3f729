package service

import (
	"context"
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/conf"
	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/data"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/msgbot"
)

// CheckAssetCampaignStatus 定时检查配置的素材上传campaign状态
func CheckAssetCampaignStatus(ctx context.Context) {
	campaigns := conf.GetAllUploadAssetCampaign()

	// 检查素材上传campaign是否是开启状态
	for _, campaign := range campaigns {
		log.DebugContextf(ctx, "CheckAssetCampaignStatusByCampaign start campaign:+%v", campaign)

		account, err := data.GetOneMediaAccounts(ctx, campaign.GameCode, campaign.AccountID)
		if err != nil {
			log.ErrorContextf(ctx, "CheckAssetCampaignStatusByCampaign fail to GetOneMediaAccounts, err:%v", err)
			continue
		}
		client := newApplovinAPIClient(account)
		apiCampaign, err := client.GetCampaign(ctx, campaign.CampaignID)
		if err != nil {
			log.ErrorContextf(ctx, "CheckAssetCampaignStatusByCampaign fail to GetCampaign, err:%v", err)
			continue
		}
		// 更新campaign name
		campaign.CampaignName = apiCampaign.Name

		// 开启状态， 告警
		if apiCampaign.Status {
			alterAssetCampaignActive(ctx, campaign)
		}
	}
}

func alterAssetCampaignActive(ctx context.Context, c *conf.UploadAssetCampaign) {
	topic := fmt.Sprintf("applovin campaign notify")
	nameContents := []msgbot.LabelContent{
		{
			Label:   "game code",
			Content: c.GameCode,
		},
		{
			Label:   "account id",
			Content: c.AccountID,
		},
		{
			Label:   "campaign id",
			Content: c.CampaignID,
		},
		{
			Label:   "campaign name",
			Content: c.CampaignName,
		},
		{
			Label:   "error",
			Content: "campaign is active",
		},
	}

	markdownMsg := msgbot.BuildAixAlarmMarkdown(topic, nameContents, nil)
	newErr := msgbot.NewDefaultLogMsgBot().SendGroupMarkdownMsg(ctx, markdownMsg)
	if newErr != nil {
		log.ErrorContextf(ctx, "alterAssetCampaignActive SendGroupMarkdownMsg error:%v", newErr)
	}

	return
}
