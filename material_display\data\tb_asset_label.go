package data

import (
	"context"

	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// GetAssetsLabels 拉取多个素材的标签信息，返回map
func GetAssetsLabels(ctx context.Context, gameCode string, ids []string) (map[string][]*pb.AssetLabel, error) {
	labels, err := repo.GetAssetsLabels(ctx, gameCode, ids)
	if err != nil {
		return nil, err
	}

	rlt := make(map[string][]*pb.AssetLabel)
	for _, l := range labels {
		t := &pb.AssetLabel{
			LabelName:   l.LabelName,
			FirstLabel:  l.FirstLabel,
			SecondLabel: l.SecondLabel,
		}
		rlt[l.AssetId] = append(rlt[l.AssetId], t)
	}

	return rlt, nil
}
