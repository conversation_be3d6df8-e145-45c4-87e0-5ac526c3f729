server: # 服务配置
  http_port: 8080 # http服务端口
  read_timeout: 60 # 读超时
  write_timeout: 60 # 写超时
  run_mode: "debug" # 运行模式, debug或release
log: # 日志配置
  level: "debug" # 日志级别
  file_name: "logs/serverlog.log" # 日志名称
  max_size: 512 # 日志文件大小, 单位MB
  max_backup: 10 # 日志文件数量
  max_age: 30 # 最长保留时间, 单位天
  stdout: true # 是否需要标准输出
  json_format: false # 打印日志格式, 是否需要使用json
database: # 数据库相关配置
  postgresql: # pg配置
    url: "postgres://postgres:123456@127.0.0.1:5432/local?sslmode=disable" # URL
  redis: # redis配置
    addr: "127.0.0.1:6379"
    password: ""

# 定时任务是否禁用(全局标志)
cron_task_disable: false
# 定时任务配置
cron_task:
  - name: refresh_token # 定时刷新账号token
    cron: "*/5 * * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行
cron_task:
  - name: check_asset_campaign_status # 定时检查配置的素材上传campaign状态
    cron: "0 10 * * *" # crontab
    start_at_once: false # 是否在服务启动时立刻执行

msg_bot: # 消息通知配置
  secret_key: "xxxxxx" # 企业微信bot webhook key
