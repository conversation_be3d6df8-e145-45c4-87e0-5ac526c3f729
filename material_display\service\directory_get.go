package service

import (
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/spf13/cast"
)

// checkMaterialGet ...
func checkMaterialGet(req *material_display.DirectoryGetReq, gamecode string) error {
	if req == nil {
		return fmt.Errorf("request data cannot be nill")
	}
	if err := checkGameCodeKey(gamecode); err != nil {
		return err
	}
	if req.GetId() == "" {
		return fmt.Errorf("directory Id Error")
	}
	return nil
}

// DirectoryGet ...
func DirectoryGet(ctx *gin.Context, req *material_display.DirectoryGetReq) (*material_display.DirectoryGetRsp, error) {
	var resp material_display.DirectoryGetRsp
	gamecode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if err := checkMaterialGet(req, gamecode); err != nil {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}

	// 如果是广告素材库目录
	if req.GetDirType() == 1 {
		directory, err := data.GetCreativeMediaDirectoryByID(ctx, gamecode, req.GetId())
		if err != nil {
			if err == pg.ErrNoRows {
				return &resp, nil
			}
			return &resp, errs.NewFormat(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB),
				"data.GetCreativeMediaDirectoryByID err:%v", err)
		}

		resp.Dir = &material_display.Directory{
			Id:                   directory.ID,
			Name:                 directory.Name,
			ParentId:             directory.ParentID,
			ParentName:           directory.ParentName,
			CreateDate:           directory.CreateDate,
			UpdateDate:           directory.UpdateDate,
			DirectChildCount:     cast.ToUint32(directory.DirectChildCount),
			TotalLeafCount:       cast.ToUint32(directory.TotalLeafCount),
			DirectDirectoryCount: cast.ToUint32(directory.DirectDirectoryCount),
			FullPathName:         directory.FullPathName,
			FullPathId:           directory.FullPathID,
		}

		return &resp, nil
	}

	db := postgresql.GetDBWithContext(ctx)
	result, err := db.Exec(`SELECT COUNT(*) FROM  pg_class WHERE relname = ?`,
		fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gamecode))
	if err != nil {
		log.ErrorContextf(ctx, "fail to check gamecode in postgresql, gamecode: %v, err: %v", gamecode, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	log.DebugContextf(ctx, "GetMaterialDirectoryById result.RowsReturned: %v, "+
		"result.RowsAffected:%v\n", result.RowsReturned(), result.RowsAffected())
	if result.RowsReturned() < 1 {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "resource db not exist")
	}

	model := model.CreativeDirectory{}
	sql := fmt.Sprintf(`SELECT * FROM %s.tb_creative_directory_%s WHERE id = ?`,
		"arthub_sync", gamecode)
	_, err = db.QueryOne(&model, sql, req.Id)
	if err != nil {
		if err == pg.ErrNoRows {
			log.InfoContextf(ctx, "Material directory information not exist, "+
				"id: %v, err: %v", req.Id, err)
			return &resp, nil
		}
		log.ErrorContextf(ctx, "Fail to get material directory information by id, "+
			"id: %v, err: %v", req.Id, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}

	resp.Dir = singleMaterialInfoToProto(&model)

	return &resp, nil
}
