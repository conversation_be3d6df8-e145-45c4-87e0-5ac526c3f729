syntax = "proto3";

package tiktok_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/tiktok_advertise";
import "aix/aix_common_message.proto";

// --------------------------------------------------- 【内部基础结构&常量定义】 start ---------------------------------------------------

// 基础结构 渠道账号信息
// 广告主状态 https://ads.tiktok.com/marketing_api/docs?id=****************
message MediaAccount {
    string account_id   = 1;  // 渠道账号id
    string account_name = 2;  // 渠道账号名称
    string status       = 3;  // 【与渠道一致】账号状态
    string status_desc  = 4;  // 账号状态描述
    int32  zone_offset  = 5;  // 时区偏移 (和UTC比较)
}

// 基础结构 MetaInfo 元数据信息（包括game_code&账号信息）
message MetaInfo {
    string       game_code = 1;  // 游戏标识game_code
    string       media     = 2;  // 渠道标识 eg: TikTok
    MediaAccount account   = 3;  // 渠道账号信息
}


// 渠道账号 内部结构
message InternalMediaAccount {
    string              game_code      = 1;  // 游戏标识game_code
    string              media          = 2;  // 渠道标识 eg: TikTok
    string              account_id     = 3;  // 渠道账号id
    string              account_name   = 4;  // 渠道账号名称
    TikTokAccountConfig account_config = 5;  // 账号配置
    int32               enabled_state  = 6;  // 账号状态 0:不启用, 1:启用
}

// TikTok TikTokDeveloper
message TikTokDeveloper {
    string secret = 1;
    string app_id = 2;
}

// TikTok TikTokAuthorizations
message TikTokAuthorizations {
    string   auth_code             = 1;
    repeated uint64 scope          = 2;
    string   access_token          = 3;
    repeated string advertiser_ids = 4;
}

// TikTok TikTokAccountInfo
message TikTokAccountInfo {
    int32 zone_offset = 1;
}

// TikTok account config
message TikTokAccountConfig {
    TikTokDeveloper   developer                           = 1;
    repeated          TikTokAuthorizations authorizations = 2;
    TikTokAccountInfo account_info                        = 3;
}
// --------------------------------------------------- 【内部基础结构&常量定义】 end  ---------------------------------------------------







    // --------------------------------------------------- 【渠道原始结构 & 渠道对内结构】 start ---------------------------------------------------

    // 注: 渠道原始结构一般不直接对内展示, 通过【渠道对内结构】对内展示

    // 渠道原始结构 OriginCampaign
    // TikTok v1.3文档 https://ads.tiktok.com/marketing_api/docs?id=****************
message OriginCampaign {
    string campaign_id          = 1;  // [v1.3] 推广系列ID
    string campaign_name        = 2;  // [v1.3] 推广系列名称
    string campaign_type        = 3;  // [v1.3] 推广系列类型
    double budget               = 4;  // [v1.3] 推广系列预算
    string budget_mode          = 5;  // [v1.3] 预算类型
    string secondary_status     = 6;  // [v1.3] 推广系列状态（二级状态）
    string operation_status     = 7;  // [v1.3] 操作状态，可选值: DISABLE（暂停）,ENABLE（开启）
    string campaign_create_time = 8;  // [v1.3] 创建时间 广告主所在时区
    string campaign_modify_time = 9;  // [v1.3] 修改时间 广告主所在时区
}

    // 渠道对内结构
    // OriginCampaign 经过转换后生成系统内部结构
message InternalCampaign {
    string   game_code                = 19;  // 游戏标识game_code
    string   media                    = 20;  // 渠道标识 eg: TikTok
    string   account_id               = 21;  // 渠道账号id
    string   inner_campaign_id        = 1;   // 内部推广系列唯一ID【TikTok接口参数的"campaign_id"使用本字段"inner_campaign_id", 除非接口明确指出需要使用渠道"campaign_id"作为参数】
    string   campaign_id              = 2;   // 【与渠道一致】推广系列ID
    string   campaign_name            = 3;   // 【与渠道一致】推广系列名称
    string   campaign_type            = 4;   // 【与渠道一致】推广系列类型
    double   budget                   = 5;   // 【与渠道一致】推广系列预算
    string   budget_mode              = 6;   // 【与渠道一致】预算类型
    string   status                   = 7;   // 推广系列状态 enable paused frozen 【关联渠道字段: secondary_status, operation_status】
    string   campaign_create_time     = 8;   // 创建时间 广告主所在时区 eg: 2022-09-19 09:11:50
    string   campaign_modify_time     = 9;   // 修改时间 广告主所在时区 eg: 2022-09-19 09:11:50
    string   strategy                 = 11;  // 策略. 仅支持: default
    string   objective_type           = 12;  // 自定义objective_type 推广目标        
    string   buy_type                 = 13;  // 售卖类型, objective_type 上层逻辑划分. 仅支持: auction.
    string   ios14_status             = 14;  // 是否开启IOS14推广系列. 仅支持: on, off
    string   budget_optimize_status   = 15;  // 是否开启系统预算优化. 仅支持: on, off【关联渠道字段: budget_optimize_on】      
    string   bid_type                 = 16;  // 【与渠道一致】推广系列层级的竞价策略. 当开启系统预算优化的时候必传, 且仅支持BID_TYPE_NO_BID      
    string   optimization_goal        = 17;  // 【与渠道一致】优化目标. 当开启系统预算优化的时候必传。枚举值: INSTALL, IN_APP_EVENT                   
    repeated InternalAdgroup adgroups = 18;  // campaign层级下adgroup列表. 非campaign属性，需要拉取campaign下层级adgroup列表时才填充此字段，其他情况下本字段为空数组
    string   remark                   = 22;  // 备注信息 例如: 展示出错的信息
    string   operation_status         = 23;  // 【与渠道一致】操作状态
    string   secondary_status         = 24;  // 【与渠道一致】推广系列状态（二级状态）
    string   aix_campaign_type        = 25;  // 自定义objective_type【由渠道objective_type 和 app_promotion_type联合确定】
}

    // 渠道原始结构 OriginAdgroup
    // TikTok v1.3文档 https://ads.tiktok.com/marketing_api/docs?id=1739314558673922
message OriginAdgroup {
    string campaign_id          = 1;   // [v1.3] 广告组所属的推广系列ID 
    string campaign_name        = 2;   // [v1.3] 推广系列名称
    string adgroup_id           = 3;   // [v1.3] 广告组ID
    string adgroup_name         = 4;   // [v1.3] 广告组名
    string app_id               = 5;   // [v1.3] 推广的应用app的id
    string secondary_status     = 6;   // [v1.3] 广告组状态 枚举值：详见枚举值-广告组状态。
    string operation_status     = 7;   // [v1.3] 操作状态，枚举值: DISABLE（暂停），ENABLE（开启）， FROZEN (冻结，无法再次开启）
    string budget_mode          = 8;   // [v1.3] 广告预算类型。如果开启了系统预算优化(budget_optimize_switch = 1), 将返回BUDGET_MODE_INFINITE。可选值及设置详见预算与预算类型
    double budget               = 9;   // [v1.3] 广告预算。当开启了系统预算优化(budget_optimize_switch=1)时，返回0.0
    string adgroup_create_time  = 10;  // [v1.3] 创建时间 广告主所在时区
    string adgroup_modify_time  = 11;  // [v1.3] 修改时间 广告主所在时区
    double bid_price            = 12;  // [v1.3] CPC、CPM出价，oCPM的学习出价
    double conversion_bid_price = 13;  // [v1.3] oCPM转化出价
    double roas_bid             = 14;  // [v1.3] 用于价值优化的ROAS目标值
    double deep_cpa_bid         = 15;  // [v1.3] 当您为app内事件选择出价策略后，您需要在该字段中传入出价价格
    string app_type             = 16;  // [v1.3] 应用类型 APP_ANDROID APP_IOS
}

    // 渠道对内结构
    // InternalAdgroup 经过转换后生成系统内部结构
message InternalAdgroup {
    string                   game_code           = 21;         // 游戏标识game_code
    string                   media               = 22;         // 渠道标识 eg: TikTok
    string                   account_id          = 23;         // 渠道账号id
    string                   inner_campaign_id   = 1;          // 内部推广系列唯一ID【TikTok接口参数的"campaign_id"使用本字段"inner_campaign_id", 除非接口明确指出需要使用渠道"campaign_id"作为参数】
    string                   inner_adgroup_id    = 2;          // 内部广告组唯一ID【TikTok接口参数的"adgroup_id"使用本字段"inner_adgroup_id", 除非接口明确指出需要使用渠道"adgroup_id"作为参数】
    string                   campaign_id         = 3;          // 【与渠道一致】广告组所属的推广系列ID
    string                   campaign_name       = 4;          // 【与渠道一致】推广系列名称
    string                   adgroup_id          = 5;          // 【与渠道一致】广告组ID
    string                   adgroup_name        = 6;          // 【与渠道一致】广告组名
    string                   app_id              = 7;          // 【与渠道一致】推广的应用app的id
    string                   status              = 8;          // 广告组状态【关联渠道原始字段[secondary_status operation_status]】
    string                   budget_mode         = 9;          // 【与渠道一致】广告预算类型【与ModuleBudgetSchedule模块的budget_mode一致】
    double                   budget              = 10;         // 【与渠道一致】广告预算【与ModuleBudgetSchedule模块的budget一致】
    string                   adgroup_create_time = 11;         // 【与渠道一致】创建时间 广告主所在时区
    string                   adgroup_modify_time = 12;         // 【与渠道一致】修改时间 广告主所在时区
    double                   offer_price         = 13;         // 出价【注：渠道原始字段[bid_price, conversion_bid_price, roas_bid, deep_cpa_bid]中，任意一个原始字段>0，则填写offer_price】
    string                   os                  = 14;         // 操作系统 ANDROID, IOS
                                                               // Creative type 模块
    string                   creative_material_mode = 15;      // 【与渠道一致】创意投放方式 枚举值： CUSTOM（自定义），DYNAMIC（程序化）
                                                               // Placement 版位模块
    ModulePlacement          module_placement = 16;            // 版位模块
                                                               // Targeting
    ModuleTargeting          module_targeting = 17;            // 定向模块
                                                               // Budget & Schedule
    ModuleBudgetSchedule     module_budget_schedule = 18;      // 预算&排期模块
                                                               // Biding & Optimization
    ModuleBidingOptimization module_biding_optimization = 19;  // 出价&优化模块
                                                               // adgroup层级下ad列表
    repeated                 InternalAd ads = 20;              // 非adgroup属性，需要拉取adgroup下层级ad列表时才填充此字段，其他情况下本字段为空数组
    string                   remark         = 24;              // 备注信息 例如: 展示出错的信息
                                                               // 程序创意广告信息
    InternalAcoAd            aco_ad           = 25;            // 程序创意广告信息. 当开启创意广告时(creative_material_mode=DYNAMIC), aco_ad才存在
    string                   operation_status = 26;            // 【与渠道一致】操作状态
    string                   secondary_status = 27;            // 【与渠道一致】广告组状态（二级状态）
}

    // 渠道adgroup下模块内部结构
    // Placement 版位模块
message ModulePlacement {
    string   placement_type          = 1;  // 【与渠道一直】PLACEMENT_TYPE_AUTOMATIC(自动版位),PLACEMENT_TYPE_NORMAL （自选版位）
    repeated string placements       = 2;  // 【与渠道一致】广告版位列表
    bool     comment_disabled        = 3;  // 【与渠道一致】是否允许用户在TikTok，Vigo， Helo上评论您的广告
    bool     video_download_disabled = 4;  // 【与渠道一致】用户是否可以在 TikTok 上下载您的广告视频（该字段创建后不允许修改）
}

    // Targeting 定向模块
message ModuleTargeting{
    bool                    auto_targeting_enabled = 1;  // 【与渠道一致】自定义定向
    ModuleDemographics      demographics           = 2;  // Demographics 模块
    ModuleAudience          audience               = 3;  // Audience 受众模块
    ModuleInterestsBehavior interests_behavior     = 4;  // 兴趣行为模块
    ModuleDevice            device                 = 5;  // Device 设备模块
}

    // Targeting - Demographics 模块
message ModuleDemographics {
    repeated string location_ids = 1;  // 【与渠道一致】定向位置列表
    repeated string languages    = 2;  // 【与渠道一致】语言列表
    string   gender              = 3;  // 【与渠道一致】定向受众性别
    repeated string age_groups   = 4;  // 【与渠道一致】受众年龄区间
}

    // Targeting - Audience 受众模块
message ModuleAudience {
    repeated string audience_ids          = 1;  // 受众ID数组
    repeated string excluded_audience_ids = 2;  // 排除受众ID数组
}

    // Targeting - Interests & Behavior 兴趣行为模块
message ModuleInterestsBehavior {
    repeated string interest_category_ids = 1;  // 【与渠道一致】兴趣分类列表
    repeated ModuleBehavior behaviors     = 2;  // 行为列表
}

    // Targeting - Behavior 行为模块
message ModuleBehavior {
    string   action_scene               = 2;  // 【与渠道一致】行为场景。枚举值：VIDEO_RELATED(视频行为),CREATOR_RELATED（创作者互动行为）, HASHTAG_RELATED（视频标签相关）
    int32    action_period              = 3;  // 【与渠道一致】选择n天内发生的行为 枚举值: 07, 15
    repeated string action_category_ids = 4;  // 【与渠道一致】您想要定向的行为分类或标签的ID列表 只有当版位为TikTok时该字段才生效
    repeated string video_user_actions  = 5;  // 【与渠道一致】视频类行为
}

    // Device 模块
message ModuleDevice {
                                             // OS versions
    string   min_os_version = 1;             // 不传或空字符串表示不限制. 受众最低iOS版本 或 受众最低Android版本，根据InternalAdgroup os判定是min_ios_version or min_android_version
                                             // Device model
    repeated string device_models_ids = 2;   // 【与渠道一致】设备机型ID列表
                                             // Connection type
    repeated string network_types = 3;       // 【与渠道一致】网络类型。默认值：unlimited。 详见枚举值-网络类型。 WIFI, 2G, 3G, 4G, 5G, unlimited
                                             // Carriers
    repeated string carrier_ids = 4;         // 【与渠道一致】运营商 ID
                                             // Device price
    repeated int32 device_price_ranges = 5;  // 【与渠道一致】设备价格区间（10000代表1000+）。该数字必须是50的倍数
}

    // Budget & Schedule 模块
message ModuleBudgetSchedule {
    double budget              = 1;  // 【与渠道一致】广告预算。当开启了系统预算优化(budget_optimize_on)时，该字段不需要传递
    string budget_mode         = 2;  // 【与渠道一致】广告预算类型 枚举值: BUDGET_MODE_DAY,BUDGET_MODE_INFINITE,BUDGET_MODE_TOTAL
    string schedule_type       = 3;  // 【与渠道一致】广告投放计划类型 枚举值 SCHEDULE_START_END, SCHEDULE_FROM_NOW
    string schedule_start_time = 4;  // 【与渠道一致】广告投放起始时间(UTC+0)，形式如：2017-01-01 00:00:00
    string schedule_end_time   = 5;  // 【与渠道一致】广告投放结束时间 (UTC+0)，形式如：2017-01-01 00:00:00
}

    // Biding & Optimization 出价&优化模块
message ModuleBidingOptimization {
    string optimization_goal            = 1;  // 【在渠道基础上，增加 INSTALL_IN_APP_EVENT 常量表示 安装与应用内事件数据 】优化目标
    string bid_strategy                 = 2;  //  LOWEST, OFFER_PRICE. OFFER_PRICE时必填offer_price
    double offer_price                  = 3;  //  conversion_bid_price, bid_price, roas_bid
    string optimization_event           = 4;  // 【与渠道一致】广告组转化事件
    string secondary_optimization_event = 5;  // 【与渠道一致】当优化目标为INSTALL或VALUE时的次要优化目标 eg: PURCHASE_ROI, ACTIVE_PAY
}

    // 渠道adgroup下 程序创意广告内部结构
    // 仅当渠道adgroup开启程序创意广告时有效 即creative_material_mode=DYNAMIC
message InternalAcoAd {
    string   ad_name                    = 1;  // 广告名称。传入 "" （空字符串）代表自动生成
    string   identity_id                = 2;  // 【与渠道一致】认证身份ID
    string   identity_type              = 3;  // 【与渠道一致】认证身份类型
    repeated AcoAdMediaInfo media_infos = 4;  // 创意信息列表
    repeated string ad_texts            = 5;  // 广告文案列表
    repeated string call_to_actions     = 6;  // 行动引导文案列表
}

message AcoAdMediaInfo {
    AcoAdVideoInfo video_info                 = 1;  // 视频信息，当素材类型为视频时必填
    repeated       AcoAdImageInfo image_infos = 2;  // 图片信息列表，当素材类型为图片时必填。部分视频类型的素材需要上传图片封面。每个image_info中仅可上传一张图片
}

message AcoAdVideoInfo {
    string video_id  = 1;  // 视频ID
    string file_name = 2;  // 视频素材名称
}

message AcoAdImageInfo {
    string image_id  = 1;  // 图像ID
    string file_name = 2;  // 图像素材名称
}

    // 渠道原始结构 Ad
    // TikTok v1.3文档 https://ads.tiktok.com/marketing_api/docs?id=1735735588640770
message OriginAd {
    string campaign_id      = 1;   // [v1.3] 推广系列 ID
    string campaign_name    = 2;   // [v1.3] 推广系列名称
    string adgroup_id       = 3;   // [v1.3] 广告组ID
    string adgroup_name     = 4;   // [v1.3] 广告组名
    string ad_id            = 5;   // [v1.3] 广告 ID
    string ad_name          = 6;   // [v1.3] 广告名称
    string secondary_status = 7;   // [v1.3] 广告状态（二级状态），见枚举值-二级状态。
    string operation_status = 8;   // [v1.3] 操作状态，可选值： DISABLE（暂停），ENABLE（开启)， FROZEN (冻结，无法再次开启）
    string ad_create_time   = 9;   // [v1.3] 创建时间 广告主所在时区
    string ad_modify_time   = 10;  // [v1.3] 修改时间 广告主所在时区
}

// 渠道对内结构
// InternalAd 经过转换后生成系统内部结构
message InternalAd {
    string         game_code         = 15;  // 游戏标识game_code
    string         media             = 16;  // 渠道标识 eg: TikTok
    string         account_id        = 17;  // 渠道账号id
    string         inner_campaign_id = 1;   // 内部推广系列唯一ID【TikTok接口参数的"campaign_id"使用本字段"inner_campaign_id", 除非接口明确指出需要使用渠道"campaign_id"作为参数】
    string         inner_adgroup_id  = 2;   // 内部广告组唯一ID【TikTok接口参数的"adgroup_id"使用本字段"inner_adgroup_id", 除非接口明确指出需要使用渠道"adgroup_id"作为参数】
    string         inner_ad_id       = 3;   // 内部广告唯一ID【TikTok接口参数的"ad_id"使用本字段"inner_ad_id", 除非接口明确指出需要使用渠道"ad_id"作为参数】
    string         campaign_id       = 4;   // 【与渠道一致】推广系列 ID
    string         campaign_name     = 5;   // 【与渠道一致】推广系列名称
    string         adgroup_id        = 6;   // 【与渠道一致】广告组ID
    string         adgroup_name      = 7;   // 【与渠道一致】广告组名
    string         ad_id             = 8;   // 【与渠道一致】广告 ID
    string         ad_name           = 9;   // 【与渠道一致】广告名称
    string         status            = 10;  // 广告状态
    string         ad_create_time    = 11;  // 【与渠道一致】创建时间 广告主所在时区
    string         ad_modify_time    = 12;  // 【与渠道一致】修改时间 广告主所在时区
    ModuleAdDetail module_ad_detail  = 13;  // Ad detail 广告详情模块
    string         remark            = 14;  // 备注信息 例如: 展示出错的信息
    bool           is_aco            = 18;  // 【与渠道一致】是否属于程序化广告，true代表程序化广告，false代表非程序化广告
    string         operation_status  = 19;  // 【与渠道一致】操作状态
    string         secondary_status  = 20;  // 【与渠道一致】广告组状态（二级状态）
    string         inner_status      = 21;  // 内部状态
}

// Ad detail 广告详情
message ModuleAdDetail {
    string                ad_format        = 1;  // 【与渠道一致】素材类型。枚举值：SINGLE_IMAGE, SINGLE_VIDEO
    ModuleAdCreative      ad_creative      = 2;  // Ad detail - Ad creative 创意素材
    ModuleDestinationPage destination_page = 3;  // Ad detail - Destination page 目标页
}

    // Ad detail - Ad creative 创意素材
message ModuleAdCreative {
    string   identity_id      = 1;  // 【与渠道一致】认证身份ID 前端需要透传
    string   identity_type    = 2;  // 【与渠道一致】认证身份类型 前端需要透传
    string   ad_text          = 3;  // 【与渠道一致】广告文案，将作为广告创意的一部分展示给你的受众，向他们传达你想要推广的信息
    string   video_id         = 4;  // 【与渠道一致】视频素材ID。当创意类型为常规视频时必填
    repeated string image_ids = 5;  // 【与渠道一致】图片ID列表. SINGLE_VIDEO时传入的值会作为视频封面（只允许传入一个值）
}

    // Ad detail - Destination page 目标页
message ModuleDestinationPage {
    string call_to_action_type = 1;  // 目标页选择 枚举值：DYNAMIC, STANDARD
    string call_to_action_id   = 2;  // 【与渠道一致】动态优选CTA ID【call_to_action_type=DYNAMIC】
    string call_to_action      = 3;  // 【与渠道一致】标准行动引导文案【call_to_action_type=STANDARD】
}


    // --------------------------------------------------- 【渠道原始结构 & 渠道对内结构】 end  ---------------------------------------------------















    // --------------------------------------------------- 【接口定义】 start       ---------------------------------------------------

    // 查询渠道账号列表, POST, /api/v1/tiktok_advertise/get_media_account
message GetMediaAccountReq {
    string game_code = 1;  // 必填 游戏标识game_code
    string media     = 2;  // 必填 渠道标识 eg: TikTok
}

message GetMediaAccountRsp {
    aix.Result result                = 1;  // 返回结果
    string     trace_id              = 7;  // 请求trace_id
    string     game_code             = 2;  // 游戏标识game_code
    string     media                 = 3;  // 渠道标识 eg: TikTok
    repeated   MediaAccount accounts = 4;  // 账号信息列表
}

// 批量查询campaign信息，单次查询最大20个, POST, /api/v1/tiktok_advertise/get_campaigns
message GetCampaignsReq {
    string   game_code           = 1;  // 必填 游戏标识game_code
    string   media               = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id          = 3;  // 必填 渠道账号id
    repeated string campaign_ids = 4;  // 必填 campaign_id列表
}

message GetCampaignsRsp {
    aix.Result result                     = 1;  // 返回结果
    string     trace_id                   = 7;  // 请求trace_id
    MetaInfo   meta                       = 2;  // 元数据信息【包括: game_code & 账号信息】
    repeated   InternalCampaign campaigns = 3;  // Campaign列表信息【resource_type=campaign时返回】
}

// 批量查询adgroup信息，单次查询最大20个, POST, /api/v1/tiktok_advertise/get_adgroups
message GetAdgroupsReq {
    string   game_code          = 1;  // 必填 游戏标识game_code
    string   media              = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id         = 3;  // 必填 渠道账号id
    repeated string adgroup_ids = 4;  // 必填 adgroup_ids列表
}

message GetAdgroupsRsp {
    aix.Result result                   = 1;  // 返回结果
    string     trace_id                 = 7;  // 请求trace_id
    MetaInfo   meta                     = 2;  // 元数据信息【包括: game_code & 账号信息】
    repeated   InternalAdgroup adgroups = 3;  // Adgroup列表信息【resource_type=adgroup时返回】
}

// 批量查询ad信息，单次查询最大20个, POST, /api/v1/tiktok_advertise/get_ads
message GetAdsReq {
    string   game_code     = 1;  // 必填 游戏标识game_code
    string   media         = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id    = 3;  // 必填 渠道账号id
    repeated string ad_ids = 4;  // 必填 ad_ids列表
}

message GetAdsRsp {
    aix.Result result         = 1;  // 返回结果
    string     trace_id       = 7;  // 请求trace_id
    MetaInfo   meta           = 2;  // 元数据信息【包括: game_code & 账号信息】
    repeated   InternalAd ads = 3;  // Ad列表信息【resource_type=ad时返回】
}

// 批量查询渠道多account多campaign信息, POST, /api/v1/tiktok_advertise/get_multi_campaigns
message GetMultiCampaignsReq {
    string   game_code                                    = 1;  // 必填 游戏标识game_code
    string   media                                        = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountMediaCampaign account_media_campaigns = 3;  // 账号+渠道campaign_id列表信息
}

message GetMultiCampaignsRsp {
    aix.Result result                     = 1;  // 返回结果
    string     trace_id                   = 2;  // 请求trace_id
    repeated   InternalCampaign campaigns = 3;  // Campaign列表信息
}

// 批量查询渠道多account多adgroup信息, POST, /api/v1/tiktok_advertise/get_multi_adgroups
message GetMultiAdgroupsReq {
    string   game_code                                  = 1;  // 必填 游戏标识game_code
    string   media                                      = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountMediaAdgroup account_media_adgroups = 3;  // 账号+渠道adgroup_id列表信息
}

message GetMultiAdgroupsRsp {
    aix.Result result                   = 1;  // 返回结果
    string     trace_id                 = 2;  // 请求trace_id
    repeated   InternalAdgroup adgroups = 3;  // Adgroup列表信息
}

// 批量查询渠道多account多ad信息, POST, /api/v1/tiktok_advertise/get_multi_ads
message GetMultiAdsReq {
    string   game_code                        = 1;  // 必填 游戏标识game_code
    string   media                            = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountMediaAd account_media_ads = 3;  // 账号+渠道ad_id列表信息
}

message GetMultiAdsRsp {
    aix.Result result         = 1;  // 返回结果
    string     trace_id       = 2;  // 请求trace_id
    repeated   InternalAd ads = 3;  // Ad列表信息
}

// 同步存储中的广告账号数据到服务本地缓存, POST, /api/v1/tiktok_advertise/sync_account_localcache_data
message SyncAccountLocalcacheDataReq {
    string media = 1;  // 必填 渠道标识 eg: TikTok
}

message SyncAccountLocalcacheDataRsp {
    aix.Result result              = 1;  // 返回结果
    string     trace_id            = 7;  // 请求trace_id
}

// 同步渠道广告账号详情信息到本地存储, POST, /api/v1/tiktok_advertise/sync_media_account_detail
message SyncMediaAccountDetailReq {
    repeated string   game_codes = 1;  // 必填 游戏标识game_code列表
    string   media               = 2;  // 必填 渠道标识 eg: TikTok
}

message SyncMediaAccountDetailRsp {
    aix.Result result              = 1;  // 返回结果
    string     trace_id            = 7;  // 请求trace_id
}

// 创建草稿campaign, POST, /api/v1/tiktok_advertise/create_draft_campaign
message CreateDraftCampaignReq {
    string           game_code  = 1;  // 必填 游戏标识game_code
    string           media      = 2;  // 必填 渠道标识 eg: TikTok
    string           account_id = 3;  // 必填 渠道账号id
    InternalCampaign campaign   = 4;  // 必填 待创建campaign信息
}

message CreateDraftCampaignRsp {
    aix.Result result            = 1;  // 返回结果
    string     trace_id          = 7;  // 请求trace_id
    MetaInfo   meta              = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_campaign_id = 3;  // 创建完成的内部ID: inner_campaign_id
}

    // 创建草稿adgroup, POST, /api/v1/tiktok_advertise/create_draft_adgroup
message CreateDraftAdgroupReq {
    string          game_code  = 1;  // 必填 游戏标识game_code
    string          media      = 2;  // 必填 渠道标识 eg: TikTok
    string          account_id = 3;  // 必填 渠道账号id
    InternalAdgroup adgroup    = 4;  // 必填 待创建adgroup信息
}

message CreateDraftAdgroupRsp {
    aix.Result result           = 1;  // 返回结果
    string     trace_id         = 7;  // 请求trace_id
    MetaInfo   meta             = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_adgroup_id = 3;  // 创建完成的内部ID: inner_adgroup_id
}

    // 创建草稿ad, POST, /api/v1/tiktok_advertise/create_draft_ad
message CreateDraftAdReq {
    string     game_code  = 1;  // 必填 游戏标识game_code
    string     media      = 2;  // 必填 渠道标识 eg: TikTok
    string     account_id = 3;  // 必填 渠道账号id
    InternalAd ad         = 4;  // 必填 待创建ad信息
}

message CreateDraftAdRsp {
    aix.Result result      = 1;  // 返回结果
    string     trace_id    = 7;  // 请求trace_id
    MetaInfo   meta        = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_ad_id = 3;  // 创建完成的内部ID: inner_ad_id
}

    // 广告名称中各项内容含义
    // 文档: https://docs.qq.com/doc/DTVBEVk1OWGxCb05q?mode=editing&client=tapd
message InnerCampaignNameField {
    string region              = 1;  // 国家地区
    string platform            = 2;  // 投放平台
    string date                = 3;  // 投放日期(广告创建日期)
    string campaign_goal       = 4;  // 广告目标
    string custom_field        = 5;  // 自定义字段 
    string spend_type          = 6;  // 花费类型
    string project_region_type = 7;  // 立项区域
}

// 自定义过滤条件
message CustomFilter {
    string                 campaign_name                               = 1;   // campaign 名称模糊搜索
    repeated               string                 campaign_names       = 2;   // campaign_names 名称批量模糊搜索
    repeated               string inner_campaign_ids                   = 3;   // inner_campaign_ids 列表
    repeated               string campaign_ids                         = 4;   // campaign_ids 列表
    repeated               string campaign_types                       = 5;   // campaign_types 列表
    repeated               string                 campaign_status_list = 6;   // campaign 状态 列表
    string                 adgroup_name                                = 7;   // adgroup 名称模糊搜索
    repeated               string                 adgroup_names        = 8;   // adgroup_names 名称批量模糊搜索
    repeated               string inner_adgroup_ids                    = 9;   // inner_adgroup_ids 列表
    repeated               string adgroup_ids                          = 10;  // adgroup_ids 列表
    repeated               string                 adgroup_status_list  = 11;  // adgroup 状态 列表
    repeated               string                 region_codes         = 12;  // 国家地区二字码列表
    string                 ad_name                                     = 13;  // ad 名称模糊搜索
    repeated               string                 ad_names             = 14;  // ad_names 名称批量模糊搜索
    repeated               string inner_ad_ids                         = 15;  // inner_ad_ids 列表
    repeated               string ad_ids                               = 16;  // ad_ids 列表
    repeated               string                 ad_status_list       = 17;  // ad 状态 列表
    InnerCampaignNameField campaign_name_field                         = 18;  // campaign名称各项字段含义
}

// 查询草稿 campaign 列表, POST, /api/v1/tiktok_advertise/get_draft_campaigns
message GetDraftCampaignsReq {
    string       game_code          = 1;  // 必填 游戏标识game_code
    string       media              = 2;  // 必填 渠道标识 eg: TikTok
    repeated     string account_ids = 3;  // 必填 渠道账号id列表
    CustomFilter filter             = 4;  // 过滤信息
}

message GetDraftCampaignsRsp {
    aix.Result result                     = 1;  // 返回结果
    string     trace_id                   = 7;  // 请求trace_id
    repeated   InternalCampaign campaigns = 2;  // campaign列表
}

    // 查询草稿 adgroup 列表, POST, /api/v1/tiktok_advertise/get_draft_adgroups
message GetDraftAdgroupsReq {
    string       game_code          = 1;  // 必填 游戏标识game_code
    string       media              = 2;  // 必填 渠道标识 eg: TikTok
    repeated     string account_ids = 3;  // 必填 渠道账号id列表
    CustomFilter filter             = 4;  // 过滤信息
}

message GetDraftAdgroupsRsp {
    aix.Result result                   = 1;  // 返回结果
    string     trace_id                 = 7;  // 请求trace_id
    repeated   InternalAdgroup adgroups = 2;  // adgroup列表
}

    // 查询草稿 ad 列表, POST, /api/v1/tiktok_advertise/get_draft_ads
message GetDraftAdsReq {
    string       game_code          = 1;  // 必填 游戏标识game_code
    string       media              = 2;  // 必填 渠道标识 eg: TikTok
    repeated     string account_ids = 3;  // 必填 渠道账号id列表
    CustomFilter filter             = 4;  // 过滤信息
}

message GetDraftAdsRsp {
    aix.Result result         = 1;  // 返回结果
    string     trace_id       = 7;  // 请求trace_id
    repeated   InternalAd ads = 2;  // ad列表
}

    // 查询 campaign及其adgroup, ad 树状列表, POST, /api/v1/tiktok_advertise/get_campaign_trees
message GetCampaignTreesReq {
    string   game_code                             = 1;  // 必填 游戏标识game_code
    string   media                                 = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountCampaignInfo account_campaigns = 3;  // 账号+campaign_id列表信息
}

message AccountCampaignInfo {
    string account_id        = 1;  // 必填 渠道账号id
    string inner_campaign_id = 2;  // 必填 内部inner_campaign_id
    string campaign_id       = 3;  // 必填 渠道campaign_id
}

message GetCampaignTreesRsp {
    aix.Result result                     = 1;  // 返回结果
    string     trace_id                   = 7;  // 请求trace_id
    repeated   InternalCampaign campaigns = 2;  // campaign树状列表(带adgroup, ad列表层级数据列表)
}

    // 批量更新渠道campaign status状态, POST, /api/v1/tiktok_advertise/update_campaign_status
message UpdateCampaignStatusReq {
    string   game_code                                    = 1;  // 必填 游戏标识game_code
    string   media                                        = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountMediaCampaign account_media_campaigns = 3;  // 账号+渠道campaign_id列表信息
    string   target_status                                = 4;  // 【与渠道一致】campaign目标状态 枚举值：DISABLE（暂停）, ENABLE（启用）。
}

message AccountMediaCampaign {
    string account_id  = 1;  // 必填 渠道账号id
    string campaign_id = 2;  // 必填 渠道campaign_id
}

message UpdateCampaignStatusRsp {
    aix.Result result                                       = 1;  // 返回结果
    string     trace_id                                     = 7;  // 请求trace_id
    repeated   AccountMediaCampaign account_media_campaigns = 2;  // 更新【成功】的账号+渠道campaign_id列表列表
    string     target_status                                = 3;  // campaign目标状态
}

    // 批量更新渠道adgroup status状态, POST, /api/v1/tiktok_advertise/update_adgroup_status
message UpdateAdgroupStatusReq {
    string   game_code                                  = 1;  // 必填 游戏标识game_code
    string   media                                      = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountMediaAdgroup account_media_adgroups = 3;  // 账号+渠道adgroup_id列表信息
    string   target_status                              = 4;  // 【与渠道一致】adgroup目标状态 枚举值：DISABLE（暂停）, ENABLE（启用）。
}

message AccountMediaAdgroup {
    string account_id = 1;  // 必填 渠道账号id
    string adgroup_id = 2;  // 必填 渠道adgroup_id
}

message UpdateAdgroupStatusRsp {
    aix.Result result                                     = 1;  // 返回结果
    string     trace_id                                   = 7;  // 请求trace_id
    repeated   AccountMediaAdgroup account_media_adgroups = 2;  // 更新【成功】的账号+渠道adgroup_id列表列表
    string     target_status                              = 3;  // adgroup目标状态
}

    // 批量更新渠道ad status状态, POST, /api/v1/tiktok_advertise/update_ad_status
message UpdateAdStatusReq {
    string   game_code                        = 1;  // 必填 游戏标识game_code
    string   media                            = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountMediaAd account_media_ads = 3;  // 账号+渠道ad_id列表信息
    string   target_status                    = 4;  // 【与渠道一致】ad目标状态 枚举值：DISABLE（暂停）, ENABLE（启用）。
}

message AccountMediaAd {
    string account_id = 1;  // 必填 渠道账号id
    string ad_id      = 2;  // 必填 渠道ad_id
    bool   is_aco     = 3;  // 返回数据，参数无需填写. 是否为程序创意广告. true:是, false: 否
}

message UpdateAdStatusRsp {
    aix.Result result                           = 1;  // 返回结果
    string     trace_id                         = 7;  // 请求trace_id
    repeated   AccountMediaAd account_media_ads = 2;  // 更新【成功】的账号+渠道ad_id列表列表
    string     target_status                    = 3;  // ad目标状态
}

    // 发布草稿ad (包括所属上层adgroup, campaign发布), POST, /api/v1/tiktok_advertise/publish_draft_ad
message PublishDraftAdReq {
    string game_code   = 1;  // 必填 游戏标识game_code
    string media       = 2;  // 必填 渠道标识 eg: TikTok
    string account_id  = 3;  // 必填 渠道账号id
    string inner_ad_id = 4;  // 仅支持内部inner_ad_id
}

message PublishDraftAdRsp {
    aix.Result result            = 1;   // 返回结果
    string     trace_id          = 12;  // 请求trace_id
    MetaInfo   meta              = 2;   // 元数据信息【包括: game_code & 账号信息】
    string     inner_campaign_id = 3;   // 内部推广系列唯一ID
    string     inner_adgroup_id  = 4;   // 内部广告组唯一ID
    string     inner_ad_id       = 5;   // 内部广告唯一ID
    string     campaign_id       = 6;   // 渠道推广系列 ID
    string     campaign_name     = 7;   // 渠道推广系列名称
    string     adgroup_id        = 8;   // 渠道广告组ID
    string     adgroup_name      = 9;   // 渠道广告组名
    string     ad_id             = 10;  // 渠道广告 ID
    string     ad_name           = 11;  // 渠道广告名称
}

    // 更新campaign推广系列(渠道或草稿), POST, /api/v1/tiktok_advertise/update_campaign
message UpdateCampaignReq {
    string           game_code  = 1;  // 必填 游戏标识game_code
    string           media      = 2;  // 必填 渠道标识 eg: TikTok
    string           account_id = 3;  // 必填 渠道账号id
    InternalCampaign campaign   = 4;  // campaign结构中, inner_campaign_id 或者 campaign_id(优先级较高)至少传一个. 【编辑草稿campaign】允许编辑所有字段且必须传全量字段; 【编辑已发布campaign】允许编辑budget,budget_mode且必传，其他字段忽略
}

message UpdateCampaignRsp {
    aix.Result result            = 1;  // 返回结果
    string     trace_id          = 7;  // 请求trace_id
    MetaInfo   meta              = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_campaign_id = 3;  // 编辑的内部inner_campaign_id
    string     campaign_id       = 4;  // 编辑的渠道campaign_id
}

    // 更新 adgroup 广告组(渠道或草稿), POST, /api/v1/tiktok_advertise/update_adgroup
message UpdateAdgroupReq {
    string          game_code  = 1;  // 必填 游戏标识game_code
    string          media      = 2;  // 必填 渠道标识 eg: TikTok
    string          account_id = 3;  // 必填 渠道账号id
    InternalAdgroup adgroup    = 4;  // adgroup结构中, inner_adgroup_id 或者 adgroup_id(优先级较高)至少传一个. 【编辑草稿adgroup】允许编辑所有字段且必须传全量字段; 【编辑已发布adgroup】允许编辑部分字段
}

message UpdateAdgroupRsp {
    aix.Result result           = 1;  // 返回结果
    string     trace_id         = 7;  // 请求trace_id
    MetaInfo   meta             = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_adgroup_id = 3;  // 编辑的内部 inner_adgroup_id
    string     adgroup_id       = 4;  // 编辑的渠道 adgroup_id
}

    // 更新 ad 广告(渠道或草稿), POST, /api/v1/tiktok_advertise/update_ad
message UpdateAdReq {
    string     game_code  = 1;  // 必填 游戏标识game_code
    string     media      = 2;  // 必填 渠道标识 eg: TikTok
    string     account_id = 3;  // 必填 渠道账号id
    InternalAd ad         = 4;  // ad结构中, inner_ad_id 或者 ad_id(优先级较高)至少传一个. 【编辑草稿ad】允许编辑所有字段且必须传全量字段; 【编辑已发布ad】允许编辑部分字段
}

message UpdateAdRsp {
    aix.Result result      = 1;  // 返回结果
    string     trace_id    = 7;  // 请求trace_id
    MetaInfo   meta        = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_ad_id = 3;  // 编辑的内部 inner_ad_id
    string     ad_id       = 4;  // 编辑的渠道 ad_id
}

    // 更新 aco ad 广告(渠道或草稿), POST, /api/v1/tiktok_advertise/update_aco_ad
message UpdateAcoAdReq {
    string        game_code        = 1;  // 必填 游戏标识game_code
    string        media            = 2;  // 必填 渠道标识 eg: TikTok
    string        account_id       = 3;  // 必填 渠道账号id
    string        inner_adgroup_id = 4;  // 内部广告组唯一ID
    InternalAcoAd aco_ad           = 5;  // 程序创意广告
}

message UpdateAcoAdRsp {
    aix.Result result           = 1;  // 返回结果
    string     trace_id         = 7;  // 请求trace_id
    MetaInfo   meta             = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_adgroup_id = 3;  // 内部广告组唯一ID
    string     inner_ad_id      = 4;  // 仅在更新草稿创意广告aco ad时, 才返回其inner_ad_id, 更新渠道创意广告时inner_ad_id为空
}

// 同步广告数据至clickhouse, POST, /api/v1/tiktok_advertise/sync_cllickhouse
message SyncClickhouseReq {
    string game_code         = 1;  // 必填 游戏标识game_code
    string media             = 2;  // 必填 渠道标识 eg: TikTok
    string account_id        = 3;  // 必填 渠道账号id
    string inner_campaign_id = 4;  // 内部推广系列唯一ID
    string inner_adgroup_id  = 5;  // 内部广告组唯一ID . 存在则忽略 inner_campaign_id 
    string inner_ad_id       = 6;  // 内部广告唯一ID. 存在则忽略 inner_campaign_id 和 inner_adgroup_id
}

message SyncClickhouseRsp {
    aix.Result result   = 1;  // 返回结果
    MetaInfo   meta     = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     trace_id = 3;  // 请求trace_id
}

    // 单个复制 ad(渠道或草稿) 广告 存为草稿, POST, /api/v1/tiktok_advertise/copy_ad
message CopyAdReq {
    string game_code   = 1;  // 必填 游戏标识game_code
    string media       = 2;  // 必填 渠道标识 eg: TikTok
    string account_id  = 3;  // 必填 渠道账号id
    string inner_ad_id = 4;  // 复制 内部 inner_ad_id
    string ad_id       = 5;  // 复制 渠道ad_id
    string copy_type   = 6;  // 复制类型. 默认"THIS_LEVEL". 仅支持: "THIS_LEVEL"
}

message CopyAdRsp {
    aix.Result result      = 1;  // 返回结果
    string     trace_id    = 7;  // 请求trace_id
    MetaInfo   meta        = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_ad_id = 3;  // 生成复制后的草稿 inner_ad_id
}

    // 单个复制 adgroup(渠道或草稿) 广告 存为草稿, POST, /api/v1/tiktok_advertise/copy_adgroup
message CopyAdgroupReq {
    string game_code        = 1;  // 必填 游戏标识game_code
    string media            = 2;  // 必填 渠道标识 eg: TikTok
    string account_id       = 3;  // 必填 渠道账号id
    string inner_adgroup_id = 4;  // 复制 内部 inner_adgroup_id
    string adgroup_id       = 5;  // 复制 渠道 adgroup_id
    string copy_type        = 6;  // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyAdgroupRsp {
    aix.Result result           = 1;  // 返回结果
    string     trace_id         = 7;  // 请求trace_id
    MetaInfo   meta             = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_adgroup_id = 3;  // 生成复制后的草稿 inner_adgroup_id
}

    // 单个复制 campaign(渠道或草稿) 广告 存为草稿, POST, /api/v1/tiktok_advertise/copy_campaign
message CopyCampaignReq {
    string game_code         = 1;  // 必填 游戏标识game_code
    string media             = 2;  // 必填 渠道标识 eg: TikTok
    string account_id        = 3;  // 必填 渠道账号id
    string inner_campaign_id = 4;  // 复制 内部 inner_campaign_id
    string campaign_id       = 5;  // 复制 渠道 campaign_id
    string copy_type         = 6;  // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyCampaignRsp {
    aix.Result result            = 1;  // 返回结果
    string     trace_id          = 7;  // 请求trace_id
    MetaInfo   meta              = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_campaign_id = 3;  // 生成复制后的草稿 inner_campaign_id
}

    // 批量删除草稿 campaign及其adgroup, ad草稿, POST, /api/v1/tiktok_advertise/delete_draft_campaigns
message DeleteDraftCampaignsReq {
    string   game_code                                    = 1;  // 必填 游戏标识game_code
    string   media                                        = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountDraftCampaign account_draft_campaigns = 3;  // 账号+草稿inner_campaign_id列表信息
}

message AccountDraftCampaign {
    string account_id        = 1;  // 必填 渠道账号id
    string inner_campaign_id = 2;  // 必填 内部inner_campaign_id
}

message DeleteDraftCampaignsRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
}

    // 批量删除草稿 adgroup及其ad草稿, POST, /api/v1/tiktok_advertise/delete_draft_adgroups
message DeleteDraftAdgroupsReq {
    string   game_code                                  = 1;  // 必填 游戏标识game_code
    string   media                                      = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountDraftAdgroup account_draft_adgroups = 3;  // 账号+草稿inner_adgroup_id列表信息
}

message AccountDraftAdgroup {
    string account_id       = 1;  // 必填 渠道账号id
    string inner_adgroup_id = 2;  // 必填 内部inner_adgroup_id
}

message DeleteDraftAdgroupsRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
}

    // 批量删除草稿 ad, POST, /api/v1/tiktok_advertise/delete_draft_ads
message DeleteDraftAdsReq {
    string   game_code                        = 1;  // 必填 游戏标识game_code
    string   media                            = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountDraftAd account_draft_ads = 3;  // 账号+草稿inner_ad_id列表信息
}

message AccountDraftAd {
    string account_id  = 1;  // 必填 渠道账号id
    string inner_ad_id = 2;  // 必填 内部inner_ad_id
}

message DeleteDraftAdsRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
}

    // 批量更新草稿campaign状态 , POST, /api/v1/tiktok_advertise/update_draft_campaign_status
message UpdateDraftCampaignStatusReq {
    string   game_code                                    = 1;  // 必填 游戏标识game_code
    string   media                                        = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountDraftCampaign account_draft_campaigns = 3;  // 账号+草稿inner_campaign_id列表信息
    string   target_status                                = 4;  // 目标草稿状态 仅支持 INDRAFT, INDRAFT_COMPLETED, INDRAFT_INCOMPLETED
}

message UpdateDraftCampaignStatusRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
}

    // 批量更新草稿adgroup状态 , POST, /api/v1/tiktok_advertise/update_draft_adgroup_status
message UpdateDraftAdgroupStatusReq {
    string   game_code                                  = 1;  // 必填 游戏标识game_code
    string   media                                      = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountDraftAdgroup account_draft_adgroups = 3;  // 账号+草稿inner_adgroup_id列表信息
    string   target_status                              = 4;  // 目标草稿状态 仅支持 INDRAFT, INDRAFT_COMPLETED, INDRAFT_INCOMPLETED
}

message UpdateDraftAdgroupStatusRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
}

    // 批量更新草稿ad状态 , POST, /api/v1/tiktok_advertise/update_draft_ad_status
message UpdateDraftAdStatusReq {
    string   game_code                        = 1;  // 必填 游戏标识game_code
    string   media                            = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountDraftAd account_draft_ads = 3;  // 账号+草稿inner_ad_id列表信息
    string   target_status                    = 4;  // 目标草稿状态 仅支持 INDRAFT, INDRAFT_COMPLETED, INDRAFT_INCOMPLETED
}

message UpdateDraftAdStatusRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
}

    // 查询 adgroup 下层 ad 状态统计信息, POST, /api/v1/tiktok_advertise/get_adgroup_sub_status_statistics
message  GetAdgroupSubStatusStatisticsReq {
    string   game_code                           = 1;  // 必填 游戏标识game_code
    string   media                               = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountAdgroupInfo account_adgroups = 3;  // 账号+adgroup_id列表信息
}

    // 账号 + inner_adgroup_id or adgroup_id
message AccountAdgroupInfo {
    string account_id       = 1;  // 必填 渠道账号id
    string inner_adgroup_id = 2;  // 内部 inner_adgroup_id [inner_adgroup_id or adgroup_id 至少存在一个不为空]
    string adgroup_id       = 3;  // 渠道 adgroup_id [inner_adgroup_id or adgroup_id 至少存在一个不为空]
}

    // ad 状态统计信息
message AdStatusStatistics {
    string status = 1;  // ad 状态
    uint32 num    = 2;  // ad 状态对应统计数量
                        //repeated string inner_ad_ids = 3;  // inner_ad_id 列表(状态为status)
}

message AdgroupSubStatusStatistics {
    string   account_id                       = 1;  // 必填 渠道账号id
    string   inner_adgroup_id                 = 2;  // 必填 内部 inner_adgroup_id
    string   adgroup_id                       = 3;  // 必填 渠道 adgroup_id
    repeated AdStatusStatistics ad_statistics = 4;  // ad 状态统计列表
}

message GetAdgroupSubStatusStatisticsRsp {
    aix.Result result                                        = 1;  // 返回结果
    string     trace_id                                      = 7;  // 请求trace_id
    repeated   AdgroupSubStatusStatistics adgroup_statistics = 2;  // adgroup统计列表信息
}

    // 查询 campaign 下层 adgroup,ad 状态统计信息, POST, /api/v1/tiktok_advertise/get_campaign_sub_status_statistics
message  GetCampaignSubStatusStatisticsReq {
    string   game_code                             = 1;  // 必填 游戏标识game_code
    string   media                                 = 2;  // 必填 渠道标识 eg: TikTok
    repeated AccountCampaignInfo account_campaigns = 3;  // 账号+campaign_id列表信息
}

    // adgroup 状态统计信息
message AdgroupStatusStatistics {
    string status = 1;  // adgroup 状态
    uint32 num    = 2;  // adgroup 状态对应统计数量
}

message CampaignSubStatusStatistics {
    string   account_id                                 = 1;  // 必填 渠道账号id
    string   inner_campaign_id                          = 2;  // 内部inner_campaign_id
    string   campaign_id                                = 3;  // 渠道campaign_id
    repeated AdgroupStatusStatistics adgroup_statistics = 4;  // adgroup 状态统计列表
    repeated AdStatusStatistics ad_statistics           = 5;  // ad 状态统计列表
}

message GetCampaignSubStatusStatisticsRsp {
    aix.Result result                                          = 1;  // 返回结果
    string     trace_id                                        = 7;  // 请求trace_id
    repeated   CampaignSubStatusStatistics campaign_statistics = 2;  // campaign统计列表信息
}

    // 创建应用app, POST, /api/v1/tiktok_advertise/create_app
message CreateAppReq {
    string game_code      = 1;  // 必填 游戏标识game_code
    string media          = 2;  // 必填 渠道标识 eg: TikTok
    string account_id     = 3;  // 必填 渠道账号id
    string download_url   = 4;  // 应用下载链接
    string partner        = 5;  // 第三方监测合作伙伴 支持: "Appsflyer", "Adjust"
    string click_url      = 6;  // 点击监测链接，由你的监测合作伙伴生成的点击监测链接，通常你可以在他们的网站上获取并复制到这个链接
    string impression_url = 7;  // 默认展示监测链接，由你的监测合作伙伴生成的展示监测链接，通常你可以在他们的网站上获取并复制到这个链接。
}

message CreateAppRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
    MetaInfo   meta     = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     app_id   = 3;  // 应用id
}

    // 创建身份认证, POST, /api/v1/tiktok_advertise/create_identity
message CreateIdentityReq {
    string game_code    = 1;  // 必填 游戏标识game_code
    string media        = 2;  // 必填 渠道标识 eg: TikTok
    string account_id   = 3;  // 必填 渠道账号id
    string icon         = 4;  // 身份认证头像url
    string display_name = 5;  // 身份认证显示名称
}

message CreateIdentityRsp {
    aix.Result result      = 1;  // 返回结果
    string     trace_id    = 7;  // 请求trace_id
    MetaInfo   meta        = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     identity_id = 3;  // 身份认证id
}

    // 查询身份认证列表, POST, /api/v1/tiktok_advertise/get_identity_list
message GetIdentityListReq {
    string game_code       = 1;  // 必填 游戏标识game_code
    string media           = 2;  // 必填 渠道标识 eg: TikTok
    string account_id      = 3;  // 必填 渠道账号id
    bool   create_if_empty = 4;  // 查询身份认证列表为空时，是否根据app_id自动创建一个身份认证. true: 是, false: 否
    string app_id          = 5;  // 配合create_if_empty使用
}

    // 身份认证结构
message Identity {
    string identity_id   = 1;  // 【与渠道一致】认证身份ID
    string identity_type = 2;  // 【与渠道一致】认证身份类型 eg: "CUSTOMIZED_USER"
    string display_name  = 3;  // 【与渠道一致】认证身份ID
    string profile_image = 4;  // 【与渠道一致】头像URL
}

message GetIdentityListRsp {
    aix.Result result                   = 1;  // 返回结果
    string     trace_id                 = 7;  // 请求trace_id
    MetaInfo   meta                     = 2;  // 元数据信息【包括: game_code & 账号信息】
    repeated   Identity   identity_list = 3;  // 身份认证列表
}

    // 创建程序化创意广告的草稿, POST, /api/v1/tiktok_advertise/create_draft_aco_ad
message CreateDraftAcoAdReq {
    string        game_code        = 1;  // 必填 游戏标识game_code
    string        media            = 2;  // 必填 渠道标识 eg: TikTok
    string        account_id       = 3;  // 必填 渠道账号id
    string        inner_adgroup_id = 4;  // 内部广告组唯一ID
    InternalAcoAd aco_ad           = 5;  // 程序创意广告信息
}

message CreateDraftAcoAdRsp {
    aix.Result result      = 1;  // 返回结果
    string     trace_id    = 7;  // 请求trace_id
    MetaInfo   meta        = 2;  // 元数据信息【包括: game_code & 账号信息】
    string     inner_ad_id = 3;  // 创建完成的内部ID: inner_ad_id (创意广告仅创建一条草稿ad)
}

// 取消campaign及其下层所有adgroup,ad的publishing状态, POST, /api/v1/tiktok_advertise/cancel_campaign_publishing
message CancelCampaignPublishingReq {
    string game_code         = 1;  // 必填 游戏标识game_code
    string media             = 2;  // 必填 渠道标识 eg: TikTok
    string account_id        = 3;  // 必填 渠道账号id
    string inner_campaign_id = 4;  // 内部campaign唯一ID
}

message CancelCampaignPublishingRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
    MetaInfo   meta     = 3;  // 元数据信息【包括: game_code & 账号信息】
}

// 取消adgroup及其下层所有ad的publishing状态, POST, /api/v1/tiktok_advertise/cancel_adgroup_publishing
message CancelAdgroupPublishingReq {
    string game_code        = 1;  // 必填 游戏标识game_code
    string media            = 2;  // 必填 渠道标识 eg: TikTok
    string account_id       = 3;  // 必填 渠道账号id
    string inner_adgroup_id = 4;  // 内部广告组唯一ID
}

message CancelAdgroupPublishingRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
    MetaInfo   meta     = 3;  // 元数据信息【包括: game_code & 账号信息】
}

// 取消ad的publishing状态, POST, /api/v1/tiktok_advertise/cancel_ad_publishing
message CancelAdPublishingReq {
    string game_code   = 1;  // 必填 游戏标识game_code
    string media       = 2;  // 必填 渠道标识 eg: TikTok
    string account_id  = 3;  // 必填 渠道账号id
    string inner_ad_id = 4;  // 内部广告唯一ID
}

message CancelAdPublishingRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
    MetaInfo   meta     = 3;  // 元数据信息【包括: game_code & 账号信息】
}

// 同步渠道属性数据, POST, /api/v1/tiktok_advertise/sync_media_attribute_data
message SyncMediaAttributeDataReq {
    repeated GameCodeAccount game_code_accounts = 1;  // 待同步game_code与账号列表信息
    string   media                              = 2;  // 必填 渠道标识 eg: TikTok
    string   sync_account_type                  = 3;  // 同步目标账号的类型. 默认为 ENABLE_ACCOUNT. ALL: 全量(包括无效)账号, ENABLE_ACCOUNT: 仅有效账号
}

// 单game_code + 多account_id
message GameCodeAccount {
    string   game_code          = 1;  // 必填 游戏标识game_code
    repeated string account_ids = 2;  // game_code下广告账号列表 【为空表示同步game_code所有账号】
}

message SyncMediaAttributeDataRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
}

// 查询 key 限频详情, POST, /api/v1/common/rate_limit
message RateLimitReq {
    string limit_app = 1;  // 必填 限频应用
    string limit_key = 2;  // 必填 限频应用key
    uint32 bucket    = 3;  // 默认为1. 限频桶容量, 允许最大QPS: (bucket + rate) req/s
    double rate      = 4;  // 必填. 限频速率. QPS: rate req/s
    uint32 take      = 5;  // 默认为1. 消费req数量
}

message RateLimitRsp {
    aix.Result result    = 1;  // 返回结果
    string     trace_id  = 2;  // 请求trace_id
    bool       allowed   = 3;  // true: 未被限频, false: 被限频, 等待 WaitNext 时间后可再次尝试
    uint64     wait_next = 4;  // 被限频. 等待 wait_next 【毫秒】后可重试
    uint32     left      = 5;  // 当前可用容量, 不超过bucket
}

// 获取翻译信息, POST, /api/v1/tiktok_advertise/get_translation
message GetTranslationReq {
    string   game_code                     = 1;  // 必填 游戏标识 game_code
    string   media                         = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id                    = 3;  // 渠道账号id 不填表示账号无关
    repeated TranslationParam translations = 4;  // 待翻译源内容列表
}

// 翻译 参数
message TranslationParam {
    string translation_type = 1;  // 翻译类型. AIX_CAMPAIGN_TYPE
    string source_content   = 2;  // 翻译源内容. APP_INSTALL. 为空则表示查询 translation_type 下所有配置翻译项列表
}

// 翻译项
message TranslationItem {
    string source_content = 1;  // 翻译源内容. 例如: APP_INSTALL.
    string target_content = 2;  // 翻译目标内容. 例如: App Installs
    bool   is_visible     = 3;  // 翻译【源内容】是否处于可见状态. (注：翻译源内容和目标内容仍然可用) true:可见  false:不可见，需要翻译内容，又不允许 source_content 可见
}

// 翻译 结果
message TranslationResult {
    string   translation_type      = 1;  // 翻译类型. AIX_CAMPAIGN_TYPE
    repeated TranslationItem items = 2;  // 翻译项列表
}

message GetTranslationRsp {
    aix.Result result                                = 1;  // 返回结果
    string     trace_id                              = 2;  // 请求trace_id
    repeated   TranslationResult translation_results = 3;  // 翻译结果列表
}

// 批量创建或更新TikTok渠道用户账号, POST, /api/v1/tiktok_advertise/save_media_accounts
message SaveMediaAccountsReq {
    string   media                         = 1;  // 必填 渠道标识 eg: TikTok
    repeated InternalMediaAccount accounts = 2;  // 账号列表 仅支持 TikTok
    string   save_type                     = 3;  // 保存类型. 默认为 DEFAULT. 支持: "DELETE_OLD_GAMECODE_ACCOUNT": 先删除旧gameCode下所有 account列表，再进行完整新增
}

message GameCodeAccountIdInfo {
    string   game_code          = 1;
    repeated string account_ids = 2;
}

message SaveMediaAccountsRsp {
    aix.Result result                                      = 1;  // 返回结果
    string     trace_id                                    = 2;  // 请求trace_id
    repeated   GameCodeAccountIdInfo game_code_account_ids = 3;  // 保存成功的 game_code及其 account_id列表
}

// 同步属性数据设置status(仅测试时使用, 现网下线此接口), POST, /api/v1/tiktok_advertise/debug_set_media_monitor_status
message DebugSetMediaMonitorStatusReq {
    string   game_code              = 1;  // 必填 游戏标识game_code
    string   media                  = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id             = 3;  // 必填 渠道账号id
    string   entity_type            = 4;  // 必填 campaign, adgroup, ad
    repeated DebugMonitorData datas = 5;  // 待设置的渠道广告信息列表
    int32    reset_after            = 6;  // 等待 reset_after 秒后，自动重置为渠道的status. 单位: 秒
}

message DebugMonitorData {
    string entity_id        = 1;  // 必填 entity_type 对应的渠道id
    string secondary_status = 2;  // tiktok 渠道 secondary_status
}

message DebugSetMediaMonitorStatusRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 2;  // 请求trace_id
}

message GetCarriersReq {
    string game_code  = 1;  // 必填 游戏标识game_code
    string media      = 2;  // 必填 渠道标识 eg: TikTok
    string account_id = 3;  // 必填 渠道账号id
}

message Carrier {
    string carrier_id = 1;  // 运营商ID
    bool   in_use     = 2;  // 运营商是否可用于定向。
    string name       = 3;  // 运营商名称
}

message Country {
    string   country_code     = 1;  // 国家或地区代码
    repeated Carrier carriers = 2;  // 当前国家或地区内运营商列表
}

message GetCarriersRsp {
    aix.Result result            = 1;  // 返回结果
    string     trace_id          = 7;  // 请求trace_id
    repeated   Country countries = 2;  // 国家或地区列表
}

message GetDeviceModelsReq {
    string game_code  = 1;  // 必填 游戏标识game_code
    string media      = 2;  // 必填 渠道标识 eg: TikTok
    string account_id = 3;  // 必填 渠道账号id
}

message DeviceModel {
    string   device_model_id         = 1;  // 设备机型ID
    string   device_model_name       = 2;  // 设备机型名称
    repeated string child_device_ids = 3;  // 下级设备机型 ID 列表
    bool     is_active               = 4;  // 设备机型是否活跃。只有活跃的设备机型才可以用于定向
    string   level                   = 5;  // 设备机型层级。枚举值: BRAND,SERIES,MODEL
    string   os_type                 = 6;  // 操作系统类型，枚举值: ANDROID,IOS
}

message GetDeviceModelsRsp {
    aix.Result result                     = 1;  // 返回结果
    string     trace_id                   = 7;  // 请求trace_id
    repeated   DeviceModel deviece_models = 2;  // 设备机型列表
}

message GetOsVersionsReq {
    string game_code  = 1;  // 必填 游戏标识game_code
    string media      = 2;  // 必填 渠道标识 eg: TikTok
    string account_id = 3;  // 必填 渠道账号id
    string os_type    = 4;  // 必填 枚举值: ANDROID,IOS,IOS14_PLUS,IOS14_MINUS
}

message OsVersion {
    string os_id   = 1;  // 受众操作系统版本 ID
    string os_type = 2;  // 操作系统类型
    string version = 3;  // 受众操作系统版本
    string name    = 4;  // 受众操作系统版本名称
}

message GetOsVersionsRsp {
    aix.Result result                = 1;  // 返回结果
    string     trace_id              = 7;  // 请求trace_id
    repeated   OsVersion os_versions = 2;  // 设备机型列表
}

message GetAppsReq {
    string   game_code               = 1;  // 必填 游戏标识game_code
    string   media                   = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id              = 3;  // 必填 渠道账号id
    repeated string app_platform_ids = 4;  // 可传入 app platform ID 列表用于筛选
}

message App {
    string account_id         = 1;   // 渠道账号id
    string app_platform_id    = 2;   // 应用平台ID
    string app_name           = 3;   // APP应用名称
    string skan_allowed       = 4;   // 该应用是否可用于创建iOS14专属推广系列。允许值：ALLOWED, NOT_ALLOWED。如果想将本应用用于创建iOS14专属推广系列，广告主需要到TikTok Ads Manager中的事件管理器中确认应用所有权。
    string download_url       = 5;   // 应用下载链接
    string app_id             = 6;   // 应用ID
    string package_name       = 7;   // 应用包名
    string platform           = 8;   // 应用平台，可选值: ANDROID,IOS,PC,UNKNOWN,UNLIMITED
    string enable_retargeting = 9;   // 表示您是否愿意启用重定向。枚举值：RETARGETING/NON_RETARGETING（重定向/非重定向）
    string create_time        = 10;  // 创建时间
}

message GetAppsRsp {
    aix.Result result   = 1;  // 返回结果
    string     trace_id = 7;  // 请求trace_id
    repeated   App apps = 2;  // 应用列表信息
}

message GetHashTagRecommandsReq {
    string   game_code       = 1;  // 必填 游戏标识game_code
    string   media           = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id      = 3;  // 必填 渠道账号id
    repeated string keywords = 4;  // 关键词，用于生成一组相关的视频话题标签（hashtag）。如果你传入了多个不相关的关键词，可能不会返回有效推荐
}

message RecommandKeyword {
    string keyword        = 1;  // 推荐话题标签
    string keyword_id     = 2;  // 话题标签ID。本ID可传入广告组创建时的action_category_ids字段，用于对相关的标签进行定向
    string keyword_status = 3;  // 标签状态。枚举值: ONLINE (可用), OFFLINE (不可用)
}

message GetHashTagRecommandsRsp {
    aix.Result result                              = 1;  // 返回结果
    string     trace_id                            = 7;  // 请求trace_id
    repeated   RecommandKeyword recommend_keywords = 2;  // 应用列表信息
}

message GetHashTagReq {
    string   game_code          = 1;  // 必填 游戏标识game_code
    string   media              = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id         = 3;  // 必填 渠道账号id
    repeated string keyword_ids = 4;  // 话题标签ID列表
}

message GetHashTagRsp {
    aix.Result result                              = 1;  // 返回结果
    string     trace_id                            = 7;  // 请求trace_id
    repeated   RecommandKeyword recommend_keywords = 2;  // 应用列表信息
}

message GetActionCategoriesReq {
    string   game_code                 = 1;  // 必填 游戏标识game_code
    string   media                     = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id                = 3;  // 必填 渠道账号id
    repeated string special_industries = 4;  // 广告分类。 枚举值:
                                             // HOUSING（房地产，房屋保险，抵押贷款或相关的广告。）
                                             // EMPLOYMENT（工作机会，实习机会，职业认证项目或相关的广告。）
                                             // CREDIT(信用卡申请，汽车贷款，长期融资或相关的广告。）。
                                             // 注意：如果返回值为空([])，则表示该分类没有支持的行业。
}

message ActionCategory {
    string   description               = 1;  // 行为描述
    string   action_category_id        = 2;  // 行为 ID
    int64    level                     = 3;  // 层级
    repeated string sub_category_ids   = 4;  // 子级行为ID分类
    string   name                      = 5;  // 行为名称
    repeated string special_industries = 6;  // 广告行业分类。 枚举值:
                                             // HOUSING（房地产，房屋保险，抵押贷款或相关的广告。）
                                             // EMPLOYMENT（工作机会，实习机会，职业认证项目或相关的广告。）
                                             // CREDIT(信用卡申请，汽车贷款，长期融资或相关的广告。）。
                                             // 注意：如果返回值为空([])，则表示该分类没有支持的行业。
    string   action_scene = 7;               // 行为场景。枚举值：CREATOR_RELATED, VIDEO_RELATED。
}

message GetActionCategoriesRsp {
    aix.Result result                           = 1;  // 返回结果
    string     trace_id                         = 7;  // 请求trace_id
    repeated   ActionCategory action_categories = 2;  // 应用列表信息
}

message GetInterestCategoriesReq {
    string   game_code                 = 1;  // 必填 游戏标识game_code
    string   media                     = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id                = 3;  // 必填 渠道账号id
    int64    version                   = 4;  // 兴趣分类版本。枚举值：1(对应 interest_category), 2 (对应 interest_category_v2)。默认值: 2。
    string   language                  = 5;  // 兴趣分类语言。默认值：en,目前支持的语言有： en, zh, ja, de, es, fr, id, it, ko, ru, th, tr, vi, ar, pt, ms。枚举值详见枚举值-语言。
    repeated string   placements       = 6;  // 广告版位。枚举值详见[附录-版位](https://ads.tiktok.com/marketing_api/docs?id=****************)。不同版位可选的兴趣分类可能不同，仅当 version为2 时有效。
    repeated string special_industries = 7;  // 广告行业分类。 枚举值:
                                             // HOUSING（房地产，房屋保险，抵押贷款或相关的广告。）
                                             // EMPLOYMENT（工作机会，实习机会，职业认证项目或相关的广告。）
                                             // CREDIT(信用卡申请，汽车贷款，长期融资或相关的广告。）。
}

message InterestCategory {
    string   interest_category_id      = 1;  // 兴趣分类ID。
    string   interest_category_name    = 2;  // 兴趣分类名称。
    int64    level                     = 3;  // 层级
    repeated string sub_category_ids   = 4;  // 子级兴趣分类 ID 列表
    repeated string special_industries = 5;  // 广告行业分类。 枚举值:
                                             // HOUSING（房地产，房屋保险，抵押贷款或相关的广告。）
                                             // EMPLOYMENT（工作机会，实习机会，职业认证项目或相关的广告。）
                                             // CREDIT(信用卡申请，汽车贷款，长期融资或相关的广告。）。
                                             // 注意：如果返回值为空([])，则表示该分类没有支持的行业。
    repeated string placements = 6;          // 行为场景。枚举值：CREATOR_RELATED, VIDEO_RELATED。
}

message GetInterestCategoriesRsp {
    aix.Result result                               = 1;  // 返回结果
    string     trace_id                             = 7;  // 请求trace_id
    repeated   InterestCategory interest_categories = 2;  // 兴趣分类列表。若需确定游戏相关兴趣分类ID对应的兴趣分类名称，请查看[游戏相关兴趣分类](https://ads.tiktok.com/marketing_api/docs?id=****************)。
}

message GetLanguagesReq {
    string game_code  = 1;  // 必填 游戏标识game_code
    string media      = 2;  // 必填 渠道标识 eg: TikTok
    string account_id = 3;  // 必填 渠道账号id
}

message Language {
    string code = 1;  // 语言代码，用于定向。
    string name = 2;  // 语言名称。
}

message GetLanguagesRsp {
    aix.Result result             = 1;  // 返回结果
    string     trace_id           = 7;  // 请求trace_id
    repeated   Language languages = 2;  // 受众语言列表。
}

message GetLocationsReq {
    string   game_code           = 1;   // 必填 游戏标识game_code
    string   media               = 2;   // 必填 渠道标识 eg: TikTok
    string   account_id          = 3;   // 必填 渠道账号id
    repeated string   placements = 4;   // 广告目标版位列表。枚举值请查看[枚举值 - 版位](https://ads.tiktok.com/marketing_api/docs?id=****************)。
    string   objective_type      = 5;   // 广告目标版位列表。推广目标。枚举值及描述请查看[推广目标](https://ads.tiktok.com/marketing_api/docs?id=****************)。
    string   operating_system    = 6;   // 定向受众设备的操作系统。枚举值: ANDROID, IOS。
    string   brand_safety_type   = 7;   // 品牌安全类型。枚举值：
                                        // THIRD_PARTY: 使用第三方品牌安全方案。你还需要传入brand_safety_partner 字段。
                                        // LIMITED_INVENTORY: 使用TikTok品牌安全方案。目前仅支持的等级为限制库存，该等级会实施较为严格的内容安全过滤，所有被识别为有风险的内容都会排除。
    string   brand_safety_partner = 8;  // 品牌安全合作伙伴。只在brand_safety为THIRD_PARTY有效。枚举值: IAS, OPEN_SLATE。
    string   level_range          = 9;  // 想要返回的地域层级。 枚举值:
                                        // ALL: 返回所有层级的地域。
                                        // TO_COUNTRY：只返回国家或地区级地域。
                                        // TO_PROVINCE：返回国家和省级地域。指定市场区域和都会区属于省级地域。
                                        // TO_CITY: 返回国家、省、市级地域。
                                        // TO_DISTRICT: 返回国家、省、市、区级地域。
}

message RegionInfo {
    string   location_id           = 1;  // 地域ID。
    string   name                  = 2;  // 地域名称。
    string   parent_id             = 3;  // 上级地域ID。如果没有上级地域，返回0。
    string   region_code           = 4;  // 国家或地区代码。例如： "DE"。
    repeated string next_level_ids = 5;  // 下级地域ID列表。
    string   area_type             = 6;  // 地域类型。枚举值: ADMIN, METROPOLITAN_OR_DMA。
    string   level                 = 7;  // 地域层级。枚举值: COUNTRY（国家或地区级）, PROVINCE, CITY, DISTRICT。 指定市场区域以及都会区属于省级地域。
    bool     support_below_18      = 8;  // 该地域是否支持向18岁以下人群投放广告。
}

message GetLocationsRsp {
    aix.Result result                 = 1;  // 返回结果
    string     trace_id               = 7;  // 请求trace_id
    repeated   string region_list     = 2;  // 国家或地区代码列表。
    repeated   RegionInfo region_info = 3;  // 可投放地域信息。
}

message GetAudiencesReq {
    string   game_code           = 1;  // 必填 游戏标识game_code
    string   media               = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id          = 3;  // 必填 渠道账号id
    repeated string audience_ids = 4;  // 指定audience_ids, 为空表示获取所有
    int64    page                = 5;  // 当前页数。默认值: 1。取值范围: ≥ 1
    int64    page_size           = 6;  // 分页大小。默认值: 10
}

message Audience {
    string audience_id   = 1;  // 受众群体ID。
    string audience_type = 2;  /* 受众群体类型。 枚举值：
                                            Customer File Audience (客户文件受众)。
                                            Engagement Audience (广告互动受众)。
                                            App Activity Audience (应用活动受众)。
                                            Website Audience (网站访客受众)。
                                            Lookalike Audience (相似受众)。*/
    string name           = 4;  // 受众群体名称。
    bool   shared         = 5;  // 是否是共享的（非本人创建）受众群体ID。
    int64  cover_num      = 6;  // 覆盖人数。
    string calculate_type = 7;  // 加密类型。只适用于客户文件受众。枚举值详见[枚举值-加密类型](https://ads.tiktok.com/marketing_api/docs?id=****************)
    string create_time    = 8;  // 创建时间，GMT时间。
}

message PageInfo {
    int64 page         = 1;
    int64 page_size    = 2;
    int64 total_number = 3;
    int64 total_page   = 4;
}

message GetAudiencesRsp {
    aix.Result result             = 1;  // 返回结果
    string     trace_id           = 7;  // 请求trace_id
    repeated   Audience audiences = 2;  // 受众群体列表
    PageInfo   page_info          = 3;  // 分页信息
}

    // 获取应用转化事件, POST, /api/v1/tiktok_advertise/get_app_optimization_event
message GetAppOptimizationEventReq {
    string   game_code         = 1;  // 必填 游戏标识game_code
    string   media             = 2;  // 必填 渠道标识 eg: TikTok
    string   account_id        = 3;  // 必填 渠道账号id
    string   objective_type    = 4;  // 【与渠道一致】必填 推广目标 eg: APP_INSTALL
    repeated string placements = 5;  // 【与渠道一致】必填 版位列表
    string   app_id            = 6;  // 【与渠道一致】推广应用ID
    string   optimization_goal = 7;  // 【与渠道一致】优化目标
    string   get_type          = 8;  // 是否自动转换. 取值: "", "DEFAULT", "AUTO_CONVERSION". AUTO_CONVERSION:自动转换 optimization_goal
}

message DeepOptimizationEvents {
    string availability_status     = 1;  // 【与渠道一致】事件可用状态，枚举值: INACTIVE（不可用）, INSUFFICIENT_POSTBACK（不可用：回传事件不足）, READY（可用）
    string optimization_event      = 2;  // 【与渠道一致】深度转化事件
    string optimization_event_desc = 3;  // 转化事件描述, 仅用于展示
}

message OptimizationEvents {
    repeated DeepOptimizationEvents deep_optimization_events = 1;
    string   availability_status                             = 2;  // 【与渠道一致】事件可用状态，枚举值: INACTIVE（不可用）, INSUFFICIENT_POSTBACK（不可用：回传事件不足）, READY（可用）
    string   optimization_event                              = 3;  // 【与渠道一致】深度转化事件
    string   optimization_event_desc                         = 4;  // 转化事件描述, 仅用于展示
}

message GetAppOptimizationEventRsp {
    aix.Result result                    = 1;  // 返回结果
    string     trace_id                  = 7;  // 请求trace_id
    MetaInfo   meta                      = 2;  // 元数据信息【包括: game_code & 账号信息】
    repeated   OptimizationEvents events = 3;  // 应用转化事件列表
}

    // 列举targeting模板, POST, /api/v1/tiktok_advertise/get_targeting_templates
message GetTargetingTemplatesReq {
    string   game_code                   = 1;  // 必填 游戏标识game_code
    string   media                       = 2;  // 必填 渠道标识 eg: TikTok
}

message TargetingTemplate {
    string          template_id          = 1;
    string          template_name        = 2;
    ModuleTargeting targeting            = 3;  // targeting信息
}

message GetTargetingTemplatesRsp {
    aix.Result                 result       = 1;  // 返回结果
    string                     trace_id     = 2;  // 请求trace_id
    repeated TargetingTemplate targetings   = 3;  // targeting模板列表
}

    // 创建/修复targeting模板, POST, /api/v1/tiktok_advertise/save_targeting_template
message SaveTargetingTemplateReq {
    string                     game_code = 1;  // 必填 游戏标识game_code
    string                     media     = 2;  // 必填 渠道标识 eg: TikTok
    TargetingTemplate          targeting = 3;  // targeting模板
}

message SaveTargetingTemplateRsp {
    aix.Result                 result    = 1;  // 返回结果
    string                     trace_id  = 2;  // 请求trace_id
    TargetingTemplate          targeting = 3;  // 返回targeting模板结果
}

    // 删除targeting模板, POST, /api/v1/tiktok_advertise/delete_targeting_templates
message DeleteTargetingTemplatesReq {
    string          game_code            = 1;  // 必填 游戏标识game_code
    string          media                = 2;  // 必填 渠道标识 eg: TikTok
    repeated string template_ids         = 3;  // targeting模板ids
}

message DeleteTargetingTemplatesRsp {
    aix.Result                 result    = 1;  // 返回结果
    string                     trace_id  = 2;  // 请求trace_id
}


// 拉取广告变更历史, POST, /api/v1/tiktok_advertise/get_changelog
message Changelog {
    string   account_id       = 1;  // 账号id
    string   change_date_time = 2;  // 变更时间
    string   object_type      = 3;  // 变更所属的广告对象
    string   object_id        = 4;  // 变更所属的广告id  
    string   objective        = 5;  // 变更对象
    string   change_type      = 6;  // 变更类型
    repeated string changes   = 7;  // 变更详情
    string   operator         = 8;  // 操作人
}

message GetChangelogReq {
    string   game_code             = 1;
    string   account_id            = 2;   // 账号id
    string   campaign_id           = 3;   // campaign_id, adgroup_id, ad_id 三选一
    string   adgroup_id            = 4;   // campaign_id, adgroup_id, ad_id 三选一
    string   ad_id                 = 5;   // campaign_id, adgroup_id, ad_id 三选一
    repeated string   change_types = 6;   // 变更类型
    repeated string   object_types = 7;   // 变更对象
    string   start_time            = 8;   // 开始时间，格式：2023-08-25 10:00, 注意要以account的时区
    string   end_time              = 9;   // 结束时间，格式：2023-08-25 10:59, 注意要以account的时区
    int32    page                  = 10;  // 分页数，从1开始
    int32    page_size             = 11;  // 每条条数，最多1000条
}

message GetChangelogRsp {
    aix.Result result          = 1;  // 返回结果
    string     trace_id        = 2;  // 请求trace_id
    repeated   Changelog  list = 3;  // 变更历史列表
    int32      total           = 4;  // 总数
}

// 拉取渠道图片视频素材信息 POST, /api/v1/tiktok_advertise/get_media_image_video
message GetMediaImageVideoReq {
    string   game_code        = 1;  // 必填 游戏标识game_code
    string   account_id       = 2;  // 必填 渠道账号id
    repeated string image_ids = 3;  // 图片id列表
    repeated string video_ids = 4;  // 视频id列表
}

// 图片视频信息
message ImageVideo {
    string id            = 1;  // 图片视频id
    int32  type          = 2;  // 1-视频，2-图片
    string name          = 3;  // 名称
    int32  width         = 4;  // 宽
    int32  height        = 5;  // 高
    string thumbnail_url = 6;  // 缩略图
    string video_url     = 7;  // 视频预览地址
}

message GetMediaImageVideoRsp {
    aix.Result result            = 1;
    string     trace_id          = 2;  // 请求trace_id
    repeated   ImageVideo assets = 3;  // 素材信息
}

    // --------------------------------------------------- 【接口定义】 end        ---------------------------------------------------

