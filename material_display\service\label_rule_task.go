package service

import (
	"context"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/arthub_sync"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
)

// AddLabelRuleTask 提交标签规则批量打标任务
func AddLabelRuleTask(ctx context.Context, req *pb.AddLabelRuleTaskReq, rsp *pb.AddLabelRuleTaskRsp) error {
	user := utils.GetHeaderUser(ctx)
	if user == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "invalid header user")
	}
	gameCode := req.GetGameCode()
	if gameCode == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "invalid game_code")
	}
	cosFile := req.GetCosFile()
	if cosFile == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "invalid cos_file")
	}

	ruleType, err := repo.GetGameLabelRuleType(ctx, gameCode)
	if err != nil {
		return err
	}

	task := &model.TbLabelRuleTask{
		GameCode:   gameCode,
		CosFile:    cosFile,
		Type:       int32(ruleType),
		Status:     constant.LabelRuleTaskStatusPending,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
		CreateUser: user,
		UpdateUser: user,
	}
	err = repo.InsertLabelRuleTask(ctx, task)
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.InsertLabelRuleTask error")
	}

	return nil
}

// AddLabelRuleTaskV2 V2提交标签规则批量打标任务(支持serial和asset层级)
func AddLabelRuleTaskV2(ctx context.Context, req *pb.AddLabelRuleTaskV2Req, rsp *pb.AddLabelRuleTaskV2Rsp) error {
	user := utils.GetHeaderUser(ctx)
	if user == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "invalid header user")
	}
	gameCode := req.GetGameCode()
	if gameCode == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "invalid game_code")
	}
	cosFile := req.GetCosFile()
	if cosFile == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "invalid cos_file")
	}

	ruleType, err := repo.GetGameLabelRuleType(ctx, gameCode)
	if err != nil {
		return err
	}

	task := &model.TbLabelRuleTask{
		GameCode:   gameCode,
		CosFile:    cosFile,
		Type:       int32(ruleType),
		Status:     constant.LabelRuleTaskStatusPending,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
		CreateUser: user,
		UpdateUser: user,
		LabelLevel: constant.LabelLevelAsset,
	}
	err = repo.InsertLabelRuleTask(ctx, task)
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "repo.InsertLabelRuleTask error")
	}

	return nil
}
