package main

import (
	"context"
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"
)

func init() {
	setting.SetupByPath("../conf/server_conf.yaml")
	log.Setup()
	postgresql.Setup()
}

func main() {
	startID := int64(45839)
	limit := 100
	ctx := log.NewSessionIDContext()
	for i := 0; i < 1000; i++ {
		db := postgresql.GetDBWithContext(ctx)

		var founds []*model.TbAssetLabelRule
		query := db.Model(&founds).Where("id > ?", startID).Order("id").Limit(limit)
		err := query.Select()
		if err != nil {
			panic(err)
		}

		length := len(founds)

		for _, rule := range founds {
			if rule.ID > startID {
				startID = rule.ID
			}

			var finalLables []model.AssetLabel
			uniq := make(map[string]bool)
			for _, label := range rule.Labels {
				label.LabelName = ""
				key := fmt.Sprintf("%v,%v", label.FirstLabel, label.SecondLabel)
				if uniq[key] {
					log.DebugContextf(ctx, "rule: %v, dup label:%v", rule.ID, key)
					continue
				}

				uniq[key] = true
				finalLables = append(finalLables, label)
			}
			rule.Labels = finalLables

			err := updateRuleLabels(ctx, rule)
			if err != nil {
				panic(err)
			}
		}

		if length < limit {
			break
		}
	}
}

func updateRuleLabels(ctx context.Context, rule *model.TbAssetLabelRule) error {
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(rule)
	_, err := query.WherePK().Column("labels").Update()

	return err
}
