// Package service 服务接口实现代码
package service

import (
	"strings"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// GetMaterialInfo 拉取素材详情
func GetMaterialInfo(ctx *gin.Context, req *pb.GetMaterialInfoReq, rsp *pb.GetMaterialInfoRsp) error {
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if gameCode == "" {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	// 查询素材overview
	overviews, err := data.QueryCreativeOverviewByIDList(ctx, gameCode, []string{req.GetAssetId()})
	if err != nil {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "GetMaterialInfo data.QueryCreativeOverviewByIDList, err: %v", err)
	}
	if len(overviews) == 0 {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "GetMaterialInfo no such asset id:%v", req.GetAssetId())
	}

	detail, err := data.GetMaterialDetail(ctx, gameCode, req.GetAssetId())
	if err != nil {
		return errs.New(int(pbAix.DemoServerErrCode_DEMO_SERVER_QUERY_DB_ERROR), "error data.GetMaterialDetail, err: %v", err)
	}

	rsp.MaterialExt = detail2proto(ctx, detail)
	// 更新时间使用overview的更新时间
	rsp.MaterialExt.Universal.UpdateTime = overviews[0].UpdatedDate
	// 创建时间
	rsp.MaterialExt.Universal.CreateTime = overviews[0].CreateDate
	// 素材类型
	rsp.MaterialExt.Universal.FormatType = int32(overviews[0].FormatType)
	return nil
}

// detail2proto 详情的db结构到pb
func detail2proto(ctx *gin.Context, detail *model.CreativeAssetDetails) *pb.MaterialExt {
	if detail == nil {
		return nil
	}
	return &pb.MaterialExt{
		AssetId: cast.ToString(detail.AssetID),
		Universal: &pb.Universal{
			Format:     detail.Format,
			Size:       detail.Size,
			Updater:    detail.Updater,
			UpdateTime: detail.UpdateDate,
			Creator:    detail.Creator,
			Cover:      detail.Cover,
		},
		Label: &pb.Label{
			RobotFirstLabel:   strings.Split(detail.RobotFirstLabel, ","),
			RobotSecondLabel:  strings.Split(detail.RobotSecondLabel, ","),
			ManualFirstLabel:  strings.Split(detail.ManualFirstLabel, ","),
			ManualSecondLabel: strings.Split(detail.ManualSecondLabel, ","),
		},
		Statistics: &pb.Statistics{
			// todo: take users count 写入
		},
		Video: &pb.Video{
			Width:             detail.Width,
			High:              detail.High,
			Duration:          cast.ToUint32(detail.Duration),
			FrameRate:         detail.FrameRate,
			AspectRatio:       detail.AspectRatio,
			BitRate:           detail.BitRate,
			CompressionFormat: detail.CompressionFormat,
		},
	}
}
