syntax = "proto3";

package facebook_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/facebook_advertise";

import "protos/aix/aix_common_message.proto";
import "google/protobuf/struct.proto";

// FB广告campaign请求, POST, /api/v1/facebook_advertise/CampaignCreate
message CampaignCreateReq {
    string account_id           = 1;  // ad_account账号
    string uuid                 = 2;  // 前端生成随机ID
    string create_name          = 3;  // campaign名字
    string campaign_objective   = 4;  // 广告类型 APP_INSTALLS, CONVERSIONS
    string mobile_app_platform  = 5;  // app_install类型广告投放平台 "ios", "android"
    string app_campaign_type    = 6;  // app_install类型广告campaign子类型(aaa或者aa的) SMART_APP_PROMOTION(aaa) GUIDED_CREATION(aa)
    string buying_type          = 7;  // buying_type类型 AUCTION
    string game_code            = 8;  // 业务名
    string strategy             = 9;  // strategy策略
    string lifetime_budget      = 10; //
    string daily_budget         = 11; //
    string bid_strategy         = 12; // 花费策略 "Highest volume or value", "Cost per result goal", "Bid cap", "ROAS goal"
  }

message CampaignCreateRsp {
    aix.Result result           = 1;
    string inner_campaign_id    = 2;  // 返回创建成功的campaignid
}

// 临时创建audience
message FlexibleSpec {
    string id       = 1;
    string name     = 2;
    string type     = 3;
}
message FlexibleSpecMeta {
  repeated FlexibleSpec    interests              = 1;
  repeated FlexibleSpec    behaviors              = 2;
  repeated FlexibleSpec    demographics           = 3;
  repeated FlexibleSpec    connections            = 4;
  repeated FlexibleSpec    friends_of_connections = 5;
  repeated FlexibleSpec    custom_audiences       = 6;
  repeated FlexibleSpec    college_years          = 7;
  repeated FlexibleSpec    education_majors       = 8;
  repeated FlexibleSpec    education_schools      = 9;
  repeated FlexibleSpec    education_statuses     = 10;
  repeated FlexibleSpec    family_statuses        = 11;
  repeated FlexibleSpec    home_value             = 12;
  repeated FlexibleSpec    interested_in          = 13;
  repeated FlexibleSpec    income                 = 14;
  repeated FlexibleSpec    industries             = 15;
  repeated FlexibleSpec    life_events            = 16;
  repeated FlexibleSpec    user_adclusters        = 17;
  repeated FlexibleSpec    work_positions         = 18;
  repeated FlexibleSpec    work_employers         = 19;
}

// 新创建的audience
message CreateAudience {
    string audience_name                        = 1;
    repeated string location                    = 2;
    uint32 gender                               = 3; // 0:全部 1:男 2:女
    repeated string language                    = 4; // 语言
    uint32 age_max                              = 5; // 最大年龄
    uint32 age_min                              = 6; // 最小年龄
    FlexibleSpecMeta flexible_spec              = 7; // 细分定位
}

// 获取已有的audience
message CustomAudience {
    string id       = 1;
    string name     = 2;
}
message AudiencePlatform {
    repeated CustomAudience custom_audiences = 1;
    repeated CustomAudience excluded_custom_audiences = 2;
}

// 事件类型
message SelectAppEvent {
    string application_id = 1;
    string custom_event_type = 2;
    string object_store_url = 3;
    string custom_event_str = 4;
    string event_id = 5;
    string temp_audience_id = 6;   // 临时audience_id, 用于延迟发布
    string pixel_id = 7;
}

// manual
message ManualPlatform {
    repeated string publisher_platforms = 1;
    repeated string facebook_positions = 2;
    repeated string instagram_positions = 3;
    repeated string device_platforms = 4;
    repeated string messenger_positions = 5;
    repeated string audience_network_positions = 6;
    repeated string wireless_carrier = 7;    // e.g "Wifi"
}

message AdvanceTargeting {
    repeated string included_devices = 1;
    repeated string excluded_devices = 2;
    string min_os_ver = 3;
    string max_os_ver = 4;
}

// FB广告adset请求, POST, /api/v1/facebook_advertise/AdSetCreate
message AdSetCreateReq {
    string  account_id               = 1;  // ad_account账号
    string  uuid                     = 2;  // 前端生成随机ID
    string  ad_set_name              = 3;  // ad_set名字
    string  inner_campaign_id        = 4;  // 内部campaign_id
    string  ad_type                  = 5;  // 广告能力(暂时不用)
    bool    is_dynamic               = 6;  // 是否开启动态广告
    string  placement                = 7;  // 自动手动 Automatic, Manual
    ManualPlatform      manual       = 8;  // 手动选项(通过接口给出)
    AudiencePlatform    audience_custom     = 10;  // 平台已有audience
    CreateAudience      audience            = 11;  // 临时用户群体

    string              lifetime_budget             = 12; //
    string              daily_budget                = 13; //
    string              optimization_goal           = 14; // APP_INSTALLS, OFFSITE_CONVERSIONS(events), VALUE, LINK_CLICKS
    string              start_time                  = 15; // 开始时间
    string              end_time                    = 16; // 结束时间
    string              cost_control                = 17; // 花费控制
    string              bid_strategy                = 18; // 花费策略 "Lowest Cost", "Set a bid cap"
    SelectAppEvent      promoted_object             = 19; // 事件
    string              when_you_get_charged        = 20; // impression
    string              min_roas_control            = 21; // value使用花费
    string              game_code                   = 22; // 业务名
    repeated string     audience_custom_region      = 23; // audience大洲信息，客户端使用，无需关心
    AdvanceTargeting    advance_targeting           = 24; // advance targeting
    string              conversion_event_location   = 25; // Conversion event location, conversion类型该值才有效, 值为 WEBSITE, APP
    repeated string     pacing_type                 = 26; // 加速类型, 对应渠道的delivery type, 值为standard, no_pacing
    string              mobile_app_platform         = 27; // conversions.app类型广告才有效, 投放平台 "ios", "android"
    string              custom_client_info          = 28; // 客户端自定义信息, 比如：audience大洲信息，客户端使用，无需关心
}

message AdSetCreateRsp {
    aix.Result result           = 1;
    string inner_ad_set_id      = 2;  // 返回创建成功的adsetid
}


message VideoInfo {
    string video_id                          = 1;
    string image_hash                        = 2;
    string image_url                         = 3;
}

message Tracking {
    string url_parameters                   = 1;
    string view_tags                        = 2;
}

message Destination {
    string website_url                      = 1;
    string display_url                     = 2;
    string deeplink_url                        = 3;
}

// FB广告ad请求, POST, /api/v1/facebook_advertise/AdCreate
message AdCreateReq {
    string game_code                        = 1;  // 业务名
    string account_id                       = 2;  // ad_account账号
    string uuid                             = 3;  // 前端生成随机ID
    string page_id                          = 4;  // 选择的pageid
    string inner_ad_set_id                  = 5;  // 内部ad-set-id
    string ad_name                          = 6;  // ad名字
    repeated string select_image_hash_list  = 8;  // 选择的image
    repeated VideoInfo select_video         = 9;  // 选择的video
    repeated string primary_text            = 10; // 自定义内容
    repeated string headline                = 11; // 自定义内容
    repeated string call_to_action          = 12; // 广告动作 INSTALL_MOBILE_APP, INSTALL_APP, PLAY_GAME, SIGN_UP
    repeated string description             = 13; // 自定义内容
    Destination destination                 = 14; // 目标
    Tracking tracking                       = 15; // 转换追踪，当前只有conversion类型中的website才会使用
}

message AdCreateRsp {
    aix.Result result           = 1;
    string inner_ad_id          = 2;  // 返回创建成功的adid
}


// FB广告发布, POST, /api/v1/facebook_advertise/AdvertisePublish
message AdvertisePublishReq {
    string game_code                        = 1;  // 业务名
    string account_id                       = 2;  // ad_account账号
    string uuid                             = 3;  // 前端生成随机ID
    string inner_ad_id                      = 4;  // 发布需要的内部adid
}
message AdvertisePublishRsp {
    aix.Result result                       = 1;
    string media_campaign_id                = 2;
    string media_ad_set_id                  = 3;
    string media_ad_id                      = 4;
}

// 获取配置信息, POST, /api/v1/facebook_advertise/GetMediaConfig
message GetMediaConfigReq {
    string uuid                             = 1;
    string media                            = 2; // 渠道 Facebook, google
    string game_code                        = 3; // 游戏业务
    string campaign_objective               = 4; // 广告类型 APP_INSTALLS, CONVERSIONS
    string conversion_event_location        = 5; // CONVERSIONS 时间类型, WEBSITE,APP
}

message AccountInfoOld {
    string      account_id                  = 1;  // account_id
    string      account_name                = 2;  // account名称
}

message FanPageInfo {
    string      page_id                  = 1;  // page_id
    string      page_name                = 2;  // page名称
}

message AppInfo {
    string      app_id                   = 1;  // app_id
    string      app_name                 = 2;  // app名称
}

message PixelInfo {
  string account_id = 1;
  repeated IdName pixels = 2;
}

message GetMediaConfigRsp {
    aix.Result result                       = 1;
    string media                            = 2; // 渠道 Facebook, google
    string game_code                        = 3; // 游戏业务
    repeated AccountInfoOld account_infos   = 4; // 获取account列表
    repeated FanPageInfo page_infos         = 5; // fan_page信息
    string time_zone                        = 6; // 业务时区
    repeated string call_to_action          = 7; // 广告标语
    repeated AppInfo app_infos              = 8; // app信息
    repeated PixelInfo pixels               = 9; // pixel列表
}

message IdName {
    string      id                  = 1;  // id
    string      name                = 2;  // 名称
}

message ApplicationInfo {
  string application_id = 1;
  string name           = 2;
  string android_app_id = 3;
  string ios_app_id     = 4;
}

message AccountInfo {
    string                    id            = 1;  // id
    string                    name          = 2;  // 名称
    string                    status        = 3;  // 账号状态, 1:正常, 2:被封
    repeated IdName           pixels        = 4;  // pixels
    repeated ApplicationInfo  applications  = 5;  // applications
}

// 获取配置信息, POST, /api/v1/facebook_advertise/GetGameConfig
message GetGameConfigReq {
    string game_code                   = 1; // 游戏业务
}

message GetGameConfigRsp {
    aix.Result result                  = 1;
    string time_zone                   = 2; // 业务时区
    repeated AccountInfo accounts     = 3; // account列表
    repeated IdName pages              = 4; // page列表
    repeated IdName applications       = 5; // application列表
    repeated PixelInfo pixels          = 6; // pixel列表
}

// 获取manual配置, POST, /api/v1/facebook_advertise/GetManualSelect
message GetManualSelectReq {
    string account_id                       = 1;  // ad_account账号
    string game_code                        = 2;  // 游戏业务
    string inner_campaign_id                = 3;  // campaign_id
    string optimization_goal                = 4;  // APP_INSTALLS, OFFSITE_CONVERSIONS(events), VALUE, LINK_CLICKS
}
message GetManualSelectRsp {
    aix.Result      result                  = 1;
    ManualPlatform  manual                  = 2;  // 手动选项(通过接口给出)
}

// 获取manual配置, POST, /api/v1/facebook_advertise/GetCampaignPlacement
message GetCampaignPlacementReq {
    string account_id                       = 1;  // ad_account账号
    string game_code                        = 2;  // 游戏业务
    string objective                        = 3;  // objective
    string optimization_goal                = 4;  // APP_INSTALLS, OFFSITE_CONVERSIONS(events), VALUE, LINK_CLICKS
}
message GetCampaignPlacementRsp {
    aix.Result      result                  = 1;
    ManualPlatform  manual                  = 2;  // 手动选项(通过接口给出)
}

// 获取target match, POST, /api/v1/facebook_advertise/GetTargetMatch
message GetTargetMatchReq {
    string account_id                       = 1;  // ad_account账号
    string media                            = 2;  // 渠道 Facebook, google
    string game_code                        = 3;  // 游戏业务
    string search_word                      = 4;  // 搜索关键词
}
message GetTargetMatchRsp {
    aix.Result          result                  = 1;
    FlexibleSpecMeta    flexible_spec           = 2;
}

// 获取前端展示数据详情, POST, /api/v1/facebook_advertise/GetMediaRemoteWeb
message GetMediaRemoteWebReq {
    string game_code                        = 1;  // 游戏业务
    string account_id                       = 2;  // ad_account账号
    string uuid                             = 3;  // 前端生成随机ID
    string media                            = 4;  // 渠道 Facebook, google
    string inner_campaign_id                = 5;  // 内部campaign_id
}
message WebAdExt {
    string      inner_ad_set_id                    = 1;  // 内部ad-set-id
    repeated    google.protobuf.Struct web_ad      = 2;  // 前端展示ad信息
}
message GetMediaRemoteWebRsp {
    aix.Result                      result          = 1;
    google.protobuf.Struct          web_campaign    = 2;  // 前端展示campaign信息
    repeated google.protobuf.Struct web_ad_set      = 3;  // 前端展示ad_set信息
    repeated WebAdExt               web_ad_meta     = 4;  // 前端展示ad信息
}


message WebCampaign {
    string              inner_campaign_id        = 1; // 数据需要覆盖为内部信息
    string              media_campaign_id        = 2;
    string              status                   = 3;
    string              os                       = 4;
    CampaignCreateReq   campaign_ext             = 5;
    repeated WebAdSet   adset_exts               = 6;
}
message WebAdSet {
    string              inner_ad_set_id          = 1; // 数据需要覆盖为内部信息
    string              media_ad_set_id          = 2;
    string              status                   = 3;
    AdSetCreateReq      ad_set_ext               = 4;
    repeated WebAd      ad_ext                   = 5;
}

message WebAd {
    string              inner_ad_set_id          = 1; // edit时, 必须携带次信息, 标注父子关系
    string              media_ad_set_id          = 2; // edit时, 必须携带次信息, 标注父子关系
    string              inner_ad_id              = 3; // 数据需要覆盖为内部信息
    string              media_ad_id              = 4;
    string              status                   = 5;
    AdCreateReq         ad_ext                   = 6;
}

// 前端获取edit修改信息展示, POST, /api/v1/facebook_advertise/GetEditAdvertise
message GetEditAdvertiseReq {
    string              game_code                = 1;  // 游戏业务
    string              account_id               = 2;  // ad_account账号
    string              uuid                     = 3;  // 前端生成随机ID
    string              media                    = 4;  // 渠道 Facebook, google
    string              os                       = 5;  // 每个广告的发送平台 "ios", "android"
    WebCampaign         campaign_info            = 6;  // campaign信息
    repeated WebAdSet   ad_set_info              = 7;  // ad_set信息
    repeated WebAd      ad_info                  = 8;  // ad信息
}
message GetEditAdvertiseRsp {
    aix.Result          result                   = 1;
    WebCampaign         campaign_info            = 2;  // campaign信息
    repeated WebAdSet   ad_set_info              = 3;  // ad_set信息
    repeated WebAd      ad_info                  = 4;  // ad信息
}

message WebCampaignMeta {
    string              inner_campaign_id        = 1;
    string              media_campaign_id        = 2;
    string              status                   = 3;
}
message WebAdSetMeta {
    string              inner_campaign_id        = 1;
    string              media_campaign_id        = 2;
    string              inner_ad_set_id          = 3;
    string              media_ad_set_id          = 4;
    string              status                   = 5;
    string              daily_budget             = 6;
    string              bid_amount               = 7;
    string              roas_average_floor       = 8;
}
message WebAdMeta {
    string              inner_ad_set_id          = 1;
    string              media_ad_set_id          = 2;
    string              inner_ad_id              = 3;
    string              media_ad_id              = 4;
    string              status                   = 5;
}
// 列表获取实时数据显示, POST, /api/v1/facebook_advertise/GetRealTimeAdvertise
message GetRealTimeAdvertiseReq {
    string                      game_code                = 1;  // 游戏业务
    string                      account_id               = 2;  // ad_account账号
    string                      uuid                     = 3;  // 前端生成随机ID
    string                      media                    = 4;  // 渠道 Facebook, google
    repeated WebCampaignMeta    campaign_info            = 5;  // campaign信息
    repeated WebAdSetMeta       ad_set_info              = 6;  // ad_set信息
    repeated WebAdMeta          ad_info                  = 7;  // ad信息
}
message GetRealTimeAdvertiseRsp {
    aix.Result                  result                   = 1;
    repeated WebCampaignMeta    campaign_info            = 2;  // campaign信息
    repeated WebAdSetMeta       ad_set_info              = 3;  // ad_set信息
    repeated WebAdMeta          ad_info                  = 4;  // ad信息
}

// 更新ad_set信息, POST, /api/v1/facebook_advertise/UpdateAdset
message UpdateAdsetOldReq {
    string                      game_code                = 1;  // 游戏业务
    string                      account_id               = 2;  // ad_account账号
    string                      uuid                     = 3;  // 前端生成随机ID
    string                      media_ad_set_id          = 4;  // facebook的adset_id
    string                      inner_ad_set_id          = 5;  // inner_adset_id, 当media_adset_id不为空，忽略该字段
    AdSetCreateReq              adset_info               = 6;  // adset内容修改
}
message UpdateAdsetOldRsp {
    aix.Result                  result                   = 1;
}

message AdModifyExt {
    string                      inner_ad_id                 = 1;  // 内部ad_id
    string                      media_ad_id                 = 2;  // 渠道ad_id
    //string                    status                      = 3;
    string                      facebookPage                = 4;
    repeated string             action                      = 5;
    repeated string             primaryText                 = 6;
    repeated string             headline                    = 7;
    repeated                    string select_image_hash_list  = 8;  // 选择的image
    repeated                    VideoInfo select_video      = 9;  // 选择的video
    string                      state                       = 10;  // state: SUCCESS FAIL
    string                      msg                         = 11;  // 错误信息
    repeated string             description                 = 12; // 自定义内容
    Destination                 Destination                 = 13; // 目标
    Tracking                    Tracking                    = 14; // 转换追踪，当前只有conversion类型中的website才会使用
}
// 更新ad信息, POST, /api/v1/facebook_advertise/UpdateAdOld
message UpdateAdOldReq {
    string                  game_code            = 1;  // 游戏业务
    string                  account_id           = 2;  // ad_account账号
    string                  uuid                 = 3;  // 前端生成随机ID
    string                  media                = 4;  // 渠道 Facebook, google
    repeated AdModifyExt    ad_modify            = 5;  // ad内容修改
}
message UpdateAdOldRsp {
    aix.Result              result               = 1;
    repeated AdModifyExt    ad_show              = 2;
}

message CampaignModifyPart {
    string              inner_campaign_id        = 1;
    string              media_campaign_id        = 2;
    string              status                   = 3;
    string              state                    = 4;  // state: SUCCESS FAIL
    string              msg                      = 5;  // 错误信息
    string              account_id               = 6;  // ad_account账号
}
message AdSetModifyPart {
    string              inner_campaign_id        = 1;
    string              media_campaign_id        = 2;
    string              inner_ad_set_id          = 3;
    string              media_ad_set_id          = 4;
    string              status                   = 5;
    string              state                    = 6;  // state: SUCCESS FAIL
    string              msg                      = 7;  // 错误信息
    string              account_id               = 8;  // ad_account账号
}
message AdModifyPart {
    string              inner_ad_set_id          = 1;
    string              media_ad_set_id          = 2;
    string              inner_ad_id              = 3;
    string              media_ad_id              = 4;
    string              status                   = 5;
    string              state                    = 6;  // state: SUCCESS FAIL
    string              msg                      = 7;  // 错误信息
    string              account_id               = 8;  // ad_account账号
}
// 全部状态更新, POST, /api/v1/facebook_advertise/BatchUpdateStatus
message BatchUpdateStatusReq {
    string                              game_code                = 1;  // 游戏业务
    string                              uuid                     = 3;  // 前端生成随机ID
    string                              media                    = 4;  // 渠道 Facebook, google
    repeated CampaignModifyPart         campaign_info            = 5;  // campaign信息
    repeated AdSetModifyPart            ad_set_info              = 6;  // ad_set信息
    repeated AdModifyPart               ad_info                  = 7;  // ad信息
}
message BatchUpdateStatusRsp {
    aix.Result                          result                   = 1;
    repeated CampaignModifyPart         campaign_info            = 5;  // campaign信息
    repeated AdSetModifyPart            ad_set_info              = 6;  // ad_set信息
    repeated AdModifyPart               ad_info                  = 7;  // ad信息
}

message OperCampaignPart {
    string              inner_campaign_id        = 1;
    CampaignCreateReq   campaign_ext             = 2; // 带了就是跟新, 不带就是删除
}
message OperAdSetPart {
    string              inner_ad_set_id          = 1;
    AdSetCreateReq      ad_set_ext               = 2; // 带了就是跟新, 不带就是删除
}
message OperAdPart {
    string              inner_ad_id              = 1;
    AdCreateReq         ad_ext                   = 2; // 带了就是跟新, 不带就是删除
}
// 草稿状态修改内容, POST, /api/v1/facebook_advertise/OperAdvertiseDraft
message OperAdvertiseDraftReq {
    string                              game_code                = 1;  // 游戏业务
    string                              account_id               = 2;  // ad_account账号
    string                              uuid                     = 3;  // 前端生成随机ID
    string                              media                    = 4;  // 渠道 Facebook, google
    OperCampaignPart                    campaign_info            = 5;  // campaign信息
    OperAdSetPart                       ad_set_info              = 6;  // ad_set信息
    OperAdPart                          ad_info                  = 7;  // ad信息
}
message OperAdvertiseDraftRsp {
    aix.Result                          result                   = 1;
    string                              update_inner_id          = 2; //  修改返回id, id不变
}


message CampaignMeta {
    string                              inner_campaign_id        = 1; // 内部id
    string                              media_campaign_id        = 2; // 渠道id
    string                              campaign_name            = 3;
    string                              region                   = 4; // region大区
    string                              country                  = 5; // country国家
    string                              os                       = 6; // 平台
    string                              date                     = 7; // 时间
    string                              campaign_goal            = 8; // campaign_goal目标
    string                              custom_field             = 9; // 自定义区域
    string                              cost_type                = 10; // 花费判断
    string                              update_time              = 11; // 更新时间
    string                              account_id               = 12; // account_id
    string                              status                   = 13; // 状态
    google.protobuf.Struct              campaign_info            = 14; // 数据库内部全部字段
}
message AdSetMeta {
    string                              inner_ad_set_id          = 1; // 内部id
    string                              media_ad_set_id          = 2; // 渠道id
    string                              ad_set_name              = 3;
    string                              daily_budget             = 4; // 预算
    string                              bid_amount               = 5;
    string                              status                   = 6; // 状态
    google.protobuf.Struct              ad_set_info              = 7; // 数据库内部全部字段
}
message AdMeta {
    string                              inner_ad_id              = 1; // 内部id
    string                              media_ad_id              = 2; // 渠道id
    string                              ad_name                  = 3;
    string                              status                   = 4; // 状态
    google.protobuf.Struct              ad_info                  = 5; // 数据库内部全部字段
}
message AdSetUnit {
    AdSetMeta                           ad_set                   = 1;
    repeated AdMeta                     ads                      = 2;
}
message AdvertiseMeta {
    CampaignMeta                        campaign                 = 1;
    repeated AdSetUnit                  ad_sets                  = 2;
}
// 获取翻页列表数据, POST, /api/v1/facebook_advertise/GetAdvertiseDraftByPage
message GetAdvertiseDraftByPageReq {
    string                              game_code                = 1;  // 游戏业务
    string                              media                    = 4;  // 渠道 Facebook, google
    string                              condition                = 5;  // 限制条件
    uint32                              begin                    = 6;  // 起始翻页
    uint32                              end                      = 7;  // 终止翻页
    repeated string                     ids                      = 8;  // [列表侧不带] 单独查找使用, 暂时只支持inner_campaign_id
    bool                                filter                   = 9;  // [列表侧不带] 单独查找过滤使用, 包括: 状态, 详情
}
message GetAdvertiseDraftByPageRsp {
    aix.Result                          result                   = 1;
    repeated AdvertiseMeta              advertise_list           = 2; // 返回的广告数据
}


message WhereMeta {
    string                              key                      = 1;
    string                              operator                 = 2;
    repeated string                     value                    = 3;
}
// 获取列表数据, POST, /api/v1/facebook_advertise/GetDraftList
message GetDraftListReq {
    string                              game_code                = 1;  // 游戏业务
    repeated WhereMeta                  where                    = 2;  // 过滤条件
    uint32                              begin                    = 3;  // 翻页开始
    uint32                              end                      = 4;  // 翻页结束
}
message GetDraftListRsp {
    aix.Result                          result                   = 1;
    uint32                              total                    = 2; // 草稿总数量
    repeated AdvertiseMeta              advertise_list           = 3; // 返回的广告数据
}

// 获取campaign详情&&campaign下的所有adset详情, POST, /api/v1/facebook_advertise/GetUmbrellaAllAdSet
message GetUmbrellaAllAdSetReq {
    string              game_code                = 1;  // 游戏业务
    string              account_id               = 2;  // ad_account账号
    string              uuid                     = 3;  // 前端生成随机ID
    string              media                    = 4;  // 渠道 Facebook, google
    string              os                       = 5;  // 每个广告的发送平台 "ios", "android"
    WebCampaign         campaign_info            = 6;  // campaign信息
    WebAdSet            ad_set_info              = 7;  // ad_set信息[不带时, 获取全部adset]
}
message GetUmbrellaAllAdSetRsp {
    aix.Result          result                   = 1;
    WebCampaign         campaign_info            = 2;  // campaign信息
    repeated WebAdSet   ad_set_info              = 3;  // ad_set信息
}

// 获取campaign详情&&campaign下adset详情&&adset下所有ad详情, POST, /api/v1/facebook_advertise/GetUmbrellaAllAd
message GetUmbrellaAllAdReq {
    string              game_code                = 1;  // 游戏业务
    string              account_id               = 2;  // ad_account账号
    string              uuid                     = 3;  // 前端生成随机ID
    string              media                    = 4;  // 渠道 Facebook, google
    string              os                       = 5;  // 每个广告的发送平台 "ios", "android"
    WebCampaign         campaign_info            = 6;  // campaign信息
    WebAdSet            ad_set_info              = 7;  // ad_set信息
    WebAd               ad_info                  = 8;  // ad信息
}
message GetUmbrellaAllAdRsp {
    aix.Result          result                   = 1;
    WebCampaign         campaign_info            = 2;  // campaign信息
    WebAdSet            ad_set_info              = 3;  // ad_set信息
    repeated WebAd      ad_info                  = 4;  // ad信息
}

// 获取发布campaign下的草稿adset数据, POST, /api/v1/facebook_advertise/GetDraftListByMediaCampaign
message GetDraftListByMediaCampaignReq {
    string                              game_code                = 1;  // 游戏业务
    repeated WhereMeta                  where                    = 2;  // 过滤条件
    uint32                              begin                    = 3;  // 翻页开始
    uint32                              end                      = 4;  // 翻页结束
}
message GetDraftListByMediaCampaignRsp {
    aix.Result                          result                   = 1;
    uint32                              total                    = 2; // 草稿总数量
    repeated AdvertiseMeta              advertise_list           = 3; // 返回的广告数据
}

// 获取发布adset下的草稿ad数据, POST, /api/v1/facebook_advertise/GetDraftListByMediaAdSet
message GetDraftListByMediaAdSetReq {
    string                              game_code                = 1;  // 游戏业务
    repeated WhereMeta                  where                    = 2;  // 过滤条件
    uint32                              begin                    = 3;  // 翻页开始
    uint32                              end                      = 4;  // 翻页结束
}
message GetDraftListByMediaAdSetRsp {
    aix.Result                          result                   = 1;
    uint32                              total                    = 2; // 草稿总数量
    repeated AdvertiseMeta              advertise_list           = 3; // 返回的广告数据
}

message PublishCampaign {
    string inner_campaign_id    = 1;
    string media_campaign_id    = 2;
    string status               = 3; // 需要发布的状态[ACTIVE, PAUSED]
    string msg                  = 4;
}
message PublishAdSet {
    string inner_campaign_id    = 1;
    string media_campaign_id    = 2;
    string inner_ad_set_id      = 3;
    string media_ad_set_id      = 4;
    string status               = 5; // 需要发布的状态[ACTIVE, PAUSED]
    string msg                  = 6;
}
message PublishAd {
    string inner_ad_set_id      = 1;
    string media_ad_set_id      = 2;
    string inner_ad_id          = 3;
    string media_ad_id          = 4;
    string status               = 5; // 需要发布的状态[ACTIVE, PAUSED]
    string msg                  = 6;
}
// 全草稿状态广告批量发布, POST, /api/v1/facebook_advertise/ManageAdsAdvertisePublish
message ManageAdsAdvertisePublishReq {
    string game_code                                 = 1;  // 业务名
    string account_id                                = 2;  // ad_account账号
    string uuid                                      = 3;  // 前端生成随机ID
    PublishCampaign publish_campaign                 = 4;  // 发布需要的内部campaignid[只能单层出现, 需要找到下层draft]
    PublishAdSet publish_ad_set                      = 5;  // 发布需要的内部adsetid[只能单层出现, 需要找到下层draft]
    PublishAd publish_ad                             = 6;  // 发布需要的内部adid[只能单层出现, 需要找到下层draft]
}
message ManageAdsAdvertisePublishRsp {
    aix.Result result                                = 1;
    PublishCampaign publish_campaign                 = 4;  // 发布需要的内部campaignid
    repeated PublishAdSet publish_ad_set             = 5;  // 发布需要的内部adsetid
    repeated PublishAd publish_ad                    = 6;  // 发布需要的内部adid
}

// Detailed targeting匹配查询, GET, /api/v1/facebook_advertise/DetailedTargeting
message DetailedTargetingInfo {
    string id                                        = 1;
    string name                                      = 2;
    uint64 audience_size_lower_bound                 = 3;
    uint64 audience_size_upper_bound                 = 4;
    repeated string path                             = 5;
    string description                               = 6;
    string type                                      = 7;
}

message GetDetailedTargetingMatchReq {
    string game_code                                 = 1;  // 业务名
    string account_id                                = 2;  // ad_account账号
    string search_word                               = 3;  // 搜索关键词
}

message GetDetailedTargetingMatchRsp {
    aix.Result result                                  = 1;
    repeated DetailedTargetingInfo detailed_targetings = 2;
}

message DetailedTargetingKeyItem {
    string name                                        = 1;
    string type                                        = 2;
}

message BatchGetDetailedTargetingsReq {
    string game_code                                   = 1;  // 业务名
    string account_id                                  = 2;  // ad_account账号
    repeated DetailedTargetingKeyItem items            = 3;
}

message BatchGetDetailedTargetingsRsp {
    aix.Result result                                  = 1;
    repeated DetailedTargetingInfo detailed_targetings = 2;
}

message GetOsVersionListReq {
    string              game_code                = 1;  // 游戏业务
    string              account_id               = 2;  // ad_account账号
    string              platform                 = 3;  // 操作系统平台 "iOS", "Android"
}

message GetOsVersionListRsp {
    aix.Result          result                       = 1;
    string              platform                     = 2;  // 操作系统平台 "iOS", "Android"
    string              version                      = 3;   // 系统版本号
}

message GetMobileDevicesByKeyWordReq {
    string              game_code               = 1;  // 游戏业务
    string              account_id              = 2;  // ad_account账号
    string              platform                = 3;  // 操作系统平台 "iOS", "Android"
    string              key_word                = 4;  // 搜索关键字
}

message MobileDeviceInfo {
    string              name                    = 1;    // 设备名称
    string              description             = 2;    // 设备描述
    string              platform                = 3;    // 设备平台
    uint64              audience_size_lower_bound  =4;  // 预估受众下限
    uint64              audience_size_upper_bound  =5;  // 预估受众上限
}

message GetMobileDevicesByKeyWordRsp {
    aix.Result          result                  = 1;
    repeated  MobileDeviceInfo  devices         = 2;
}

message GetRealTimeCampaignDetailReq {
    string              game_code               = 1;  // 游戏业务
    string              account_id              = 2;  // ad_account账号
    string              campaign_id             = 3;  // 渠道campaign_id
}

message RealTimeCampaignDetail {
    string              name                    = 1;
    string              objective               = 2;
    string              app_campaign_type       = 3;  // app_install类型广告campaign子类型(aaa或者aa的) SMART_APP_PROMOTION(aaa) GUIDED_CREATION(aa)
    string              buying_type             = 4;  // buying_type类型 AUCTION
    bool                campaign_budget_optimization = 5;
    string              bid_strategy            = 6;
    string              daily_budget            = 7;
    string              status                  = 8;
}

message GetRealTimeCampaignDetailRsp {
    aix.Result          result                  = 1;
    RealTimeCampaignDetail detail               = 2;
}

message ListRealTimeAdsetsByCampaignReq {
    string              game_code               = 1;  // 游戏业务
    string              account_id              = 2;  // ad_account账号
    string              campaign_id             = 3;
}

message RealTimeAdsetInfo {
    string              adset_id                = 1;
    string              name                    = 2;
}

message ListRealTimeAdsetsByCampaignRsp {
    aix.Result                  result         = 1;
    repeated  RealTimeAdsetInfo adsets         = 2;
}

message GetRealTimeAdsetDetailReq {
    string              game_code               = 1;  // 游戏业务
    string              account_id              = 2;  // ad_account账号
    string              adset_id                = 3;
}

message AttributionSpec {
	string              event_type              = 1;
	uint32              window_days             = 2;
}

message RealTimeAdsetDetail {
    string              adset_id                = 1;
    string              name                    = 2;
    string              optimization_goal       = 3;
    string              bid_strategy            = 4;
    string              daily_budget            = 5;
    uint32              bid_amount              = 6;
    bool                is_dynamic_creative     = 7;
    AudiencePlatform    audience_custom         = 8;  // 平台已有audience
    CreateAudience      audience                = 9;  // 临时用户群体
    repeated string     brand_safety_content_filter_levels      = 10;    // audience network
    string              placement              = 11;
    string              start_time              = 13;
    string              end_time                = 14;
    repeated AttributionSpec  attribution_spec  = 15;
    string              status                  = 16;
    repeated string audience_custom_region      = 17; // audience大洲信息，客户端使用，无需关心
}

message GetRealTimeAdsetDetailRsp {
    aix.Result          result                  = 1;
    RealTimeAdsetDetail detail                  = 2;
}

message UpdateRealTimeAdsetReq {
    string              game_code               = 1;  // 游戏业务
    string              account_id              = 2;  // ad_account账号
    string              adset_id                = 3;
    string              daily_budget            = 4;
    uint32              bid_amount              = 5;
}

message UpdateRealTimeAdsetRsp {
    aix.Result          result                  = 1;
}

message ListRealTimeAdsByAdsetReq {
    string              game_code               = 1;  // 游戏业务
    string              account_id              = 2;  // ad_account账号
    string              adset_id                = 3;
}

message RealTimeAdInfo {
    string              ad_id                   = 1;
    string              name                    = 2;
}

message ListRealTimeAdsByAdsetRsp {
    aix.Result                  result          = 1;
    repeated RealTimeAdInfo     ads             = 2;
}

message GetRealTimeAdDetailReq {
    string              game_code               = 1;  // 游戏业务
    string              account_id              = 2;  // ad_account账号
    string              ad_id                   = 3;
}

message Video {
    string              video_id                = 1;
    string              thumbnail_url           = 2;
}

message Image {
    string              hash                    = 1;
    string              link                    = 2;
}

message RealTimeAdDetail {
    string              ad_id                   = 1;
    string              name                    = 2;
    string              status                  = 3;
    string              thumbnail_url           = 4;
    repeated string     primary_texts           = 5;
    repeated string     headlines               = 6;
    repeated Image      images                  = 7;
    repeated Video      videos                  = 8;
    string              account_id              = 9;
}

message GetRealTimeAdDetailRsp {
    aix.Result          result                  = 1;
    RealTimeAdDetail    detail                  = 2;
}

// 获取广告业务配置信息, POST, /api/v1/facebook_advertise/GetBizConfig
message GetBizConfigReq {
    string              catelog = 1;
    string              key = 2;
}

message GetBizConfigRsp {
    aix.Result             result    = 1;
    google.protobuf.Struct data      = 2;  // 返回的业务数据, 参数不同返回内容格式会有所不同
}

message TestReq {
    string              type   = 1;     // delay_publish, sync_account, sync_audience, sync_attribute, campaign, adset, ad
    string              token  = 2;
    repeated string     params = 3;
}

message TestRsp {
    aix.Result          result = 1;
    string              data   = 2;
}

message ReloadReq {
    string              token = 1;
}

message ReloadRsp {
    aix.Result          result                  = 1;
}

message GetUmbrellaAdvertiseReq {
  string                game_code             = 1;
  string                account_id            = 2; // ad_account账号
  string                uuid                  = 3; // 前端生成随机ID
  repeated WebCampaign  campaign_info         = 4;
}

message GetUmbrellaAdvertiseRsp {
  aix.Result            result                = 1;
  repeated WebCampaign  campaign_info         = 2;  // campaign信息
}

// 新接口 新结构
message AixCampaign {
  string              game_code                 = 1;  // 业务名
  string              account_id                = 2;  // account账号
  string              name                      = 3;  // campaign名字
  string              inner_campaign_id         = 4;  // 内部campaign_id
  string              media_campaign_id         = 5;  // 渠道campaign_id
  string              mobile_app_platform       = 6;  // app_install类型广告投放平台 "ios", "android"
  string              smart_promotion_type      = 7;  // app_install类型广告campaign子类型(aaa或者aa的) SMART_APP_PROMOTION(aaa) GUIDED_CREATION(aa)
  string              buying_type               = 8;  // buying_type类型 AUCTION
  string              strategy                  = 9;  // strategy策略
  string              objective                 = 10; // 广告类型 APP_INSTALLS, CONVERSIONS
  string              status                    = 11; // 状态，draft,paused,imcompleted,
  string              failed_reason             = 12; // 失败原因, 目前指发布到渠道侧失败的原因
  string              inner_status              = 13;
  string              lifetime_budget           = 14; //
  string              daily_budget              = 15; //
  string              bid_strategy              = 16; // 花费策略 LOWEST_COST_WITHOUT_CAP:Highest volume or value, COST_CAP: Cost per result goal, LOWEST_COST_WITH_BID_CAP:Bid cap, LOWEST_COST_WITH_MIN_ROAS:ROAS goal
  repeated AixAdset   adsets                    = 17;
}

// 临时结构体, 用于兼容audience大洲信息，客户端使用，无需关心
message AixCustomClientInfo {
  repeated string     custom_region             = 1; // audience大洲信息，客户端使用，无需关心
}

message AixAdset {
  string              game_code                 = 1;  // 业务名
  string              account_id                = 2;  // account账号
  string              name                      = 3;  // adset名字
  string              inner_adset_id            = 4;  // 内部adset_id
  string              media_adset_id            = 5;  // 渠道adset_id
  string              inner_campaign_id         = 6;  // 内部campaign_id
  string              media_campaign_id         = 7;  // 渠道campaign_id
  bool                is_dynamic                = 8;  // 是否开启动态广告
  string              placement                 = 9;  // 自动手动 Automatic, Manual
  ManualPlatform      manual                    = 10;  // 手动选项(通过接口给出)
  repeated CustomAudience custom_audiences      = 11;
  repeated CustomAudience excluded_custom_audiences = 12;
  CreateAudience      audience                  = 13; // 用户群体
  string              lifetime_budget           = 14; //
  string              daily_budget              = 15; //
  string              optimization_goal         = 16; // APP_INSTALLS, OFFSITE_CONVERSIONS(events), VALUE, LINK_CLICKS
  string              start_time                = 17; // 开始时间, 格式:unit time
  string              end_time                  = 18; // 结束时间, 格式:unit time
  string              bid_strategy              = 19; // 花费策略 "Lowest Cost", "Set a bid cap", "Cost per result goal"
  SelectAppEvent      promoted_object           = 20; // 事件
  string              when_you_get_charged      = 21; // impression
  string              roas_average_floor        = 22; // value使用花费, min_ros_control
  string              bid_amount                = 23; // 花费控制, 原名称 cost_control
  string              custom_client_info        = 24; // 客户端自定义信息, 比如：audience大洲信息，客户端使用，无需关心
  AdvanceTargeting    advance_targeting         = 25; // advance targeting
  string              conversion_event_location = 26; // Conversion event location, conversion类型该值才有效, 值为 WEBSITE, APP
  repeated string     pacing_type               = 27; // 加速类型, 对应渠道的delivery type, 值为standard, no_pacing(加速投放), day_parting
  string              mobile_app_platform       = 28; // conversions.app类型广告才有效, 投放平台 "ios", "android"
  string              status                    = 29;
  string              failed_reason             = 30; // 失败原因, 目前指发布到渠道侧失败的原因
  string              inner_status              = 31;
  repeated AixAd      ads                       = 32;
}

message AixAd {
  string game_code                        = 1;  // 业务名
  string account_id                       = 2;  // account账号
  string name                             = 3;  // ad名字
  string inner_ad_id                      = 4;  // 内部ad_id
  string media_ad_id                      = 5;  // 渠道ad_id
  string inner_adset_id                   = 6;  // 内部adset_id
  string media_adset_id                   = 7;  // 渠道adset_id
  repeated string select_image_hash_list  = 8;  // 选择的image
  repeated VideoInfo select_video         = 9;  // 选择的video
  repeated string primary_text            = 10; // 自定义内容
  repeated string headline                = 11; // 自定义内容
  repeated string call_to_action          = 12; // 广告动作 INSTALL_MOBILE_APP, INSTALL_APP, PLAY_GAME, SIGN_UP
  repeated string description             = 13; // 自定义内容
  Destination destination                 = 14; // 目标
  Tracking tracking                       = 15; // 转换追踪，当前只有conversion类型中的website才会使用
  string page_id                          = 16; // 选择的pageid
  string ad_format                        = 17; // ad format: SINGLE_IMAGE_OR_VIDEO, CAROUSEL
  repeated string carousel_optimizations  = 18; // carousel_optimizations: DISPLAY_CAROUSEL_CARDS_AS_VIEDO, AUTOMATICALLY
  string status                           = 19;
  string inner_status                     = 20;
  string failed_reason                    = 21; // 失败原因, 目前指发布到渠道侧失败的原因
}

// FB广告发布, POST, /api/v1/facebook_advertise/PublishAdLink
message PublishAdLinkReq {
    string game_code                        = 1;  // 业务名
    string account_id                       = 2;  // ad_account账号
    string inner_ad_id                      = 3;  // 发布需要的内部adid
}
message PublishAdLinkRsp {
    aix.Result result                       = 1;
    string media_campaign_id                = 2;  // 渠道campaign_id
    string media_adset_id                   = 3;  // 渠道adset_id
    string media_ad_id                      = 4;  // 渠道ad_id
}

message GetCampaignTreeEntry {
  string game_code = 1; // 业务名
  string account_id = 2; // account_id
  string media_campaign_id = 3; // 渠道端的campaign_id 未发布的草稿，该字段为空
  string inner_campaign_id = 4; // 内部campaign_id
  string mobile_app_platform = 5; // app_install类型广告投放平台 "ios", "android"
}

message GetCampaignTreeDetailReq {
  repeated GetCampaignTreeEntry campaigns = 1; // inner_campaign_id或者media_campaign_id至少有一项，game_code必填
}

message GetCampaignTreeDetailRsp {
  aix.Result result = 1;
  repeated AixCampaign campaigns = 2; // campaign信息
}

message GetCampaignTreeSummaryReq {
  repeated GetCampaignTreeEntry campaigns = 1; // campaign信息
}

message GetCampaignTreeSummaryRsp {
  aix.Result result = 1;
  repeated AixCampaign campaigns = 2; // campaign信息
}

message StatusStatistic {
  string status = 1;
  sint32 num = 2;
}

message CampaignStatusStatistics {
  string id = 1;
  repeated StatusStatistic adset_statistics = 2;
  repeated StatusStatistic ad_statistics = 3;
}

message AdsetStatusStatistics {
  string id = 1;
  repeated StatusStatistic ad_statistics = 3;
}

message GetCampaignStatusStatisticsReq {
  string game_code = 1;
  repeated string inner_ids = 2;      // inner_id列表
  repeated string media_ids = 3;      // media_id列表
}

message GetCampaignStatusStatisticsRsp {
  aix.Result result = 1;
  repeated CampaignStatusStatistics inner_statistics = 2; // 状态统计结果列表, 结果与请求inner_ids顺序一致
  repeated CampaignStatusStatistics media_statistics = 3; // 状态统计结果列表, 结果与请求media_ids顺序一致
}

message GetAdsetStatusStatisticsReq {
  string game_code = 1;
  repeated string inner_ids = 2;      // inner_id列表
  repeated string media_ids = 3;      // media_id列表
}

message GetAdsetStatusStatisticsRsp {
  aix.Result result = 1;
  repeated AdsetStatusStatistics inner_statistics = 2; // 状态统计结果列表, 结果与请求inner_ids顺序一致
  repeated AdsetStatusStatistics media_statistics = 3; // 状态统计结果列表, 结果与请求media_ids顺序一致
}

message CreateCampaignReq {
  AixCampaign campaign = 1; // campaign信息
}

message CreateCampaignRsp {
  aix.Result result = 1;
  AixCampaign campaign = 2; // campaign信息
}

message CreateAdsetReq {
  AixAdset adset = 1;  // adset信息
}

message CreateAdsetRsp {
  aix.Result result     = 1;
  AixAdset adset        = 2;
  string campaign_name  = 3;  // 创建adset后, 可能会改变campaign name, 该字段返回修改后的campaign name, 为空表示campaign name没有改变
}

message CreateAdReq {
  AixAd ad = 1;  // ad信息
}

message CreateAdRsp {
  aix.Result result = 1;
  AixAd ad = 2;
}

message SqlCondition {
  string column = 1;
  string operator = 2; // 目前只支持: IN, =, LIKE, OR_IN; 条件之间是 AND
  repeated string value = 3;
}


// 拉取草稿数据
message GetDraftsReq {
  repeated SqlCondition conditions = 1;
  int32 offset = 2;          // 拉取偏移
  int32 limit = 3;           // 拉取条数
  string advertise_type = 4; // 类型：campaign, adset, ad
}

// 草稿视图数据
message DraftView {
  string game_code = 1;             // 业务名
  string account_id = 2;            // account账号
  string inner_campaign_id = 3;     // 内部 campaign_id
  string media_campaign_id = 4;     // 渠道 campaign_id
  string campaign_name = 5;         // campaign 名
  string region = 6;                // campaign 投放地区缩写
  string country = 7;               // campaign 投放国家缩写
  string platform = 8;              // Campaign 投放的系统, 比如ios, and, pc, all
  string campaign_status = 9;       // campaign 状态
  string inner_adset_id = 10;       // 内部 adset_id
  string media_adset_id = 11;       // 渠道 adset_id
  string adset_name = 12;           // adset 名
  string adset_status = 13;         // adset 状态
  string inner_ad_id = 14;          // 内部 ad_id
  string media_ad_id = 15;          // 渠道 ad_id
  string ad_name = 16;              // ad 名
  string ad_status = 17;            // ad 状态
  string aix_campaign_type = 18;    // aix campaign type(objective)
  bool   is_dynamic = 19;
}

message GetDraftsRsp {
  aix.Result result = 1;
  repeated DraftView drafts = 2;   // 草稿列表
}

message DeleteAdvertiseReq {
  repeated string inner_ids  = 1;   // 想要删除的campaign,adset或者ad的inner id
  string advertise_type = 2;        // 类型：campaign, adset, ad
}

message DeleteAdvertiseRsp {
  aix.Result result = 1;
}

message UpdateCampaignReq {
  AixCampaign campaign = 1;     // campaign信息
  repeated string fields = 2;   // 更新的字段
}

message UpdateCampaignRsp {
  aix.Result result = 1;
  AixCampaign campaign = 2; // campaign信息
}

message UpdateAdsetReq {
  AixAdset adset = 1;           // adset信息
  repeated string fields = 2;   // 更新的字段
}

message UpdateAdsetRsp {
  aix.Result result = 1;
  AixAdset adset = 2; // adset信息
  string campaign_name  = 3;  // 修改adset后, 可能会改变campaign name, 该字段返回修改后的campaign name, 为空表示campaign name没有改变
}

message UpdateAdReq {
  AixAd ad = 1;                 // ad信息
  repeated string fields = 2;   // 更新的字段
}

message UpdateAdRsp {
  aix.Result result = 1;
  AixAd ad = 2; // ad信息
}

message CancelPublishingReq {
  string game_code = 1;   // 游戏业务
  string inner_id = 2;    // inner_campaign_id, inner_adset_id or inner_ad_id
}

message CancelPublishingRsp {
  aix.Result result = 1;
}

message GetAudiencesOrEventsReq {
  string               type                   = 1;  // app_installs.audience, app_installs.event, conversions.website.event, conversions.app.event
  string               game_code              = 2;  // 游戏业务
  string               account_id             = 3;  // account_id
  string               pixel_id               = 4;  // pixel_id
}

message AudienceOrEvent {
  string              id                      = 1;
  string              name                    = 2;
  string              type                    = 3;  // audience, event, standard_event, aix_custom_event, custom_event, custom_conversions
  SelectAppEvent      promoted_object         = 4;
}

message GetAudiencesOrEventsRsp {
  aix.Result                result            = 1;
  repeated AudienceOrEvent  events            = 2;
}

message PageInfo {
    int64 page         = 1;
    int64 page_size    = 2;
    int64 total_number = 3;
    int64 total_page   = 4;
}

message GetAudiencesReq {
  string                    game_code               = 1;  // 游戏业务
  string                    account_id              = 2;  // 必填 渠道账号id
  repeated string           audience_ids            = 3;  // 指定audience_ids, 为空表示获取所有
  int64                     page                    = 4;  // 当前页数。默认值: 1。取值范围: ≥ 1
  int64                     page_size               = 5;  // 分页大小。默认值: 0(获取所有)
}

message Audience {
  string                    audience_id             = 1;  // 受众群体ID。
  string                    name                    = 2;  // 受众群体名称。
  string                    create_time             = 3;  // 创建时间，GMT时间。
}

message GetAudiencesRsp {
  aix.Result                result                  = 1;
  repeated Audience         audiences               = 2;
  PageInfo                  page_info               = 3;  // 分页信息
}

message GetAppEventsReq {
  string                    game_code               = 1;  // 游戏业务
  string                    application_id          = 2;  // 必填 渠道 application_id
  string                    optimization_goal       = 14; // APP_INSTALLS, OFFSITE_CONVERSIONS(events), VALUE, LINK_CLICKS
  int64                     page                    = 3;  // 当前页数。默认值: 1。取值范围: ≥ 1
  int64                     page_size               = 4;  // 分页大小。默认值: 0(获取所有)
}

message AppEvent {
  string                    name                    = 1;  // 受众群体名称。
  string                    create_time             = 2;  // 创建时间，GMT时间。
  SelectAppEvent            promoted_object         = 3;
  string                    type                    = 4;  // standard_event, custom_event
}

message GetAppEventsRsp {
  aix.Result                result                  = 1;
  repeated AppEvent         events                  = 2;
  PageInfo                  page_info               = 3;  // 分页信息
}

message GetConversionEventsReq {
  string                    type                    = 1;  // conversions.website.event, conversions.app.event
  string                    game_code               = 2;  // 游戏业务
  string                    pixel_id                = 3;  // pixel_id
  string                    objective               = 4;  // objective
}

message ConversionEvent {
  string                    id                      = 1;
  string                    name                    = 2;
  string                    type                    = 3;  // standard_event, aix_custom_event, custom_event, custom_conversions
  SelectAppEvent            promoted_object         = 4;
  string                    creator                 = 5;  // creator, only valid for custom_event
}

message GetConversionEventsRsp {
  aix.Result                result                  = 1;
  repeated ConversionEvent  events                  = 2;
}

message CreateCustomEventsReq {
  string                    game_code               = 1;  // 游戏业务
  string                    pixel_id                = 2;  // pixel_id
  repeated string           event_names             = 3;  // 自定义事件列表
}

  
message CreateCustomEventsRsp {
  aix.Result                result                  = 1;
  repeated string           created_event_names     = 2;  // 成功创建的事件名称列表
}

message DeleteCustomEventsReq {
  string                    game_code              = 1;  // 游戏业务
  string                    pixel_id               = 2;  // pixel_id
  repeated string           event_names            = 3;  // 事件名称列表, 不区分大小写
}

message DeleteCustomEventsRsp {
  aix.Result                result                 = 1;
  repeated string           deleted_event_names    = 2;  // 成功删除的事件名称列表
}

message BatchGetPublishedCampaignSummaryReq {
  string                    game_code              = 1;  // 游戏业务
  string                    account_id             = 2;  // account_id
  repeated string           campaign_ids           = 3;  // 渠道campaign id列表
}

message PublishedCampaignSummary {
  int64                     error_code             = 1;  // 返回错误码, 成功为0
  string                    error_message          = 2;  // 返回错误信息, 成功为空
  string                    campaign_id            = 3;  // 渠道campaign id
  string                    account_id             = 4;  // 渠道account id
  string                    status                 = 5;  // 渠道campaign状态
  string                    objective              = 6;  // 渠道campaign objective
}

message BatchGetPublishedCampaignSummaryRsp {
  aix.Result                result                 = 1;
  repeated PublishedCampaignSummary campaigns      = 2;  // 返回结果与请求列表顺序是一致的
}

message CopyCampaignReq {
  string                    game_code              = 1;  // 游戏业务
  string                    account_id             = 2;  // 渠道账号id
  string                    inner_campaign_id      = 3;  // 复制 内部 inner_campaign_id
  string                    campaign_id            = 4;  // 复制 渠道 campaign_id
  string                    copy_type              = 5;  // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyCampaignRsp {
  aix.Result                result                 = 1;
  string                    inner_campaign_id      = 2;  // 生成复制后的草稿 inner_campaign_id
}

message CopyAdsetReq {
  string                    game_code              = 1;  // 游戏业务
  string                    account_id             = 2;  // 渠道账号id
  string                    inner_adset_id         = 3;  // 复制 内部 inner_adset_id
  string                    adset_id               = 4;  // 复制 渠道 adset_id
  string                    copy_type              = 5;  // 复制类型. 默认"ALL_LEVEL". 支持: "THIS_LEVEL","ALL_LEVEL"
}

message CopyAdsetRsp {
  aix.Result                result                 = 1;
  string                    inner_adset_id         = 2;  // 生成复制后的草稿 inner_adset_id
}

message CopyAdReq {
  string                    game_code              = 1;  // 游戏业务
  string                    account_id             = 2;  // 渠道账号id
  string                    inner_ad_id            = 3;  // 复制 内部 inner_ad_id
  string                    ad_id                  = 4;  // 复制 渠道 ad_id
}
  
message CopyAdRsp {
  aix.Result                result                 = 1;
  string                    inner_ad_id            = 2; //  生成复制后的草稿 inner_ad_id
}

message ChangeHistory {
  string   account_id       = 1;  // 账号id
  string   change_date_time = 2;  // 变更时间, 格式：2023-09-20T06:42:41+0000
  string   object_id        = 3;  // 变更所属的广告id  
  string   objective        = 4;  // 变更对象
  string   change_type      = 5;  // 变更分类
  repeated string changes   = 6;  // 变更详情
  repeated string before    = 7;  // 变更前数据(如果有)，此时changes是变更后数据，可以作为比较
  string   operator         = 8;  // 操作人
}

// 拉取广告变更历史, POST, /api/v1/facebook_advertise/GetChangeHistory
message GetChangeHistoryReq {
  string   game_code             = 1;
  string   account_id            = 2;   // 账号id
  string   campaign_id           = 3;   // campaign_id, adset_id, ad_id 三选一
  string   adset_id              = 4;   // campaign_id, adset_id, ad_id 三选一
  string   ad_id                 = 5;   // campaign_id, adset_id, ad_id 三选一
  repeated string   change_types = 6;   // 变更类型
  repeated string   object_types = 7;   // 变更对象
  string   start_time            = 8;   // 变更开始时间: YYYY-MM-dd HH:mm:ss (注意要以account的时区)
  string   end_time              = 9;   // 变更结束时间: YYYY-MM-dd HH:mm:ss (注意要以account的时区)
  int32    page                  = 10;  // 分页数，从1开始
  int32    page_size             = 11;  // 每条条数，最多1000条
}

message GetChangeHistoryRsp {
  aix.Result result              = 1;  // 返回结果
  repeated   ChangeHistory  list = 2;  // 变更历史列表
  int32      total               = 3;  // 总数
}

// 拉取渠道图片视频素材信息 POST, /api/v1/facebook_advertise/GetMediaImageVideo
message GetMediaImageVideoReq {
  string   game_code           = 1;
  string   account_id          = 2;
  repeated string image_hashes = 3;  // 图片hash列表
  repeated string video_ids    = 4;  // 视频id列表
}

// 图片视频信息
message ImageVideo {
  string id              = 1;  // 图片视频id
  int32  type            = 2;  // 1-视频，2-图片
  string name            = 3;  // 名称
  int32  width           = 4;  // 宽
  int32  height          = 5;  // 高
  string thumbnail_url   = 6;  // 缩略图
}

message GetMediaImageVideoRsp {
  aix.Result result     = 1;
  repeated ImageVideo assets = 2; // 素材信息
}

message AixTracker {
    Destination destination = 1;
    Tracking    tracking    = 2;
}