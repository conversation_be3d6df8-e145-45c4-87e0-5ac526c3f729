**Generated by protoc-gen-md. DO NOT EDIT.**
[TOC]
# analysis_pivot列表请求, POST, /api/v1/pivot_server/analysis_pivot_list
test curl:
```shell
curl 'http://target/api/v1/pivot_server/analysis_pivot_list' -H 'Content-Type:application/json' -d '{"group":[""],"metric":[""],"need_agg_num":0,"orderby":[{"by":"","order":""}],"pageIndex":0,"pageSize":0,"where":[{"in_list":[""],"in_list_type":0,"is_lower_equal":0,"is_upper_equal":0,"like":"","lower":0,"name":"","type":0,"upper":0}]}'
```
#### analysis pivot list req
parameter explain:
```yaml
AnalysisPivotListReq: # analysis_pivot列表请求, POST, /api/v1/pivot_server/analysis_pivot_list
  group: [""] # 聚合字段
  where: # 筛选条件
  - name: "" # 字段名
    upper: 0.0 # 最大值，type为2时适用
    lower: 0.0 # 最小值，type为2时适用
    in_list: [""] # 列表值，type为1时适用, string类型
    in_list_type: 0 # 列表值类型, 1表示字符串, 2表示整型, 3表示浮点型
    like: "" # 模糊匹配值，type为3时适用
    type: 0 # 1-in多个值任一匹配 2-where的range范围内查找 3-like模糊匹配 4-having的range范围查询,5-not in多个值任一匹配
    is_upper_equal: 0 # 最大值是否可等于，-1没有上限 0不等于 1等于，type为2时适用
    is_lower_equal: 0 # 最小值是否可等于，-1没有下限 0不等于 1等于，type为2时适用
  pageIndex: 0 # 起始偏移
  pageSize: 0 # 拉取数量
  metric: [""] # 需要的字段
  orderby: # order by
  - by: "" # 字段名
    order: "" # 'desc' 'asc'
  need_agg_num: 0 # 是否需要聚合数量, 0-否, 1-是

```
data example:
```json
{
    "group": [
        ""
    ],
    "metric": [
        ""
    ],
    "need_agg_num": 0,
    "orderby": [
        {
            "by": "",
            "order": ""
        }
    ],
    "pageIndex": 0,
    "pageSize": 0,
    "where": [
        {
            "in_list": [
                ""
            ],
            "in_list_type": 0,
            "is_lower_equal": 0,
            "is_upper_equal": 0,
            "like": "",
            "lower": 0,
            "name": "",
            "type": 0,
            "upper": 0
        }
    ]
}
```
#### analysis pivot list rsp
parameter explain:
```yaml
AnalysisPivotListRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  pivots: # 列表
  - ctr: 0.0 # 
    cvr: 0.0 # 
    ipm: 0.0 # 
    cpi: 0.0 # 
    daily_impressions: 0.0 # 
    impression_change: 0.0 # 
    click_change: 0.0 # 
    game_code: "" # 
    dtstatdate: 0 # 
    mcc_id: "" # 
    account_id: "" # 
    asset_id: "" # 
    asset_name: "" # 
    asset_status: "" # 
    asset_serial_id: "" # 
    asset_project: "" # 
    asset_format: "" # 
    asset_size: "" # 
    asset_language: "" # 
    asset_organization: "" # 
    asset_custom_name: "" # 
    asset_play: "" # 
    asset_perform: "" # 
    asset_stage: "" # 
    asset_delivery_date: "" # 
    creative_id: "" # 
    creative_name: "" # 
    creative_status: "" # 
    ad_id: "" # 
    ad_name: "" # 
    ad_status: "" # 
    adgroup_id: "" # 
    adgroup_name: "" # 
    adgroup_status: "" # 
    campaign_id: "" # 
    campaign_name: "" # 
    campaign_status: "" # 
    campaign_type: "" # 
    parse_network: "" # 
    parse_region: "" # 
    parse_platform: "" # 
    parse_date: "" # 
    parse_campaign_goal: "" # 
    parse_custom_field: "" # 
    parse_cost_type: "" # 
    parse_cost_region: "" # 
    online_date: "" # 
    offline_date: "" # 
    online_days: 0 # 
    country_code: "" # 
    country_name_ch: "" # 
    country_name_en: "" # 
    ua_region: "" # 
    language: "" # 
    network: "" # 
    asset_url: "" # 
    start_date: "" # 
    end_date: "" # 
    impressions: 0.0 # 
    clicks: 0.0 # 
    conversions: 0.0 # 
    registers: 0.0 # 
    installs: 0.0 # 
    spend: 0.0 # 
    machine_lables: "" # 
    human_lables: "" # 
    asset_type: "" # 
    youtube_id: "" # 
    watch_time: 0.0 # 
    video_played_25: 0.0 # 
    video_played_50: 0.0 # 
    video_played_75: 0.0 # 
    video_played_100: 0.0 # 
    video_duration: 0.0 # 
    num_of_ads: 0.0 # 
    d1_pay_users: 0.0 # 
    d2_pay_users: 0.0 # 
    d3_pay_users: 0.0 # 
    d7_pay_users: 0.0 # 
    d1_pay_rate: 0.0 # 
    d2_pay_rate: 0.0 # 
    d3_pay_rate: 0.0 # 
    d7_pay_rate: 0.0 # 
    d1_pay_amount: 0.0 # 
    d2_pay_amount: 0.0 # 
    d3_pay_amount: 0.0 # 
    d7_pay_amount: 0.0 # 
    d1_roas: 0.0 # 
    d2_roas: 0.0 # 
    d3_roas: 0.0 # 
    d7_roas: 0.0 # 
    d1_ltv: 0.0 # 
    d2_ltv: 0.0 # 
    d3_ltv: 0.0 # 
    d7_ltv: 0.0 # 
    r1: 0.0 # 
    r2: 0.0 # 
    r3: 0.0 # 
    r7: 0.0 # 
    agg_num: 0 # 聚合数量
  total: 0 # 总数

```
data example:
```json
{
    "pivots": [
        {
            "account_id": "",
            "ad_id": "",
            "ad_name": "",
            "ad_status": "",
            "adgroup_id": "",
            "adgroup_name": "",
            "adgroup_status": "",
            "agg_num": 0,
            "asset_custom_name": "",
            "asset_delivery_date": "",
            "asset_format": "",
            "asset_id": "",
            "asset_language": "",
            "asset_name": "",
            "asset_organization": "",
            "asset_perform": "",
            "asset_play": "",
            "asset_project": "",
            "asset_serial_id": "",
            "asset_size": "",
            "asset_stage": "",
            "asset_status": "",
            "asset_type": "",
            "asset_url": "",
            "campaign_id": "",
            "campaign_name": "",
            "campaign_status": "",
            "campaign_type": "",
            "click_change": 0,
            "clicks": 0,
            "conversions": 0,
            "country_code": "",
            "country_name_ch": "",
            "country_name_en": "",
            "cpi": 0,
            "creative_id": "",
            "creative_name": "",
            "creative_status": "",
            "ctr": 0,
            "cvr": 0,
            "d1_ltv": 0,
            "d1_pay_amount": 0,
            "d1_pay_rate": 0,
            "d1_pay_users": 0,
            "d1_roas": 0,
            "d2_ltv": 0,
            "d2_pay_amount": 0,
            "d2_pay_rate": 0,
            "d2_pay_users": 0,
            "d2_roas": 0,
            "d3_ltv": 0,
            "d3_pay_amount": 0,
            "d3_pay_rate": 0,
            "d3_pay_users": 0,
            "d3_roas": 0,
            "d7_ltv": 0,
            "d7_pay_amount": 0,
            "d7_pay_rate": 0,
            "d7_pay_users": 0,
            "d7_roas": 0,
            "daily_impressions": 0,
            "dtstatdate": 0,
            "end_date": "",
            "game_code": "",
            "human_lables": "",
            "impression_change": 0,
            "impressions": 0,
            "installs": 0,
            "ipm": 0,
            "language": "",
            "machine_lables": "",
            "mcc_id": "",
            "network": "",
            "num_of_ads": 0,
            "offline_date": "",
            "online_date": "",
            "online_days": 0,
            "parse_campaign_goal": "",
            "parse_cost_region": "",
            "parse_cost_type": "",
            "parse_custom_field": "",
            "parse_date": "",
            "parse_network": "",
            "parse_platform": "",
            "parse_region": "",
            "r1": 0,
            "r2": 0,
            "r3": 0,
            "r7": 0,
            "registers": 0,
            "spend": 0,
            "start_date": "",
            "ua_region": "",
            "video_duration": 0,
            "video_played_100": 0,
            "video_played_25": 0,
            "video_played_50": 0,
            "video_played_75": 0,
            "watch_time": 0,
            "youtube_id": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
# 获取creative pivot表筛选项信息, POST, /api/v1/pivot_server/get_filter_info
test curl:
```shell
curl 'http://target/api/v1/pivot_server/get_filter_info' -H 'Content-Type:application/json' -d '{}'
```
#### get filter info req
parameter explain:
```yaml
GetFilterInfoReq: # 获取creative pivot表筛选项信息, POST, /api/v1/pivot_server/get_filter_info

```
data example:
```json
{}
```
#### get filter info rsp
parameter explain:
```yaml
GetFilterInfoRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  dtstatdate: 0 # 最新数据时间

```
data example:
```json
{
    "dtstatdate": 0,
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 插入mock数据, POST, /api/v1/pivot_server/insert_mock
test curl:
```shell
curl 'http://target/api/v1/pivot_server/insert_mock' -H 'Content-Type:application/json' -d '{"number":0}'
```
#### insert mock req
parameter explain:
```yaml
InsertMockReq: # 插入mock数据, POST, /api/v1/pivot_server/insert_mock
  number: 0 # 插入mock数据数量

```
data example:
```json
{
    "number": 0
}
```
#### insert mock rsp
parameter explain:
```yaml
InsertMockRsp: # 插入mock数据-返回结构
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 测试语句
test curl:
```shell
curl 'http://target// 测试语句' -H 'Content-Type:application/json' -d '{}'
```
#### say hi req
parameter explain:
```yaml
SayHiReq: # 测试语句

```
data example:
```json
{}
```
#### say hi rsp
parameter explain:
```yaml
SayHiRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 

```
data example:
```json
{
    "result": {
        "error_code": 0,
        "error_message": ""
    }
}
```
# 请求google实时统计数据信息, POST, /api/v1/pivot_server/query_google_realtime_asset_info
test curl:
```shell
curl 'http://target/api/v1/pivot_server/query_google_realtime_asset_info' -H 'Content-Type:application/json' -d '{"group":[""],"metric":[""],"need_agg_num":0,"orderby":[{"by":"","order":""}],"pageIndex":0,"pageSize":0,"where":[{"in_list":[""],"in_list_type":0,"is_lower_equal":0,"is_upper_equal":0,"like":"","lower":0,"name":"","type":0,"upper":0}]}'
```
#### query google realtime asset info req
parameter explain:
```yaml
QueryGoogleRealtimeAssetInfoReq: # 请求google实时统计数据信息, POST, /api/v1/pivot_server/query_google_realtime_asset_info
  group: [""] # 聚合字段
  where: # 筛选条件
  - name: "" # 字段名
    upper: 0.0 # 最大值，type为2时适用
    lower: 0.0 # 最小值，type为2时适用
    in_list: [""] # 列表值，type为1时适用, string类型
    in_list_type: 0 # 列表值类型, 1表示字符串, 2表示整型, 3表示浮点型
    like: "" # 模糊匹配值，type为3时适用
    type: 0 # 1-in多个值任一匹配 2-where的range范围内查找 3-like模糊匹配 4-having的range范围查询,5-not in多个值任一匹配
    is_upper_equal: 0 # 最大值是否可等于，-1没有上限 0不等于 1等于，type为2时适用
    is_lower_equal: 0 # 最小值是否可等于，-1没有下限 0不等于 1等于，type为2时适用
  pageIndex: 0 # 起始偏移
  pageSize: 0 # 拉取数量
  metric: [""] # 需要的字段
  orderby: # order by
  - by: "" # 字段名
    order: "" # 'desc' 'asc'
  need_agg_num: 0 # 是否需要聚合数量, 0-否, 1-是

```
data example:
```json
{
    "group": [
        ""
    ],
    "metric": [
        ""
    ],
    "need_agg_num": 0,
    "orderby": [
        {
            "by": "",
            "order": ""
        }
    ],
    "pageIndex": 0,
    "pageSize": 0,
    "where": [
        {
            "in_list": [
                ""
            ],
            "in_list_type": 0,
            "is_lower_equal": 0,
            "is_upper_equal": 0,
            "like": "",
            "lower": 0,
            "name": "",
            "type": 0,
            "upper": 0
        }
    ]
}
```
#### query google realtime asset info rsp
parameter explain:
```yaml
QueryGoogleRealtimeAssetInfoRsp: # 
  result: # 返回结果
    error_code: 0 # 
    error_message: "" # 
  info_list: # 返回结果
  - dtstatdate: "" # 
    hour: "" # 
    game_code: "" # 
    customer_id: "" # 
    asset_id: "" # 
    asset_name: "" # 
    asset_image_size: "" # 
    asset_img_height: "" # 
    asset_img_url: "" # 
    asset_img_width: "" # 
    asset_mime_type: "" # 
    asset_resource_name: "" # 
    asset_type: "" # 
    asset_video_id: "" # 
    asset_video_title: "" # 
    ad_group_id: "" # 
    ad_group_name: "" # 
    ad_group_resource_name: "" # 
    ad_group_status: "" # 
    ad_status: "" # 
    ad_group_ad_resource_name: "" # 
    ad_id: "" # 
    ad_resource_name: "" # 
    headline_list_str: "" # 
    description_list_str: "" # 
    image_list_str: "" # 
    video_list_str: "" # 
    campaign_id: "" # 
    campaign_name: "" # 
    campaign_resource_name: "" # 
    campaign_budget_resource_name: "" # 
    campaign_status: "" # 
    campaign_serving_status: "" # 
    campaign_advertising_channel_type: "" # 
    campaign_advertising_channel_sub_type: "" # 
    campaign_cpa: "" # 
    campaign_roas: "" # 
    campaign_app_id: "" # 
    campaign_app_store: "" # 
    campaign_bidding_strategy_goal_type: "" # 
    campaign_start_date: "" # 
    campaign_end_date: "" # 
    campaign_conversion_actions: "" # 
    clicks: 0 # 
    conversions: 0 # 
    cost_micros: 0 # 
    impressions: 0 # 
    country_list: "" # 
    main_country: "" # 
    account_id: "" # 
    asset_serial_id: "" # 
    asset_project: "" # 
    asset_format: "" # 
    asset_size: "" # 
    asset_language: "" # 
    asset_organization: "" # 
    asset_custom_name: "" # 
    asset_play: "" # 
    asset_perform: "" # 
    asset_stage: "" # 
    asset_delivery_date: "" # 
    campaign_type: "" # 
    parse_network: "" # 
    parse_region: "" # 
    parse_platform: "" # 
    parse_date: "" # 
    parse_campaign_goal: "" # 
    parse_custom_field: "" # 
    parse_cost_type: "" # 
    parse_cost_region: "" # 
    country_code: "" # 
    language: "" # 
    network: "" # 
    asset_url: "" # 
    spend: 0 # 
    youtube_id: "" # 
    agg_num: 0 # 
    ctr: 0.0 # 
    cvr: 0.0 # 
    ipm: 0.0 # 
    cpi: 0.0 # 
    daily_impressions: 0.0 # 
    impression_change: 0.0 # 
    click_change: 0.0 # 
  total: 0 # 总数

```
data example:
```json
{
    "info_list": [
        {
            "account_id": "",
            "ad_group_ad_resource_name": "",
            "ad_group_id": "",
            "ad_group_name": "",
            "ad_group_resource_name": "",
            "ad_group_status": "",
            "ad_id": "",
            "ad_resource_name": "",
            "ad_status": "",
            "agg_num": 0,
            "asset_custom_name": "",
            "asset_delivery_date": "",
            "asset_format": "",
            "asset_id": "",
            "asset_image_size": "",
            "asset_img_height": "",
            "asset_img_url": "",
            "asset_img_width": "",
            "asset_language": "",
            "asset_mime_type": "",
            "asset_name": "",
            "asset_organization": "",
            "asset_perform": "",
            "asset_play": "",
            "asset_project": "",
            "asset_resource_name": "",
            "asset_serial_id": "",
            "asset_size": "",
            "asset_stage": "",
            "asset_type": "",
            "asset_url": "",
            "asset_video_id": "",
            "asset_video_title": "",
            "campaign_advertising_channel_sub_type": "",
            "campaign_advertising_channel_type": "",
            "campaign_app_id": "",
            "campaign_app_store": "",
            "campaign_bidding_strategy_goal_type": "",
            "campaign_budget_resource_name": "",
            "campaign_conversion_actions": "",
            "campaign_cpa": "",
            "campaign_end_date": "",
            "campaign_id": "",
            "campaign_name": "",
            "campaign_resource_name": "",
            "campaign_roas": "",
            "campaign_serving_status": "",
            "campaign_start_date": "",
            "campaign_status": "",
            "campaign_type": "",
            "click_change": 0,
            "clicks": 0,
            "conversions": 0,
            "cost_micros": 0,
            "country_code": "",
            "country_list": "",
            "cpi": 0,
            "ctr": 0,
            "customer_id": "",
            "cvr": 0,
            "daily_impressions": 0,
            "description_list_str": "",
            "dtstatdate": "",
            "game_code": "",
            "headline_list_str": "",
            "hour": "",
            "image_list_str": "",
            "impression_change": 0,
            "impressions": 0,
            "ipm": 0,
            "language": "",
            "main_country": "",
            "network": "",
            "parse_campaign_goal": "",
            "parse_cost_region": "",
            "parse_cost_type": "",
            "parse_custom_field": "",
            "parse_date": "",
            "parse_network": "",
            "parse_platform": "",
            "parse_region": "",
            "spend": 0,
            "video_list_str": "",
            "youtube_id": ""
        }
    ],
    "result": {
        "error_code": 0,
        "error_message": ""
    },
    "total": 0
}
```
