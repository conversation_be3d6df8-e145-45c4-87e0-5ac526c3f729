package service

import (
	"context"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
)

// SyncImpressionDateHistory ...
func SyncImpressionDateHistory() {
	ctx := log.NewSessionIDContext()

	tasks := conf.GetBizConf().SyncImpressionDateHistorys
	for _, t := range tasks {
		if !t.IfExecute {
			continue
		}

		start_time, err := time.Parse("2006-01-02 15:04:05", t.StartTime)
		if err != nil {
			log.ErrorContextf(ctx, "parse start time failed: %s", err)
		}

		end_time, err := time.Parse("2006-01-02 15:04:05", t.EndTime)
		if err != nil {
			log.ErrorContextf(ctx, "parse end time failed: %s", err)
		}

		syncImpressionDateHistoryPeriod(t.GameCode, start_time, end_time)
	}

	log.InfoContextf(ctx, "SyncImpressionDateHistory end")
}

// syncImpressionDateHistoryPeriodByChannelType 同步某游戏一段时间的历史数据
func syncImpressionDateHistoryPeriod(game_code string, start_time, end_time time.Time) {
	ctx := log.NewSessionIDContext()
	log.InfoContextf(ctx, "syncImpressionDateHistoryPeriod, game code: %s, start_time: %v, end_time: %v", game_code, start_time, end_time)

	err := syncImpressionDateHistoryPeriodByChannelType(ctx, game_code, constant.MediaGoogle, start_time, end_time)
	if err != nil {
		log.ErrorContextf(ctx, "syncImpressionDateHistoryPeriodByChannelType failed: %s", err)
	}

	err = syncImpressionDateHistoryPeriodByChannelType(ctx, game_code, constant.MediaFacebook, start_time, end_time)
	if err != nil {
		log.ErrorContextf(ctx, "syncImpressionDateHistoryPeriodByChannelType failed: %s", err)
	}
}

// syncImpressionDateHistoryPeriodByChannelType 同步一段时间的上线时间
func syncImpressionDateHistoryPeriodByChannelType(ctx context.Context, game_code string, channel_type int, start_time, end_time time.Time) error {
	existing_assets, err := getImpressionDateAssetsMapByChannelType(ctx, game_code, channel_type)
	if err != nil {
		log.ErrorContextf(ctx, "getImpressionDateAssetsMapGoogle failed: %s", err)
		return err
	}

	for start_time.Before(end_time) {
		existing_assets, err = syncImpressionDateHistoryOneDayByChannelType(ctx, game_code, start_time, channel_type, existing_assets)
		if err != nil {
			log.ErrorContextf(ctx, "syncImpressionDateHistoryOneDayByChannelType failed: %s", err)
		}
		start_time = start_time.AddDate(0, 0, 1)
	}

	return nil
}

// syncImpressionDateHistoryOneDayByChannelType 同步一天Google的数据
func syncImpressionDateHistoryOneDayByChannelType(ctx context.Context, game_code string, date time.Time, channel_type int, existing_assets map[string]bool) (map[string]bool, error) {
	online_assets, err := getOnlineChannelAssetsGoogleByChannelType(ctx, game_code, date, channel_type)
	if err != nil {
		log.ErrorContextf(ctx, "getOnlineChannelAssetsGoogleByChannelType failed: %s", err)
		return nil, err
	}

	var new_assets []*channelAsset
	for _, a := range online_assets {
		k := genChannelAssetKeyByStruct(a)
		if existing_assets[k] {
			continue
		}

		new_assets = append(new_assets, a)
		existing_assets[k] = true
	}

	log.InfoContextf(ctx, "get new impression date number: %d, channel type: %d, game code: %s, date: %v", len(new_assets), channel_type, game_code, date)

	err = insertImpressionDateByChannelAssets(ctx, game_code, new_assets, date)
	if err != nil {
		log.ErrorContextf(ctx, "insertImpressionDateByChannelAssets failed: %s", err)
		return nil, err
	}

	return existing_assets, nil
}
