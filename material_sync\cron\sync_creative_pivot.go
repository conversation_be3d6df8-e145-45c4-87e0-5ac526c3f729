package cron

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"regexp"

	chModel "e.coding.intlgame.com/ptc/aix-backend/common/model/clickhouse"
	pgModel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/conf"
	dataOp "e.coding.intlgame.com/ptc/aix-backend/material_sync/data"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

const (
	PivotSyncWeek = 5
	CountrySep    = ","
)

// 定时同步 analysis pivot
func syncCreativeAnalysisPivot(ctx context.Context) {
	for _, gameCode := range conf.GetBizConf().SyncGameCodeList {
		log.DebugContextf(ctx, "start syncGameCreativeAnalysisPivot: %v", gameCode)
		syncGameCreativeAnalysisPivot(ctx, gameCode)
	}
}

// getRecentSunday 获取最近上几个周日的日期列表
func getRecentSunday(size int) []string {
	resList := []string{}
	now := time.Now()
	offset := int(time.Sunday - now.Weekday())
	if offset > 0 {
		offset = -6
	}
	for i := 1; i < size+1; i++ {
		weekStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset-i*7)
		resList = append(resList, weekStart.Format("20060102"))
	}
	return resList
}

func parseInfoFromName(ctx context.Context, gameCode string, data *pgModel.CreativeAnalysisPivot) {
	if gameCode == "pubgm" {
		reg, err := regexp.Compile(`([^\d]+)(\d+)-([^-]+)-([pPvV])-(\d)-([a-zA-Z]+)-(.+)`)
		if err != nil {
			log.ErrorContextf(ctx, "error regexp.Compile, err: %v", err)
			return
		}
		resList := reg.FindStringSubmatch(data.AssetName)
		if len(resList) <= 0 {
			return
		}
		data.Size = resList[5]
		data.Lang = resList[6]
		data.AssetProjName = resList[3]
	} else {
		reg, err := regexp.Compile(`([VvPpUuSzZz])([^-]*)-([^-]*)-([^-]*)-([a-zA-Z]+)-([HhSsFf])-([^-]*)-(\d+)`)
		if err != nil {
			log.ErrorContextf(ctx, "error regexp.Compile, err: %v", err)
			return
		}
		resList := reg.FindStringSubmatch(data.AssetName)
		if len(resList) <= 0 {
			return
		}
		data.AssetBizType = resList[1]
		data.AssetBizName = resList[2]
		data.AssetBizTricks = resList[3]
		data.AssetBizFormat = resList[4]
		data.Lang = resList[5]
		data.Size = resList[6]
		data.AssetBizStep = resList[7]
		data.AssetDeliverDate = cast.ToInt64(resList[8])
	}
}

func syncGameCreativeAnalysisPivot(ctx context.Context, gameCode string) error {
	srcDateDataListMap := make(map[string][]*pgModel.CreativeAnalysisPivot)
	// 算出近n周周日的时间，把近几周的数据全拿出来
	for _, date := range getRecentSunday(PivotSyncWeek) {
		chDataList := dataOp.LoadPivotDateData(ctx, gameCode, date)
		dataListTmp := funk.Map(chDataList, func(data chModel.CreativeAnalysisPivot) *pgModel.CreativeAnalysisPivot {
			res := &pgModel.CreativeAnalysisPivot{}
			b, _ := json.Marshal(&data)
			json.Unmarshal(b, res)
			return res
		})
		dataList := dataListTmp.([]*pgModel.CreativeAnalysisPivot)
		log.DebugContextf(ctx, "len(dataList)=%v, date: %v", len(dataList), date)
		if len(dataList) <= 0 {
			continue
		}
		srcDateDataListMap[date] = dataList
	}
	// 对日期遍历，进行数据处理
	for date := range srcDateDataListMap {
		dataList := srcDateDataListMap[date]
		for _, srcData := range dataList {
			parseInfoFromName(ctx, gameCode, srcData)
		}
	}

	for date, dataList := range srcDateDataListMap {
		log.DebugContextf(ctx, "write date: %v", date)
		// 写入结果
		tx, err := postgresql.Pgdb.Begin()
		if err != nil {
			log.ErrorContextf(ctx, "error resql.Pgdb.Begin, err: %v", err)
			tx.Rollback()
			return err
		}
		// 将原始数据删除并插入新的数据
		_, err = tx.Exec(fmt.Sprintf("DELETE FROM %s WHERE dtstatdate=?", conf.GetBizConf().PGPivotTable), date)
		if err != nil {
			log.ErrorContextf(ctx, "error delete from pg error, err: %v, date: %v", err, date)
			tx.Rollback()
			return err
		}
		_, err = tx.Model(&dataList).Table(conf.GetBizConf().PGPivotTable).Insert()
		if err != nil {
			log.ErrorContextf(ctx, "error insert data to pg error, err: %v, date: %v", err, date)
			tx.Rollback()
			return err
		}
		if err := tx.Commit(); err != nil {
			log.ErrorContextf(ctx, "fail to commit transaction, err: %v", err)
			tx.Rollback()
			return err
		}
	}
	return nil
}

/*
// genDataKey 获取数据key
func genDataKey(assetName string, date int64) string {
	return fmt.Sprintf("%v_%v", assetName, date)
}

// getPrevWeekDate 获取上周时间戳
func getPrevWeekDate(date int64) int64 {
	t, err := time.Parse("20060102", cast.ToString(date))
	if err != nil {
		return 0
	}
	return cast.ToInt64(t.AddDate(0, 0, -7).Format("20060102"))
}

// buildListJson 构建列表的json，把逗号分割的字符串 "1,2,3" 变成 ["1", "2", "3"]
func buildListJson(strList string) string {
	jsonList := []string{}
	for _, countryCode := range strings.Split(strList, CountrySep) {
		jsonList = append(jsonList, fmt.Sprintf("\"%v\"", countryCode))
	}
	return fmt.Sprintf("[%v]", strings.Join(jsonList, ","))
}

// syncGameCreativeAnalysisPivotLegacy (已废弃)同步某个游戏的analysis pivot
func syncGameCreativeAnalysisPivotLegacy(ctx context.Context, gameCode string) error {
	srcDateDataListMap := make(map[string][]*pgModel.CreativeAnalysisPivot)
	srcTotalDataMap := make(map[string]*pgModel.CreativeAnalysisPivot)
	dataDataListMap := make(map[string][]*pgModel.CreativeAnalysisPivot)
	totalDataMap := make(map[string]*pgModel.CreativeAnalysisPivot)
	// 算出近n周周日的时间，把近几周的数据全拿出来
	for _, date := range getRecentSunday(PivotSyncWeek) {
		chDataList := dataOp.LoadPivotDateData(ctx, gameCode, date)
		dataListTmp := funk.Map(chDataList, func(data chModel.CreativeAnalysisPivot) *pgModel.CreativeAnalysisPivot {
			res := &pgModel.CreativeAnalysisPivot{}
			b, _ := json.Marshal(&data)
			json.Unmarshal(b, res)
			return res
		})
		dataList := dataListTmp.([]*pgModel.CreativeAnalysisPivot)
		srcDateDataListMap[date] = dataList
		for _, data := range dataList {
			srcTotalDataMap[genDataKey(data.AssetName, data.Dtstatdate)] = data
		}
	}
	// 对日期遍历，进行数据融合
	for date := range srcDateDataListMap {
		dataList := srcDateDataListMap[date]
		// assetUrlMap := make(map[string]*pgModel.CreativeAnalysisPivot)
		for _, srcData := range dataList {
			data, ok := totalDataMap[genDataKey(srcData.AssetName, cast.ToInt64(date))]
			if !ok {
				parseInfoFromName(ctx, gameCode, srcData)
				// 过滤不正确的名称
				if srcData.AssetName == "" {
					continue
				}
				// 写入结果列表和结果大map
				totalDataMap[genDataKey(srcData.AssetName, cast.ToInt64(date))] = srcData
				dataDataListMap[date] = append(dataDataListMap[date], srcData)
			} else {
				// 对能加和的数据加和
				data.Impressions += srcData.Impressions
				data.Clicks += srcData.Clicks
				data.Conversions += srcData.Conversions
				data.Spend += srcData.Spend
				data.Installs += srcData.Installs
				data.PrevWeekImpressions += srcData.PrevWeekImpressions
				data.PrevWeekClicks += srcData.PrevWeekClicks
				// 如果是新的国家
				newCountry := true
				for _, c := range strings.Split(data.CountryCode, CountrySep) {
					if srcData.CountryCode == c {
						newCountry = false
						break
					}
				}
				if newCountry {
					data.CountryCode += CountrySep + srcData.CountryCode
					data.CountryNameCh += CountrySep + srcData.CountryNameCh
					data.CountryNameEn += CountrySep + srcData.CountryNameEn
				}
				// 如果新地区
				newRegin := true
				for _, c := range strings.Split(data.UaRegion, CountrySep) {
					if srcData.UaRegion == c {
						newRegin = false
						break
					}
				}
				if newRegin {
					data.UaRegion += CountrySep + srcData.UaRegion
				}
				// 如果新渠道
				newNetwork := true
				for _, c := range strings.Split(data.ShowNetwork, CountrySep) {
					if srcData.ShowNetwork == c {
						newNetwork = false
						break
					}
				}
				if newNetwork {
					data.ShowNetwork += CountrySep + srcData.ShowNetwork
				}
				// 取最大的online days作为聚合结果的online days
				if srcData.Onlinedays > data.Onlinedays {
					data.Onlinedays = srcData.Onlinedays
				}
				// 取最早的start_date和最晚的end_date
				if srcData.StartDate < data.StartDate {
					data.StartDate = srcData.StartDate
				}
				if srcData.EndDate > data.EndDate {
					data.EndDate = srcData.EndDate
				}
			}
		}
		log.DebugContextf(ctx, "len(dataList)=%v, date: %v", len(dataList), date)
	}
	// 计算某些指标
	for _, data := range totalDataMap {
		// ctr=click / impression * 100%
		if data.Impressions > 0 {
			data.Ctr = float64(data.Clicks) / float64(data.Impressions)
		}
		// cvr = install / click * 100%
		if data.Clicks > 0 {
			data.Cvr = data.Installs / float64(data.Clicks)
		}
		// ipm = install / impression * 100%
		if data.Impressions > 0 {
			data.Ipm = data.Installs / float64(data.Impressions)
		}
		// cpi = spend / install * 100%
		if data.Installs > 0 {
			data.Cpi = data.Spend / data.Installs
		}
		// daily_impressions = impressions / onlinedays
		if data.Onlinedays > 0 {
			data.DailyImpressions = float64(data.Impressions) / float64(data.Onlinedays)
		}
		// impression_change = impressions - prev_week_impressions
		data.ImpressionChange = float64(data.Impressions) - data.PrevWeekImpressions
		// click_change = clicks - prev_week_clicks
		data.ClickChange = float64(data.Clicks) - data.PrevWeekClicks
		// prev_week_cpi, cpi_change
		if prevWeekData, ok := totalDataMap[genDataKey(data.AssetName, getPrevWeekDate(data.Dtstatdate))]; ok {
			data.PrevWeekCpi = prevWeekData.Cpi
			data.CpiChange = data.Cpi - prevWeekData.PrevWeekCpi
		}
		// 渠道按字母序排序
		countryList := strings.Split(data.ShowNetwork, CountrySep)
		sort.SliceStable(countryList, func(i, j int) bool { return strings.Compare(countryList[i], countryList[j]) > 0 })
		data.CountryCode = strings.Join(countryList, CountrySep)
		// 构建 country_code_json
		data.CountryCodeJson = buildListJson(data.CountryCode)
		// 构建 show_network_json
		data.ShowNetworkJson = buildListJson(data.ShowNetwork)
	}

	// 写入结果
	tx, err := postgresql.Pgdb.Begin()
	if err != nil {
		log.ErrorContextf(ctx, "error resql.Pgdb.Begin, err: %v", err)
		tx.Rollback()
		return err
	}
	for date, dataList := range dataDataListMap {
		log.DebugContextf(ctx, "write date: %v", date)
		// 将原始数据删除并插入新的数据
		_, err = tx.Exec(fmt.Sprintf("DELETE FROM %s WHERE dtstatdate=?", conf.GetBizConf().PGPivotTable), date)
		if err != nil {
			log.ErrorContextf(ctx, "error delete from pg error, err: %v, date: %v", err, date)
			tx.Rollback()
			return err
		}
		_, err = tx.Model(&dataList).Table(conf.GetBizConf().PGPivotTable).Insert()
		if err != nil {
			log.ErrorContextf(ctx, "error insert data to pg error, err: %v, date: %v", err, date)
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit(); err != nil {
		log.ErrorContextf(ctx, "fail to commit transaction, err: %v", err)
		tx.Rollback()
		return err
	}
	return nil
}
*/
