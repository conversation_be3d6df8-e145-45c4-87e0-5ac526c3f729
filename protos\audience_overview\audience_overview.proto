syntax = "proto3";

package audience_overview;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/audience_overview";

import "aix/aix_common_message.proto";

// 获取audience折线图数据, POST, /api/v1/audience_overview/audience_plots
message AudiencePlotsReq {
    repeated AudienceFilter filters = 1;  // 客户端选中的筛选器
    string metric_field             = 2;  // 需要返回的metric字段信息
}

message Campaign {
    string campaign_id   = 1;  // campaign_id
    string campaign_name = 2;  // campaign_name
}

message Metric {
    string date_time                        = 5;    // date time
    double ipm                              = 12;   // ipm
    double ctr                              = 13;   // ctr
    double cvr                              = 14;   // cvr
    double cpc                              = 15;   // cpc
    double cpm                              = 16;   // cpm
    double cpi                              = 17;   // cpi
    double cpa                              = 18;   // cpa
    double realtime_cohort_register_rate    = 19;   // realtime_cohort_register_rate
    double realtime_cpi                     = 20;   // realtime_cpi
    double realtime_d1_roas                 = 21;   // realtime_d1_roas
    double offline_d2_cohort_register_rate  = 22;   // offline_d2_cohort_register_rate
    double offline_d3_cohort_register_rate  = 23;   // offline_d3_cohort_register_rate
    double offline_d7_cohort_register_rate  = 24;   // offline_d7_cohort_register_rate
    double offline_d14_cohort_register_rate = 25;   // offline_d14_cohort_register_rate
    double offline_d2_retention_rate        = 26;   // offline_d2_retention_rate
    double offline_d3_retention_rate        = 27;   // offline_d3_retention_rate
    double offline_d7_retention_rate        = 28;   // offline_d7_retention_rate
    double offline_d14_retention_rate       = 29;   // offline_d14_retention_rate
    double offline_d2_roas                  = 30;   // offline_d2_roas
    double offline_d3_roas                  = 31;   // offline_d3_roas
    double offline_d7_roas                  = 32;   // offline_d7_roas
    double offline_d14_roas                 = 33;   // offline_d14_roas
    double impressions                      = 64;   //  impressions
    double clicks                           = 65;   //  clicks
    double spend                            = 66;   //  spend
    double conversions                      = 67;   //  conversions
    double interactions                     = 68;   //  interactions
    double installs                         = 69;   //  installs
    double in_app_action                    = 70;   //  in_app_action
    double campaign_daily_budget            = 71;   //  campaign_daily_budget
    double strategy_total_budget            = 72;   //  strategy_total_budget
    double realtime_cohort_register         = 73;   //  realtime_cohort_register
    double realtime_install                 = 74;   //  realtime_install
    double realtime_spend                   = 75;   //  realtime_spend
    double realtime_cohort_revenue          = 76;   //  realtime_cohort_revenue
    double realtime_d1_cohort_revenue       = 77;   //  realtime_d1_cohort_revenue
    double realtime_dau                     = 78;   //  realtime_dau
    double media_daily_budget               = 79;   //  media_daily_budget
    double media_bid_amount                 = 80;   //  media_bid_amount
    double offline_install                  = 81;   //  offline_install
    double offline_d1_cohort_register       = 82;   //  offline_d1_cohort_register
    double offline_d2_cohort_register       = 83;   //  offline_d2_cohort_register
    double offline_d3_cohort_register       = 84;   //  offline_d3_cohort_register
    double offline_d7_cohort_register       = 85;   //  offline_d7_cohort_register
    double offline_d14_cohort_register      = 86;   //  offline_d14_cohort_register
    double offline_d30_cohort_register      = 87;   //  offline_d30_cohort_register
    double offline_d60_cohort_register      = 88;   //  offline_d60_cohort_register
    double offline_d90_cohort_register      = 89;   //  offline_d90_cohort_register
    double offline_d120_cohort_register     = 90;   //  offline_d120_cohort_register
    double offline_d150_cohort_register     = 91;   //  offline_d150_cohort_register
    double offline_d180_cohort_register     = 92;   //  offline_d180_cohort_register
    double offline_d1_retention             = 93;   //  offline_d1_retention
    double offline_d2_retention             = 94;   //  offline_d2_retention
    double offline_d3_retention             = 95;   //  offline_d3_retention
    double offline_d7_retention             = 96;   //  offline_d7_retention
    double offline_d14_retention            = 97;   //  offline_d14_retention
    double offline_d30_retention            = 98;   //  offline_d30_retention
    double offline_d60_retention            = 99;   //  offline_d60_retention
    double offline_d90_retention            = 100;  //  offline_d90_retention
    double offline_d120_retention           = 101;  //  offline_d120_retention
    double offline_d150_retention           = 102;  //  offline_d150_retention
    double offline_d180_retention           = 103;  //  offline_d180_retention
    double offline_d1_cohort_revenue        = 104;  //  offline_d1_cohort_revenue
    double offline_d2_cohort_revenue        = 105;  //  offline_d2_cohort_revenue
    double offline_d3_cohort_revenue        = 106;  //  offline_d3_cohort_revenue
    double offline_d7_cohort_revenue        = 107;  //  offline_d7_cohort_revenue
    double offline_d14_cohort_revenue       = 108;  //  offline_d14_cohort_revenue
    double offline_d30_cohort_revenue       = 109;  //  offline_d30_cohort_revenue
    double offline_d60_cohort_revenue       = 110;  //  offline_d60_cohort_revenue
    double offline_d90_cohort_revenue       = 111;  //  offline_d90_cohort_revenue
    double offline_d120_cohort_revenue      = 112;  //  offline_d120_cohort_revenue
    double offline_d150_cohort_revenue      = 113;  //  offline_d150_cohort_revenue
    double offline_d180_cohort_revenue      = 114;  //  offline_d180_cohort_revenue
    double offline_spend                    = 115;  //  offline_spend
}

message AudiencePlot {
    string audience_name        = 1;  // audience name
    repeated Campaign campaigns = 2;  // campaign list
    repeated Metric metrics     = 3;  // metric list
}

message AudiencePlotsRsp {
    aix.Result result               = 1;  // 返回结果
    repeated AudiencePlot audiences = 2;  // audience列表
}

// 获取audience表格数据, POST, /api/v1/audience_overview/audience_tables
message AudienceTablesReq {
    repeated AudienceFilter filters = 1;  // 客户端选中的筛选器
}

message AudienceTable {
    string audience_name        = 1;  // audience name
    repeated Campaign campaigns = 2;  // campaign list
    Metric metric               = 3;  // metric info
}

message AudienceTablesRsp {
    aix.Result result                = 1;  // 返回结果
    repeated AudienceTable audiences = 2;  // audience列表
}

// 获取audience汇总面板数据, POST, /api/v1/audience_overview/audience_sum_panel
message AudienceSumPanelReq {
    repeated AudienceFilter filters = 1;  // 客户端选中的筛选器
}

message AudienceSumPanel {
    string audience_name        = 1;  // audience name
    repeated Campaign campaigns = 2;  // campaign list
    double spend                = 3;  // spend
}

message AudienceSumPanelRsp {
    aix.Result result                   = 1;  // 返回结果
    repeated AudienceSumPanel audiences = 2;  // audience列表
    double sum_spend                    = 3;  // spend汇总数据
}

// 获取audience filter列表, POST, /api/v1/audience_overview/audience_filter_list
message AudienceFilterListReq {
}

message AudienceFilterTimeSpan {
    string start_time = 1;  // 开始时间
    string end_time   = 2;  // 结束时间
}

message AudienceFilterEnum {
    string name      = 1;  // 枚举名
    string enum_id   = 2;  // 枚举ID
    string addition1 = 3;  // 附加字段1，当filter_id为country时表示region
    string addition2 = 4;  // 附加字段2，当filter_id为country时表示region_name
}

message AudienceFilter {
    string name                       = 1;  // 名称
    string filter_id                  = 2;  // 过滤器ID
    uint32 filter_type                = 3;  // 过滤器类型，1-时间跨度；2-枚举值
    AudienceFilterTimeSpan time_span  = 4;  // 时间跨度
    repeated AudienceFilterEnum enums = 5;  // 枚举列表
}

message AudienceFilterListRsp {
    aix.Result result               = 1;  // 返回结果
    repeated AudienceFilter filters = 2;  // 过滤器列表
}

// 自测接口, POST, /api/v1/audience_overview/say_hi
message SayHiReq {
    int32 offset          = 1;
    int32 limit           = 2;
    int32 update_on_cycle = 3;
    string account_id     = 4;
    string campaign_id    = 5;
}

message SayHiRsp {
    aix.Result result = 1;  // 返回结果
}

message UpdateAudienceListManulReq {
    string start_time = 1;  // 开始时间
    string end_time   = 2;  // 结束时间
    string platform   = 3;  // 平台名称: Google, Facebook
}

message UpdateAudienceListManulRsp {
    aix.Result result = 1;  // 返回结果
}

// 获取audience_metric列表, POST, /api/v1/audience_overview/audience_metrics
message AudienceMetricsReq {
}

message AudienceMetric {
    string key     = 1;  // Audience中的字段名
    string title   = 2;  // 前端显示名称
    string format  = 3;  // 显示格式
    string type    = 4;  // metric类型
    int32 agg_type = 5;  // 聚合类型，1-表示可以累加；2-表示比率类型，不可以累加
}

message AudienceMetricsRsp {
    aix.Result result               = 1;  // 返回结果
    repeated AudienceMetric metrics = 2;  // audience列表
}