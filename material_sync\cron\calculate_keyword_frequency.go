package cron

import (
	"context"
	"fmt"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
)

type KeywordFrequency struct {
	Keyword string `db:"keyword"`
	Count   int    `pg:"count"`
}

func calculateKeywordFrequency(ctx context.Context) {
	log.InfoContextf(ctx, "CalculateKeywordFrequency start")
	gameCodeList, err := getAllGameCodeList()
	if err != nil {
		log.ErrorContextf(ctx, "error getAllGameCodeList error: %s", err)
		return
	}
	for _, gameCode := range gameCodeList {

		err = deleteAndInsert(ctx, gameCode)
		if err != nil {
			log.ErrorContextf(ctx, "error deleteAndInsert error: %s", err)
			continue
		}

		//keywordList, err := getKeywordFrequency(ctx, gameCode)
		//if err != nil {
		//	log.ErrorContextf(ctx, "error getKeywordFrequency error: %s", err)
		//	continue
		//}
		//err = saveCreativeKeyword(ctx, gameCode, keywordList)
		//if err != nil {
		//	log.ErrorContextf(ctx, "error saveCreativeKeyword error: %s", err)
		//	continue
		//}
	}
}

// getAllAssetName 获取所有素材名称列表
func getAllAssetName(ctx context.Context, gameCode string) ([]string, error) {
	var assetNameList []string
	db := pgdb.Pgdb
	_, err := db.QueryContext(ctx, &assetNameList, fmt.Sprintf("SELECT asset_name FROM arthub_sync.tb_creative_overview_%s WHERE asset_status=1 ", gameCode), gameCode)
	return assetNameList, err
}

// getKeywordFrequency 将asset_name按照间隔符分割，统计每个关键词出现的次数
func getKeywordFrequency(ctx context.Context, gameCode string) ([]KeywordFrequency, error) {
	var keywordFrequencyList []KeywordFrequency
	db := pgdb.Pgdb
	_, err := db.QueryContext(ctx, &keywordFrequencyList, fmt.Sprintf(`SELECT keyword,COUNT(*) as count FROM (SELECT regexp_split_to_table(asset_name, '[-|\s]') AS keyword FROM arthub_sync.tb_creative_overview_%s) AS tb WHERE tb.keyword!='' GROUP BY tb.keyword `, gameCode), gameCode)
	return keywordFrequencyList, err
}

// saveCreativeKeyword 将关键词和出现次数保存到数据库
func saveCreativeKeyword(ctx context.Context, gameCode string, keywordFrequencyList []KeywordFrequency) error {
	keywordList := make([]pgmodel.CreativeKeyword, 0, len(keywordFrequencyList))
	for _, keywordFrequency := range keywordFrequencyList {
		keywordList = append(keywordList, pgmodel.CreativeKeyword{
			Word:       keywordFrequency.Keyword,
			Frequency:  int32(keywordFrequency.Count),
			CreateTime: utils.GetCSTNowStr(),
			UpdateTime: utils.GetCSTNowStr(),
		})
	}

	db := pgdb.Pgdb
	tx, err := db.Begin()
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	_, err = tx.Model(&pgmodel.CreativeKeyword{}).Table(fmt.Sprintf("arthub_sync.tb_creative_keyword_%s", gameCode)).Where("1=1").Delete()
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	_, err = tx.Model(&keywordList).Table(fmt.Sprintf("arthub_sync.tb_creative_keyword_%s", gameCode)).Returning("NULL").Insert()
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	return tx.Commit()
}

// deleteAndInsert 删除旧数据，插入新数据
func deleteAndInsert(ctx context.Context, gameCode string) error {
	db := pgdb.Pgdb
	tx, err := db.Begin()
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	// 只删除自动统计创建的数据
	_, err = tx.Model(&pgmodel.CreativeKeyword{}).Table(fmt.Sprintf("arthub_sync.tb_creative_keyword_%s", gameCode)).Where("update_by='aix'").Delete()
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	// 直接在数据库中统计并插入数据
	_, err = tx.Exec(fmt.Sprintf(`INSERT INTO "arthub_sync"."tb_creative_keyword_%s" ("word", "frequency", "create_by", "create_time", "update_by", "update_time") SELECT word word,COUNT(*) frequency,'aix' create_by,to_char(now(), 'yyyy-MM-dd HH24:mi:ss') create_time,'aix' update_by,to_char(now(), 'yyyy-MM-dd HH24:mi:ss') update_time FROM (SELECT regexp_split_to_table(asset_name, '[-|\s]') AS word FROM arthub_sync.tb_creative_overview_%s) AS tb WHERE tb.word!='' GROUP BY tb.word ON CONFLICT ("word") DO NOTHING;`, gameCode, gameCode))
	if err != nil {
		_ = tx.Rollback()
		return err
	}
	return tx.Commit()
}
