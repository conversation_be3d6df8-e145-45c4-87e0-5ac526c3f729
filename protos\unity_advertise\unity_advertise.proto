syntax = "proto3";

package unity_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/unity_advertise";

import "aix/aix_common_message.proto";


// POST /api/v1/unity_advertise/say_hi
message SayHiReq {
    string msg = 1;
}

message SayHiRsp {
    aix.Result result = 1;
    string     msg    = 2;
}

// 基础结构 渠道账号信
message Account {
    string account_id = 1;
    string name       = 2;
    string game_id    = 3;
    string store_id   = 4;
}

// 增加游戏渠道账号(只能pod里面请求) POST /api/v1/unity_advertise/add_media_accounts
message AddMediaAccountsReq {
    string game_code            = 1;
    string organization_core_id = 2;
    string key_id               = 3;
    string secret_key           = 4;
    repeated Account accounts   = 5;
}

message AddMediaAccountsRsp {
    aix.Result result = 1;
}

// 查询渠道账号列表 POST /api/v1/unity_advertise/get_media_accounts
message GetMediaAccountsReq {
    string game_code = 1; // 必填 游戏标识game_code
}

message GetMediaAccountsRsp {
    aix.Result result = 1;
    repeated Account accounts = 2;
}