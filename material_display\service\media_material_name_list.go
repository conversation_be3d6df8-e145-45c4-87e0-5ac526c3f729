// Package service 服务接口实现代码
package service

import (
	"sort"
	"strconv"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/scylladb/go-set"
)

// 根据ID(resource_name)列表查询素材名称列表
func MediaMaterialNameList(ctx *gin.Context, req *pb.MediaMaterialNameListReq, rsp *pb.MediaMaterialNameListRsp) error {
	if len(req.GetResourceNames()) == 0 {
		return nil
	}
	if req.GetAccountId() == "" || req.GetGameCode() == "" {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "param error: AccountId and GameCode cannot be empty")
	}
	// 查询素材asset_id列表
	mediaUploads, err := data.QueryCreativeMediaUploadAssetIds(
		ctx, req.GetAccountId(), req.GetMedia(), req.GetFormatType(), req.GetGameCode(), req.GetResourceNames())
	if err != nil {
		log.ErrorContextf(ctx, "data.QueryCreativeMediaUploadAssetIds error:%v", err)
		return err
	}
	assetIds := make([]string, 0, len(mediaUploads))
	assetInfos := make(map[string]string)
	asset2cover_hash := make(map[string]string)
	asset2asset_ratio := make(map[string]string)
	for _, mediaUpload := range mediaUploads {
		if mediaUpload.AssetId == "" {
			continue
		}
		assetInfos[mediaUpload.AssetId] = mediaUpload.ResourceName
		asset2cover_hash[mediaUpload.AssetId] = mediaUpload.CoverResourceName
		assetIds = append(assetIds, mediaUpload.AssetId)
		asset2asset_ratio[mediaUpload.AssetId] = mediaUpload.AssetRatio
	}
	if len(assetIds) == 0 {
		return nil
	}
	// 查询素材asset名称列表
	overviews, err := data.QueryCreativeOverviewByIDList(ctx, req.GetGameCode(), assetIds)
	if err != nil {
		log.ErrorContextf(ctx, "data.QueryCreativeOverviewByIDList error:%v", err)
		return err
	}
	// 查询素材详情
	gameCode := req.GetGameCode()
	arthubCode := ""
	depotCache, ok := cache.DepotTbArthubDepotCache[gameCode]
	if ok {
		arthubCode = depotCache.ArthubCode
	} else {
		depot, err := GetArthubToken(ctx, gameCode)
		if err != nil {
			log.ErrorContextf(ctx, "error data.GetMaterialList GetArthubToken, gameCode: %v, err: %v", gameCode, err)
			return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR), "system error")
		}
		arthubCode = depot.ArthubCode
	}
	assetID2Detail, err := mediaMaterialDetails(ctx, assetIds, gameCode)
	if err != nil {
		log.ErrorContextf(ctx, "mediaMaterialDetails failed, err: %s", err.Error())
		return err
	}

	overviewsMap := make(map[string]*model.CreativeOverview)
	var assetProviewUrlIDs []string
	for _, view := range overviews {
		overviewsMap[view.AssetID] = view
		if view.AssetStatus == arthub.ARTHUB_ASSET_STATUS_NORMAL {
			assetProviewUrlIDs = append(assetProviewUrlIDs, view.AssetID)
		}
	}

	assetPreviewUrls, _, err := getAssetPreviewUrl(ctx, gameCode, arthubCode, assetProviewUrlIDs)
	if err != nil {
		log.ErrorContextf(ctx, "getAssetPreviewUrl failed, err: %s", err.Error())
		return err
	}

	var rlt []*pb.MaterialAssetNameInfo
	for _, upload := range mediaUploads {
		overview, ok := overviewsMap[upload.AssetId]
		if !ok {
			continue
		}

		assetId := overview.AssetID
		resourceName, ok := assetInfos[assetId]
		if !ok {
			log.WarningContextf(ctx, "assetId(%v) not exist in assetInfos:%+v", assetId, assetInfos)
			continue
		}
		detail := assetID2Detail[assetId]
		if detail == nil {
			continue
		}
		previewUrl := ""
		for _, asset := range assetPreviewUrls {
			if strconv.FormatUint(asset.ID, 10) == assetId {
				previewUrl = asset.PreviewUrl
				break
			}
		}
		cover_hash := asset2cover_hash[assetId]
		asset_ratio := asset2asset_ratio[assetId]
		rlt = append(rlt, &pb.MaterialAssetNameInfo{
			ResourceName: resourceName,
			CoverHash:    cover_hash,
			AssetId:      assetId,
			AssetName:    overview.AssetName,
			Format:       detail.Format,
			PreviewUrl:   previewUrl,
			Duration:     detail.Duration,
			Detail: &pb.MediaMaterialMetaDetail{
				Cover: detail.Cover,
				Width: detail.Width,
				High:  detail.High,
			},
			AssetRatio: asset_ratio,
		})
	}

	// 如果图片内容是一样的，多个素材上传到google生成的resource_name是一样的
	// 根据resource_name查出来可能有多个素材，需要过滤一下

	// 根据是否有previw_url排序，优先使用有preview_url的
	sort.SliceStable(rlt, func(i, j int) bool {
		if rlt[i].PreviewUrl != "" && rlt[j].PreviewUrl != "" {
			return i < j
		}

		if rlt[i].PreviewUrl != "" {
			return true
		}

		return false
	})
	// 去重过滤
	s := set.NewStringSet()
	for _, asset := range rlt {
		if !s.Has(asset.GetResourceName()) {
			s.Add(asset.GetResourceName())
			rsp.AssetNameInfos = append(rsp.AssetNameInfos, asset)
		}
	}
	return nil
}
