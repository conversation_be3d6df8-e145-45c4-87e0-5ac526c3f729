package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/cache"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	"e.coding.intlgame.com/ptc/aix-backend/protos/material_display"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10"
	"github.com/spf13/cast"
)

var GAME_CODE_KEY = "game"

type ArthubDirectoryCreate struct {
	Name                   string `json:"name"`
	ParentId               uint64 `json:"parent_id"`
	RenameRepeatedCategory int32  `json:"rename_repeated_category"`
}

// NodeList ...
type ArthubDirectoryList struct {
	Items []ArthubDirectoryItem `json:"items"`
}

type ArthubDirectoryItem struct {
	ParamIndex int64  `json:"param_index"`
	Id         uint64 `json:"id"`
}

// ArthubDirectoryCreate ...
type ArthubDirectoryCreateRsp struct {
	Code   int32               `json:"code"`
	Result ArthubDirectoryList `json:"result"`
}

// arthub 素材移动
type ArthubMaterialMoveReq struct {
	Ids           []uint64 `json:"ids"`
	OtherParentId uint64   `json:"other_parent_id"`
}

type ArthubMaterialMoveList struct {
	Items []ArthubMaterialMoveItem `json:"items"`
}
type ArthubMaterialMoveItem struct {
	ParamIndex int64  `json:"param_index"`
	Id         uint64 `json:"id"`
}

type ArthubMaterialMoveError struct {
	ParamIndex int64  `json:"param_index"`
	Id         uint64 `json:"id"`
	Message    string `json:"message"`
}

type ArthubMaterialMoveResp struct {
	Code   int32                  `json:"code"`
	Result ArthubMaterialMoveList `json:"result"`
	// Error  []ArthubMaterialMoveError `json:"error"`
}

// correctDirectoryFullPath 矫正目录的full_path_id及full_path_name
func correctDirectoryFullPath(ctx context.Context, gameCode string, depot *model.ArthubDepot, dirs []*model.CreativeDirectory) []*model.CreativeDirectory {
	if len(dirs) == 0 {
		return dirs
	}

	var err error
	// 拉取depot信息
	if depot == nil {
		depot, err = data.GetDepotWithContext(ctx, gameCode)
		if err != nil {
			log.WarningContextf(ctx, "correctDirectoryFullPath data.GetDepotWithContext err: %v", err)
			return dirs
		}
	}
	tableName := genCreativeDirectoryTableName(gameCode)
	mdl := postgresql.GetDBWithContext(ctx).Model(&model.CreativeDirectory{}).Table(tableName)
	mdl.Where("id=?", depot.DepotId)

	var record model.CreativeDirectory
	err = mdl.Select(&record)
	if err != nil && err != pg.ErrNoRows {
		log.ErrorContextf(ctx, "get depot directory failed: %s", err)
		return dirs
	}

	if err == pg.ErrNoRows {
		return dirs
	}

	if len(record.FullPathID) == 0 {
		return dirs
	}
	cutID := fmt.Sprintf("%s,", record.FullPathID)
	cutName := fmt.Sprintf("%s,", record.FullPathName)
	log.DebugContextf(ctx, "correctDirectoryFullPath get cutID:%v, cutName:%v", cutID, cutName)
	for _, dir := range dirs {
		dir.FullPathID = strings.TrimPrefix(dir.FullPathID, cutID)
		dir.FullPathName = strings.TrimPrefix(dir.FullPathName, cutName)
	}
	return dirs
}

func createArthubDir(ctx *gin.Context, depot *model.ArthubDepot, name, parentID string, renameRepeatedCategory int32) (string, error) {
	// arthub 创建目录
	url := fmt.Sprintf("https://service.arthub.qq.com/%s/data/openapi/v2/core/create-directory",
		depot.ArthubCode)
	parentIDUInt64, err := strconv.ParseUint(parentID, 10, 64)
	if err != nil {
		return "", err
	}
	arthubRequestObj := []ArthubDirectoryCreate{
		{
			Name:                   name,
			ParentId:               parentIDUInt64,
			RenameRepeatedCategory: renameRepeatedCategory,
		},
	}
	body, err := json.Marshal(&arthubRequestObj)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to format arthub request object, obj: %v, error: %v", arthubRequestObj, err)
		return "", err
	}
	arthubRespStr, err := HTTPClientDo(ctx, "POST", url, depot.PublicToken, body)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to create directory in arthub request, url: %v, token: %v, "+
			"body: %v, error: %v", url, depot.PublicToken, string(body), err)
		return "", err
	}
	log.InfoContextf(ctx, "CreateMaterialDirectory arthub responce: %s, token: %v,"+
		" url: %v\n", arthubRespStr, depot.PublicToken, url)
	arthubRespObj := ArthubDirectoryCreateRsp{}
	if err := json.Unmarshal([]byte(arthubRespStr), &arthubRespObj); err != nil {
		log.ErrorContextf(ctx, "Fail to unmarshal arthub response object, resp: %v, error: %v", arthubRespStr, err)
		return "", err
	}
	if arthubRespObj.Code != 0 {
		err := fmt.Errorf("Fail to create directory in arthub, code: %v, result:%v", arthubRespObj.Code, arthubRespObj.Result)
		return "", err
	}
	id := strconv.FormatUint(arthubRespObj.Result.Items[0].Id, 10)
	return id, nil
}

func createGoogleDir(ctx *gin.Context, depot *model.ArthubDepot, name, parentID string) (string, error) {
	//srv, err := google.NewGoogleService([]byte(depot.GoogleServiceAccount))
	//if err != nil {
	//	log.ErrorContextf(ctx, "fail to create google service, err: %v", err)
	//	return "", err
	//}
	srv, err := cache.GoogleServiceCache{}.Get(ctx, depot.GameCode)
	if err != nil {
		log.ErrorContextf(ctx, "cache.GoogleServiceCache fail to get google service, gameCode: %v, err: %v", depot.GameCode, err)
		return "", err
	}
	id, err := srv.Create(name, []string{parentID})
	if err != nil {
		log.ErrorContextf(ctx, "fail to create google directroy, name: %s, parentID: %v, err: %v", name, parentID, err)
		return "", err
	}
	return id, nil
}

func syncDbInCreateMaterialDirectory(ctx *gin.Context, directoryType, directoryId, parentId string,
	directoryName, parentDirectoryName, fullPathName, fullPathIds, timeStamp string, gameCode string) error {
	//检查sql注入
	directoryId = cast.ToString(&directoryId)
	parentId = cast.ToString(&parentId)
	directoryName = cast.ToString(&directoryName)
	parentDirectoryName = cast.ToString(&parentDirectoryName)
	fullPathName = cast.ToString(&fullPathName)
	fullPathIds = cast.ToString(&fullPathIds)

	tx, err := postgresql.GetDBWithContext(ctx).Begin()
	if err != nil {
		return err
	}

	sql := fmt.Sprintf(`insert into %s.tb_creative_directory_%s (id, name, parent_id,
				parent_name, create_date, update_date, direct_child_count, total_leaf_count,
				direct_directory_count, full_path_name, full_path_id, type)
			values (?,?,?,?,?,?,?,?,?,?,?,?)`, "arthub_sync", gameCode)
	_, err = tx.Exec(sql, directoryId, directoryName, parentId, parentDirectoryName, timeStamp, timeStamp, 0,
		0, 0, fullPathName, fullPathIds, directoryType)
	if err != nil {
		log.ErrorContextf(ctx, "fail to insert directory in postgresql, gamecode: %v, "+
			"directory object id: %+v, err: %v", gameCode, directoryId, err)
		tx.Rollback()
		return fmt.Errorf("fail to insert directory in postgresql")
	}

	// 同步父节点目录数量
	parentDirectoryInDb := model.CreativeDirectory{}
	tablename := fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gameCode)
	sql = fmt.Sprintf(`SELECT * FROM %s WHERE id = ?`, tablename)
	_, err = tx.QueryOne(&parentDirectoryInDb, sql, parentId)
	if err != nil {
		tx.Rollback()
		log.ErrorContextf(ctx, "fail to get parent directory information, sql: %v, err: %v", sql, err)
		return err
	}

	directChildCount := parentDirectoryInDb.DirectChildCount + 1
	directDirectoryCount := parentDirectoryInDb.DirectDirectoryCount + 1
	sql = fmt.Sprintf(`UPDATE %s.tb_creative_directory_%s
			SET direct_child_count = ?,
				direct_directory_count = ?
			WHERE id = ?`, "arthub_sync", gameCode)
	_, err = tx.Exec(sql, directChildCount, directDirectoryCount, parentId)
	if err != nil {
		log.ErrorContextf(ctx, "fail to update target directory information by id,"+
			" id: %d, err: %v\n", parentId, err)
		tx.Rollback()
		return err
	}

	err = tx.Commit()
	if err != nil {
		tx.Rollback()
		log.ErrorContextf(ctx, "fail to commit transaction in sync directory info to db, err: %v", err)
	}
	return err
}

func MoveMaterialToOtherDirectory(ctx *gin.Context, req *material_display.MaterialMoveReq) (*material_display.MaterialMoveRsp, error) {
	var resp material_display.MaterialMoveRsp
	gamecode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if err := checkParamInMoveMaterialToOtherDirectory(req, gamecode); err != nil {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}
	db := postgresql.GetDBWithContext(ctx)
	result, err := db.Exec(`SELECT COUNT(*) FROM  pg_class WHERE relname = ?`,
		fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gamecode))
	if err != nil {
		log.ErrorContextf(ctx, "fail to check gamecode in postgresql, gamecode: %v, err: %v", gamecode, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	if result.RowsReturned() < 1 {
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "resource db not exist")
	}

	//校验父目录是否存在
	parentDirectory := model.CreativeDirectory{}
	sql := fmt.Sprintf(`SELECT * FROM %s.tb_creative_directory_%s WHERE id = ?`,
		"arthub_sync", gamecode)
	_, err = db.QueryOne(&parentDirectory, sql, req.OtherParentId)
	if err != nil {
		if err == pg.ErrNoRows {
			log.InfoContextf(ctx, "Material directory information not exist, "+
				"ParentId: %v, err: %v", req.OtherParentId, err)
			return &resp, nil
		}
		log.ErrorContextf(ctx, "Fail to get material directory information by id, "+
			"ParentId: %v, err: %v", req.OtherParentId, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}

	//获取根节点信息
	depot, err := data.GetDepot(gamecode)
	if err != nil {
		log.ErrorContextf(ctx, "Fail to get depot token information, gamecode: %v, err: %v", gamecode, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}

	assetList := []model.CreativeOverview{}
	err = postgresql.GetDBWithContext(ctx).Model(&assetList).
		Table(fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", depot.GameCode)).
		WhereIn("asset_id IN (?)", req.GetIds()).
		Where("asset_status = ?", arthub.ARTHUB_ASSET_STATUS_NORMAL).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "fail to get asset info, asset ids: %v, err: %v", req.GetIds(), err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "error db")
	}
	ids := []string{}
	for _, asset := range assetList {
		ids = append(ids, asset.AssetID)
	}
	var successIds, failIds []string
	switch depot.Type {
	case utils.GAME_DEPOT_TYPE_ARTHUB:
		// 调用arthub api移动素材
		arthubResp, err := moveMaterialInArthub(ctx, depot.ArthubCode, depot.PublicToken, ids, req.GetOtherParentId())
		if err != nil {
			log.ErrorContextf(ctx, "Fail to move material to other directory in arthub, depotName: %s, "+
				"token: %s, err: %v", depot.GameCode, depot.PublicToken, err)
			return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "error arthub")
		}
		successIds, failIds = formatMoveMaterial(arthubResp)
		log.DebugContextf(ctx, "materil move fail id list: %v\n", failIds)
	case utils.GAME_DEPOT_TYPE_GOOGLE_DRIVER:
		successIds, failIds, err = moveGoogleMaterial(ctx, &depot, ids, req.GetOtherParentId())
		if err != nil {
			log.ErrorContextf(ctx, "Fail to move material to directory in google, depotName: %s, err: %v", depot.GameCode, err)
			return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "error google")
		}
	default:
		log.ErrorContextf(ctx, "depot type error")
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "depot type error")
	}

	//更新db
	successAssets, err := syncDbInMoveMaterialToOtherDirectory(ctx, depot.GameCode,
		depot.DepotId, req.GetOtherParentId(), successIds)
	if err != nil {
		log.ErrorContextf(ctx, "fail to sync db asset and directory info, "+
			"depotName: %s, rootId: %v, target directory id: %v, successIds: %v, err: %v", depot.DepotName,
			depot.DepotId, req.GetOtherParentId(), successIds, err)
		return &resp, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_RET), "error sync db")
	}
	items := formatMoveMaterialResp(successAssets)
	resp = material_display.MaterialMoveRsp{
		Result: &pbAix.Result{
			ErrorCode:    0,
			ErrorMessage: "ok",
		},
		MaterialItems:     items,
		FailedMaterialIds: failIds,
	}

	return &resp, nil
}

func moveGoogleMaterial(ctx *gin.Context, depot *model.ArthubDepot, ids []string, parentID string) ([]string, []string, error) {
	//srv, err := google.NewGoogleService([]byte(depot.GoogleServiceAccount))
	//if err != nil {
	//	log.ErrorContextf(ctx, "fail to create google service, err: %v", err)
	//	return nil, nil, err
	//}

	srv, err := cache.GoogleServiceCache{}.Get(ctx, depot.GameCode)
	if err != nil {
		log.ErrorContextf(ctx, "cache.GoogleServiceCache fail to get google service, gameCode: %v, err: %v", depot.GameCode, err)
		return nil, nil, err
	}

	var (
		succeedIDs = []string{}
		failedIDs  = []string{}
	)
	for _, id := range ids {
		f, err := srv.GetFileInfo(id)
		if err != nil {
			log.ErrorContextf(ctx, "fail to get material information in moveGoogleMaterial, new parentID：%v, id: %s, err: %v", parentID, id, err)
			failedIDs = append(failedIDs, id)
			continue
		}
		log.DebugContextf(ctx, "fileinfo: %+v", f)
		err = srv.Move(id, f.Parents[0], parentID)
		if err != nil {
			log.ErrorContextf(ctx, "fail to move material to directroy %q', id: %s, err: %v", parentID, id, err)
			failedIDs = append(failedIDs, id)
		}
		succeedIDs = append(succeedIDs, id)
	}

	return succeedIDs, failedIDs, nil
}

// otherParentId： 素材移动到目标目录的id
// ids: arthub素材移动成功的素材id列表
func syncDbInMoveMaterialToOtherDirectory(ctx *gin.Context, gameCode string, rootID string, otherParentId string,
	ids []string) ([]model.CreativeOverview, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	//检查sql 注入
	otherParentId = cast.ToString(&otherParentId)
	for idx := range ids {
		ids[idx] = cast.ToString(&ids[idx])
	}

	tx, err := postgresql.GetDBWithContext(ctx).Begin()
	if err != nil {
		return nil, err
	}
	//defer tx.Close()
	sql := fmt.Sprintf(`SELECT * FROM %s.tb_creative_directory_%s WHERE id = ?`,
		"arthub_sync", gameCode)
	directoryModel := model.CreativeDirectory{}
	_, err = tx.QueryOne(&directoryModel, sql, otherParentId)
	if err != nil {
		log.ErrorContextf(ctx, "fail to get directory information by id, id: %d, err: %v\n", otherParentId, err)
		tx.Rollback()
		return nil, err
	}

	tableName := fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", gameCode)
	assets := []model.CreativeOverview{}
	err = tx.Model(&assets).
		Table(tableName).
		WhereIn("asset_id IN (?)", ids).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "fail to get asset information by id list, ids: %v, err: %v", ids, err)
		tx.Rollback()
		return nil, err
	}

	if len(assets) > 0 {
		//更新素材full_path_name, full_path_id
		fullPathName := fmt.Sprintf("%s,%s", directoryModel.FullPathName, directoryModel.Name)
		fullPathId := fmt.Sprintf("%s,%s", directoryModel.FullPathID, directoryModel.ID)
		tableName = fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", gameCode)
		_, err = tx.Model((*model.CreativeOverview)(nil)).
			Table(tableName).
			Set("full_path_name = ?, full_path_id = ?", fullPathName, fullPathId).
			WhereIn("asset_id IN (?)", ids).
			Update()
		if err != nil {
			log.ErrorContextf(ctx, "fail to update assets full_path_name, full_path_id, sql: %s, "+
				"ids: %v, full_path_name: %v, full_path_id: %v, err: %v\n", sql,
				ids, fullPathName, fullPathId, err)
			tx.Rollback()
			return nil, err
		}

		//目标目录数量加一, 目标目录到根但不包括根节点，递归素材数量增加
		//若是root节点，无需更新递归素材数量
		assetsCount := len(assets)
		parentDirectoryInDb := model.CreativeDirectory{}
		tablename := fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gameCode)
		sql = fmt.Sprintf(`SELECT * FROM %s WHERE id = ?`, tablename)
		_, err = tx.QueryOne(&parentDirectoryInDb, sql, otherParentId)
		if err != nil {
			tx.Rollback()
			log.ErrorContextf(ctx, "fail to get parent directory information, sql: %v, otherParentId: %v, err: %v",
				sql, otherParentId, err)
			return nil, err
		}
		directChildCount := parentDirectoryInDb.DirectChildCount + uint32(assetsCount)
		sql = fmt.Sprintf(`UPDATE %s.tb_creative_directory_%s SET direct_child_count = ?
			WHERE id = ?`, "arthub_sync", gameCode)
		_, err = tx.Exec(sql, directChildCount, otherParentId)
		if err != nil {
			log.ErrorContextf(ctx, "fail to update target directory information by id, id: %d, "+
				"assetsCount: %d,  err: %v\n", otherParentId, assetsCount, err)
			tx.Rollback()
			return nil, err
		}

		log.DebugContextf(ctx, "syncDbInMoveMaterialToOtherDirectory directoryModel: %+v", directoryModel)
		//更新目标目录的递归素材数量，total_leaf_count，
		//根节点无需更新
		fullPathIdStrList := strings.Split(directoryModel.FullPathID, ",")
		if len(fullPathIdStrList) > 0 {
			fullPathIdStrListNoRoot := fullPathIdStrList[1:]
			directoryParentIds := []string{}
			for idx := range fullPathIdStrListNoRoot {
				if fullPathIdStrListNoRoot[idx] == "" {
					continue
				}
				directoryParentIds = append(directoryParentIds, fullPathIdStrListNoRoot[idx])
			}
			if otherParentId != rootID {
				directoryParentIds = append(directoryParentIds, otherParentId)
			}
			log.DebugContextf(ctx, "syncDbInMoveMaterialToOtherDirectory directoryParentIds: %v", directoryParentIds)
			if len(directoryParentIds) > 0 {
				tableName = fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gameCode)
				for _, parentId := range directoryParentIds {
					directoryInDb := model.CreativeDirectory{}
					err = tx.Model(&directoryInDb).Table(tableName).Where("id = ?", parentId).Select()
					if err != nil {
						tx.Rollback()
						log.DebugContextf(ctx, "fail to get directory information, parentId: %v, err: %v", parentId, err)
						return nil, err
					}
					_, err = tx.Model((*model.CreativeDirectory)(nil)).
						Table(tableName).
						Set("total_leaf_count = ?", directoryInDb.TotalLeafCount+uint32(assetsCount)).
						Where("id = ?", parentId).
						Update()
					if err != nil {
						log.ErrorContextf(ctx, "fail to update target directory 'total_leaf_count', id: %v, "+
							"directoryInDb: %v, err: %v", parentId, directoryInDb, err)
						tx.Rollback()
						return nil, err
					}
				}
			}
		}

		mp := map[string]uint64{} // key: 素材所在目录至根目录，val: 更新total_leaf_count， 无需更新根目录
		m := map[string]uint64{}  // key: 素材所在目录id, value: 需减少的数量，更新direct_child_count，
		for idx := range assets {
			// 更新素材目录下非递归素材数量 DirectChildCount
			count, ok := mp[assets[idx].FullPathID]
			if ok {
				mp[assets[idx].FullPathID] = count + 1
			} else {
				mp[assets[idx].FullPathID] = 1
			}
			count2, ok2 := m[assets[idx].NodeID]
			if ok2 {
				m[assets[idx].NodeID] = count2 + 1
			} else {
				m[assets[idx].NodeID] = 1
			}
		}

		log.DebugContextf(ctx, "total_leaf_count map: %v, direct_child_count: %v", mp, m)

		// 更新素材nodeId信息
		tableName = fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", gameCode)
		_, err = tx.Model((*model.CreativeOverview)(nil)).
			Table(tableName).
			Set("node_id = ?", otherParentId).
			WhereIn("asset_id IN (?)", ids).
			Update()
		if err != nil {
			log.ErrorContextf(ctx, "fail to update assets node_id, sql: %s, "+
				"otherParentId: %v, ids: %v, err: %v\n", sql, otherParentId, ids, err)
			tx.Rollback()
			return nil, err
		}

		// 更新素材所在目录的素材数量，非递归
		sql = fmt.Sprintf(`UPDATE %s.tb_creative_directory_%s SET direct_child_count = direct_child_count - ? WHERE id = ?`,
			"arthub_sync", gameCode)
		for nodeId, num := range m {
			_, err = tx.Exec(sql, num, nodeId)
			if err != nil {
				log.ErrorContextf(ctx, "fail to update directory 'direct_child_count', sql: %s, "+
					"num: %v, nodeId: %v, err: %v\n", sql, num, nodeId, err)
				tx.Rollback()
				return nil, err
			}
		}

		// 更新total_leaf_count
		// 更新素材所在目录包含素材数量，递归到根节点，但不包括根节点
		// 如果当前节点是根节点，无需更新
		var directoryIDNoRootMap = make(map[string]uint64)
		for fullPathIdKey, num := range mp {
			directoryIdStrList := strings.Split(fullPathIdKey, ",")
			if len(directoryIdStrList) == 1 {
				continue
			}
			directoryIdStrListNoRoot := directoryIdStrList[1:]
			for _, directoryID := range directoryIdStrListNoRoot {
				if directoryID == "" {
					continue
				}
				count, ok := directoryIDNoRootMap[directoryID]
				if ok {
					directoryIDNoRootMap[directoryID] = count + num
				} else {
					directoryIDNoRootMap[directoryID] = num
				}
			}
		}

		log.DebugContextf(ctx, "syncDbInMoveMaterialToOtherDirectory directoryIDNoRootMap: %v", directoryIDNoRootMap)
		if len(directoryIDNoRootMap) > 0 {
			directoryIDNoRootList := make([]string, 0)
			for directoryID := range directoryIDNoRootMap {
				directoryIDNoRootList = append(directoryIDNoRootList, directoryID)
			}
			tableName = fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gameCode)
			directoryIDNoRootModelList := make([]model.CreativeDirectory, 0)
			err = tx.Model(&directoryIDNoRootModelList).
				Table(tableName).
				WhereIn("id IN (?)", directoryIDNoRootList).
				Select()
			if err != nil {
				log.ErrorContextf(ctx, "fail to get directory list info, directoryIDNoRootList: %v, "+
					"err: %v", directoryIDNoRootList, err)
				tx.Rollback()
				return nil, err
			}
			if len(directoryIDNoRootModelList) > 0 {
				tableName = fmt.Sprintf("%s.tb_creative_directory_%s", "arthub_sync", gameCode)
				for _, directory := range directoryIDNoRootModelList {
					num, ok := directoryIDNoRootMap[directory.ID]
					if ok {
						_, err = tx.Model((*model.CreativeDirectory)(nil)).
							Table(tableName).
							Set("total_leaf_count = ? ", directory.TotalLeafCount-uint32(num)).
							Where("id = ?", directory.ID).
							Update()
						if err != nil {
							log.ErrorContextf(ctx, "fail to update directory 'total_leaf_count', "+
								"num: %v, directoryID: %v, err: %v\n", num, directory.ID, err)
							tx.Rollback()
							return nil, err
						}
					}
				}
			}

		}
	}

	if err := tx.Commit(); err != nil {
		log.ErrorContextf(ctx, "fail to commit transaction, err: %v\n", err)
		tx.Rollback()
		return nil, err
	}

	return assets, nil
}

// 格式话arthub 返回的信息，分别返回素材拖拽成功的ID、拖拽失败的ID
func moveMaterialInArthub(ctx *gin.Context, arthubCode, token string, assetIDs []string, destDir string) (*ArthubMaterialMoveResp, error) {
	var (
		ids      = []uint64{}
		parentID uint64
	)
	for idx := range assetIDs {
		ids = append(ids, cast.ToUint64(&assetIDs[idx]))
	}
	parentID = cast.ToUint64(destDir)

	obj := ArthubMaterialMoveReq{
		Ids:           ids,
		OtherParentId: parentID,
	}
	byts, err := json.Marshal(&obj)
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://service.arthub.qq.com/%s/data/openapi/v2/core/move-node", arthubCode)
	log.DebugContextf(ctx, "moveMaterialInArthub body: %v", string(byts))
	respStr, err := HTTPClientDo(ctx, "POST", url, token, byts)
	if err != nil {
		log.ErrorContextf(ctx, "moveMaterialInArthub error, url: %s, token: %s, object: %v, body: %s\n", url, token, obj, string(byts))
		return nil, err
	}
	respObj := ArthubMaterialMoveResp{}
	if err := json.Unmarshal([]byte(respStr), &respObj); err != nil {
		return nil, err
	}
	if respObj.Code != 0 {
		log.ErrorContextf(ctx, "moveMaterialInArthub error, code: %v, response string: %s\n", respObj.Code, respStr)
		return nil, fmt.Errorf("fail to move material in arthub")
	}
	return &respObj, nil
}

func formatMoveMaterialResp(assets []model.CreativeOverview) []*material_display.MaterialInfo {
	result := []*material_display.MaterialInfo{}
	if len(assets) > 0 {
		for idx := range assets {
			material := material_display.MaterialInfo{
				NodeId:       assets[idx].NodeID,
				NodeName:     assets[idx].NodeName,
				AssetId:      assets[idx].AssetID,
				AssetName:    assets[idx].AssetName,
				Status:       assets[idx].Status,
				CreateDate:   assets[idx].CreateDate,
				UplineDate:   assets[idx].UplineDate,
				OfflineDate:  assets[idx].OfflineDate,
				OnlineDays:   assets[idx].OnlineDays,
				FullPathName: assets[idx].FullPathName,
				FullPathId:   assets[idx].FullPathID,
			}
			result = append(result, &material)
		}
	}
	return result
}

func formatMoveMaterial(data *ArthubMaterialMoveResp) ([]string, []string) {
	m := make(map[uint64]string)
	// if len(data.Error) > 0 {
	// 	for idx := range data.Error {
	// 		m[data.Error[idx].Id] = data.Error[idx].Message
	// 	}
	// }
	var (
		successIds []string
		failIds    []string
	)
	if len(data.Result.Items) > 0 {
		for idx := range data.Result.Items {
			if len(m) > 0 {
				_, ok := m[data.Result.Items[idx].Id]
				if ok {
					failIds = append(failIds, strconv.FormatUint(data.Result.Items[idx].Id, 10))
				} else {
					successIds = append(successIds, strconv.FormatUint(data.Result.Items[idx].Id, 10))
				}
			} else {
				successIds = append(successIds, strconv.FormatUint(data.Result.Items[idx].Id, 10))
			}
		}
	}
	return successIds, failIds
}

// HTTPDo 发送http请求
func HTTPClientDo(ctx *gin.Context, method string, url string, token string, body []byte) (string, error) {
	client := &http.Client{}
	reader := bytes.NewReader(body)
	req, err := http.NewRequest(method, url, reader)
	if err != nil {
		log.ErrorContextf(ctx, "[error] get http req error %v", err)
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Publictoken", token)
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorContextf(ctx, "[error] send http req error, request body: %v, err: %v", string(body), err)
		return "", err
	}
	defer resp.Body.Close()
	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorContextf(ctx, "[error] get http rsp error %v", err)
		return "", err
	}
	log.DebugContextf(ctx, "[info] http rsp: %s", res)
	return string(res), nil
}

func GetArthubToken(ctx *gin.Context, gamecode string) (*model.ArthubDepot, error) {
	var depot model.ArthubDepot
	db := postgresql.GetDBWithContext(ctx)
	sql := fmt.Sprintf(`SELECT depot_id,depot_name,public_token,game_code,arthub_code FROM %s.tb_arthub_depot WHERE game_code = ?`,
		"arthub_sync")
	_, err := db.QueryOne(&depot, sql, gamecode)
	if err != nil {
		if err == pg.ErrNoRows {
			log.ErrorContextf(ctx, "fail to get depot information by gamecode %q, ErrNoRows: %v", gamecode, err)
			return &depot, nil
		}
		log.ErrorContextf(ctx, "fail to get depot information by gamecode %q, error: %v", gamecode, err)
		return &depot, err
	}
	return &depot, nil
}

func GetArthubTokenByArthubCode(ctx *gin.Context, arthubCode string) (*model.ArthubDepot, error) {
	var depot model.ArthubDepot
	db := postgresql.GetDBWithContext(ctx)
	sql := fmt.Sprintf(`SELECT depot_id,depot_name,public_token,game_code,arthub_code FROM %s.tb_arthub_depot WHERE arthub_code = ?`,
		"arthub_sync")
	_, err := db.QueryOne(&depot, sql, arthubCode)
	if err != nil {
		if err == pg.ErrNoRows {
			log.ErrorContextf(ctx, "fail to get depot information by arthubCode %q, ErrNoRows: %v", arthubCode, err)
			return &depot, nil
		}
		log.ErrorContextf(ctx, "fail to get depot information by arthubCode %q, error: %v", arthubCode, err)
		return &depot, err
	}
	return &depot, nil
}

func checkParamInMoveMaterialToOtherDirectory(req *material_display.MaterialMoveReq, gamecode string) error {
	if req == nil {
		return fmt.Errorf("request data cannot be nill")
	}
	if err := checkGameCodeKey(gamecode); err != nil {
		return err
	}
	if len(req.Ids) == 0 {
		return fmt.Errorf("material id list cannot be empty")
	}
	if req.OtherParentId == "" {
		return fmt.Errorf("parent directory id cannot be empty string")
	}

	return nil
}

func checkGameCodeKey(gamecode string) error {
	if gamecode == "" {
		return fmt.Errorf("game code cannot be empty")
	}
	return nil
}

func materialInfoToProto(directorys []*model.CreativeDirectory) []*material_display.Directory {
	dirs := []*material_display.Directory{}
	if len(directorys) == 0 {
		return dirs
	}
	for idx := range directorys {
		if directorys[idx] != nil {
			dir := singleMaterialInfoToProto(directorys[idx])
			dirs = append(dirs, dir)
		}
	}
	return dirs
}

func singleMaterialInfoToProto(directory *model.CreativeDirectory) *material_display.Directory {
	if directory == nil {
		return nil
	}
	dir := material_display.Directory{
		Id:                   directory.ID,
		Name:                 directory.Name,
		ParentId:             directory.ParentID,
		ParentName:           directory.ParentName,
		CreateDate:           directory.CreateDate,
		UpdateDate:           directory.UpdateDate,
		DirectChildCount:     directory.DirectChildCount,
		TotalLeafCount:       directory.TotalLeafCount,
		DirectDirectoryCount: directory.DirectDirectoryCount,
		FullPathName:         directory.FullPathName,
		FullPathId:           directory.FullPathID,
		SyncTime:             directory.SyncTime,
	}
	return &dir
}
