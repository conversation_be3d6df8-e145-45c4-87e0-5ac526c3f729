// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: snapchat_advertise/snapchat_advertise.proto

package snapchat_advertise

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 自测接口, POST, /api/v1/snapchat_advertise/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_snapchat_advertise_snapchat_advertise_proto_rawDescGZIP(), []int{0}
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_snapchat_advertise_snapchat_advertise_proto_rawDescGZIP(), []int{1}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 基础结构 渠道账号信息
type MediaAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`       // 渠道账号id
	AccountName string `protobuf:"bytes,2,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"` // 渠道账号名称
}

func (x *MediaAccount) Reset() {
	*x = MediaAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaAccount) ProtoMessage() {}

func (x *MediaAccount) ProtoReflect() protoreflect.Message {
	mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaAccount.ProtoReflect.Descriptor instead.
func (*MediaAccount) Descriptor() ([]byte, []int) {
	return file_snapchat_advertise_snapchat_advertise_proto_rawDescGZIP(), []int{2}
}

func (x *MediaAccount) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MediaAccount) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

// 查询渠道账号列表, POST, /api/v1/snapchat_advertise/get_media_accounts
type GetMediaAccountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 必填 游戏标识game_code
}

func (x *GetMediaAccountsReq) Reset() {
	*x = GetMediaAccountsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsReq) ProtoMessage() {}

func (x *GetMediaAccountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsReq.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsReq) Descriptor() ([]byte, []int) {
	return file_snapchat_advertise_snapchat_advertise_proto_rawDescGZIP(), []int{3}
}

func (x *GetMediaAccountsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

type GetMediaAccountsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                     // 返回结果
	GameCode string          `protobuf:"bytes,2,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 游戏标识game_code
	Accounts []*MediaAccount `protobuf:"bytes,3,rep,name=accounts,proto3" json:"accounts,omitempty"`                 // 账号信息列表
}

func (x *GetMediaAccountsRsp) Reset() {
	*x = GetMediaAccountsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsRsp) ProtoMessage() {}

func (x *GetMediaAccountsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_snapchat_advertise_snapchat_advertise_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsRsp.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsRsp) Descriptor() ([]byte, []int) {
	return file_snapchat_advertise_snapchat_advertise_proto_rawDescGZIP(), []int{4}
}

func (x *GetMediaAccountsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetMediaAccountsRsp) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetMediaAccountsRsp) GetAccounts() []*MediaAccount {
	if x != nil {
		return x.Accounts
	}
	return nil
}

var File_snapchat_advertise_snapchat_advertise_proto protoreflect.FileDescriptor

var file_snapchat_advertise_snapchat_advertise_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x73, 0x6e, 0x61, 0x70, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2f, 0x73, 0x6e, 0x61, 0x70, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x73,
	0x6e, 0x61, 0x70, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x0a, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71, 0x22, 0x2f, 0x0a, 0x08, 0x53,
	0x61, 0x79, 0x48, 0x69, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x50, 0x0a, 0x0c,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x32,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x08,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x73, 0x6e, 0x61, 0x70, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x42, 0x41, 0x5a, 0x3f, 0x65, 0x2e,
	0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x73, 0x6e, 0x61, 0x70, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_snapchat_advertise_snapchat_advertise_proto_rawDescOnce sync.Once
	file_snapchat_advertise_snapchat_advertise_proto_rawDescData = file_snapchat_advertise_snapchat_advertise_proto_rawDesc
)

func file_snapchat_advertise_snapchat_advertise_proto_rawDescGZIP() []byte {
	file_snapchat_advertise_snapchat_advertise_proto_rawDescOnce.Do(func() {
		file_snapchat_advertise_snapchat_advertise_proto_rawDescData = protoimpl.X.CompressGZIP(file_snapchat_advertise_snapchat_advertise_proto_rawDescData)
	})
	return file_snapchat_advertise_snapchat_advertise_proto_rawDescData
}

var file_snapchat_advertise_snapchat_advertise_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_snapchat_advertise_snapchat_advertise_proto_goTypes = []interface{}{
	(*SayHiReq)(nil),            // 0: snapchat_advertise.SayHiReq
	(*SayHiRsp)(nil),            // 1: snapchat_advertise.SayHiRsp
	(*MediaAccount)(nil),        // 2: snapchat_advertise.MediaAccount
	(*GetMediaAccountsReq)(nil), // 3: snapchat_advertise.GetMediaAccountsReq
	(*GetMediaAccountsRsp)(nil), // 4: snapchat_advertise.GetMediaAccountsRsp
	(*aix.Result)(nil),          // 5: aix.Result
}
var file_snapchat_advertise_snapchat_advertise_proto_depIdxs = []int32{
	5, // 0: snapchat_advertise.SayHiRsp.result:type_name -> aix.Result
	5, // 1: snapchat_advertise.GetMediaAccountsRsp.result:type_name -> aix.Result
	2, // 2: snapchat_advertise.GetMediaAccountsRsp.accounts:type_name -> snapchat_advertise.MediaAccount
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_snapchat_advertise_snapchat_advertise_proto_init() }
func file_snapchat_advertise_snapchat_advertise_proto_init() {
	if File_snapchat_advertise_snapchat_advertise_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_snapchat_advertise_snapchat_advertise_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_snapchat_advertise_snapchat_advertise_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_snapchat_advertise_snapchat_advertise_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_snapchat_advertise_snapchat_advertise_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_snapchat_advertise_snapchat_advertise_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_snapchat_advertise_snapchat_advertise_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_snapchat_advertise_snapchat_advertise_proto_goTypes,
		DependencyIndexes: file_snapchat_advertise_snapchat_advertise_proto_depIdxs,
		MessageInfos:      file_snapchat_advertise_snapchat_advertise_proto_msgTypes,
	}.Build()
	File_snapchat_advertise_snapchat_advertise_proto = out.File
	file_snapchat_advertise_snapchat_advertise_proto_rawDesc = nil
	file_snapchat_advertise_snapchat_advertise_proto_goTypes = nil
	file_snapchat_advertise_snapchat_advertise_proto_depIdxs = nil
}
