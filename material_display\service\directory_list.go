package service

import (
	"fmt"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/gin-gonic/gin"
)

// checkDirectoryList ...
func checkDirectoryList(req *pb.DirectoryListReq, gamecode string) error {
	if req.GetLimit() <= 0 {
		req.Limit = 10
	}

	if err := checkGameCodeKey(gamecode); err != nil {
		return err
	}

	return nil
}

// DirectoryList ...
func DirectoryList(ctx *gin.Context, req *pb.DirectoryListReq) (*pb.DirectoryListRsp, error) {
	gameCode := ctx.Request.Header.Get(GAME_CODE_KEY)
	if err := checkDirectoryList(req, gameCode); err != nil {
		return nil, errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_PARAM), "error params")
	}

	pgQuery := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeDirectory{})
	tableName := pgmodel.GetCreativeDirectoryTableName(gameCode)
	pgQuery.Table(tableName)
	if req.GetGetChild() == 1 { // 递归获取子目录
		pathPrefix, err := getPathPrefix(ctx, gameCode, req.GetParentId())
		if err != nil {
			return nil, fmt.Errorf("getPathPrefix failed: %s", err)
		}
		pgQuery.Where("full_path_id like ?", fmt.Sprintf("%s%%", pathPrefix))
	} else {
		pgQuery.Where("parent_id=?", req.GetParentId())
	}
	pgQuery.Order("name")
	pgQuery.Offset(int(req.GetOffset()))
	pgQuery.Limit(int(req.GetLimit()))

	var records []*pgmodel.CreativeDirectory
	count, err := pgQuery.SelectAndCount(&records)
	if err != nil {
		return nil, fmt.Errorf("select creative directory failed: %s", err)
	}

	records = correctDirectoryFullPath(ctx, gameCode, nil, records)

	var resp pb.DirectoryListRsp
	resp.Dirs = materialInfoToProto(records)
	resp.Total = uint64(count)

	return &resp, nil
}
