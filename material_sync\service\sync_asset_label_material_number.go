package service

import (
	"regexp"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
)

// SyncAssetLabelMaterialNumberForGameCode 同步某个game code下的素材标签, 注意是素材标签而不是广告素材标签
func SyncAssetLabelMaterialNumberForGameCode(game_code string, overviews []pgmodel.CreativeOverview) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "SyncAssetLabelMaterialNumberForGameCode start, game code: %s", game_code)

	start_time := time.Now()
	defer func() {
		cost := time.Since(start_time)
		log.InfoContextf(ctx, "SyncAssetLabelMaterialNumberForGameCode end, game code: %s, cost: %v", game_code, cost)
	}()

	var asset_labels []pgmodel.AssetLabel
	author := "material_sync.sync_asset_label_material_number"
	for _, overview := range overviews {
		material_number := getMaterialNumber(game_code, overview.AssetName)
		if len(material_number) == 0 {
			continue
		}

		var asset_label pgmodel.AssetLabel
		asset_label.AssetId = overview.AssetID
		asset_label.LabelName = "素材命名"
		asset_label.FirstLabel = "素材编号"
		asset_label.SecondLabel = material_number
		asset_label.CreateBy = author
		asset_label.UpdateBy = author

		asset_labels = append(asset_labels, asset_label)
	}

	log.InfoContextf(ctx, "get asset labels number: %d, game code: %s", len(asset_labels), game_code)
	if len(asset_labels) == 0 {
		return
	}

	err := upsertAssetLabels(ctx, game_code, asset_labels)
	if err != nil {
		log.ErrorContextf(ctx, "upsertAssetLabels failed: %s", err)
	}
}

// getMaterialNumber 获取素材编号
func getMaterialNumber(game_code, name string) string {
	switch game_code {
	case "pubgm":
		return getMaterialNumberForPubgm(name)
	case "hok_prod":
		return getMaterialNumberForHokProd(name)
	}

	return ""
}

// getMaterialNumberForPubgm 获取pubgm素材编号
func getMaterialNumberForPubgm(name string) string {
	if len(name) < 7 {
		return ""
	}

	prefix := name[:7]
	reg_exp := regexp.MustCompile(`[A-Z]{2}[0-9]{5}`)
	if !reg_exp.Match([]byte(prefix)) {
		return ""
	}

	return prefix
}

// getMaterialNumberForHokProd 获取hok_prod素材编号
func getMaterialNumberForHokProd(name string) string {
	if len(name) < 5 {
		return ""
	}

	reg_exp := regexp.MustCompile(`(^[A-Za-z-]{2,3}[0-9]{1,5})([^0-9].*)`)
	sub_matchs := reg_exp.FindSubmatch([]byte(name))

	if len(sub_matchs) < 3 {
		return ""
	}

	return string(sub_matchs[1])
}
