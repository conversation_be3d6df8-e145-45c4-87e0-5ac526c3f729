// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: applovin_advertise/applovin_advertise.proto

package applovin_advertise

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// POST /api/v1/applovin_advertise/say_hi
type SayHiReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SayHiReq) Reset() {
	*x = SayHiReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiReq) ProtoMessage() {}

func (x *SayHiReq) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiReq.ProtoReflect.Descriptor instead.
func (*SayHiReq) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{0}
}

func (x *SayHiReq) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SayHiRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Msg    string      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *SayHiRsp) Reset() {
	*x = SayHiRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SayHiRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SayHiRsp) ProtoMessage() {}

func (x *SayHiRsp) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SayHiRsp.ProtoReflect.Descriptor instead.
func (*SayHiRsp) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{1}
}

func (x *SayHiRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SayHiRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type Campaign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CampaignId   string `protobuf:"bytes,1,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	CampaignName string `protobuf:"bytes,2,opt,name=campaign_name,json=campaignName,proto3" json:"campaign_name,omitempty"`
	Status       bool   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"` // campaign状态：true开启， false关闭
}

func (x *Campaign) Reset() {
	*x = Campaign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Campaign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Campaign) ProtoMessage() {}

func (x *Campaign) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Campaign.ProtoReflect.Descriptor instead.
func (*Campaign) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{2}
}

func (x *Campaign) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *Campaign) GetCampaignName() string {
	if x != nil {
		return x.CampaignName
	}
	return ""
}

func (x *Campaign) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type CreativeSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeSetId   string   `protobuf:"bytes,1,opt,name=creative_set_id,json=creativeSetId,proto3" json:"creative_set_id,omitempty"`
	CampaignId      string   `protobuf:"bytes,2,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`                  // 创建必填
	CreativeSetName string   `protobuf:"bytes,3,opt,name=creative_set_name,json=creativeSetName,proto3" json:"creative_set_name,omitempty"` // 创建必填
	Languages       []string `protobuf:"bytes,4,rep,name=languages,proto3" json:"languages,omitempty"`                                      // 创建必填
	Countries       []string `protobuf:"bytes,5,rep,name=countries,proto3" json:"countries,omitempty"`                                      // 创建选填
	ProductPage     string   `protobuf:"bytes,6,opt,name=product_page,json=productPage,proto3" json:"product_page,omitempty"`
}

func (x *CreativeSet) Reset() {
	*x = CreativeSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreativeSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreativeSet) ProtoMessage() {}

func (x *CreativeSet) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreativeSet.ProtoReflect.Descriptor instead.
func (*CreativeSet) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{3}
}

func (x *CreativeSet) GetCreativeSetId() string {
	if x != nil {
		return x.CreativeSetId
	}
	return ""
}

func (x *CreativeSet) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *CreativeSet) GetCreativeSetName() string {
	if x != nil {
		return x.CreativeSetName
	}
	return ""
}

func (x *CreativeSet) GetLanguages() []string {
	if x != nil {
		return x.Languages
	}
	return nil
}

func (x *CreativeSet) GetCountries() []string {
	if x != nil {
		return x.Countries
	}
	return nil
}

func (x *CreativeSet) GetProductPage() string {
	if x != nil {
		return x.ProductPage
	}
	return ""
}

type Ad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdId            int64  `protobuf:"varint,1,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	Name            string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CreativeSetId   string `protobuf:"bytes,3,opt,name=creative_set_id,json=creativeSetId,proto3" json:"creative_set_id,omitempty"`
	CreativeSetName string `protobuf:"bytes,4,opt,name=creative_set_name,json=creativeSetName,proto3" json:"creative_set_name,omitempty"`
	Size            string `protobuf:"bytes,5,opt,name=size,proto3" json:"size,omitempty"`
	Template        string `protobuf:"bytes,6,opt,name=template,proto3" json:"template,omitempty"`
	Status          bool   `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"` // Whether the ad is active (true) or not (false)
}

func (x *Ad) Reset() {
	*x = Ad{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ad) ProtoMessage() {}

func (x *Ad) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ad.ProtoReflect.Descriptor instead.
func (*Ad) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{4}
}

func (x *Ad) GetAdId() int64 {
	if x != nil {
		return x.AdId
	}
	return 0
}

func (x *Ad) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Ad) GetCreativeSetId() string {
	if x != nil {
		return x.CreativeSetId
	}
	return ""
}

func (x *Ad) GetCreativeSetName() string {
	if x != nil {
		return x.CreativeSetName
	}
	return ""
}

func (x *Ad) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *Ad) GetTemplate() string {
	if x != nil {
		return x.Template
	}
	return ""
}

func (x *Ad) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// 基础结构 渠道账号信息
type MediaAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId           string    `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                                 // 渠道账号id
	AccountName         string    `protobuf:"bytes,2,opt,name=account_name,json=accountName,proto3" json:"account_name,omitempty"`                           // 渠道账号名称
	UploadAssetCampaign *Campaign `protobuf:"bytes,3,opt,name=upload_asset_campaign,json=uploadAssetCampaign,proto3" json:"upload_asset_campaign,omitempty"` // 不为null表示配置的素材上传的campaign
}

func (x *MediaAccount) Reset() {
	*x = MediaAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaAccount) ProtoMessage() {}

func (x *MediaAccount) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaAccount.ProtoReflect.Descriptor instead.
func (*MediaAccount) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{5}
}

func (x *MediaAccount) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MediaAccount) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *MediaAccount) GetUploadAssetCampaign() *Campaign {
	if x != nil {
		return x.UploadAssetCampaign
	}
	return nil
}

// POST /api/v1/applovin_advertise/get_media_accounts
type GetMediaAccountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 必填 游戏标识game_code
}

func (x *GetMediaAccountsReq) Reset() {
	*x = GetMediaAccountsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsReq) ProtoMessage() {}

func (x *GetMediaAccountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsReq.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsReq) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{6}
}

func (x *GetMediaAccountsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

type GetMediaAccountsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Accounts []*MediaAccount `protobuf:"bytes,3,rep,name=accounts,proto3" json:"accounts,omitempty"` // 账号信息列表
}

func (x *GetMediaAccountsRsp) Reset() {
	*x = GetMediaAccountsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediaAccountsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediaAccountsRsp) ProtoMessage() {}

func (x *GetMediaAccountsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediaAccountsRsp.ProtoReflect.Descriptor instead.
func (*GetMediaAccountsRsp) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{7}
}

func (x *GetMediaAccountsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetMediaAccountsRsp) GetAccounts() []*MediaAccount {
	if x != nil {
		return x.Accounts
	}
	return nil
}

// POST /api/v1/applovin_advertise/get_campaigns
type GetCampaignsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识game_code
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 渠道账号id
}

func (x *GetCampaignsReq) Reset() {
	*x = GetCampaignsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignsReq) ProtoMessage() {}

func (x *GetCampaignsReq) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignsReq.ProtoReflect.Descriptor instead.
func (*GetCampaignsReq) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{8}
}

func (x *GetCampaignsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetCampaignsReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type GetCampaignsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Campaigns []*Campaign `protobuf:"bytes,2,rep,name=campaigns,proto3" json:"campaigns,omitempty"` // 广告campaign信息列表
}

func (x *GetCampaignsRsp) Reset() {
	*x = GetCampaignsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCampaignsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCampaignsRsp) ProtoMessage() {}

func (x *GetCampaignsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCampaignsRsp.ProtoReflect.Descriptor instead.
func (*GetCampaignsRsp) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{9}
}

func (x *GetCampaignsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetCampaignsRsp) GetCampaigns() []*Campaign {
	if x != nil {
		return x.Campaigns
	}
	return nil
}

// POST /api/v1/applovin_advertise/get_creative_set_by_campaign
type GetCreativeSetByCampaignReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode   string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`       // 必填 游戏标识game_code
	AccountId  string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`    // 必填 渠道账号id
	CampaignId string `protobuf:"bytes,3,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"` // 必填 广告campaign id
}

func (x *GetCreativeSetByCampaignReq) Reset() {
	*x = GetCreativeSetByCampaignReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreativeSetByCampaignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreativeSetByCampaignReq) ProtoMessage() {}

func (x *GetCreativeSetByCampaignReq) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreativeSetByCampaignReq.ProtoReflect.Descriptor instead.
func (*GetCreativeSetByCampaignReq) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{10}
}

func (x *GetCreativeSetByCampaignReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetCreativeSetByCampaignReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetCreativeSetByCampaignReq) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

type GetCreativeSetByCampaignRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result       *aix.Result    `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	CreativeSets []*CreativeSet `protobuf:"bytes,2,rep,name=creative_sets,json=creativeSets,proto3" json:"creative_sets,omitempty"`
}

func (x *GetCreativeSetByCampaignRsp) Reset() {
	*x = GetCreativeSetByCampaignRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreativeSetByCampaignRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreativeSetByCampaignRsp) ProtoMessage() {}

func (x *GetCreativeSetByCampaignRsp) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreativeSetByCampaignRsp.ProtoReflect.Descriptor instead.
func (*GetCreativeSetByCampaignRsp) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{11}
}

func (x *GetCreativeSetByCampaignRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetCreativeSetByCampaignRsp) GetCreativeSets() []*CreativeSet {
	if x != nil {
		return x.CreativeSets
	}
	return nil
}

// POST /api/v1/applovin_advertise/create_creative_set
type CreateCreativeSetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode    string       `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识game_code
	AccountId   string       `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 渠道账号id
	CreativeSet *CreativeSet `protobuf:"bytes,3,opt,name=creative_set,json=creativeSet,proto3" json:"creative_set,omitempty"`
}

func (x *CreateCreativeSetReq) Reset() {
	*x = CreateCreativeSetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCreativeSetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCreativeSetReq) ProtoMessage() {}

func (x *CreateCreativeSetReq) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCreativeSetReq.ProtoReflect.Descriptor instead.
func (*CreateCreativeSetReq) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{12}
}

func (x *CreateCreativeSetReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CreateCreativeSetReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CreateCreativeSetReq) GetCreativeSet() *CreativeSet {
	if x != nil {
		return x.CreativeSet
	}
	return nil
}

type CreateCreativeSetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result      *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	CreativeSet *CreativeSet `protobuf:"bytes,2,opt,name=creative_set,json=creativeSet,proto3" json:"creative_set,omitempty"`
}

func (x *CreateCreativeSetRsp) Reset() {
	*x = CreateCreativeSetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCreativeSetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCreativeSetRsp) ProtoMessage() {}

func (x *CreateCreativeSetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCreativeSetRsp.ProtoReflect.Descriptor instead.
func (*CreateCreativeSetRsp) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{13}
}

func (x *CreateCreativeSetRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CreateCreativeSetRsp) GetCreativeSet() *CreativeSet {
	if x != nil {
		return x.CreativeSet
	}
	return nil
}

// POST /api/v1/applovin_advertise/get_ads_by_creative_set
type GetAdsByCreativeSetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode      string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // 必填 游戏标识game_code
	AccountId     string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"` // 必填 渠道账号id
	CreativeSetId string `protobuf:"bytes,3,opt,name=creative_set_id,json=creativeSetId,proto3" json:"creative_set_id,omitempty"`
}

func (x *GetAdsByCreativeSetReq) Reset() {
	*x = GetAdsByCreativeSetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdsByCreativeSetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdsByCreativeSetReq) ProtoMessage() {}

func (x *GetAdsByCreativeSetReq) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdsByCreativeSetReq.ProtoReflect.Descriptor instead.
func (*GetAdsByCreativeSetReq) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{14}
}

func (x *GetAdsByCreativeSetReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetAdsByCreativeSetReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetAdsByCreativeSetReq) GetCreativeSetId() string {
	if x != nil {
		return x.CreativeSetId
	}
	return ""
}

type GetAdsByCreativeSetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Ads    []*Ad       `protobuf:"bytes,2,rep,name=ads,proto3" json:"ads,omitempty"`
}

func (x *GetAdsByCreativeSetRsp) Reset() {
	*x = GetAdsByCreativeSetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAdsByCreativeSetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAdsByCreativeSetRsp) ProtoMessage() {}

func (x *GetAdsByCreativeSetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAdsByCreativeSetRsp.ProtoReflect.Descriptor instead.
func (*GetAdsByCreativeSetRsp) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{15}
}

func (x *GetAdsByCreativeSetRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAdsByCreativeSetRsp) GetAds() []*Ad {
	if x != nil {
		return x.Ads
	}
	return nil
}

// POST /api/v1/applovin_advertise/change_ad_status
type ChangeAdStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode      string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                  // 必填 游戏标识game_code
	AccountId     string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`               // 必填 渠道账号id
	AdId          int64  `protobuf:"varint,3,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`                             // 必填 广告id
	CreativeSetId string `protobuf:"bytes,4,opt,name=creative_set_id,json=creativeSetId,proto3" json:"creative_set_id,omitempty"` // 必填 creative_set_id
	Status        bool   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`                                     // 必填 广告状态 active (true) or not (false)
}

func (x *ChangeAdStatusReq) Reset() {
	*x = ChangeAdStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeAdStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeAdStatusReq) ProtoMessage() {}

func (x *ChangeAdStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeAdStatusReq.ProtoReflect.Descriptor instead.
func (*ChangeAdStatusReq) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{16}
}

func (x *ChangeAdStatusReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *ChangeAdStatusReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ChangeAdStatusReq) GetAdId() int64 {
	if x != nil {
		return x.AdId
	}
	return 0
}

func (x *ChangeAdStatusReq) GetCreativeSetId() string {
	if x != nil {
		return x.CreativeSetId
	}
	return ""
}

func (x *ChangeAdStatusReq) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type ChangeAdStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *ChangeAdStatusRsp) Reset() {
	*x = ChangeAdStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeAdStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeAdStatusRsp) ProtoMessage() {}

func (x *ChangeAdStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_applovin_advertise_applovin_advertise_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeAdStatusRsp.ProtoReflect.Descriptor instead.
func (*ChangeAdStatusRsp) Descriptor() ([]byte, []int) {
	return file_applovin_advertise_applovin_advertise_proto_rawDescGZIP(), []int{17}
}

func (x *ChangeAdStatusRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_applovin_advertise_applovin_advertise_proto protoreflect.FileDescriptor

var file_applovin_advertise_applovin_advertise_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x61,
	0x70, 0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x1a, 0x1c, 0x61, 0x69, 0x78, 0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x1c, 0x0a, 0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x41, 0x0a,
	0x08, 0x53, 0x61, 0x79, 0x48, 0x69, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x22, 0x68, 0x0a, 0x08, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xe1, 0x01, 0x0a, 0x0b, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x50, 0x61, 0x67, 0x65, 0x22, 0xc9,
	0x01, 0x0a, 0x02, 0x41, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x0c, 0x4d,
	0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x50, 0x0a,
	0x15, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x13, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x22,
	0x32, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x22, 0x78, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x3c, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x4d, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x72, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x76,
	0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x61, 0x6d,
	0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x09, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73,
	0x22, 0x7a, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53,
	0x65, 0x74, 0x42, 0x79, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x22, 0x88, 0x01, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x42,
	0x79, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x44, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x6f,
	0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0c,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x53, 0x65, 0x74, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74,
	0x22, 0x7f, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x53, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76,
	0x65, 0x53, 0x65, 0x74, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65,
	0x74, 0x22, 0x7c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x42, 0x79, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x49, 0x64, 0x22,
	0x67, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x64, 0x73, 0x42, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x28,
	0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70,
	0x70, 0x6c, 0x6f, 0x76, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x2e, 0x41, 0x64, 0x52, 0x03, 0x61, 0x64, 0x73, 0x22, 0xa4, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x41, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x61, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x61, 0x64, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x76, 0x65, 0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x38, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x41, 0x5a, 0x3f, 0x65, 0x2e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x6f, 0x76,
	0x69, 0x6e, 0x5f, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_applovin_advertise_applovin_advertise_proto_rawDescOnce sync.Once
	file_applovin_advertise_applovin_advertise_proto_rawDescData = file_applovin_advertise_applovin_advertise_proto_rawDesc
)

func file_applovin_advertise_applovin_advertise_proto_rawDescGZIP() []byte {
	file_applovin_advertise_applovin_advertise_proto_rawDescOnce.Do(func() {
		file_applovin_advertise_applovin_advertise_proto_rawDescData = protoimpl.X.CompressGZIP(file_applovin_advertise_applovin_advertise_proto_rawDescData)
	})
	return file_applovin_advertise_applovin_advertise_proto_rawDescData
}

var file_applovin_advertise_applovin_advertise_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_applovin_advertise_applovin_advertise_proto_goTypes = []interface{}{
	(*SayHiReq)(nil),                    // 0: applovin_advertise.SayHiReq
	(*SayHiRsp)(nil),                    // 1: applovin_advertise.SayHiRsp
	(*Campaign)(nil),                    // 2: applovin_advertise.Campaign
	(*CreativeSet)(nil),                 // 3: applovin_advertise.CreativeSet
	(*Ad)(nil),                          // 4: applovin_advertise.Ad
	(*MediaAccount)(nil),                // 5: applovin_advertise.MediaAccount
	(*GetMediaAccountsReq)(nil),         // 6: applovin_advertise.GetMediaAccountsReq
	(*GetMediaAccountsRsp)(nil),         // 7: applovin_advertise.GetMediaAccountsRsp
	(*GetCampaignsReq)(nil),             // 8: applovin_advertise.GetCampaignsReq
	(*GetCampaignsRsp)(nil),             // 9: applovin_advertise.GetCampaignsRsp
	(*GetCreativeSetByCampaignReq)(nil), // 10: applovin_advertise.GetCreativeSetByCampaignReq
	(*GetCreativeSetByCampaignRsp)(nil), // 11: applovin_advertise.GetCreativeSetByCampaignRsp
	(*CreateCreativeSetReq)(nil),        // 12: applovin_advertise.CreateCreativeSetReq
	(*CreateCreativeSetRsp)(nil),        // 13: applovin_advertise.CreateCreativeSetRsp
	(*GetAdsByCreativeSetReq)(nil),      // 14: applovin_advertise.GetAdsByCreativeSetReq
	(*GetAdsByCreativeSetRsp)(nil),      // 15: applovin_advertise.GetAdsByCreativeSetRsp
	(*ChangeAdStatusReq)(nil),           // 16: applovin_advertise.ChangeAdStatusReq
	(*ChangeAdStatusRsp)(nil),           // 17: applovin_advertise.ChangeAdStatusRsp
	(*aix.Result)(nil),                  // 18: aix.Result
}
var file_applovin_advertise_applovin_advertise_proto_depIdxs = []int32{
	18, // 0: applovin_advertise.SayHiRsp.result:type_name -> aix.Result
	2,  // 1: applovin_advertise.MediaAccount.upload_asset_campaign:type_name -> applovin_advertise.Campaign
	18, // 2: applovin_advertise.GetMediaAccountsRsp.result:type_name -> aix.Result
	5,  // 3: applovin_advertise.GetMediaAccountsRsp.accounts:type_name -> applovin_advertise.MediaAccount
	18, // 4: applovin_advertise.GetCampaignsRsp.result:type_name -> aix.Result
	2,  // 5: applovin_advertise.GetCampaignsRsp.campaigns:type_name -> applovin_advertise.Campaign
	18, // 6: applovin_advertise.GetCreativeSetByCampaignRsp.result:type_name -> aix.Result
	3,  // 7: applovin_advertise.GetCreativeSetByCampaignRsp.creative_sets:type_name -> applovin_advertise.CreativeSet
	3,  // 8: applovin_advertise.CreateCreativeSetReq.creative_set:type_name -> applovin_advertise.CreativeSet
	18, // 9: applovin_advertise.CreateCreativeSetRsp.result:type_name -> aix.Result
	3,  // 10: applovin_advertise.CreateCreativeSetRsp.creative_set:type_name -> applovin_advertise.CreativeSet
	18, // 11: applovin_advertise.GetAdsByCreativeSetRsp.result:type_name -> aix.Result
	4,  // 12: applovin_advertise.GetAdsByCreativeSetRsp.ads:type_name -> applovin_advertise.Ad
	18, // 13: applovin_advertise.ChangeAdStatusRsp.result:type_name -> aix.Result
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_applovin_advertise_applovin_advertise_proto_init() }
func file_applovin_advertise_applovin_advertise_proto_init() {
	if File_applovin_advertise_applovin_advertise_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_applovin_advertise_applovin_advertise_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SayHiRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Campaign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreativeSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ad); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediaAccountsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCampaignsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreativeSetByCampaignReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreativeSetByCampaignRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCreativeSetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCreativeSetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAdsByCreativeSetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAdsByCreativeSetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeAdStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_applovin_advertise_applovin_advertise_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeAdStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_applovin_advertise_applovin_advertise_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_applovin_advertise_applovin_advertise_proto_goTypes,
		DependencyIndexes: file_applovin_advertise_applovin_advertise_proto_depIdxs,
		MessageInfos:      file_applovin_advertise_applovin_advertise_proto_msgTypes,
	}.Build()
	File_applovin_advertise_applovin_advertise_proto = out.File
	file_applovin_advertise_applovin_advertise_proto_rawDesc = nil
	file_applovin_advertise_applovin_advertise_proto_goTypes = nil
	file_applovin_advertise_applovin_advertise_proto_depIdxs = nil
}
