package data

import (
	"context"
	"fmt"
	"strings"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	pg "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/sdk/arthub"

	"github.com/gin-gonic/gin"
	"github.com/go-pg/pg/v10/orm"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
)

const (
	default_limit = 10
	max_limit     = 100
)

// GetMaterialDetail 获取材料详情
func GetMaterialDetail(ctx context.Context, gameCode, assetID string) (*model.CreativeAssetDetails, error) {
	db := postgresql.GetDBWithContext(ctx)
	detail := &model.CreativeAssetDetails{}
	sql := fmt.Sprintf("SELECT * FROM %s.tb_creative_details_%s WHERE asset_id=?", "arthub_sync", gameCode)
	_, err := db.QueryOne(detail, sql, assetID)
	if err != nil {
		return nil, err
	}
	return detail, err
}

// BtGetMaterialDetail 批量获取材料详情
func BtGetMaterialDetail(ctx *gin.Context, gameCode string, assetIDList []string) (map[string]*model.CreativeAssetDetails, error) {
	db := pg.GetDBWithContext(ctx)
	detailList := []model.CreativeAssetDetails{}
	resMap := make(map[string]*model.CreativeAssetDetails)
	if len(assetIDList) <= 0 {
		return resMap, nil
	}

	//sql := fmt.Sprintf("SELECT * FROM %s.tb_creative_details_%s WHERE asset_id IN (%s)", "arthub_sync", gameCode, strings.Join(assetIDList, ","))
	//_, err := db.Query(&detailList, sql)
	err := db.Model(&detailList).
		Table(fmt.Sprintf("%s.tb_creative_details_%s", "arthub_sync", gameCode)).
		WhereIn("asset_id IN (?)", assetIDList).
		Select()
	if err != nil {
		log.ErrorContextf(ctx, "[postgresql] get material detail error, err: %v", err)
		return nil, err
	}

	for i, detail := range detailList {
		resMap[cast.ToString(detail.AssetID)] = &detailList[i]
	}
	return resMap, err
}

// SetUploadState 设置上传至媒体标志
func SetUploadState(ctx *gin.Context, gameCode string, assetIDList []string) error {
	db := pg.GetDBWithContext(ctx)
	toUpdate := []string{}
	for _, assetID := range assetIDList {
		// 防止sql注入
		toUpdate = append(toUpdate, cast.ToString(cast.ToUint64(assetID)))
	}
	sql := fmt.Sprintf("UPDATE %s.tb_creative_overview_%s SET upload_state=1 WHERE asset_id IN (%s)", "arthub_sync", gameCode,
		strings.Join(funk.UniqString(toUpdate), ","))
	_, err := db.Exec(sql)
	if err != nil {
		log.ErrorContextf(ctx, "sql: %v", sql)
		return err
	}
	return nil
}

// 查询数量
type QueryCount struct {
	Count uint32 `json:"count,omitempty"` // 统计数量
}

// GetMaterialList 获取素材列表
func GetMaterialList(ctx *gin.Context, offset, count uint32, is_filter_online_status bool, online_status uint32, path_profix, gameCode string) (uint32, []*model.CreativeOverview, error) {
	var total QueryCount
	var overviews []*model.CreativeOverview
	var err error
	whereList := []string{"asset_status=?"}
	paramList := []interface{}{arthub.ARTHUB_ASSET_STATUS_NORMAL}
	if is_filter_online_status {
		if online_status == 0 {
			whereList = append(whereList, "(online_status is null or online_status=0)")
		} else {
			whereList = append(whereList, "online_status=?")
			paramList = append(paramList, online_status)
		}
	}
	if path_profix != "" {
		// whereList = append(whereList, "node_id=?")
		// paramList = append(paramList, path_profix)
		whereList = append(whereList, fmt.Sprintf("full_path_id like '%s%%'", path_profix))
	}
	whereSql := ""
	if len(whereList) > 0 {
		whereSql = " where " + strings.Join(whereList, " and ")
	}
	_, err = postgresql.GetDBWithContext(ctx).Query(&overviews, fmt.Sprintf("select * from %s.tb_creative_overview_%s %s order by updated_date desc offset %d limit %d", "arthub_sync", gameCode, whereSql, offset, count), paramList...)
	if err != nil {
		return 0, nil, err
	}
	_, err = postgresql.GetDBWithContext(ctx).Query(&total, fmt.Sprintf("select count(*) from %s.tb_creative_overview_%s %s ", "arthub_sync", gameCode, whereSql), paramList...)
	if err != nil {
		return 0, nil, err
	}

	if total.Count == 0 {
		total.Count = uint32(len(overviews))
	}
	return total.Count, overviews, err
}

// GetMediaMaterialByDirectoryId 根据目录获取媒体列表
func GetMediaMaterialByDirectoryId(ctx *gin.Context, offset, count, isFilterStatus, status, online_status uint32, dirID, gameCode string, assetStatus uint8, filter_online_status bool) (uint32, []*model.CreativeOverview, error) {
	var total QueryCount
	var overviews []*model.CreativeOverview
	var err error
	whereList := []string{}
	paramList := []interface{}{}
	ids, err := ListAssetIdInMediaMaterial(gameCode, dirID, 0, 0, true)
	if err != nil {
		log.ErrorContextf(ctx, "error data.ListAssetIdInMediaMaterial, gamecode: %v, "+
			"directoryID: %v, err: %v", gameCode, dirID, err)
		return 0, nil, err
	}
	if len(ids) == 0 {
		log.InfoContextf(ctx, "data.ListAssetIdInMediaMaterial ids is 0, gamecode: %v, "+
			"directoryID: %v", gameCode, dirID)
		return 0, overviews, nil
	}
	var idsStr string
	for _, id := range ids {
		idsStr += fmt.Sprintf("'%s',", id)
	}
	idsStr = idsStr[:len(idsStr)-1]
	whereList = append(whereList, fmt.Sprintf("asset_id in (%s)", idsStr))

	if assetStatus > 0 {
		whereList = append(whereList, "asset_status = ?")
		paramList = append(paramList, assetStatus)
	}
	if isFilterStatus > 0 {
		whereList = append(whereList, "status=?")
		paramList = append(paramList, status)
	}

	if filter_online_status {
		if online_status == 0 {
			whereList = append(whereList, "(online_status is null or online_status=0)")
		} else {
			whereList = append(whereList, "online_status=?")
			paramList = append(paramList, online_status)
		}
	}
	whereSql := " where "
	if len(whereList) > 0 {
		whereSql += strings.Join(whereList, " and ")
	}
	_, err = postgresql.GetDBWithContext(ctx).Query(&overviews, fmt.Sprintf("select * from %s.tb_creative_overview_%s %s order by asset_id desc offset %d limit %d", "arthub_sync", gameCode, whereSql, offset, count), paramList...)
	if err != nil {
		return 0, nil, err
	}
	_, err = postgresql.GetDBWithContext(ctx).Query(&total, fmt.Sprintf("select count(*) from %s.tb_creative_overview_%s %s ", "arthub_sync", gameCode, whereSql), paramList...)
	if err != nil {
		return 0, nil, err
	}

	if total.Count == 0 {
		total.Count = uint32(len(overviews))
	}
	return total.Count, overviews, err
}

// 获取game depot信息
func GetDepot(gamecode string) (model.ArthubDepot, error) {
	model := model.ArthubDepot{}
	err := postgresql.Pgdb.Model(&model).
		Where("game_code = ?", gamecode).Select()
	return model, err
}

// GetDepotWithContext 获取game depot信息
func GetDepotWithContext(ctx context.Context, gamecode string) (*model.ArthubDepot, error) {
	model := &model.ArthubDepot{}
	db := postgresql.GetDBWithContext(ctx)
	err := db.Model(model).
		Where("game_code = ?", gamecode).Select()
	return model, err
}

func GetParentIDByAssetIDS(gameCode string, ids []string) ([]string, error) {
	overviews := []model.CreativeOverview{}
	tableName := fmt.Sprintf("%s.tb_creative_overview_%s", "arthub_sync", gameCode)
	//_, err := postgresql.Pgdb.Model((*arthub.CreativeAssetOverview)(nil)).
	//	Table(tableName).
	//	Query(pgv10.Scan(pgv10.Array(&parentIDList)), "SELECT ?", "node_id")
	err := postgresql.Pgdb.Model(&overviews).
		Table(tableName).
		Column("node_id").
		WhereIn("asset_id IN (?)", ids).
		Select()
	if err != nil {
		return nil, err
	}
	m := map[string]struct{}{}
	for _, overview := range overviews {
		m[overview.NodeID] = struct{}{}
	}
	parentIDList := []string{}
	for k := range m {
		parentIDList = append(parentIDList, k)
	}

	return parentIDList, nil
}

// 如果isAll - true, 查询所有数据;
// 若 isAll - false, 分页查询, 有限制最大查询数量;
func ListAssetIdInMediaMaterial(gameCode, directoryID string, offset, limit int, isAll bool) ([]string, error) {
	tableName := fmt.Sprintf("%s.tb_creative_media_material_%s", "arthub_sync", gameCode)
	var mediaMaterialModels = make([]model.CreativeMediaMaterial, 0)
	var err error
	var query *orm.Query
	if directoryID == "" {
		query = postgresql.Pgdb.Model(&mediaMaterialModels).
			Table(tableName).
			Column("asset_id").
			DistinctOn("asset_id").
			OrderExpr("asset_id ASC")
	} else {
		query = postgresql.Pgdb.Model(&mediaMaterialModels).
			Table(tableName).
			DistinctOn("asset_id").
			Column("asset_id").
			OrderExpr("asset_id ASC").
			Where("directory_id = ?", directoryID)
	}
	if !isAll {
		if offset < 0 {
			offset = 0
		}
		if limit <= 0 {
			limit = default_limit
		}
		if limit > max_limit {
			limit = max_limit
		}
		query = query.Offset(offset).Limit(limit)
	}
	err = query.Select()
	if err != nil {
		return nil, err
	}
	var ids = make([]string, 0)
	for _, model := range mediaMaterialModels {
		ids = append(ids, model.AssetID)
	}
	return ids, nil
}
