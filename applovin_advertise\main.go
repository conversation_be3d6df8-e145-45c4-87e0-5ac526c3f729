package main

import (
	"fmt"
	"net/http"
	"time"

	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/conf"
	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/cron"
	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/router"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/redis"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/setting"

	"github.com/gin-gonic/gin"
)

func init() {
	setting.Setup()
	log.Setup()
	conf.LoadBizConf()
	postgresql.Setup()
	redis.Init()
	cron.InitCron()
}

func main() {
	defer func() {
		postgresql.Close()
	}()

	gin.SetMode(setting.GetConf().Server.RunMode)

	routersInit := router.InitRouter()
	readTimeout := time.Duration(setting.GetConf().Server.ReadTimeout * int(time.Second))
	writeTimeout := time.Duration(setting.GetConf().Server.WriteTimeout * int(time.Second))
	endPoint := fmt.Sprintf(":%d", setting.GetConf().Server.HTTPPort)
	maxHeaderBytes := 1 << 20

	server := &http.Server{
		Addr:           endPoint,
		Handler:        routersInit,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		MaxHeaderBytes: maxHeaderBytes,
	}

	log.Infof("start http server listening %s", endPoint)

	_ = server.ListenAndServe()
}
