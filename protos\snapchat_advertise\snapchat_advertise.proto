syntax = "proto3";

package snapchat_advertise;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/snapchat_advertise";

import "aix/aix_common_message.proto";

// 自测接口, POST, /api/v1/snapchat_advertise/say_hi
message SayHiReq {
}

message SayHiRsp {
    aix.Result result = 1;  // 返回结果
}

// 基础结构 渠道账号信息
message MediaAccount {
    string account_id   = 1;  // 渠道账号id
    string account_name = 2;  // 渠道账号名称
}

// 查询渠道账号列表, POST, /api/v1/snapchat_advertise/get_media_accounts
message GetMediaAccountsReq {
  string game_code = 1; // 必填 游戏标识game_code
}

message GetMediaAccountsRsp {
  aix.Result result = 1;              // 返回结果
  string game_code = 2;               // 游戏标识game_code
  repeated MediaAccount accounts = 3; // 账号信息列表
}