package data

import (
	"context"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"github.com/thoas/go-funk"
)

// GetGameCreativeLabels 获取游戏的一二级标签
func GetGameCreativeLabels(ctx context.Context, gameCode string) ([]*model.CreativeLabel, error) {
	var rlt []*model.CreativeLabel

	query := postgresql.GetDBWithContext(ctx).Model(&rlt)
	query.Where("game_code = ?", gameCode)
	query.Where("soft_del = ?", false) // 未删除

	err := query.Select()
	return rlt, err
}

// UpsertCreativeLabels 插入更新游戏的一二级标签
func UpsertCreativeLabels(ctx context.Context, rows []*model.CreativeLabel) error {
	query := postgresql.GetDBWithContext(ctx).Model(&rows)
	query.OnConflict("(id) DO UPDATE")
	query.Set("name=EXCLUDED.name")
	query.Set("options=EXCLUDED.options")
	query.Set("updated_at=EXCLUDED.updated_at")
	query.Set("updater=EXCLUDED.updater")
	// 其他不改

	_, err := query.Insert()
	return err
}

// BatchUpdateCreativeLabelsSecondLabel 批量更新标签库一级标签下的二级标签
func BatchUpdateCreativeLabelsSecondLabel(ctx context.Context, rows []*model.CreativeLabel) error {
	// 分批
	chunks := funk.Chunk(rows, 500).([][]*model.CreativeLabel)
	for _, chunk := range chunks {
		query := postgresql.GetDBWithContext(ctx).Model(&chunk)
		query.WherePK()
		query.Column("options", "updated_at", "updater")
		// 其他不改

		_, err := query.Update()
		return err
	}

	return nil
}
