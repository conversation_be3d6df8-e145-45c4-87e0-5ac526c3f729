#!/bin/bash

# 当前脚本绝对路径
current_path=$(cd "$(dirname "$0")";pwd)

cd ${current_path}/../

echo -e "【1】 build tiktok_advertise.proto"
sh build_proto.sh ./tiktok_advertise/tiktok_advertise.proto
if [ $? -ne 0 ]; then
    echo -e "【ERROR】tiktok_advertise proto build error"
    exit 1
fi

echo -e "【2】 build tiktok_advertise jsonschema"
proto-to-jsonschema ./tiktok_advertise/tiktok_advertise.proto
if [ $? -ne 0 ]; then
    echo -e "【ERROR】tiktok_advertise generate jsonschema error"
    exit 1
fi


