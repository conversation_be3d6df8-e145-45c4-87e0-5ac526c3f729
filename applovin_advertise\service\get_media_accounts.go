package service

import (
	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/conf"
	"e.coding.intlgame.com/ptc/aix-backend/applovin_advertise/data"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/applovin_advertise"
	"github.com/gin-gonic/gin"
)

// GetMediaAccounts 获取渠道账号列表
func GetMediaAccounts(ctx *gin.Context, req *pb.GetMediaAccountsReq, rsp *pb.GetMediaAccountsRsp) error {
	if req.GetGameCode() == "" {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "game_code is required")
	}
	accounts, err := data.GetGameMediaAccounts(ctx, req.GetGameCode())
	if err != nil {
		return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "data.GetGameMediaAccounts err:%v", err)
	}

	for _, a := range accounts {
		t := &pb.MediaAccount{
			AccountId:   a.AccountId,
			AccountName: a.AccountName,
		}
		uploadCampaign := conf.GetUploadAssetCampaign(req.GetGameCode(), a.AccountId)
		if uploadCampaign != nil {
			// 实时获取campaign信息
			client := newApplovinAPIClient(a)
			campaign, err := client.GetCampaign(ctx, uploadCampaign.CampaignID)
			if err != nil {
				return errs.New(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "client.GetCampaign err:%v", err)

			}
			t.UploadAssetCampaign = &pb.Campaign{
				CampaignId:   campaign.CampaignID,
				CampaignName: campaign.Name,
				Status:       campaign.Status,
			}
		}

		rsp.Accounts = append(rsp.Accounts, t)
	}
	return nil
}
