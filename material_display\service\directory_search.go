package service

import (
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/errs"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/utils"
	"e.coding.intlgame.com/ptc/aix-backend/material_display/data"
	"e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pbAix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	pb "e.coding.intlgame.com/ptc/aix-backend/protos/material_display"
	"github.com/gin-gonic/gin"
)

// DirectorySearch 搜索素材目录
func DirectorySearch(ctx *gin.Context, req *pb.DirectorySearchReq, rsp *pb.DirectorySearchRsp) error {
	gameCode := utils.GetHeaderGame(ctx)

	page := req.GetPage()
	pageSize := req.GetPageSize()
	if page < 0 || pageSize <= 0 || pageSize > 100 {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "page or page_size error")
	}
	if req.GetName() == "" {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_PARAM), "name is empty")
	}

	// 拉取depot信息
	depot, err := data.GetDepotWithContext(ctx, gameCode)
	if err != nil {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "DirectorySearch data.GetDepotWithContext err: %v", err)
	}
	pathPrefix, err := getPathPrefix(ctx, gameCode, depot.DepotId)
	if err != nil {
		return errs.New(int(pbAix.AixCommErrCode_AIX_COMM_ERR_DB), "DirectorySearch getPathPrefix err: %v", err)
	}

	filter := &data.SearchCreativeDirectoryFilter{
		PathPrefix: pathPrefix,
		Name:       req.GetName(),
		Offset:     int(page * pageSize),
		Limit:      int(pageSize),
	}
	rows, total, err := data.SearchCreativeDirectory(ctx, gameCode, filter)
	if err != nil {
		return errs.NewFormat(int(aix.AixCommErrCode_AIX_COMM_ERR_DB), "DirectorySearch data.SearchCreativeDirectory err:%v", err)
	}

	records := correctDirectoryFullPath(ctx, gameCode, depot, rows)
	rsp.Dirs = materialInfoToProto(records)
	rsp.Total = int32(total)

	return nil
}
