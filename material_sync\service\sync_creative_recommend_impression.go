package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"github.com/go-pg/pg/v10/orm"
)

// syncCreativeRecommendImpression 同步素材推荐的曝光集, 即impression > 0的素材
func syncCreativeRecommendImpression(game_code string, key2asset_id map[string]string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "syncCreativeRecommendImpression start, game code: %s", game_code)

	st := time.Now()

	err := createCreativeRecommendImpressionForGameCode(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "createCreativeRecommendImpressionForGameCode failed: %s", err)
		return
	}

	today := time.Now().AddDate(0, 0, -1)
	ad_assets, err := getLatestImpressionAdAsset(ctx, game_code, today)
	if err != nil {
		log.ErrorContextf(ctx, "getLatestImpressionAdAsset failed: %s", err)
		return
	}

	if len(ad_assets) == 0 {
		return
	}

	impressions, err := genCreativeRecommendImpressions(ctx, game_code, ad_assets, key2asset_id)
	if err != nil {
		log.ErrorContextf(ctx, "genCreativeRecommendImpressions failed: %s", err)
	}

	log.DebugContextf(ctx, "game %s get impressions number: %d", game_code, len(impressions))

	err = insertUpdateCreativeRecommendImpressionForGameCode(ctx, game_code, impressions)
	if err != nil {
		log.ErrorContextf(ctx, "insertUpdateCreativeRecommendImpressionForGameCode failed: %s", err)
	}

	cost := time.Since(st)
	log.DebugContextf(ctx, "syncCreativeRecommendImpression end, game code: %s, cost: %v", game_code, cost)
}

// createCreativeRecommendImpressionForGameCode ...
func createCreativeRecommendImpressionForGameCode(ctx context.Context, game_code string) error {
	model := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendImpression{})
	table_name := pgmodel.GetCreativeRecommendImpressionTableName(game_code)
	model.Table(table_name)

	err := model.CreateTable(&orm.CreateTableOptions{IfNotExists: true})
	if err != nil {
		return err
	}

	return nil
}

// insertUpdateCreativeRecommendImpressionForGameCode ...
func insertUpdateCreativeRecommendImpressionForGameCode(ctx context.Context, game_code string, impressions []pgmodel.CreativeRecommendImpression) error {
	log.DebugContextf(ctx, "get valid impressions number: %d", len(impressions))
	table_name := pgmodel.GetCreativeRecommendImpressionTableName(game_code)

	if len(impressions) > 0 {
		author := "material_sync.sync_creative_recommend_impression_update"
		model := postgresql.GetDBWithContext(ctx).Model(&impressions)
		model.Table(table_name)
		model.OnConflict("(channel_type, channel_account_id, channel_asset_id) DO Update")
		model.Set("asset_id=excluded.asset_id")
		model.Set("asset_version=excluded.asset_version")
		model.Set("asset_theme=excluded.asset_theme")
		model.Set("update_by=?", author)
		model.Set("update_time=excluded.update_time")
		model.Set("impression_status=excluded.impression_status")
		_, err := model.Insert()
		if err != nil {
			return fmt.Errorf("insert update impression failed: %s", err)
		}
	}

	time_now := time.Now().Format("2006-01-02 15:04:05")
	update_time := time_now
	for _, impression := range impressions {
		if update_time > impression.UpdateTime {
			update_time = impression.UpdateTime
		}
	}
	pg_query := postgresql.GetDBWithContext(ctx).Model(&pgmodel.CreativeRecommendImpression{}).Table(table_name)
	pg_query.Where("update_time<?", update_time)
	pg_query.Set("impression_status=?", 2) // 下线
	pg_query.Set("update_time=?", time_now)
	_, err := pg_query.Update()
	if err != nil {
		return fmt.Errorf("update offline impressions failed: %s", err)
	}

	return nil
}

// genCreativeRecommendImpressions ...
func genCreativeRecommendImpressions(ctx context.Context, game_code string, ad_assets []latestAdAsset, key2asset_id map[string]string) ([]pgmodel.CreativeRecommendImpression, error) {
	overviews, err := getValidAssetsForDigging(ctx, game_code)
	if err != nil {
		return nil, fmt.Errorf("getValidAssetsForDigging failed: %s", err)
	}

	asset_id2overview := make(map[string]pgmodel.CreativeOverview)
	for _, overview := range overviews {
		asset_id2overview[overview.AssetID] = overview // 这个是大坑, 一定不能用range中的地址给变量赋值
	}

	impression_set := make(map[string]bool)
	var impressions []pgmodel.CreativeRecommendImpression
	for _, ad_asset := range ad_assets {
		key := genChannelAssetKey(ad_asset.ChannelType, ad_asset.AccountId, ad_asset.AssetId)
		asset_id, ok := key2asset_id[key]
		if !ok {
			continue
		}

		overview, ok := asset_id2overview[asset_id]
		if !ok {
			continue
		}

		impression, err := genCreativeRecommendImpressionRecord(ctx, overview, ad_asset)
		if err != nil {
			log.ErrorContextf(ctx, "genCreativeRecommendOnlineRecord failed: %s", err)
			continue
		}

		pk := pgmodel.GenCreativeRecommendImpressionPK(&impression)
		if impression_set[pk] {
			log.DebugContextf(ctx, "duplicate impression info: %+v", impression)
			continue
		}

		if impression.ChannelAssetId == "***********" {
			log.DebugContextf(ctx, "get overview: %+v", overview)
		}

		impression_set[pk] = true
		impressions = append(impressions, impression)
	}

	return impressions, nil
}

// genCreativeRecommendImpressionRecord ...
func genCreativeRecommendImpressionRecord(ctx context.Context, overview pgmodel.CreativeOverview, asset latestAdAsset) (pgmodel.CreativeRecommendImpression, error) {
	impression := pgmodel.CreativeRecommendImpression{}
	impression.AssetId = overview.AssetID
	impression.ChannelType = asset.ChannelType
	impression.ChannelAccountID = asset.AccountId
	impression.ChannelAssetId = asset.AssetId
	author := "material_sync.sync_creative_recommend_impression"
	impression.CreateBy = author
	time_now := time.Now().Format("2006-01-02 15:04:05")
	impression.CreateTime = time_now
	impression.UpdateTime = time_now
	impression.UpdateBy = author
	impression.FirstImpressionDate = time_now
	impression.ImpressionStatus = 1 // 在线

	label, err := parsePUBGMFullPathName(ctx, overview.FullPathName)
	if err != nil {
		log.ErrorContextf(ctx, "parsePUBGMFullPathName failed: %s", err)
	}
	if label != nil {
		impression.AssetVersion = label.Version
		impression.AssetTheme = label.Theme
	}

	return impression, nil
}

// pubgmPathLabel 根据pubgm的路径解析的
type pubgmPathLabel struct {
	Version string // 素材版本
	Theme   string // 素材主题
}

func parsePUBGMFullPathName(ctx context.Context, full_path_name string) (*pubgmPathLabel, error) {
	words := strings.Split(full_path_name, ",")
	if len(words) <= 1 {
		return nil, nil
	}
	version := words[1]
	r := regexp.MustCompile(`[\d]+.[\d]+.[\d]+`)
	if !r.Match([]byte(version)) {
		return nil, nil
	}
	var label pubgmPathLabel
	label.Version = version
	if len(words) <= 2 {
		return &label, nil
	}

	theme := words[2]
	if strings.Contains(theme, "本地化素材") || strings.Contains(theme, "IP合作") || strings.Contains(theme, "本地素材") {
		if len(words) <= 3 {
			return &label, nil
		}
		theme = words[3]
	}
	label.Theme = theme
	return &label, nil
}

// getLatestImpressionAdAsset 获取在线素材
func getLatestImpressionAdAsset(ctx context.Context, game_code string, date time.Time) ([]latestAdAsset, error) {
	pg_query := postgresql.GetDBWithContext(ctx).Model(&pgmodel.FacebookRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(game_code))
	today := date.Format("********")
	pg_query.Where("dtstatdate=?", today)
	pg_query.Having("sum(impressions)>0")
	select_columns := []string{"asset_name", "account_id", "asset_id"}
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)
	var fb_records []pgmodel.FacebookRealtimeAssetInfo
	err := pg_query.Select(&fb_records)
	if err != nil {
		log.ErrorContextf(ctx, "get facebook online number failed: %s", err)
		fb_records = nil
	}
	log.DebugContextf(ctx, "get facebook online data number: %d", len(fb_records))

	pg_query = postgresql.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.Where("dtstatdate=?", today)
	pg_query.Having("sum(impressions)>0")
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)
	var gg_records []pgmodel.GoogleRealtimeAssetInfo
	err = pg_query.Select(&gg_records)
	if err != nil {
		log.ErrorContextf(ctx, "get google online numbers failed: %s", err)
	}
	log.DebugContextf(ctx, "get google online data number: %d", len(gg_records))

	var ad_assets []latestAdAsset
	for _, record := range gg_records {
		if len(record.AssetId) == 0 {
			continue
		}

		as_asset := latestAdAsset{}
		as_asset.AssetName = record.AssetName
		as_asset.AccountId = record.AccountId
		as_asset.AssetId = record.AssetId
		as_asset.MainCountry = record.MainCountry
		as_asset.Network = record.Network
		as_asset.AdgroupId = record.AdGroupId
		as_asset.AdgroupName = record.AdGroupName
		as_asset.CountryList = record.CountryList
		as_asset.ChannelType = 1 // google

		ad_assets = append(ad_assets, as_asset)
	}

	for _, record := range fb_records {
		if len(record.AssetId) == 0 {
			continue
		}

		ad_asset := latestAdAsset{}
		ad_asset.AssetName = record.AssetName
		ad_asset.AccountId = record.AccountId
		ad_asset.AssetId = record.AssetId
		ad_asset.MainCountry = record.MainCountry
		ad_asset.Network = record.Network
		ad_asset.AdgroupId = record.AdGroupId
		ad_asset.AdgroupName = record.AdGroupName
		ad_asset.CountryList = record.CountryList
		ad_asset.ChannelType = 2 // facebook

		ad_assets = append(ad_assets, ad_asset)
	}

	return ad_assets, nil
}
