package data

import (
	"context"
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	repo "e.coding.intlgame.com/ptc/aix-backend/common/repo/common_config"
)

// GetAllMediaAccounts 拉取所有的账号列表
func GetAllMediaAccounts(ctx context.Context) ([]*model.MediaAccount, error) {
	return repo.GetApplovinAllMediaAccounts(ctx)
}

// GetGameMediaAccounts 拉取游戏下的账号列表
func GetGameMediaAccounts(ctx context.Context, gameCode string) ([]*model.MediaAccount, error) {
	return repo.GetApplovinGameMediaAccounts(ctx, gameCode)
}

// GetOneMediaAccounts 拉取某个账号信息, 没有找到返回err
func GetOneMediaAccounts(ctx context.Context, gameCode string, accountID string) (*model.MediaAccount, error) {
	account, err := repo.GetApplovinOneMediaAccount(ctx, gameCode, accountID)
	if err != nil {
		return nil, err
	}
	// 账号不存在
	if account == nil {
		return nil, fmt.Errorf("game_code:%v, account_id:%v not found", gameCode, accountID)
	}

	return account, nil
}

// UpdateMediaAccountByField 更新账号配置信息指定字段
func UpdateMediaAccountByField(ctx context.Context, account *model.MediaAccount, fields []string) error {
	if len(fields) == 0 {
		return fmt.Errorf("no fields to update")
	}
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(account).Table(model.GetMediaAccountTableName())
	query.Where("game_code = ?", account.GameCode)
	query.Where("media = ?", constant.MediaNameApplovin)
	query.Where("account_id = ?", account.AccountId)
	query.Column(fields...)
	_, err := query.Update()
	return err
}
