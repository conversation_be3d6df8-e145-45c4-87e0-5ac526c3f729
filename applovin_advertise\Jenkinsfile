// see https://www.jenkins.io/doc/book/pipeline/syntax/
pipeline {
  agent any
  environment {
    WORKLOAD_BASE = 'applovin-advertise' // 工作负载名称前缀（后缀为环境，如-dev），不同服务需要替换此变量
    CODING_DOCKER_REG_HOST = "${CCI_CURRENT_TEAM}-${CCI_DOCKER_REGISTRY_DOMAIN}"
    IMAGE_VERSION = "build-${env.CI_BUILD_NUMBER}"
    GOMOD_GIT_CREDIT = 'f39437f5-959e-4a30-8298-53fb976a1c3a'
  }

  stages {
    stage('设置变量') {
      steps {
        script {
          switch (env.GIT_LOCAL_BRANCH) {
            case "master":
              env.DOCKER_REPO_NAME = 'production';
              env.WEB_HOOK_SOURCE = 'https://api-spinnaker.devops.intlgame.com/webhooks/webhook/aix-prod-env-cluster';
              env.WORKLOAD_NAMESPACE = 'aix-prod';
              env.WORKLOAD_NAME = "${WORKLOAD_BASE}-prod"
              break;
            case "exp":
              env.DOCKER_REPO_NAME = 'exp';
              env.WEB_HOOK_SOURCE = ' https://api-spinnaker-beta.devops.intlgame.com/webhooks/webhook/aix-test-env-cluster';
              env.WORKLOAD_NAMESPACE = 'aix-exp';
              env.WORKLOAD_NAME = "${WORKLOAD_BASE}-exp"
              break;
            case "develop":
            case "test":
              env.DOCKER_REPO_NAME = 'test';
              env.WEB_HOOK_SOURCE = ' https://api-spinnaker-beta.devops.intlgame.com/webhooks/webhook/aix-test-env-cluster';
              env.WORKLOAD_NAMESPACE = 'aix-test';
              env.WORKLOAD_NAME = "${WORKLOAD_BASE}-test"
              break;
            default:
              env.DOCKER_REPO_NAME = 'dev';
              env.WEB_HOOK_SOURCE = ' https://api-spinnaker-beta.devops.intlgame.com/webhooks/webhook/aix-test-env-cluster';
              env.WORKLOAD_NAMESPACE = 'aix-dev';
              env.WORKLOAD_NAME = "${WORKLOAD_BASE}-dev"
              break;
          }
          env.CODING_DOCKER_IMAGE_NAME = "${PROJECT_NAME.toLowerCase()}/${DOCKER_REPO_NAME}/${CCI_JOB_NAME}"
          env.IMAGE_FULL_NAME = "${CODING_DOCKER_REG_HOST}/${env.CODING_DOCKER_IMAGE_NAME}:${IMAGE_VERSION}"
          sh 'printenv'
        }
      }
    }

    stage('检出Git') {
      steps {
        checkout([$class: 'GitSCM', branches: [[name: env.GIT_BUILD_REF]], userRemoteConfigs: [[url: env.GIT_REPO_URL, credentialsId: env.CREDENTIALS_ID]]])
      }
    }

    stage('构建推送镜像') {
      steps {
        script {
          withCredentials([usernamePassword(credentialsId: GOMOD_GIT_CREDIT, usernameVariable: 'GOMOD_GIT_USER', passwordVariable: 'GOMOD_GIT_PWD')]) {
            docker.withRegistry(
              "${CCI_CURRENT_WEB_PROTOCOL}://${env.CODING_DOCKER_REG_HOST}",
              "${env.CODING_ARTIFACTS_CREDENTIALS_ID}"
            ) {
              docker.build("${env.CODING_DOCKER_IMAGE_NAME}:${IMAGE_VERSION}", "--build-arg GOMOD_GIT_USER=${GOMOD_GIT_USER} --build-arg GOMOD_GIT_PWD=${GOMOD_GIT_PWD} .").push()
              sh "docker image rm ${env.IMAGE_FULL_NAME}"
            }
          }
        }
      }
    }

    stage('自动部署') {
      when {
        anyOf { branch 'master'; branch 'develop'; branch 'dev'; branch 'test'; branch 'exp' }
      }
      steps {
        script {
          sh 'curl ${WEB_HOOK_SOURCE} -X POST -H "content-type: application/json" -d \'{"parameters": {"image": "\'"${IMAGE_FULL_NAME}"\'","name": "\'"${WORKLOAD_NAME}"\'","namespace": "\'"${WORKLOAD_NAMESPACE}"\'"}}\''
          echo '部署已经触发，可以在Spinnaker查看状态: https://api-spinnaker.devops.intlgame.com'
        }
      }
    }
  }
}