syntax = "proto3";

package material_display;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/material_display";

import "aix/aix_common_message.proto";

enum MaterialStatus {
    NOT_UPLOAD    = 0;  // 未上传
    UPLOADING     = 1;  // 正在上传
    UPLOADED      = 2;  // 上传成功
    UPLOAD_FAILED = 3;  // 上传成功
}

// 素材列表请求, POST, /api/v1/material_display/material_list
message MaterialListReq {
    uint64 uid                = 1;   // 保留字段
    string uuid               = 2;   // 前端生成随机ID
    uint32 offset             = 3;   // 起始偏移
    uint32 count              = 4;   // 拉取数量
    uint32 is_filter_status   = 5;   // 是否按照状态过滤，0-否，1-是
    uint32 status             = 6;   // 过滤状态，is_filter_status=1生效 0-未上传；1-正在上传；2-上传成功；3-上传失败
    string directory_id       = 7;   // 目录ID
    uint32 upload_state       = 8;   // (已废弃) 是否上传至广告库，0-否，1-是
    uint32 with_detail        = 9;   // 是否需要拉取详情 0-不需要 1-需要
    uint32 online_status      = 10;  // 在线状态筛选, 0表示不需要筛选
    bool filter_online_status = 11;  // 是否过滤在线状态
}
// 素材元数据
message MaterialMeta {
    string asset_id              = 1;   // 素材索引
    string name                  = 2;   // 素材名称
    uint32 status                = 3;   // 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
    string upline_date           = 4;   // 上线日期
    string offline_date          = 5;   // 素材下线时间
    uint32 online_days           = 6;   // 在线天数
    string formate               = 7;   // 素材格式
    string duration              = 8;   // 素材时长
    string preview_url           = 9;   // 素材预览url
    string create_date           = 10;  // 网盘上的创建时间(即素材上传到网盘的时间)
    MaterialExt material_ext     = 11;  // 素材详情
    int32 asset_status           = 12;  // 素材在仓库状态, 1-正常, 2-回收站, 3-删除
    int32 online_status          = 13;  // 素材曝光状态, 0-未上线, 1-上线, 2-下架
    string online_date           = 14;  // 素材第一次曝光时间
    string full_path_id          = 15;  // 素材路径ID
    string full_path_name        = 16;  // 素材路径名称
    string aix_uploader          = 17;  // aix平台的素材上传者
    int32 google_online_state    = 18;  // Google渠道上线状态, 0-从未上线, 1-上线, 2-下线
    string google_online_time    = 19;  // Google渠道上线时间
    string google_offline_time   = 20;  // Google渠道下线时间
    int32 facebook_online_state  = 21;  // Facebook渠道上线状态, 0-从未上线, 1-上线, 2-下线
    string facebook_online_time  = 22;  // Facebook渠道上线时间
    string facebook_offline_time = 23;  // Facebook渠道下线时间

    string number            = 24;  // 素材编号. 如UA00012
    string project_name      = 25;  // 项目名称. 如PUBGM
    string asset_format      = 26;  // 素材格式. 如图片, 视频等.
    string asset_size        = 27;  // 素材尺寸. 如横版素材(horizontal), 竖版素材(vertical), 方版素材(square ratio)等.
    string language          = 28;  // 语言. 如KR.
    string agent_name        = 29;  // 代理名称. 如Tencent.
    string simple_asset_name = 30;  // 简洁版素材名称. 如0001选择时装战斗
    string playing_method    = 31;  // 游戏内玩法. 如时装.
    string asset_form        = 32;  // 素材表现形式. 如包装剪辑.
    string asset_stage       = 33;  // 素材对应阶段. 如CBT, TBT等.
    string delivery_date     = 34;  // 交付日期. 如211116.
    string custom1           = 35;  // 自定义字段1
    string custom2           = 36;  // 自定义字段2
    string custom3           = 37;  // 自定义字段3
    string AuditId           = 38;  // 审核记录id
    int32 AuditStatus        = 39;  // 审核状态

    repeated AssetLabel labels = 40;  // 素材自己的标签
    string   impression_status = 41;  // 曝光状态
    string   impression_date   = 42;  // 曝光日期
    string   sync_time         = 43;  // 同步时间

    string another_name = 44; // 其他名字，一些接口需要

    repeated SyncMedia sync_media_list = 45; // 已经上传的渠道列表
}

message MaterialListRsp {
    aix.Result result               = 1;  // 返回结果
    repeated MaterialMeta materials = 2;  // 素材列表
    uint32 total                    = 3;  // 素材总数
}

// 获取素材详情, POST, /api/v1/material_display/get_material_info
message GetMaterialInfoReq {
    uint64 uid      = 1;
    string uuid     = 2;
    string asset_id = 3;  // 唯一索引
}
// 素材通用数据
message Universal {
    string format      = 1;  // 文件格式
    uint64 size        = 2;  // 文件大小
    string create_time = 3;  // 创建时间
    string updater     = 4;  // 更新者
    string update_time = 5;  // 更新时间
    string creator     = 6;  // 创建者
    string cover       = 7;  // 封面
    int32  format_type = 8;  // 素材类型
}

// 素材标签数据
message Label {
    repeated string manual_first_label  = 1;  // 人工一级标签
    repeated string manual_second_label = 2;  // 人工二级标签
    repeated string robot_first_label   = 3;  // 机器一级标签
    repeated string robot_second_label  = 4;  // 机器二级标签
}

// 素材统计数据
message Statistics {
    uint64 take_users = 1;  // 取用人数
    uint64 take_count = 2;  // 取用次数
}
// 素材视频信息
message Video {
    uint32 width              = 1;  // 宽
    uint32 high               = 2;  // 高
    uint32 duration           = 3;  // 时长
    string frame_rate         = 4;  // 帧率
    string aspect_ratio       = 5;  // 高宽比
    string bit_rate           = 6;  // 比特率
    string compression_format = 7;  // 压缩格式
}

// 素材详情扩展数据
message MaterialExt {
    string asset_id       = 1;  // 素材索引
    Universal universal   = 2;  // 通用
    Label label           = 3;  // 标签
    Statistics statistics = 4;  // 统计
    Video video           = 5;  // 视频
}
message GetMaterialInfoRsp {
    aix.Result result        = 1;
    MaterialExt material_ext = 2;
}

// 批量获取素材详情, POST, /api/v1/material_display/bt_get_material_info
message BtGetMaterialInfoReq {
    uint64 uid                    = 1;
    string uuid                   = 2;
    repeated string asset_id_list = 3;  // 唯一索引
}

message BtGetMaterialInfoRsp {
    aix.Result result                      = 1;
    repeated MaterialExt material_ext_list = 2;
}

// 批量拉取素材详细信息, POST, /api/v1/material_display/bt_get_material_info_detail
message BtGetMaterialInfoDetailReq {
    repeated string asset_ids = 1;  // 素材ID列表
}

message BtGetMaterialInfoDetailRsp {
    aix.Result result               = 1;  // 返回结果
    repeated MaterialMeta materials = 2;  // 素材列表
}

// 素材标签
message AssetLabel {
    string label_name   = 1;  // 标签名称，旧字段已废弃
    string first_label  = 2;  // 一级标签
    string second_label = 3;  // 二级标签
}

message AssetFieldFilter {
    string name            = 1;  // 字段名称
    repeated string values = 2;  // 值列表
}

// 上传渠道信息
message SyncMedia {
    int32 channel = 1; // 渠道
}

// 素材搜索, POST, /api/v1/material_display/search_material
message SearchMaterialsReq {
    uint64   uid                                  = 1;
    string   uuid                                 = 2;
    string   text                                 = 3;   // 文本搜索, 为空表示搜索全部
    uint32   status                               = 4;   // 状态搜索(已废弃)
    uint32   offset                               = 5;   // 起始偏移
    uint32   count                                = 6;   // 拉取数量
    string   directory_id                         = 7;   // 目录ID
    uint32   upload_state                         = 8;   // 是否上传至广告库，0-否，1-是(已废弃)
    uint32   with_detail                          = 9;   // 是否需要拉取详情 0-不需要 1-需要
    bool     is_ad                                = 10;  // 是否是搜索广告库; false: 搜索素材库，true: 搜索广告库
    uint32   online_status                        = 11;  // 在线状态筛选, 0表示不需要筛选
    bool     filter_online_status                 = 12;  // 是否过滤在线状态
    int32    search_type                          = 13;  // 搜索类型, 1-搜索单个名称(模糊搜索); 2-搜索多个关键字取并集(模糊搜索); 3-搜索多个素材ID; 4-搜索多个名称取交集(模糊搜索)
    repeated string names                         = 14;  // 搜索名称, 为空表示搜索全部
    repeated string asset_ids                     = 15;  // 搜索素材ID列表, 为空表示搜索全部
    string   aix_uploader                         = 16;  // aix平台的上传人, 空表示不进行过滤
    repeated AssetLabel labels                    = 17;  // 需要搜索的标签列表, 为空表示不需要搜索
    int32    labels_search_type                   = 18;  // 标签搜索方式, 0-默认(取并集), 1-取并集; 2-取交集
    repeated AssetFieldFilter asset_field_filters = 19;  // 素材属性字段过滤器
    uint32   format_type                          = 20;  // 是否类型，0-全部，1-视频，2-图片
    uint32   media                                = 21;  // 1-google 目前只用到google
    repeated string asset_ratios                  = 22;  // 需要筛选的素材比例, 不填默认不进行筛选
    int32    sync_media_filter                    = 23;  // 0-不过滤，1-未上传渠道，2-已上传渠道
    repeated int32 sync_media_list                = 24;  // sync_media_filter=2有效，空为只要上传过就行
    string   start_create_date                    = 25;  // 不为空，过滤 >= 上传网盘时间, 格式为 2021-01-01
    string   end_create_date                      = 26;  // 不为空，过滤 <= 上传网盘时间, 格式为 2021-01-01
    repeated int32 format_type_list               = 27;  // 多选素材格式类型列表, 为空不过滤, 枚举参看AssetXXXXType
}
message SearchMaterialsRsp {
    aix.Result result               = 1;  // 返回结果
    repeated MaterialMeta materials = 2;  // 素材列表
    uint32 total                    = 3;  // 素材总数
}

// 素材名字搜索, POST, /api/v1/material_display/search_materials_by_name
message SearchMaterialsByNameReq {
    string asset_name  = 1;  // 素材名字
    string search_type = 2;  // "by_full|by_prefix", 多种搜索方式竖线分割， by_full:名字全匹配， by_prefix:匹配编号前缀
}

message SearchMaterialsByNameRsp {
    aix.Result result               = 1;  // 返回结果
    repeated MaterialMeta materials = 2;  // 素材列表
    uint32 total                    = 3;  // 素材总数
}

// 批量素材名字搜索, POST, /api/v1/material_display/batch_search_materials_by_name
message BatchSearchMaterialsByNameReq {
    repeated string asset_names  = 1;  // 素材名字列表，最多50条
}

message BatchSearchMaterialsByNameRsp {
    aix.Result result               = 1;  // 返回结果
    repeated MaterialMeta materials = 2;  // 素材列表
    uint32 total                    = 3;  // 素材总数
}

// 拉取目录列表, POST, /api/v1/material_display/directory_list
message DirectoryListReq {
    string parent_id       = 1;  // 父目录id
    uint32 offset          = 2;  // 分页偏移量
    uint32 limit           = 3;  // 最大获取数量
    string filter_dir_name = 4;  // 过滤目录名称
    int32 get_child        = 5;  // 是否包含子节点信息, 0-否, 1-是
}

//返回的目录数据信息
message Directory {
    string id                     = 1;   //目录id
    string name                   = 2;   // 目录名称
    string parent_id              = 3;   // 父目录id
    string parent_name            = 4;   // 父目录名称
    string create_date            = 5;   // 创建日期
    string update_date            = 6;   // 更新日期（索引）
    uint32 direct_child_count     = 7;   // 当前目录包含子目录和元素数量（不递归）
    uint32 total_leaf_count       = 8;   // 当前目录下包含素材数量（递归）
    uint32 direct_directory_count = 9;   // 当前目录下子目录数量（不递归）
    string full_path_name         = 10;  // 当前目录全路径名称
    string full_path_id           = 11;  // 当前目录全路径id
    string media_directory_id     = 12;  // 媒体素材id
    string media_directory_name   = 13;  // 媒体素材名称
    string sync_time              = 14;  // 最近同步时间
}

message DirectoryListRsp {
    aix.Result result       = 1;  // 返回结果
    repeated Directory dirs = 2;  // 目录列表
    uint64 total            = 3;  // 总数
}

// 搜索素材目录, POST, /api/v1/material_display/directory_search
message DirectorySearchReq {
    string name      = 1;  // 匹配目录名
    int32  page      = 2;  // 分页数，从0开始
    int32  page_size = 3;  // 每页条数，最多100条
}

message DirectorySearchRsp {
    aix.Result result         = 1;  // 返回结果
    repeated   Directory dirs = 2;  // 目录列表
    int32      total          = 3;  // 总数
}

// 获取指定目录的数据信息, POST, /api/v1/material_display/directory_get
message DirectoryGetReq {
    string id       = 1;  // 目录id
    uint32 dir_type = 2;  // 目录类型 0-普通目录 1-iegg媒体目录
}

message DirectoryGetRsp {
    aix.Result result = 1;  // 返回结果
    Directory dir     = 2;  // 目录信息
}

// 新建目录, POST, /api/v1/material_display/directory_create
message DirectoryCreateReq {
    string name                    = 1;  // 目录名称
    string parent_id               = 2;  // 父目录id
    int32 rename_repeated_category = 3;  // arthub接口参数，是否是新版本
}

message DirectoryCreateRsp {
    aix.Result result = 1;  // 返回结果
    Directory dir     = 2;  // 目录信息
}

// 素材拖拽到其他目录, POST, /api/v1/material_display/materials_move
message MaterialMoveReq {
    repeated string ids    = 1;  //被移动的文件和文件夹ID
    string other_parent_id = 2;  //移动到的目标文件夹ID
}

message MaterialInfo {
    string node_id        = 1;   // 当前素材所属目录id
    string node_name      = 2;   // 当前素材所属目录名称
    string asset_id       = 3;   // 当前素材id（主键）
    string asset_name     = 4;   // 当前素材名称
    uint32 status         = 5;   // 素材状态（索引）
    string create_date    = 6;   // 素材创建日期（索引）
    string upline_date    = 7;   // 素材上线日期（索引）
    string offline_date   = 8;   // 素材下线日期（索引）
    uint32 online_days    = 9;   // 素材上线天数
    string full_path_name = 10;  // 当前素材全路径名称
    string full_path_id   = 11;  // 当前素材全路径id
}

message MaterialMoveRsp {
    aix.Result result                    = 1;  // 返回结果
    repeated MaterialInfo material_items = 2;  //素材移动到其他目录后的素材信息
    repeated string failed_material_ids  = 3;  //移动失败的素材id列表
}

message MaterialSetInfo {
    string asset_id = 1;  // 素材id
    string name     = 2;  // 新名称
    string cover    = 3;  // 新封面
    string audit_id = 4;  // 素材审核id
    int32 audit_status = 5; // 素材审核状态
    Label label     = 6;  // 新标签
}

// 编辑素材信息(所有参数必须填写!), POST, /api/v1/material_display/set_material_info
message SetMaterialInfoReq {
    repeated MaterialSetInfo material_set_list = 1;  // 待设置的素材
}

message SetRes {
    string asset_id = 1;  // 素材id
    uint32 set_res  = 2;  // 设置结果 0-失败 1-成功
    string res_msg  = 3;  // 设置结果描述
}

message SetMaterialInfoRsp {
    aix.Result result            = 1;  // 返回结果
    repeated SetRes set_res_list = 2;  // 设置结果列表
}

message UploadAsset {
    string asset_id            = 1;  // 素材id
    repeated uint32 to_channel = 2;  // 1-google 2-facebook
    repeated string country    = 3;  // 国家
    string assert_name         = 4;  // 素材名称
    string node_id             = 5;  // 所属目录id
    string asset_media_path    = 6;  // 素材media路径， 依'/'分割，不包括game_code根目录； 若为空，默认放在根目录
}

// 上传素材至媒体上报, POST, /api/v1/material_display/upload_to_channel
message UploadToChannelReq {
    repeated UploadAsset assets = 1;  // 上传素材id
    string asset_media_path     = 2;  // 素材media路径， 依'/'分割，不包括game_code根目录； 若为空，默认放在根目录
}

message UploadToChannelRsp {
    aix.Result result = 1;  // 返回结果
}

// 拉取用户上传的媒体素材目录列表, POST, /api/v1/material_display/media_directory_list
message MediaDirectoryListReq {
    string parent_id = 1;  // 父目录id
    uint32 offset    = 2;  // 分页偏移量
    uint32 limit     = 3;  // 最大获取数量
}

//返回的目录数据信息
message MediaDirectory {
    string id                     = 1;   // 目录id
    string name                   = 2;   // 目录名称
    string parent_id              = 3;   // 父目录id
    string parent_name            = 4;   // 父目录名称
    string create_date            = 5;   // 创建日期
    string update_date            = 6;   // 更新日期（索引）
    uint32 direct_child_count     = 7;   // 当前目录包含子目录和元素数量（不递归）
    uint32 total_leaf_count       = 8;   // 当前目录下包含素材数量（递归）
    uint32 direct_directory_count = 9;   // 当前目录下子目录数量（不递归）
    string full_path_name         = 10;  // 当前目录全路径名称
    string full_path_id           = 11;  // 当前目录全路径id
    string directory_id           = 12;  // arthub目录id
    string directory_name         = 13;  // arthub目录名称
}

message MediaDirectoryListRsp {
    aix.Result result            = 1;  // 返回结果
    repeated MediaDirectory dirs = 2;  // 目录列表
    uint64 total                 = 3;  // 当前目录下目录总数
}

// 获取网盘token信息, POST, /api/v1/material_display/get_depot_token
message GetDepotTokenReq {
    repeated string game_code_list = 1;  // game_code
}

message DepotGoogleDrive {
    bool is_oauth2_grant = 1; // 是否是oauth2授权， 默认为service account
}

message DepotExtra {
    DepotGoogleDrive google_drive = 1;
}

message DepotInfo {
    string     depot_id               = 1;   // depot id
    string     depot_name             = 2;   // depot名称
    string     public_token           = 3;   // token信息
    string     game_code              = 4;   // game_code
    string     game_name              = 5;   // game_code名称
    string     arthub_code            = 6;   // arthub_code名称
    int64      type                   = 7;   // 默认 1表示arthub，2表示google drive
    string     google_service_account = 8;   // google drive账户鉴权信息 (service account 或者 oauth2)
    string     cloud_drive_status     = 9;   // 网盘状态，为空或pending-待接入， initializing - 初始化中， active-正常使用中， unauthorized - 授权失效
    DepotExtra extra                  = 10;
}

message GetDepotTokenRsp {
    aix.Result result        = 1;  // 返回结果
    repeated DepotInfo items = 2;  // depot信息列表
}

message GenerateArthubTempDownloadUrlReqItem {
    string asset_id      = 1;  // 素材id列表
    string download_name = 2;  // 下载文件重命名，默认为存储文件名
    string content_disposition = 3;
}

// 获取arthub素材临时下载url, POST, /api/v1/material_display/generate_arthub_temp_download_url
message GenerateArthubTempDownloadUrlReq {
    repeated GenerateArthubTempDownloadUrlReqItem items = 1;  // 素材id列表
    string depot_code                                   = 2;  // 对应指定game_code
}

message GenerateArthubTempDownloadUrlSucceedItem {
    string asset_id   = 1;  // 文件在S3上的路径
    string signed_url = 2;  // 签名后的url
    uint64 expire     = 3;  // signed_url超时时间
}

message GenerateArthubTempDownloadUrlFailedItem {
    string asset_id = 1;  // 文件在S3上的路径
    string message  = 2;  // 失败原因
}

message GenerateArthubTempDownloadUrlRsp {
    aix.Result result                                               = 1;  // 返回结果
    repeated GenerateArthubTempDownloadUrlSucceedItem succeed_items = 2;  // 成功生成的arthub临时下载url
    repeated GenerateArthubTempDownloadUrlFailedItem failed_items   = 3;  // 生成arthub临时下载url失败的素材列表信息
}

// 获取arthub gamecode信息, POST, /api/v1/material_display/list_depot
message ListDepotReq {
    uint64 offset = 1;  // 偏移量
    uint64 limit  = 2;  // 每次请求最大数量, 默认返回10 条数据
}

message ListDepotInfo {
    string depot_id   = 1;  // depot id
    string depot_name = 2;  // depot名称
    string game_code  = 3;  // game_code
    string game_name  = 4;  // game_code名称
}

message ListDepotRsp {
    aix.Result result            = 1;  // 返回结果
    repeated ListDepotInfo items = 2;  // depot信息列表
    uint64 total                 = 3;  // gamecode总数
}

// 上传素材上报, POST, /api/v1/material_display/upload_to_arthub
message UploadToArthubReq {
    repeated string asset_id_list        = 1;  // 上传素材id
    string game_code                     = 2;  // 素材game_code，即素材来源
    bool is_meta                         = 3;  // 是否提供素材详情信息，false: 无需提供素材详细信息；true: 提供素材详情信息
    repeated UploadToArthubReqMeta metas = 4;  // 素材详情信息
    string aix_uploader                  = 5;  // aix平台的上传人
}
message UploadToArthubReqMeta {
    string asset_id = 1;  // 上传素材id
    uint64 width    = 2;  // 宽
    uint64 high     = 3;  // 高
    string duration = 4;  // 时长（视频类）
}

message UploadToArthubRsp {
    aix.Result result                 = 1;  // 返回结果
    repeated string succeed_asset_ids = 2;  // 成功上报asset信息的素材id
    repeated string failed_asset_ids  = 3;  // 成功上报asset信息的素材id
}

//获取用户上传的素材列表请求, POST, /api/v1/material_display/media_material_list
message MediaMaterialListReq {
    uint32 offset                = 1;   // 起始偏移
    uint32 count                 = 2;   // 拉取数量
    uint32 format_type           = 3;   // 是否类型，0-全部，1-视频，2-图片
    uint32 media                 = 4;   // 1-google 2-facebook
    string game_code             = 5;   // game_code
    string account_id            = 6;   // 上传账号
    uint32 campaign_sub_type     = 7;   // campaign sub_type: 具体值查看枚举: google_advertise.proto CampaignSubType
    string keyword               = 8;   // 素材名称模糊搜索关键字
    repeated string asset_names  = 9;   // 素材名称批量精确搜索，若指定asset_names，则不分页
    repeated string asset_ratios = 10;  // 需要筛选的素材比例, 不填默认不进行筛选
}

// 用户上传的素材元数据
message MediaMaterialMeta {
    string asset_id                = 1;   // 素材索引
    string name                    = 2;   // 素材名称
    uint32 status                  = 3;   // 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
    string upline_date             = 4;   // 上线日期
    string offline_date            = 5;   // 下线日期
    uint32 online_days             = 6;   // 在线天数
    string format                  = 7;   // 素材格式
    string duration                = 8;   // 素材时长
    string preview_url             = 9;   // 素材预览url
    string create_date             = 10;  // 创建时间
    string origin_url              = 11;  // arthub的originUrl
    MediaMaterialMetaDetail detail = 12;  // 素材详情
    string resource_name           = 13;  // resource_name
    string cover_hash              = 14;  // 封面hash
    int32 asset_status             = 15;  // 素材在仓库状态, 1-正常, 2-回收站, 3-删除
    int32 online_status            = 16;  // 素材曝光状态
    string online_date             = 17;  // 素材第一次曝光时间
    string asset_ratio             = 18;  // 素材宽高比, 精确到小数点后2位, 如1.00
}

// 素材详情
message MediaMaterialMetaDetail {
    uint64 size                = 1;   // 素材大小
    string update_date         = 2;   // 更新日期
    string creator             = 3;   // 素材创建者
    string updater             = 4;   // 素材更新者
    string manual_first_label  = 5;   // 人工一级标签
    string manual_second_label = 6;   // 人工二级标签
    uint32 width               = 7;   // 宽
    uint32 high                = 8;   // 高
    string frame_rate          = 9;   // 帧率（视频类）
    string aspect_ratio        = 10;  // 比例（视频类）
    string bit_rate            = 11;  // 视频比特率
    string compression_format  = 12;  // 视频压缩格式
    string robot_first_label   = 13;  // 第1层机器标签
    string robot_second_label  = 14;  // 第2层机器标签
    string cover               = 15;  // 封面
}

message MediaMaterialListRsp {
    aix.Result result                    = 1;  // 返回结果
    repeated MediaMaterialMeta materials = 2;  // 用户素材列表
    uint32 total                         = 3;  // 素材总数
}

// 获取其他信息, POST, /api/v1/material_display/get_ext_info
message GetExtInfoReq {
}

message GetExtInfoRsp {
    aix.Result result          = 1;  // 返回结果
    repeated string label_list = 2;  // 一级标签下拉列表
}
// 通过目录获取媒体列表请求, POST, /api/v1/material_display/media_material_by_directory_id
message MediaMaterialByDirectoryIdReq {
    uint64 uid                = 1;   // 保留字段
    string uuid               = 2;   // 前端生成随机ID
    uint32 offset             = 3;   // 起始偏移
    uint32 count              = 4;   // 拉取数量
    uint32 is_filter_status   = 5;   // 是否按照状态过滤，0-否，1-是
    uint32 status             = 6;   // 过滤状态，is_filter_status=1生效 0-未上传；1-正在上传；2-上传成功；3-上传失败
    string directory_id       = 7;   // 目录ID
    uint32 upload_state       = 8;   // (已废弃) 是否上传至广告库，0-否，1-是
    uint32 with_detail        = 9;   // 是否需要拉取详情 0-不需要 1-需要
    uint32 online_status      = 10;  // 在线状态筛选
    bool filter_online_status = 11;  // 是否过滤在线状态
}
// 素材元数据
message MediaMaterialByDirectoryIdMeta {
    string asset_id          = 1;   // 素材索引
    string name              = 2;   // 素材名称
    uint32 status            = 3;   // 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
    string upline_date       = 4;   // 上线日期
    string offline_date      = 5;   // 下线日期
    uint32 online_days       = 6;   // 在线天数
    string formate           = 7;   // 素材格式
    string duration          = 8;   // 素材时长
    string preview_url       = 9;   // 素材预览url
    string create_date       = 10;  // 创建时间
    MaterialExt material_ext = 11;  // 素材详情
    int32 asset_status       = 12;  // 素材在仓库状态, 1-正常, 2-回收站, 3-删除
    int32 online_status      = 13;  // 素材曝光状态
    string online_date       = 14;  // 素材第一次曝光时间
}

message MediaMaterialByDirectoryIdRsp {
    aix.Result result                                 = 1;  // 返回结果
    repeated MediaMaterialByDirectoryIdMeta materials = 2;  // 素材列表
    uint32 total                                      = 3;  // 素材总数
}

// 根据ID(resource_name)列表查询素材名称列表, POST, /api/v1/material_display/media_material_name_list
message MediaMaterialNameListReq {
    uint32 format_type             = 1;  // 是否类型，0-全部，1-视频，2-图片
    uint32 media                   = 2;  // 1-google 2-facebook
    string game_code               = 3;  // game_code
    string account_id              = 4;  // 上传账号
    uint32 campaign_sub_type       = 5;  // campaign sub_type: 具体值查看枚举: google_advertise.proto CampaignSubType
    repeated string resource_names = 6;  // 素材ID(resource_name)列表
}

message MaterialAssetNameInfo {
    string resource_name           = 1;   // 素材ID(resource_name)
    string asset_id                = 2;   // 素材唯一id
    string asset_name              = 3;   // 素材名称
    string format                  = 4;   // 素材格式
    string duration                = 5;   // 素材时长
    string preview_url             = 6;   // 素材预览url
    MediaMaterialMetaDetail detail = 7;   // 素材详情
    string cover_hash              = 8;   // 素材封面hash, tiktok中即为封面image resource name
    string asset_ratio             = 18;  // 素材宽高比, 精确到小数点后2位, 如1.00
}

message MediaMaterialNameListRsp {
    aix.Result result                               = 1;  // 返回结果
    repeated MaterialAssetNameInfo asset_name_infos = 2;  // 素材信息列表
}

// 上传渠道任务相关 ----------
message UploadChannelInfo {
    uint32 channel    = 1;  // 参看common constant.MediaXXXX 枚举
    string media_path = 2;  // 渠道目录名称
    string media_directory_id = 3;  // [后台用，前端不传]渠道目录id
    string accounts   = 4;  // 上传账号, 多个账号用逗号分隔
    string language   = 5;  // 指定素材语言, 目前unity/applovin渠道有使用

    string   campaign_id       = 6;  // 指定上传到某个campaign, 目前applovin有使用
    string   creative_set_name = 7;  // creative set name, 目前applovin有使用
    string   creative_set_id   = 8;  // [后台用，前端不传]creative set id
    repeated string countries  = 9;  // 指定国家列表, 目前applovin有使用
}

// 增加上传任务, POST, /api/v1/material_display/add_upload_to_channel_task
message AddUploadToChannelTaskReq {
    string game_code                       = 1;
    string country                         = 2;
    repeated UploadChannelInfo to_channels = 3;  // 上传渠道配置信息
    repeated string asset_ids              = 4;  // 素材ID列表
    string creator                         = 5;  // 创建人
    string accounts                        = 6;  // 指定上传账号，用逗号给开 - deprecated
    uint32 notify_days                     = 7;  // 提醒天数，为0不提醒
    string offline_date                    = 8;  // 下线日期，为空不指定
    string language                        = 9;  // 指定素材语言, 目前unity渠道有使用 - deprecated
    int64 automatic_sync_task_rule_id      = 10; // 自动上传任务规则id(后台内部使用)
}

message AddUploadToChannelTaskRsp {
    aix.Result result = 1;  // 返回结果
}

// 取消正在上传的任务, POST, /api/v1/material_display/cancel_upload_to_channel_task
message CancelUploadToChannelTaskReq {
    string game_code         = 1;
    repeated string task_ids = 2;  // 任务ID列表
}

message CancelUploadToChannelTaskRsp {
    aix.Result result = 1;  // 返回结果
}

// 重启上传失败的任务, POST, /api/v1/material_display/resume_upload_to_channel_task
message ResumeUploadToChannelTaskReq {
    string game_code         = 1;
    repeated string task_ids = 2;  // 任务ID列表
}

message ResumeUploadToChannelTaskRsp {
    aix.Result result = 1;  // 返回结果
}

// 获取提示关键词, POST, /api/v1/material_display/get_keyword_list
message GetKeywordListReq {
    string prefix = 1;  // 前缀
    uint32 limit  = 2;  // 限制数量
}

message GetKeywordListRsp {
    aix.Result result               = 1;  // 返回结果
    repeated Suggestion Suggestions = 2;  // 关键词列表
}

message Suggestion {
    string keyword   = 1;
    uint32 frequency = 2;
}

// ----- 新标签体系 ----

message LabelOption {
    string label_name = 1;
    string options    = 2;
}

// 标签规则
message LabelRule {
    int64    id                = 1;  // 自增id
    string   game_code         = 2;
    string   rule              = 3;  // 规则名 如：RG3030
    int32    type              = 4;  // 规则类型，目前只有 2:前缀
    repeated AssetLabel labels = 5;  // 配置的标签
    string   creater           = 6;  // 创建者
    string   updater           = 7;  // 修改人
    string   create_time       = 8;
    string   update_time       = 9;
    repeated     LabelOption label_options = 10;
    string       impression_status         = 11;  // 曝光状态
    string       impression_date           = 12;  // 曝光日期
    string       offline_date              = 13;  // 下线日期
    MaterialMeta link_asset                = 14;  // 关联的素材信息-如果有
}

// 拉取标签规则, POST, /api/v1/material_display/get_label_rules
message GetLabelRulesReq {
    string   game_code             = 1;
    repeated string names          = 2;   // 名字匹配，或
    repeated AssetLabel labels     = 3;
    int32    label_search_type     = 4;   // 标签搜索方式, 0-默认(取并集), 1-取并集; 2-取交集
    string   impression_status     = 5;   // 非空时 过滤Published/Unpublished	
    string   start_impression_date = 6;   // 非空时 >=曝光日期20240101	
    string   end_impression_date   = 7;   // 非空时 <=曝光日期20240103
    int32    page                  = 8;   // 分页数，从0开始
    int32    page_size             = 9;   // 每页条数，最多100条
    string   start_offline_date    = 10;  // 非空时 >=下线日期20240101	
    string   end_offline_date      = 11;  // 非空时 <=下线日期20240103
}

message GetLabelRulesRsp {
    aix.Result result        = 1;  // 返回结果
    repeated LabelRule rules = 2;  // 标签规则列表
    uint32 total             = 3;  // 标签规则总数 
}

// 导出标签规则, POST, /api/v1/material_display/download_label_rules
message DownloadLabelRulesReq {
    string   game_code             = 1;
    repeated string names          = 2;  // 名字匹配，或
    repeated AssetLabel labels     = 3;
    int32    label_search_type     = 4;  // 标签搜索方式, 0-默认(取并集), 1-取并集; 2-取交集
    string   impression_status     = 5;  // 非空时 过滤Published/Unpublished	
    string   start_impression_date = 6;  // 非空时 >=曝光日期20240101	
    string   end_impression_date   = 7;  // 非空时 <=曝光日期20240103
    string   start_offline_date    = 8;  // 非空时 >=下线日期20240101	
    string   end_offline_date      = 9;  // 非空时 <=下线日期20240103
}

message DownloadLabelRulesRsp {
    aix.Result result   = 1;  // 返回结果
    string     cos_file = 2;  // 生成的下载文件cos地址
}

// 增加/更新标签规则, POST, /api/v1/material_display/add_label_rule
message AddLabelRuleReq {
    LabelRule rule = 1;
}

message AddLabelRuleRsp {
    aix.Result result        = 1;  // 返回结果
}

// 更新asset层级标签, POST, /api/v1/material_display/update_asset_level_labels
message UpdateAssetLevelLabelsReq {
    string   game_code         = 1;
    string   asset_name        = 2;
    repeated AssetLabel labels = 3;  // 配置的标签, 如果为空则清空
}

message UpdateAssetLevelLabelsRsp {
    aix.Result result        = 1;  // 返回结果
}

// 修改标签规则机器内容标签, POST, /api/v1/material_display/modify_rule_content_labels
message ModifyRuleContentLabelsReq {
    string   game_code         = 1;
    string   rule              = 2;  // 标签规则，如： V-123
    repeated AssetLabel add    = 3;  // 增加的内容标签列表
    repeated AssetLabel remove = 4;  // 删除内容标签列表
}

message ModifyRuleContentLabelsRsp {
    aix.Result result        = 1;  // 返回结果
}

// 修改标签规则二级标签, POST, /api/v1/material_display/modify_label_rule_second_label
message ModifyLabelRuleSecondLabelReq {
    string game_code        = 1;
    string serial_id        = 2;  // 标签规则serial id
    string first_label      = 3;  // 一级标签
    string second_label     = 4;  // 二级标签
    string new_second_label = 5;  // 新的二级标签, 当is_delete=false时必填
    bool   is_delete        = 6;  // 是否删除二级标签
}

message ModifyLabelRuleSecondLabelRsp {
    aix.Result result        = 1;  // 返回结果
}

// 删除标签规则, POST, /api/v1/material_display/delete_label_rule
message DeleteLabelRuleReq {
    LabelRule rule = 1;
}

message DeleteLabelRuleRsp {
    aix.Result result        = 1;  // 返回结果
}

// 拉取标签规则下的素材, POST, /api/v1/material_display/get_assets_by_label_rule
message GetAssetsByLabelRuleReq {
    string game_code = 1;
    int64  rule_id   = 2;  // 标签规则id
    int32  page      = 3;  // 分页数，从0开始
    int32  page_size = 4;  // 每页条数，最多100条
}

message GetAssetsByLabelRuleRsp {
    aix.Result result        = 1;  // 返回结果
    repeated MaterialMeta materials = 2;  // 素材列表
    uint32 total                    = 3;  // 素材总数 
}

// 渠道素材结构
message ChannelAsset {
	int64  id                 = 1;   // 自增ID
	int32  channel_type       = 2;   // 渠道类型, 1-google; 2-facebook
	string channel_account_id = 3;   // 渠道账号(account_id)
	string channel_asset_id   = 4;   // 渠道端ID(asset_id)
	string asset_name         = 5;   // 素材名称
	string youtube_id         = 6;   // youtube_id, google渠道使用
	string create_by          = 7;   // 创建人
	string create_time        = 8;   // 创建时间
	string update_by          = 9;   // 更新人
	string update_time        = 10;  // 更新时间
	string sdate              = 11;  // 从realtime表同步过来的日期

    repeated AssetLabel labels = 12;  // 素材自己的标签
    string   impression_status = 13;  // 曝光状态
    string   impression_date   = 14;  // 曝光日期
}

// 拉取标签规则下的渠道素材, POST, /api/v1/material_display/get_channel_assets_by_label_rule
message GetChannelAssetsByLabelRuleReq {
    string game_code = 1;
    int64  rule_id   = 2;  // 标签规则id
    int32  page      = 3;  // 分页数，从0开始
    int32  page_size = 4;  // 每页条数，最多100条
}

message GetChannelAssetsByLabelRuleRsp {
    aix.Result result        = 1;  // 返回结果
    repeated ChannelAsset assets = 2;  // 素材列表
    uint32 total                 = 3;  // 素材总数 
}

// 提交标签规则批量打标任务 POST, /api/v1/material_display/add_label_rule_task
message AddLabelRuleTaskReq {
    string game_code = 1;
    string cos_file  = 2;
}

message AddLabelRuleTaskRsp {
    aix.Result result = 1;  // 返回结果
}

// V2提交标签规则批量打标任务(支持serial和asset层级) POST, /api/v1/material_display/add_label_rule_task_v2
message AddLabelRuleTaskV2Req {
    string game_code = 1;
    string cos_file  = 2;
}

message AddLabelRuleTaskV2Rsp {
    aix.Result result = 1;  // 返回结果
}

message SyncTaskDir {
    string id              = 1;  // 目录id
    string name            = 2;  // 目录名称
    string full_path_name  = 3;  // 目录全路径名称
    bool   include_sub_dir = 4;  // 是否包括子目录
}

message SyncTaskMedia {
    int32  channel  = 1;  // 渠道
    string accounts = 2;  // 指定上传账号，用逗号分割多个账号
    string language = 3;  // 语言, 目前untiy需要指定
}

message AutomaticSyncTaskRule {
    int64    id                      = 1;   // 自增ID，不为0时表示更新
    string   game_code               = 2;   // 游戏code
    string   name                    = 3;   // 规则名称
    int32    status                  = 4;   // 规则状态, 0-禁用, 1-启用
    int32    asset_type              = 5;   // 指定素材类型, 0-不限， 1-视频， 2-图片, 3-html
    repeated SyncTaskDir dirs        = 6;   // 目录列表
    repeated SyncTaskMedia   medias  = 7;   // 上传渠道列表
    string   end_date                = 8;   // 结束日期, 格式: ********, 为空表示永久
    string   create_user             = 9;   // 创建人
    string   create_time             = 10;  // 创建时间
    string   update_user             = 11;  // 更新人
    string   update_time             = 12;  // 更新时间
    string   status_change_time      = 13;  // 状态变更时间
    string   start_cloud_upload_time = 14;  // 格式: 2024-01-01 01:02:03  表示自动化规则 处理那些 > start_cloud_upload_time 的素材
}

// 增加/更新自动化上传任务规则 POST, /api/v1/material_display/add_automatic_sync_task_rule
message AddAutomaticSyncTaskRuleReq {
    AutomaticSyncTaskRule task_rule = 1;
}

message AddAutomaticSyncTaskRuleRsp {
    aix.Result result = 1;  // 返回结果
    int64      id     = 2;  // 规则id
}

// 检查自动化上传任务规则名称是否重复 POST, /api/v1/material_display/check_automatic_sync_task_rule_name
message CheckAutomaticSyncTaskRuleNameReq {
    string game_code = 1;  // 游戏code
    int64  id        = 2;  // 规则id
    string name      = 3;  // 规则名称
}

message CheckAutomaticSyncTaskRuleNameRsp {
    aix.Result result = 1;  // 返回结果
    bool duplicate = 2;  // 是否重复
}

// 拉取自动化上传任务规则 POST, /api/v1/material_display/get_automatic_sync_task_rule
message GetAutomaticSyncTaskRuleReq {
    string game_code = 1;
    int64  id        = 2;  // 任务规则id，不为0只取这一个
    string name      = 3;  // 非空时名字搜索
    int32  page      = 4;  // 分页数，从0开始
    int32  page_size = 5;  // 每页条数，最多100条
}

message GetAutomaticSyncTaskRuleRsp {
    aix.Result result                           = 1;  // 返回结果
    repeated   AutomaticSyncTaskRule task_rules = 2;  // 规则列表
    uint32     total                            = 3;  // 总数
}

// 更新自动化上传任务规则状态 POST, /api/v1/material_display/change_automatic_sync_task_rule_status
message changeAutomaticSyncTaskRuleStatusReq {
    string game_code = 1;
    int64  id        = 2;  // 规则id
    int32  status    = 3;  // 规则状态, 0-禁用, 1-启用
}

message changeAutomaticSyncTaskRuleStatusRsp {
    aix.Result result = 1;  // 返回结果
}

// 删除自动化上传任务规则 POST, /api/v1/material_display/delete_automatic_sync_task_rule
message DeleteAutomaticSyncTaskRuleReq {
    string game_code = 1;
    int64  id        = 2;  // 规则id
}

message DeleteAutomaticSyncTaskRuleRsp {
    aix.Result result = 1;  // 返回结果
}

message ArthubAuth {
    string arthub_code  = 1;
    string public_token = 2;
}

message Oauth2Config {
    string client_id     = 1;
    string client_secret = 2;
    string redirect_uri  = 3;
    string auth_uri      = 4;
    string token_uri     = 5;
}

message Oauth2Token {
    string access_token  = 1;
    string refresh_token = 2;
    string token_type    = 3;
    string expiry        = 4;
}

message GoogleDriveAuth {
    Oauth2Config config = 1;
    Oauth2Token  token  = 2;
}

// 网盘接入授权 POST /api/v1/material_display/cloud_drive_grant
message CloudDriveGrantReq {
    string          game_code         = 1;  // 游戏code
    int32           type              = 2;  // 网盘类型， 参看utils.GAME_DEPOT_TYPE_XXX
    string          root_id           = 3;  // 网盘根目录id
    ArthubAuth      arthub_auth       = 4;  // arthub授权
    GoogleDriveAuth google_drive_auth = 5;  // google drive授权
}

message CloudDriveGrantRsp {
    aix.Result result = 1;  // 返回结果
}