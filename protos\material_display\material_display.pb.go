// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: material_display/material_display.proto

package material_display

import (
	aix "e.coding.intlgame.com/ptc/aix-backend/protos/aix"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MaterialStatus int32

const (
	MaterialStatus_NOT_UPLOAD    MaterialStatus = 0 // 未上传
	MaterialStatus_UPLOADING     MaterialStatus = 1 // 正在上传
	MaterialStatus_UPLOADED      MaterialStatus = 2 // 上传成功
	MaterialStatus_UPLOAD_FAILED MaterialStatus = 3 // 上传成功
)

// Enum value maps for MaterialStatus.
var (
	MaterialStatus_name = map[int32]string{
		0: "NOT_UPLOAD",
		1: "UPLOADING",
		2: "UPLOADED",
		3: "UPLOAD_FAILED",
	}
	MaterialStatus_value = map[string]int32{
		"NOT_UPLOAD":    0,
		"UPLOADING":     1,
		"UPLOADED":      2,
		"UPLOAD_FAILED": 3,
	}
)

func (x MaterialStatus) Enum() *MaterialStatus {
	p := new(MaterialStatus)
	*p = x
	return p
}

func (x MaterialStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MaterialStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_material_display_material_display_proto_enumTypes[0].Descriptor()
}

func (MaterialStatus) Type() protoreflect.EnumType {
	return &file_material_display_material_display_proto_enumTypes[0]
}

func (x MaterialStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MaterialStatus.Descriptor instead.
func (MaterialStatus) EnumDescriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{0}
}

// 素材列表请求, POST, /api/v1/material_display/material_list
type MaterialListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                                                            // 保留字段
	Uuid               string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`                                                           // 前端生成随机ID
	Offset             uint32 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`                                                      // 起始偏移
	Count              uint32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                                                        // 拉取数量
	IsFilterStatus     uint32 `protobuf:"varint,5,opt,name=is_filter_status,json=isFilterStatus,proto3" json:"is_filter_status,omitempty"`              // 是否按照状态过滤，0-否，1-是
	Status             uint32 `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`                                                      // 过滤状态，is_filter_status=1生效 0-未上传；1-正在上传；2-上传成功；3-上传失败
	DirectoryId        string `protobuf:"bytes,7,opt,name=directory_id,json=directoryId,proto3" json:"directory_id,omitempty"`                          // 目录ID
	UploadState        uint32 `protobuf:"varint,8,opt,name=upload_state,json=uploadState,proto3" json:"upload_state,omitempty"`                         // (已废弃) 是否上传至广告库，0-否，1-是
	WithDetail         uint32 `protobuf:"varint,9,opt,name=with_detail,json=withDetail,proto3" json:"with_detail,omitempty"`                            // 是否需要拉取详情 0-不需要 1-需要
	OnlineStatus       uint32 `protobuf:"varint,10,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"`                     // 在线状态筛选, 0表示不需要筛选
	FilterOnlineStatus bool   `protobuf:"varint,11,opt,name=filter_online_status,json=filterOnlineStatus,proto3" json:"filter_online_status,omitempty"` // 是否过滤在线状态
}

func (x *MaterialListReq) Reset() {
	*x = MaterialListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialListReq) ProtoMessage() {}

func (x *MaterialListReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialListReq.ProtoReflect.Descriptor instead.
func (*MaterialListReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{0}
}

func (x *MaterialListReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *MaterialListReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MaterialListReq) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *MaterialListReq) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *MaterialListReq) GetIsFilterStatus() uint32 {
	if x != nil {
		return x.IsFilterStatus
	}
	return 0
}

func (x *MaterialListReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MaterialListReq) GetDirectoryId() string {
	if x != nil {
		return x.DirectoryId
	}
	return ""
}

func (x *MaterialListReq) GetUploadState() uint32 {
	if x != nil {
		return x.UploadState
	}
	return 0
}

func (x *MaterialListReq) GetWithDetail() uint32 {
	if x != nil {
		return x.WithDetail
	}
	return 0
}

func (x *MaterialListReq) GetOnlineStatus() uint32 {
	if x != nil {
		return x.OnlineStatus
	}
	return 0
}

func (x *MaterialListReq) GetFilterOnlineStatus() bool {
	if x != nil {
		return x.FilterOnlineStatus
	}
	return false
}

// 素材元数据
type MaterialMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId             string        `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                                         // 素材索引
	Name                string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                              // 素材名称
	Status              uint32        `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`                                                         // 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
	UplineDate          string        `protobuf:"bytes,4,opt,name=upline_date,json=uplineDate,proto3" json:"upline_date,omitempty"`                                // 上线日期
	OfflineDate         string        `protobuf:"bytes,5,opt,name=offline_date,json=offlineDate,proto3" json:"offline_date,omitempty"`                             // 素材下线时间
	OnlineDays          uint32        `protobuf:"varint,6,opt,name=online_days,json=onlineDays,proto3" json:"online_days,omitempty"`                               // 在线天数
	Formate             string        `protobuf:"bytes,7,opt,name=formate,proto3" json:"formate,omitempty"`                                                        // 素材格式
	Duration            string        `protobuf:"bytes,8,opt,name=duration,proto3" json:"duration,omitempty"`                                                      // 素材时长
	PreviewUrl          string        `protobuf:"bytes,9,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`                                // 素材预览url
	CreateDate          string        `protobuf:"bytes,10,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`                               // 网盘上的创建时间(即素材上传到网盘的时间)
	MaterialExt         *MaterialExt  `protobuf:"bytes,11,opt,name=material_ext,json=materialExt,proto3" json:"material_ext,omitempty"`                            // 素材详情
	AssetStatus         int32         `protobuf:"varint,12,opt,name=asset_status,json=assetStatus,proto3" json:"asset_status,omitempty"`                           // 素材在仓库状态, 1-正常, 2-回收站, 3-删除
	OnlineStatus        int32         `protobuf:"varint,13,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"`                        // 素材曝光状态, 0-未上线, 1-上线, 2-下架
	OnlineDate          string        `protobuf:"bytes,14,opt,name=online_date,json=onlineDate,proto3" json:"online_date,omitempty"`                               // 素材第一次曝光时间
	FullPathId          string        `protobuf:"bytes,15,opt,name=full_path_id,json=fullPathId,proto3" json:"full_path_id,omitempty"`                             // 素材路径ID
	FullPathName        string        `protobuf:"bytes,16,opt,name=full_path_name,json=fullPathName,proto3" json:"full_path_name,omitempty"`                       // 素材路径名称
	AixUploader         string        `protobuf:"bytes,17,opt,name=aix_uploader,json=aixUploader,proto3" json:"aix_uploader,omitempty"`                            // aix平台的素材上传者
	GoogleOnlineState   int32         `protobuf:"varint,18,opt,name=google_online_state,json=googleOnlineState,proto3" json:"google_online_state,omitempty"`       // Google渠道上线状态, 0-从未上线, 1-上线, 2-下线
	GoogleOnlineTime    string        `protobuf:"bytes,19,opt,name=google_online_time,json=googleOnlineTime,proto3" json:"google_online_time,omitempty"`           // Google渠道上线时间
	GoogleOfflineTime   string        `protobuf:"bytes,20,opt,name=google_offline_time,json=googleOfflineTime,proto3" json:"google_offline_time,omitempty"`        // Google渠道下线时间
	FacebookOnlineState int32         `protobuf:"varint,21,opt,name=facebook_online_state,json=facebookOnlineState,proto3" json:"facebook_online_state,omitempty"` // Facebook渠道上线状态, 0-从未上线, 1-上线, 2-下线
	FacebookOnlineTime  string        `protobuf:"bytes,22,opt,name=facebook_online_time,json=facebookOnlineTime,proto3" json:"facebook_online_time,omitempty"`     // Facebook渠道上线时间
	FacebookOfflineTime string        `protobuf:"bytes,23,opt,name=facebook_offline_time,json=facebookOfflineTime,proto3" json:"facebook_offline_time,omitempty"`  // Facebook渠道下线时间
	Number              string        `protobuf:"bytes,24,opt,name=number,proto3" json:"number,omitempty"`                                                         // 素材编号. 如UA00012
	ProjectName         string        `protobuf:"bytes,25,opt,name=project_name,json=projectName,proto3" json:"project_name,omitempty"`                            // 项目名称. 如PUBGM
	AssetFormat         string        `protobuf:"bytes,26,opt,name=asset_format,json=assetFormat,proto3" json:"asset_format,omitempty"`                            // 素材格式. 如图片, 视频等.
	AssetSize           string        `protobuf:"bytes,27,opt,name=asset_size,json=assetSize,proto3" json:"asset_size,omitempty"`                                  // 素材尺寸. 如横版素材(horizontal), 竖版素材(vertical), 方版素材(square ratio)等.
	Language            string        `protobuf:"bytes,28,opt,name=language,proto3" json:"language,omitempty"`                                                     // 语言. 如KR.
	AgentName           string        `protobuf:"bytes,29,opt,name=agent_name,json=agentName,proto3" json:"agent_name,omitempty"`                                  // 代理名称. 如Tencent.
	SimpleAssetName     string        `protobuf:"bytes,30,opt,name=simple_asset_name,json=simpleAssetName,proto3" json:"simple_asset_name,omitempty"`              // 简洁版素材名称. 如0001选择时装战斗
	PlayingMethod       string        `protobuf:"bytes,31,opt,name=playing_method,json=playingMethod,proto3" json:"playing_method,omitempty"`                      // 游戏内玩法. 如时装.
	AssetForm           string        `protobuf:"bytes,32,opt,name=asset_form,json=assetForm,proto3" json:"asset_form,omitempty"`                                  // 素材表现形式. 如包装剪辑.
	AssetStage          string        `protobuf:"bytes,33,opt,name=asset_stage,json=assetStage,proto3" json:"asset_stage,omitempty"`                               // 素材对应阶段. 如CBT, TBT等.
	DeliveryDate        string        `protobuf:"bytes,34,opt,name=delivery_date,json=deliveryDate,proto3" json:"delivery_date,omitempty"`                         // 交付日期. 如211116.
	Custom1             string        `protobuf:"bytes,35,opt,name=custom1,proto3" json:"custom1,omitempty"`                                                       // 自定义字段1
	Custom2             string        `protobuf:"bytes,36,opt,name=custom2,proto3" json:"custom2,omitempty"`                                                       // 自定义字段2
	Custom3             string        `protobuf:"bytes,37,opt,name=custom3,proto3" json:"custom3,omitempty"`                                                       // 自定义字段3
	AuditId             string        `protobuf:"bytes,38,opt,name=AuditId,proto3" json:"AuditId,omitempty"`                                                       // 审核记录id
	AuditStatus         int32         `protobuf:"varint,39,opt,name=AuditStatus,proto3" json:"AuditStatus,omitempty"`                                              // 审核状态
	Labels              []*AssetLabel `protobuf:"bytes,40,rep,name=labels,proto3" json:"labels,omitempty"`                                                         // 素材自己的标签
	ImpressionStatus    string        `protobuf:"bytes,41,opt,name=impression_status,json=impressionStatus,proto3" json:"impression_status,omitempty"`             // 曝光状态
	ImpressionDate      string        `protobuf:"bytes,42,opt,name=impression_date,json=impressionDate,proto3" json:"impression_date,omitempty"`                   // 曝光日期
	SyncTime            string        `protobuf:"bytes,43,opt,name=sync_time,json=syncTime,proto3" json:"sync_time,omitempty"`                                     // 同步时间
	AnotherName         string        `protobuf:"bytes,44,opt,name=another_name,json=anotherName,proto3" json:"another_name,omitempty"`                            // 其他名字，一些接口需要
	SyncMediaList       []*SyncMedia  `protobuf:"bytes,45,rep,name=sync_media_list,json=syncMediaList,proto3" json:"sync_media_list,omitempty"`                    // 已经上传的渠道列表
}

func (x *MaterialMeta) Reset() {
	*x = MaterialMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialMeta) ProtoMessage() {}

func (x *MaterialMeta) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialMeta.ProtoReflect.Descriptor instead.
func (*MaterialMeta) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{1}
}

func (x *MaterialMeta) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *MaterialMeta) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MaterialMeta) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MaterialMeta) GetUplineDate() string {
	if x != nil {
		return x.UplineDate
	}
	return ""
}

func (x *MaterialMeta) GetOfflineDate() string {
	if x != nil {
		return x.OfflineDate
	}
	return ""
}

func (x *MaterialMeta) GetOnlineDays() uint32 {
	if x != nil {
		return x.OnlineDays
	}
	return 0
}

func (x *MaterialMeta) GetFormate() string {
	if x != nil {
		return x.Formate
	}
	return ""
}

func (x *MaterialMeta) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

func (x *MaterialMeta) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *MaterialMeta) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *MaterialMeta) GetMaterialExt() *MaterialExt {
	if x != nil {
		return x.MaterialExt
	}
	return nil
}

func (x *MaterialMeta) GetAssetStatus() int32 {
	if x != nil {
		return x.AssetStatus
	}
	return 0
}

func (x *MaterialMeta) GetOnlineStatus() int32 {
	if x != nil {
		return x.OnlineStatus
	}
	return 0
}

func (x *MaterialMeta) GetOnlineDate() string {
	if x != nil {
		return x.OnlineDate
	}
	return ""
}

func (x *MaterialMeta) GetFullPathId() string {
	if x != nil {
		return x.FullPathId
	}
	return ""
}

func (x *MaterialMeta) GetFullPathName() string {
	if x != nil {
		return x.FullPathName
	}
	return ""
}

func (x *MaterialMeta) GetAixUploader() string {
	if x != nil {
		return x.AixUploader
	}
	return ""
}

func (x *MaterialMeta) GetGoogleOnlineState() int32 {
	if x != nil {
		return x.GoogleOnlineState
	}
	return 0
}

func (x *MaterialMeta) GetGoogleOnlineTime() string {
	if x != nil {
		return x.GoogleOnlineTime
	}
	return ""
}

func (x *MaterialMeta) GetGoogleOfflineTime() string {
	if x != nil {
		return x.GoogleOfflineTime
	}
	return ""
}

func (x *MaterialMeta) GetFacebookOnlineState() int32 {
	if x != nil {
		return x.FacebookOnlineState
	}
	return 0
}

func (x *MaterialMeta) GetFacebookOnlineTime() string {
	if x != nil {
		return x.FacebookOnlineTime
	}
	return ""
}

func (x *MaterialMeta) GetFacebookOfflineTime() string {
	if x != nil {
		return x.FacebookOfflineTime
	}
	return ""
}

func (x *MaterialMeta) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *MaterialMeta) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *MaterialMeta) GetAssetFormat() string {
	if x != nil {
		return x.AssetFormat
	}
	return ""
}

func (x *MaterialMeta) GetAssetSize() string {
	if x != nil {
		return x.AssetSize
	}
	return ""
}

func (x *MaterialMeta) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *MaterialMeta) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *MaterialMeta) GetSimpleAssetName() string {
	if x != nil {
		return x.SimpleAssetName
	}
	return ""
}

func (x *MaterialMeta) GetPlayingMethod() string {
	if x != nil {
		return x.PlayingMethod
	}
	return ""
}

func (x *MaterialMeta) GetAssetForm() string {
	if x != nil {
		return x.AssetForm
	}
	return ""
}

func (x *MaterialMeta) GetAssetStage() string {
	if x != nil {
		return x.AssetStage
	}
	return ""
}

func (x *MaterialMeta) GetDeliveryDate() string {
	if x != nil {
		return x.DeliveryDate
	}
	return ""
}

func (x *MaterialMeta) GetCustom1() string {
	if x != nil {
		return x.Custom1
	}
	return ""
}

func (x *MaterialMeta) GetCustom2() string {
	if x != nil {
		return x.Custom2
	}
	return ""
}

func (x *MaterialMeta) GetCustom3() string {
	if x != nil {
		return x.Custom3
	}
	return ""
}

func (x *MaterialMeta) GetAuditId() string {
	if x != nil {
		return x.AuditId
	}
	return ""
}

func (x *MaterialMeta) GetAuditStatus() int32 {
	if x != nil {
		return x.AuditStatus
	}
	return 0
}

func (x *MaterialMeta) GetLabels() []*AssetLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *MaterialMeta) GetImpressionStatus() string {
	if x != nil {
		return x.ImpressionStatus
	}
	return ""
}

func (x *MaterialMeta) GetImpressionDate() string {
	if x != nil {
		return x.ImpressionDate
	}
	return ""
}

func (x *MaterialMeta) GetSyncTime() string {
	if x != nil {
		return x.SyncTime
	}
	return ""
}

func (x *MaterialMeta) GetAnotherName() string {
	if x != nil {
		return x.AnotherName
	}
	return ""
}

func (x *MaterialMeta) GetSyncMediaList() []*SyncMedia {
	if x != nil {
		return x.SyncMediaList
	}
	return nil
}

type MaterialListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MaterialMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 素材列表
	Total     uint32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 素材总数
}

func (x *MaterialListRsp) Reset() {
	*x = MaterialListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialListRsp) ProtoMessage() {}

func (x *MaterialListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialListRsp.ProtoReflect.Descriptor instead.
func (*MaterialListRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{2}
}

func (x *MaterialListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *MaterialListRsp) GetMaterials() []*MaterialMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

func (x *MaterialListRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取素材详情, POST, /api/v1/material_display/get_material_info
type GetMaterialInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid     uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Uuid    string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	AssetId string `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // 唯一索引
}

func (x *GetMaterialInfoReq) Reset() {
	*x = GetMaterialInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaterialInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaterialInfoReq) ProtoMessage() {}

func (x *GetMaterialInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaterialInfoReq.ProtoReflect.Descriptor instead.
func (*GetMaterialInfoReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{3}
}

func (x *GetMaterialInfoReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GetMaterialInfoReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *GetMaterialInfoReq) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

// 素材通用数据
type Universal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Format     string `protobuf:"bytes,1,opt,name=format,proto3" json:"format,omitempty"`                            // 文件格式
	Size       uint64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`                               // 文件大小
	CreateTime string `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`  // 创建时间
	Updater    string `protobuf:"bytes,4,opt,name=updater,proto3" json:"updater,omitempty"`                          // 更新者
	UpdateTime string `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`  // 更新时间
	Creator    string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`                          // 创建者
	Cover      string `protobuf:"bytes,7,opt,name=cover,proto3" json:"cover,omitempty"`                              // 封面
	FormatType int32  `protobuf:"varint,8,opt,name=format_type,json=formatType,proto3" json:"format_type,omitempty"` // 素材类型
}

func (x *Universal) Reset() {
	*x = Universal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Universal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Universal) ProtoMessage() {}

func (x *Universal) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Universal.ProtoReflect.Descriptor instead.
func (*Universal) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{4}
}

func (x *Universal) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *Universal) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Universal) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Universal) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *Universal) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Universal) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *Universal) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *Universal) GetFormatType() int32 {
	if x != nil {
		return x.FormatType
	}
	return 0
}

// 素材标签数据
type Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ManualFirstLabel  []string `protobuf:"bytes,1,rep,name=manual_first_label,json=manualFirstLabel,proto3" json:"manual_first_label,omitempty"`    // 人工一级标签
	ManualSecondLabel []string `protobuf:"bytes,2,rep,name=manual_second_label,json=manualSecondLabel,proto3" json:"manual_second_label,omitempty"` // 人工二级标签
	RobotFirstLabel   []string `protobuf:"bytes,3,rep,name=robot_first_label,json=robotFirstLabel,proto3" json:"robot_first_label,omitempty"`       // 机器一级标签
	RobotSecondLabel  []string `protobuf:"bytes,4,rep,name=robot_second_label,json=robotSecondLabel,proto3" json:"robot_second_label,omitempty"`    // 机器二级标签
}

func (x *Label) Reset() {
	*x = Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{5}
}

func (x *Label) GetManualFirstLabel() []string {
	if x != nil {
		return x.ManualFirstLabel
	}
	return nil
}

func (x *Label) GetManualSecondLabel() []string {
	if x != nil {
		return x.ManualSecondLabel
	}
	return nil
}

func (x *Label) GetRobotFirstLabel() []string {
	if x != nil {
		return x.RobotFirstLabel
	}
	return nil
}

func (x *Label) GetRobotSecondLabel() []string {
	if x != nil {
		return x.RobotSecondLabel
	}
	return nil
}

// 素材统计数据
type Statistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TakeUsers uint64 `protobuf:"varint,1,opt,name=take_users,json=takeUsers,proto3" json:"take_users,omitempty"` // 取用人数
	TakeCount uint64 `protobuf:"varint,2,opt,name=take_count,json=takeCount,proto3" json:"take_count,omitempty"` // 取用次数
}

func (x *Statistics) Reset() {
	*x = Statistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Statistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Statistics) ProtoMessage() {}

func (x *Statistics) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Statistics.ProtoReflect.Descriptor instead.
func (*Statistics) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{6}
}

func (x *Statistics) GetTakeUsers() uint64 {
	if x != nil {
		return x.TakeUsers
	}
	return 0
}

func (x *Statistics) GetTakeCount() uint64 {
	if x != nil {
		return x.TakeCount
	}
	return 0
}

// 素材视频信息
type Video struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Width             uint32 `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`                                                 // 宽
	High              uint32 `protobuf:"varint,2,opt,name=high,proto3" json:"high,omitempty"`                                                   // 高
	Duration          uint32 `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`                                           // 时长
	FrameRate         string `protobuf:"bytes,4,opt,name=frame_rate,json=frameRate,proto3" json:"frame_rate,omitempty"`                         // 帧率
	AspectRatio       string `protobuf:"bytes,5,opt,name=aspect_ratio,json=aspectRatio,proto3" json:"aspect_ratio,omitempty"`                   // 高宽比
	BitRate           string `protobuf:"bytes,6,opt,name=bit_rate,json=bitRate,proto3" json:"bit_rate,omitempty"`                               // 比特率
	CompressionFormat string `protobuf:"bytes,7,opt,name=compression_format,json=compressionFormat,proto3" json:"compression_format,omitempty"` // 压缩格式
}

func (x *Video) Reset() {
	*x = Video{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{7}
}

func (x *Video) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Video) GetHigh() uint32 {
	if x != nil {
		return x.High
	}
	return 0
}

func (x *Video) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Video) GetFrameRate() string {
	if x != nil {
		return x.FrameRate
	}
	return ""
}

func (x *Video) GetAspectRatio() string {
	if x != nil {
		return x.AspectRatio
	}
	return ""
}

func (x *Video) GetBitRate() string {
	if x != nil {
		return x.BitRate
	}
	return ""
}

func (x *Video) GetCompressionFormat() string {
	if x != nil {
		return x.CompressionFormat
	}
	return ""
}

// 素材详情扩展数据
type MaterialExt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId    string      `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // 素材索引
	Universal  *Universal  `protobuf:"bytes,2,opt,name=universal,proto3" json:"universal,omitempty"`            // 通用
	Label      *Label      `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`                    // 标签
	Statistics *Statistics `protobuf:"bytes,4,opt,name=statistics,proto3" json:"statistics,omitempty"`          // 统计
	Video      *Video      `protobuf:"bytes,5,opt,name=video,proto3" json:"video,omitempty"`                    // 视频
}

func (x *MaterialExt) Reset() {
	*x = MaterialExt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialExt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialExt) ProtoMessage() {}

func (x *MaterialExt) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialExt.ProtoReflect.Descriptor instead.
func (*MaterialExt) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{8}
}

func (x *MaterialExt) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *MaterialExt) GetUniversal() *Universal {
	if x != nil {
		return x.Universal
	}
	return nil
}

func (x *MaterialExt) GetLabel() *Label {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *MaterialExt) GetStatistics() *Statistics {
	if x != nil {
		return x.Statistics
	}
	return nil
}

func (x *MaterialExt) GetVideo() *Video {
	if x != nil {
		return x.Video
	}
	return nil
}

type GetMaterialInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result      *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	MaterialExt *MaterialExt `protobuf:"bytes,2,opt,name=material_ext,json=materialExt,proto3" json:"material_ext,omitempty"`
}

func (x *GetMaterialInfoRsp) Reset() {
	*x = GetMaterialInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMaterialInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMaterialInfoRsp) ProtoMessage() {}

func (x *GetMaterialInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMaterialInfoRsp.ProtoReflect.Descriptor instead.
func (*GetMaterialInfoRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{9}
}

func (x *GetMaterialInfoRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetMaterialInfoRsp) GetMaterialExt() *MaterialExt {
	if x != nil {
		return x.MaterialExt
	}
	return nil
}

// 批量获取素材详情, POST, /api/v1/material_display/bt_get_material_info
type BtGetMaterialInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Uuid        string   `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	AssetIdList []string `protobuf:"bytes,3,rep,name=asset_id_list,json=assetIdList,proto3" json:"asset_id_list,omitempty"` // 唯一索引
}

func (x *BtGetMaterialInfoReq) Reset() {
	*x = BtGetMaterialInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BtGetMaterialInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BtGetMaterialInfoReq) ProtoMessage() {}

func (x *BtGetMaterialInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BtGetMaterialInfoReq.ProtoReflect.Descriptor instead.
func (*BtGetMaterialInfoReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{10}
}

func (x *BtGetMaterialInfoReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *BtGetMaterialInfoReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *BtGetMaterialInfoReq) GetAssetIdList() []string {
	if x != nil {
		return x.AssetIdList
	}
	return nil
}

type BtGetMaterialInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          *aix.Result    `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	MaterialExtList []*MaterialExt `protobuf:"bytes,2,rep,name=material_ext_list,json=materialExtList,proto3" json:"material_ext_list,omitempty"`
}

func (x *BtGetMaterialInfoRsp) Reset() {
	*x = BtGetMaterialInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BtGetMaterialInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BtGetMaterialInfoRsp) ProtoMessage() {}

func (x *BtGetMaterialInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BtGetMaterialInfoRsp.ProtoReflect.Descriptor instead.
func (*BtGetMaterialInfoRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{11}
}

func (x *BtGetMaterialInfoRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *BtGetMaterialInfoRsp) GetMaterialExtList() []*MaterialExt {
	if x != nil {
		return x.MaterialExtList
	}
	return nil
}

// 批量拉取素材详细信息, POST, /api/v1/material_display/bt_get_material_info_detail
type BtGetMaterialInfoDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetIds []string `protobuf:"bytes,1,rep,name=asset_ids,json=assetIds,proto3" json:"asset_ids,omitempty"` // 素材ID列表
}

func (x *BtGetMaterialInfoDetailReq) Reset() {
	*x = BtGetMaterialInfoDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BtGetMaterialInfoDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BtGetMaterialInfoDetailReq) ProtoMessage() {}

func (x *BtGetMaterialInfoDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BtGetMaterialInfoDetailReq.ProtoReflect.Descriptor instead.
func (*BtGetMaterialInfoDetailReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{12}
}

func (x *BtGetMaterialInfoDetailReq) GetAssetIds() []string {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

type BtGetMaterialInfoDetailRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MaterialMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 素材列表
}

func (x *BtGetMaterialInfoDetailRsp) Reset() {
	*x = BtGetMaterialInfoDetailRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BtGetMaterialInfoDetailRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BtGetMaterialInfoDetailRsp) ProtoMessage() {}

func (x *BtGetMaterialInfoDetailRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BtGetMaterialInfoDetailRsp.ProtoReflect.Descriptor instead.
func (*BtGetMaterialInfoDetailRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{13}
}

func (x *BtGetMaterialInfoDetailRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *BtGetMaterialInfoDetailRsp) GetMaterials() []*MaterialMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

// 素材标签
type AssetLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LabelName   string `protobuf:"bytes,1,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`       // 标签名称，旧字段已废弃
	FirstLabel  string `protobuf:"bytes,2,opt,name=first_label,json=firstLabel,proto3" json:"first_label,omitempty"`    // 一级标签
	SecondLabel string `protobuf:"bytes,3,opt,name=second_label,json=secondLabel,proto3" json:"second_label,omitempty"` // 二级标签
}

func (x *AssetLabel) Reset() {
	*x = AssetLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetLabel) ProtoMessage() {}

func (x *AssetLabel) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetLabel.ProtoReflect.Descriptor instead.
func (*AssetLabel) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{14}
}

func (x *AssetLabel) GetLabelName() string {
	if x != nil {
		return x.LabelName
	}
	return ""
}

func (x *AssetLabel) GetFirstLabel() string {
	if x != nil {
		return x.FirstLabel
	}
	return ""
}

func (x *AssetLabel) GetSecondLabel() string {
	if x != nil {
		return x.SecondLabel
	}
	return ""
}

type AssetFieldFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`     // 字段名称
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"` // 值列表
}

func (x *AssetFieldFilter) Reset() {
	*x = AssetFieldFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetFieldFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetFieldFilter) ProtoMessage() {}

func (x *AssetFieldFilter) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetFieldFilter.ProtoReflect.Descriptor instead.
func (*AssetFieldFilter) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{15}
}

func (x *AssetFieldFilter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AssetFieldFilter) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// 上传渠道信息
type SyncMedia struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Channel int32 `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"` // 渠道
}

func (x *SyncMedia) Reset() {
	*x = SyncMedia{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncMedia) ProtoMessage() {}

func (x *SyncMedia) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncMedia.ProtoReflect.Descriptor instead.
func (*SyncMedia) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{16}
}

func (x *SyncMedia) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

// 素材搜索, POST, /api/v1/material_display/search_material
type SearchMaterialsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                uint64              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Uuid               string              `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Text               string              `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`                                                           // 文本搜索, 为空表示搜索全部
	Status             uint32              `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`                                                      // 状态搜索(已废弃)
	Offset             uint32              `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`                                                      // 起始偏移
	Count              uint32              `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`                                                        // 拉取数量
	DirectoryId        string              `protobuf:"bytes,7,opt,name=directory_id,json=directoryId,proto3" json:"directory_id,omitempty"`                          // 目录ID
	UploadState        uint32              `protobuf:"varint,8,opt,name=upload_state,json=uploadState,proto3" json:"upload_state,omitempty"`                         // 是否上传至广告库，0-否，1-是(已废弃)
	WithDetail         uint32              `protobuf:"varint,9,opt,name=with_detail,json=withDetail,proto3" json:"with_detail,omitempty"`                            // 是否需要拉取详情 0-不需要 1-需要
	IsAd               bool                `protobuf:"varint,10,opt,name=is_ad,json=isAd,proto3" json:"is_ad,omitempty"`                                             // 是否是搜索广告库; false: 搜索素材库，true: 搜索广告库
	OnlineStatus       uint32              `protobuf:"varint,11,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"`                     // 在线状态筛选, 0表示不需要筛选
	FilterOnlineStatus bool                `protobuf:"varint,12,opt,name=filter_online_status,json=filterOnlineStatus,proto3" json:"filter_online_status,omitempty"` // 是否过滤在线状态
	SearchType         int32               `protobuf:"varint,13,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`                           // 搜索类型, 1-搜索单个名称(模糊搜索); 2-搜索多个关键字取并集(模糊搜索); 3-搜索多个素材ID; 4-搜索多个名称取交集(模糊搜索)
	Names              []string            `protobuf:"bytes,14,rep,name=names,proto3" json:"names,omitempty"`                                                        // 搜索名称, 为空表示搜索全部
	AssetIds           []string            `protobuf:"bytes,15,rep,name=asset_ids,json=assetIds,proto3" json:"asset_ids,omitempty"`                                  // 搜索素材ID列表, 为空表示搜索全部
	AixUploader        string              `protobuf:"bytes,16,opt,name=aix_uploader,json=aixUploader,proto3" json:"aix_uploader,omitempty"`                         // aix平台的上传人, 空表示不进行过滤
	Labels             []*AssetLabel       `protobuf:"bytes,17,rep,name=labels,proto3" json:"labels,omitempty"`                                                      // 需要搜索的标签列表, 为空表示不需要搜索
	LabelsSearchType   int32               `protobuf:"varint,18,opt,name=labels_search_type,json=labelsSearchType,proto3" json:"labels_search_type,omitempty"`       // 标签搜索方式, 0-默认(取并集), 1-取并集; 2-取交集
	AssetFieldFilters  []*AssetFieldFilter `protobuf:"bytes,19,rep,name=asset_field_filters,json=assetFieldFilters,proto3" json:"asset_field_filters,omitempty"`     // 素材属性字段过滤器
	FormatType         uint32              `protobuf:"varint,20,opt,name=format_type,json=formatType,proto3" json:"format_type,omitempty"`                           // 是否类型，0-全部，1-视频，2-图片
	Media              uint32              `protobuf:"varint,21,opt,name=media,proto3" json:"media,omitempty"`                                                       // 1-google 目前只用到google
	AssetRatios        []string            `protobuf:"bytes,22,rep,name=asset_ratios,json=assetRatios,proto3" json:"asset_ratios,omitempty"`                         // 需要筛选的素材比例, 不填默认不进行筛选
	SyncMediaFilter    int32               `protobuf:"varint,23,opt,name=sync_media_filter,json=syncMediaFilter,proto3" json:"sync_media_filter,omitempty"`          // 0-不过滤，1-未上传渠道，2-已上传渠道
	SyncMediaList      []int32             `protobuf:"varint,24,rep,packed,name=sync_media_list,json=syncMediaList,proto3" json:"sync_media_list,omitempty"`         // sync_media_filter=2有效，空为只要上传过就行
	StartCreateDate    string              `protobuf:"bytes,25,opt,name=start_create_date,json=startCreateDate,proto3" json:"start_create_date,omitempty"`           // 不为空，过滤 >= 上传网盘时间, 格式为 2021-01-01
	EndCreateDate      string              `protobuf:"bytes,26,opt,name=end_create_date,json=endCreateDate,proto3" json:"end_create_date,omitempty"`                 // 不为空，过滤 <= 上传网盘时间, 格式为 2021-01-01
	FormatTypeList     []int32             `protobuf:"varint,27,rep,packed,name=format_type_list,json=formatTypeList,proto3" json:"format_type_list,omitempty"`      // 多选素材格式类型列表, 为空不过滤, 枚举参看AssetXXXXType
}

func (x *SearchMaterialsReq) Reset() {
	*x = SearchMaterialsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchMaterialsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchMaterialsReq) ProtoMessage() {}

func (x *SearchMaterialsReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchMaterialsReq.ProtoReflect.Descriptor instead.
func (*SearchMaterialsReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{17}
}

func (x *SearchMaterialsReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *SearchMaterialsReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *SearchMaterialsReq) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SearchMaterialsReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SearchMaterialsReq) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *SearchMaterialsReq) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SearchMaterialsReq) GetDirectoryId() string {
	if x != nil {
		return x.DirectoryId
	}
	return ""
}

func (x *SearchMaterialsReq) GetUploadState() uint32 {
	if x != nil {
		return x.UploadState
	}
	return 0
}

func (x *SearchMaterialsReq) GetWithDetail() uint32 {
	if x != nil {
		return x.WithDetail
	}
	return 0
}

func (x *SearchMaterialsReq) GetIsAd() bool {
	if x != nil {
		return x.IsAd
	}
	return false
}

func (x *SearchMaterialsReq) GetOnlineStatus() uint32 {
	if x != nil {
		return x.OnlineStatus
	}
	return 0
}

func (x *SearchMaterialsReq) GetFilterOnlineStatus() bool {
	if x != nil {
		return x.FilterOnlineStatus
	}
	return false
}

func (x *SearchMaterialsReq) GetSearchType() int32 {
	if x != nil {
		return x.SearchType
	}
	return 0
}

func (x *SearchMaterialsReq) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *SearchMaterialsReq) GetAssetIds() []string {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

func (x *SearchMaterialsReq) GetAixUploader() string {
	if x != nil {
		return x.AixUploader
	}
	return ""
}

func (x *SearchMaterialsReq) GetLabels() []*AssetLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *SearchMaterialsReq) GetLabelsSearchType() int32 {
	if x != nil {
		return x.LabelsSearchType
	}
	return 0
}

func (x *SearchMaterialsReq) GetAssetFieldFilters() []*AssetFieldFilter {
	if x != nil {
		return x.AssetFieldFilters
	}
	return nil
}

func (x *SearchMaterialsReq) GetFormatType() uint32 {
	if x != nil {
		return x.FormatType
	}
	return 0
}

func (x *SearchMaterialsReq) GetMedia() uint32 {
	if x != nil {
		return x.Media
	}
	return 0
}

func (x *SearchMaterialsReq) GetAssetRatios() []string {
	if x != nil {
		return x.AssetRatios
	}
	return nil
}

func (x *SearchMaterialsReq) GetSyncMediaFilter() int32 {
	if x != nil {
		return x.SyncMediaFilter
	}
	return 0
}

func (x *SearchMaterialsReq) GetSyncMediaList() []int32 {
	if x != nil {
		return x.SyncMediaList
	}
	return nil
}

func (x *SearchMaterialsReq) GetStartCreateDate() string {
	if x != nil {
		return x.StartCreateDate
	}
	return ""
}

func (x *SearchMaterialsReq) GetEndCreateDate() string {
	if x != nil {
		return x.EndCreateDate
	}
	return ""
}

func (x *SearchMaterialsReq) GetFormatTypeList() []int32 {
	if x != nil {
		return x.FormatTypeList
	}
	return nil
}

type SearchMaterialsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MaterialMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 素材列表
	Total     uint32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 素材总数
}

func (x *SearchMaterialsRsp) Reset() {
	*x = SearchMaterialsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchMaterialsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchMaterialsRsp) ProtoMessage() {}

func (x *SearchMaterialsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchMaterialsRsp.ProtoReflect.Descriptor instead.
func (*SearchMaterialsRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{18}
}

func (x *SearchMaterialsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SearchMaterialsRsp) GetMaterials() []*MaterialMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

func (x *SearchMaterialsRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 素材名字搜索, POST, /api/v1/material_display/search_materials_by_name
type SearchMaterialsByNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetName  string `protobuf:"bytes,1,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`    // 素材名字
	SearchType string `protobuf:"bytes,2,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"` // "by_full|by_prefix", 多种搜索方式竖线分割， by_full:名字全匹配， by_prefix:匹配编号前缀
}

func (x *SearchMaterialsByNameReq) Reset() {
	*x = SearchMaterialsByNameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchMaterialsByNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchMaterialsByNameReq) ProtoMessage() {}

func (x *SearchMaterialsByNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchMaterialsByNameReq.ProtoReflect.Descriptor instead.
func (*SearchMaterialsByNameReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{19}
}

func (x *SearchMaterialsByNameReq) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *SearchMaterialsByNameReq) GetSearchType() string {
	if x != nil {
		return x.SearchType
	}
	return ""
}

type SearchMaterialsByNameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MaterialMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 素材列表
	Total     uint32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 素材总数
}

func (x *SearchMaterialsByNameRsp) Reset() {
	*x = SearchMaterialsByNameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchMaterialsByNameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchMaterialsByNameRsp) ProtoMessage() {}

func (x *SearchMaterialsByNameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchMaterialsByNameRsp.ProtoReflect.Descriptor instead.
func (*SearchMaterialsByNameRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{20}
}

func (x *SearchMaterialsByNameRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SearchMaterialsByNameRsp) GetMaterials() []*MaterialMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

func (x *SearchMaterialsByNameRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 批量素材名字搜索, POST, /api/v1/material_display/batch_search_materials_by_name
type BatchSearchMaterialsByNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetNames []string `protobuf:"bytes,1,rep,name=asset_names,json=assetNames,proto3" json:"asset_names,omitempty"` // 素材名字列表，最多50条
}

func (x *BatchSearchMaterialsByNameReq) Reset() {
	*x = BatchSearchMaterialsByNameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchSearchMaterialsByNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSearchMaterialsByNameReq) ProtoMessage() {}

func (x *BatchSearchMaterialsByNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSearchMaterialsByNameReq.ProtoReflect.Descriptor instead.
func (*BatchSearchMaterialsByNameReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{21}
}

func (x *BatchSearchMaterialsByNameReq) GetAssetNames() []string {
	if x != nil {
		return x.AssetNames
	}
	return nil
}

type BatchSearchMaterialsByNameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MaterialMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 素材列表
	Total     uint32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 素材总数
}

func (x *BatchSearchMaterialsByNameRsp) Reset() {
	*x = BatchSearchMaterialsByNameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchSearchMaterialsByNameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchSearchMaterialsByNameRsp) ProtoMessage() {}

func (x *BatchSearchMaterialsByNameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchSearchMaterialsByNameRsp.ProtoReflect.Descriptor instead.
func (*BatchSearchMaterialsByNameRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{22}
}

func (x *BatchSearchMaterialsByNameRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *BatchSearchMaterialsByNameRsp) GetMaterials() []*MaterialMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

func (x *BatchSearchMaterialsByNameRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 拉取目录列表, POST, /api/v1/material_display/directory_list
type DirectoryListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentId      string `protobuf:"bytes,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`                  // 父目录id
	Offset        uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`                                     // 分页偏移量
	Limit         uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`                                       // 最大获取数量
	FilterDirName string `protobuf:"bytes,4,opt,name=filter_dir_name,json=filterDirName,proto3" json:"filter_dir_name,omitempty"` // 过滤目录名称
	GetChild      int32  `protobuf:"varint,5,opt,name=get_child,json=getChild,proto3" json:"get_child,omitempty"`                 // 是否包含子节点信息, 0-否, 1-是
}

func (x *DirectoryListReq) Reset() {
	*x = DirectoryListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectoryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryListReq) ProtoMessage() {}

func (x *DirectoryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryListReq.ProtoReflect.Descriptor instead.
func (*DirectoryListReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{23}
}

func (x *DirectoryListReq) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *DirectoryListReq) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *DirectoryListReq) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *DirectoryListReq) GetFilterDirName() string {
	if x != nil {
		return x.FilterDirName
	}
	return ""
}

func (x *DirectoryListReq) GetGetChild() int32 {
	if x != nil {
		return x.GetChild
	}
	return 0
}

// 返回的目录数据信息
type Directory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                    //目录id
	Name                 string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                // 目录名称
	ParentId             string `protobuf:"bytes,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`                                        // 父目录id
	ParentName           string `protobuf:"bytes,4,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`                                  // 父目录名称
	CreateDate           string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`                                  // 创建日期
	UpdateDate           string `protobuf:"bytes,6,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`                                  // 更新日期（索引）
	DirectChildCount     uint32 `protobuf:"varint,7,opt,name=direct_child_count,json=directChildCount,proto3" json:"direct_child_count,omitempty"`             // 当前目录包含子目录和元素数量（不递归）
	TotalLeafCount       uint32 `protobuf:"varint,8,opt,name=total_leaf_count,json=totalLeafCount,proto3" json:"total_leaf_count,omitempty"`                   // 当前目录下包含素材数量（递归）
	DirectDirectoryCount uint32 `protobuf:"varint,9,opt,name=direct_directory_count,json=directDirectoryCount,proto3" json:"direct_directory_count,omitempty"` // 当前目录下子目录数量（不递归）
	FullPathName         string `protobuf:"bytes,10,opt,name=full_path_name,json=fullPathName,proto3" json:"full_path_name,omitempty"`                         // 当前目录全路径名称
	FullPathId           string `protobuf:"bytes,11,opt,name=full_path_id,json=fullPathId,proto3" json:"full_path_id,omitempty"`                               // 当前目录全路径id
	MediaDirectoryId     string `protobuf:"bytes,12,opt,name=media_directory_id,json=mediaDirectoryId,proto3" json:"media_directory_id,omitempty"`             // 媒体素材id
	MediaDirectoryName   string `protobuf:"bytes,13,opt,name=media_directory_name,json=mediaDirectoryName,proto3" json:"media_directory_name,omitempty"`       // 媒体素材名称
	SyncTime             string `protobuf:"bytes,14,opt,name=sync_time,json=syncTime,proto3" json:"sync_time,omitempty"`                                       // 最近同步时间
}

func (x *Directory) Reset() {
	*x = Directory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Directory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Directory) ProtoMessage() {}

func (x *Directory) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Directory.ProtoReflect.Descriptor instead.
func (*Directory) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{24}
}

func (x *Directory) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Directory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Directory) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *Directory) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

func (x *Directory) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *Directory) GetUpdateDate() string {
	if x != nil {
		return x.UpdateDate
	}
	return ""
}

func (x *Directory) GetDirectChildCount() uint32 {
	if x != nil {
		return x.DirectChildCount
	}
	return 0
}

func (x *Directory) GetTotalLeafCount() uint32 {
	if x != nil {
		return x.TotalLeafCount
	}
	return 0
}

func (x *Directory) GetDirectDirectoryCount() uint32 {
	if x != nil {
		return x.DirectDirectoryCount
	}
	return 0
}

func (x *Directory) GetFullPathName() string {
	if x != nil {
		return x.FullPathName
	}
	return ""
}

func (x *Directory) GetFullPathId() string {
	if x != nil {
		return x.FullPathId
	}
	return ""
}

func (x *Directory) GetMediaDirectoryId() string {
	if x != nil {
		return x.MediaDirectoryId
	}
	return ""
}

func (x *Directory) GetMediaDirectoryName() string {
	if x != nil {
		return x.MediaDirectoryName
	}
	return ""
}

func (x *Directory) GetSyncTime() string {
	if x != nil {
		return x.SyncTime
	}
	return ""
}

type DirectoryListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Dirs   []*Directory `protobuf:"bytes,2,rep,name=dirs,proto3" json:"dirs,omitempty"`     // 目录列表
	Total  uint64       `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`  // 总数
}

func (x *DirectoryListRsp) Reset() {
	*x = DirectoryListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectoryListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryListRsp) ProtoMessage() {}

func (x *DirectoryListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryListRsp.ProtoReflect.Descriptor instead.
func (*DirectoryListRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{25}
}

func (x *DirectoryListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *DirectoryListRsp) GetDirs() []*Directory {
	if x != nil {
		return x.Dirs
	}
	return nil
}

func (x *DirectoryListRsp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 搜索素材目录, POST, /api/v1/material_display/directory_search
type DirectorySearchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                          // 匹配目录名
	Page     int32  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 分页数，从0开始
	PageSize int32  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页条数，最多100条
}

func (x *DirectorySearchReq) Reset() {
	*x = DirectorySearchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectorySearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectorySearchReq) ProtoMessage() {}

func (x *DirectorySearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectorySearchReq.ProtoReflect.Descriptor instead.
func (*DirectorySearchReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{26}
}

func (x *DirectorySearchReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectorySearchReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DirectorySearchReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type DirectorySearchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Dirs   []*Directory `protobuf:"bytes,2,rep,name=dirs,proto3" json:"dirs,omitempty"`     // 目录列表
	Total  int32        `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`  // 总数
}

func (x *DirectorySearchRsp) Reset() {
	*x = DirectorySearchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectorySearchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectorySearchRsp) ProtoMessage() {}

func (x *DirectorySearchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectorySearchRsp.ProtoReflect.Descriptor instead.
func (*DirectorySearchRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{27}
}

func (x *DirectorySearchRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *DirectorySearchRsp) GetDirs() []*Directory {
	if x != nil {
		return x.Dirs
	}
	return nil
}

func (x *DirectorySearchRsp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取指定目录的数据信息, POST, /api/v1/material_display/directory_get
type DirectoryGetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                           // 目录id
	DirType uint32 `protobuf:"varint,2,opt,name=dir_type,json=dirType,proto3" json:"dir_type,omitempty"` // 目录类型 0-普通目录 1-iegg媒体目录
}

func (x *DirectoryGetReq) Reset() {
	*x = DirectoryGetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectoryGetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryGetReq) ProtoMessage() {}

func (x *DirectoryGetReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryGetReq.ProtoReflect.Descriptor instead.
func (*DirectoryGetReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{28}
}

func (x *DirectoryGetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DirectoryGetReq) GetDirType() uint32 {
	if x != nil {
		return x.DirType
	}
	return 0
}

type DirectoryGetRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Dir    *Directory  `protobuf:"bytes,2,opt,name=dir,proto3" json:"dir,omitempty"`       // 目录信息
}

func (x *DirectoryGetRsp) Reset() {
	*x = DirectoryGetRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectoryGetRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryGetRsp) ProtoMessage() {}

func (x *DirectoryGetRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryGetRsp.ProtoReflect.Descriptor instead.
func (*DirectoryGetRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{29}
}

func (x *DirectoryGetRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *DirectoryGetRsp) GetDir() *Directory {
	if x != nil {
		return x.Dir
	}
	return nil
}

// 新建目录, POST, /api/v1/material_display/directory_create
type DirectoryCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                   string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                                                      // 目录名称
	ParentId               string `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`                                              // 父目录id
	RenameRepeatedCategory int32  `protobuf:"varint,3,opt,name=rename_repeated_category,json=renameRepeatedCategory,proto3" json:"rename_repeated_category,omitempty"` // arthub接口参数，是否是新版本
}

func (x *DirectoryCreateReq) Reset() {
	*x = DirectoryCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectoryCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryCreateReq) ProtoMessage() {}

func (x *DirectoryCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryCreateReq.ProtoReflect.Descriptor instead.
func (*DirectoryCreateReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{30}
}

func (x *DirectoryCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DirectoryCreateReq) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *DirectoryCreateReq) GetRenameRepeatedCategory() int32 {
	if x != nil {
		return x.RenameRepeatedCategory
	}
	return 0
}

type DirectoryCreateRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Dir    *Directory  `protobuf:"bytes,2,opt,name=dir,proto3" json:"dir,omitempty"`       // 目录信息
}

func (x *DirectoryCreateRsp) Reset() {
	*x = DirectoryCreateRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DirectoryCreateRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryCreateRsp) ProtoMessage() {}

func (x *DirectoryCreateRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryCreateRsp.ProtoReflect.Descriptor instead.
func (*DirectoryCreateRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{31}
}

func (x *DirectoryCreateRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *DirectoryCreateRsp) GetDir() *Directory {
	if x != nil {
		return x.Dir
	}
	return nil
}

// 素材拖拽到其他目录, POST, /api/v1/material_display/materials_move
type MaterialMoveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids           []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`                                            //被移动的文件和文件夹ID
	OtherParentId string   `protobuf:"bytes,2,opt,name=other_parent_id,json=otherParentId,proto3" json:"other_parent_id,omitempty"` //移动到的目标文件夹ID
}

func (x *MaterialMoveReq) Reset() {
	*x = MaterialMoveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialMoveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialMoveReq) ProtoMessage() {}

func (x *MaterialMoveReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialMoveReq.ProtoReflect.Descriptor instead.
func (*MaterialMoveReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{32}
}

func (x *MaterialMoveReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *MaterialMoveReq) GetOtherParentId() string {
	if x != nil {
		return x.OtherParentId
	}
	return ""
}

type MaterialInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeId       string `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`                      // 当前素材所属目录id
	NodeName     string `protobuf:"bytes,2,opt,name=node_name,json=nodeName,proto3" json:"node_name,omitempty"`                // 当前素材所属目录名称
	AssetId      string `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                   // 当前素材id（主键）
	AssetName    string `protobuf:"bytes,4,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`             // 当前素材名称
	Status       uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`                                   // 素材状态（索引）
	CreateDate   string `protobuf:"bytes,6,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`          // 素材创建日期（索引）
	UplineDate   string `protobuf:"bytes,7,opt,name=upline_date,json=uplineDate,proto3" json:"upline_date,omitempty"`          // 素材上线日期（索引）
	OfflineDate  string `protobuf:"bytes,8,opt,name=offline_date,json=offlineDate,proto3" json:"offline_date,omitempty"`       // 素材下线日期（索引）
	OnlineDays   uint32 `protobuf:"varint,9,opt,name=online_days,json=onlineDays,proto3" json:"online_days,omitempty"`         // 素材上线天数
	FullPathName string `protobuf:"bytes,10,opt,name=full_path_name,json=fullPathName,proto3" json:"full_path_name,omitempty"` // 当前素材全路径名称
	FullPathId   string `protobuf:"bytes,11,opt,name=full_path_id,json=fullPathId,proto3" json:"full_path_id,omitempty"`       // 当前素材全路径id
}

func (x *MaterialInfo) Reset() {
	*x = MaterialInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialInfo) ProtoMessage() {}

func (x *MaterialInfo) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialInfo.ProtoReflect.Descriptor instead.
func (*MaterialInfo) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{33}
}

func (x *MaterialInfo) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *MaterialInfo) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *MaterialInfo) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *MaterialInfo) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *MaterialInfo) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MaterialInfo) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *MaterialInfo) GetUplineDate() string {
	if x != nil {
		return x.UplineDate
	}
	return ""
}

func (x *MaterialInfo) GetOfflineDate() string {
	if x != nil {
		return x.OfflineDate
	}
	return ""
}

func (x *MaterialInfo) GetOnlineDays() uint32 {
	if x != nil {
		return x.OnlineDays
	}
	return 0
}

func (x *MaterialInfo) GetFullPathName() string {
	if x != nil {
		return x.FullPathName
	}
	return ""
}

func (x *MaterialInfo) GetFullPathId() string {
	if x != nil {
		return x.FullPathId
	}
	return ""
}

type MaterialMoveRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result            *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                                  // 返回结果
	MaterialItems     []*MaterialInfo `protobuf:"bytes,2,rep,name=material_items,json=materialItems,proto3" json:"material_items,omitempty"`               //素材移动到其他目录后的素材信息
	FailedMaterialIds []string        `protobuf:"bytes,3,rep,name=failed_material_ids,json=failedMaterialIds,proto3" json:"failed_material_ids,omitempty"` //移动失败的素材id列表
}

func (x *MaterialMoveRsp) Reset() {
	*x = MaterialMoveRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialMoveRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialMoveRsp) ProtoMessage() {}

func (x *MaterialMoveRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialMoveRsp.ProtoReflect.Descriptor instead.
func (*MaterialMoveRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{34}
}

func (x *MaterialMoveRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *MaterialMoveRsp) GetMaterialItems() []*MaterialInfo {
	if x != nil {
		return x.MaterialItems
	}
	return nil
}

func (x *MaterialMoveRsp) GetFailedMaterialIds() []string {
	if x != nil {
		return x.FailedMaterialIds
	}
	return nil
}

type MaterialSetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId     string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`              // 素材id
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                   // 新名称
	Cover       string `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover,omitempty"`                                 // 新封面
	AuditId     string `protobuf:"bytes,4,opt,name=audit_id,json=auditId,proto3" json:"audit_id,omitempty"`              // 素材审核id
	AuditStatus int32  `protobuf:"varint,5,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"` // 素材审核状态
	Label       *Label `protobuf:"bytes,6,opt,name=label,proto3" json:"label,omitempty"`                                 // 新标签
}

func (x *MaterialSetInfo) Reset() {
	*x = MaterialSetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialSetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialSetInfo) ProtoMessage() {}

func (x *MaterialSetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialSetInfo.ProtoReflect.Descriptor instead.
func (*MaterialSetInfo) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{35}
}

func (x *MaterialSetInfo) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *MaterialSetInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MaterialSetInfo) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *MaterialSetInfo) GetAuditId() string {
	if x != nil {
		return x.AuditId
	}
	return ""
}

func (x *MaterialSetInfo) GetAuditStatus() int32 {
	if x != nil {
		return x.AuditStatus
	}
	return 0
}

func (x *MaterialSetInfo) GetLabel() *Label {
	if x != nil {
		return x.Label
	}
	return nil
}

// 编辑素材信息(所有参数必须填写!), POST, /api/v1/material_display/set_material_info
type SetMaterialInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaterialSetList []*MaterialSetInfo `protobuf:"bytes,1,rep,name=material_set_list,json=materialSetList,proto3" json:"material_set_list,omitempty"` // 待设置的素材
}

func (x *SetMaterialInfoReq) Reset() {
	*x = SetMaterialInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMaterialInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaterialInfoReq) ProtoMessage() {}

func (x *SetMaterialInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaterialInfoReq.ProtoReflect.Descriptor instead.
func (*SetMaterialInfoReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{36}
}

func (x *SetMaterialInfoReq) GetMaterialSetList() []*MaterialSetInfo {
	if x != nil {
		return x.MaterialSetList
	}
	return nil
}

type SetRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // 素材id
	SetRes  uint32 `protobuf:"varint,2,opt,name=set_res,json=setRes,proto3" json:"set_res,omitempty"`   // 设置结果 0-失败 1-成功
	ResMsg  string `protobuf:"bytes,3,opt,name=res_msg,json=resMsg,proto3" json:"res_msg,omitempty"`    // 设置结果描述
}

func (x *SetRes) Reset() {
	*x = SetRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRes) ProtoMessage() {}

func (x *SetRes) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRes.ProtoReflect.Descriptor instead.
func (*SetRes) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{37}
}

func (x *SetRes) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *SetRes) GetSetRes() uint32 {
	if x != nil {
		return x.SetRes
	}
	return 0
}

func (x *SetRes) GetResMsg() string {
	if x != nil {
		return x.ResMsg
	}
	return ""
}

type SetMaterialInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result     *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                             // 返回结果
	SetResList []*SetRes   `protobuf:"bytes,2,rep,name=set_res_list,json=setResList,proto3" json:"set_res_list,omitempty"` // 设置结果列表
}

func (x *SetMaterialInfoRsp) Reset() {
	*x = SetMaterialInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMaterialInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaterialInfoRsp) ProtoMessage() {}

func (x *SetMaterialInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaterialInfoRsp.ProtoReflect.Descriptor instead.
func (*SetMaterialInfoRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{38}
}

func (x *SetMaterialInfoRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SetMaterialInfoRsp) GetSetResList() []*SetRes {
	if x != nil {
		return x.SetResList
	}
	return nil
}

type UploadAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId        string   `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                        // 素材id
	ToChannel      []uint32 `protobuf:"varint,2,rep,packed,name=to_channel,json=toChannel,proto3" json:"to_channel,omitempty"`          // 1-google 2-facebook
	Country        []string `protobuf:"bytes,3,rep,name=country,proto3" json:"country,omitempty"`                                       // 国家
	AssertName     string   `protobuf:"bytes,4,opt,name=assert_name,json=assertName,proto3" json:"assert_name,omitempty"`               // 素材名称
	NodeId         string   `protobuf:"bytes,5,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`                           // 所属目录id
	AssetMediaPath string   `protobuf:"bytes,6,opt,name=asset_media_path,json=assetMediaPath,proto3" json:"asset_media_path,omitempty"` // 素材media路径， 依'/'分割，不包括game_code根目录； 若为空，默认放在根目录
}

func (x *UploadAsset) Reset() {
	*x = UploadAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadAsset) ProtoMessage() {}

func (x *UploadAsset) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadAsset.ProtoReflect.Descriptor instead.
func (*UploadAsset) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{39}
}

func (x *UploadAsset) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *UploadAsset) GetToChannel() []uint32 {
	if x != nil {
		return x.ToChannel
	}
	return nil
}

func (x *UploadAsset) GetCountry() []string {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *UploadAsset) GetAssertName() string {
	if x != nil {
		return x.AssertName
	}
	return ""
}

func (x *UploadAsset) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *UploadAsset) GetAssetMediaPath() string {
	if x != nil {
		return x.AssetMediaPath
	}
	return ""
}

// 上传素材至媒体上报, POST, /api/v1/material_display/upload_to_channel
type UploadToChannelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Assets         []*UploadAsset `protobuf:"bytes,1,rep,name=assets,proto3" json:"assets,omitempty"`                                         // 上传素材id
	AssetMediaPath string         `protobuf:"bytes,2,opt,name=asset_media_path,json=assetMediaPath,proto3" json:"asset_media_path,omitempty"` // 素材media路径， 依'/'分割，不包括game_code根目录； 若为空，默认放在根目录
}

func (x *UploadToChannelReq) Reset() {
	*x = UploadToChannelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadToChannelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadToChannelReq) ProtoMessage() {}

func (x *UploadToChannelReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadToChannelReq.ProtoReflect.Descriptor instead.
func (*UploadToChannelReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{40}
}

func (x *UploadToChannelReq) GetAssets() []*UploadAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *UploadToChannelReq) GetAssetMediaPath() string {
	if x != nil {
		return x.AssetMediaPath
	}
	return ""
}

type UploadToChannelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *UploadToChannelRsp) Reset() {
	*x = UploadToChannelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadToChannelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadToChannelRsp) ProtoMessage() {}

func (x *UploadToChannelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadToChannelRsp.ProtoReflect.Descriptor instead.
func (*UploadToChannelRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{41}
}

func (x *UploadToChannelRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 拉取用户上传的媒体素材目录列表, POST, /api/v1/material_display/media_directory_list
type MediaDirectoryListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentId string `protobuf:"bytes,1,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"` // 父目录id
	Offset   uint32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`                    // 分页偏移量
	Limit    uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`                      // 最大获取数量
}

func (x *MediaDirectoryListReq) Reset() {
	*x = MediaDirectoryListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaDirectoryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaDirectoryListReq) ProtoMessage() {}

func (x *MediaDirectoryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaDirectoryListReq.ProtoReflect.Descriptor instead.
func (*MediaDirectoryListReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{42}
}

func (x *MediaDirectoryListReq) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *MediaDirectoryListReq) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *MediaDirectoryListReq) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

// 返回的目录数据信息
type MediaDirectory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                                                    // 目录id
	Name                 string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                // 目录名称
	ParentId             string `protobuf:"bytes,3,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`                                        // 父目录id
	ParentName           string `protobuf:"bytes,4,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`                                  // 父目录名称
	CreateDate           string `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`                                  // 创建日期
	UpdateDate           string `protobuf:"bytes,6,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`                                  // 更新日期（索引）
	DirectChildCount     uint32 `protobuf:"varint,7,opt,name=direct_child_count,json=directChildCount,proto3" json:"direct_child_count,omitempty"`             // 当前目录包含子目录和元素数量（不递归）
	TotalLeafCount       uint32 `protobuf:"varint,8,opt,name=total_leaf_count,json=totalLeafCount,proto3" json:"total_leaf_count,omitempty"`                   // 当前目录下包含素材数量（递归）
	DirectDirectoryCount uint32 `protobuf:"varint,9,opt,name=direct_directory_count,json=directDirectoryCount,proto3" json:"direct_directory_count,omitempty"` // 当前目录下子目录数量（不递归）
	FullPathName         string `protobuf:"bytes,10,opt,name=full_path_name,json=fullPathName,proto3" json:"full_path_name,omitempty"`                         // 当前目录全路径名称
	FullPathId           string `protobuf:"bytes,11,opt,name=full_path_id,json=fullPathId,proto3" json:"full_path_id,omitempty"`                               // 当前目录全路径id
	DirectoryId          string `protobuf:"bytes,12,opt,name=directory_id,json=directoryId,proto3" json:"directory_id,omitempty"`                              // arthub目录id
	DirectoryName        string `protobuf:"bytes,13,opt,name=directory_name,json=directoryName,proto3" json:"directory_name,omitempty"`                        // arthub目录名称
}

func (x *MediaDirectory) Reset() {
	*x = MediaDirectory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaDirectory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaDirectory) ProtoMessage() {}

func (x *MediaDirectory) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaDirectory.ProtoReflect.Descriptor instead.
func (*MediaDirectory) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{43}
}

func (x *MediaDirectory) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MediaDirectory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MediaDirectory) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *MediaDirectory) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

func (x *MediaDirectory) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *MediaDirectory) GetUpdateDate() string {
	if x != nil {
		return x.UpdateDate
	}
	return ""
}

func (x *MediaDirectory) GetDirectChildCount() uint32 {
	if x != nil {
		return x.DirectChildCount
	}
	return 0
}

func (x *MediaDirectory) GetTotalLeafCount() uint32 {
	if x != nil {
		return x.TotalLeafCount
	}
	return 0
}

func (x *MediaDirectory) GetDirectDirectoryCount() uint32 {
	if x != nil {
		return x.DirectDirectoryCount
	}
	return 0
}

func (x *MediaDirectory) GetFullPathName() string {
	if x != nil {
		return x.FullPathName
	}
	return ""
}

func (x *MediaDirectory) GetFullPathId() string {
	if x != nil {
		return x.FullPathId
	}
	return ""
}

func (x *MediaDirectory) GetDirectoryId() string {
	if x != nil {
		return x.DirectoryId
	}
	return ""
}

func (x *MediaDirectory) GetDirectoryName() string {
	if x != nil {
		return x.DirectoryName
	}
	return ""
}

type MediaDirectoryListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result       `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Dirs   []*MediaDirectory `protobuf:"bytes,2,rep,name=dirs,proto3" json:"dirs,omitempty"`     // 目录列表
	Total  uint64            `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`  // 当前目录下目录总数
}

func (x *MediaDirectoryListRsp) Reset() {
	*x = MediaDirectoryListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaDirectoryListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaDirectoryListRsp) ProtoMessage() {}

func (x *MediaDirectoryListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaDirectoryListRsp.ProtoReflect.Descriptor instead.
func (*MediaDirectoryListRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{44}
}

func (x *MediaDirectoryListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *MediaDirectoryListRsp) GetDirs() []*MediaDirectory {
	if x != nil {
		return x.Dirs
	}
	return nil
}

func (x *MediaDirectoryListRsp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取网盘token信息, POST, /api/v1/material_display/get_depot_token
type GetDepotTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCodeList []string `protobuf:"bytes,1,rep,name=game_code_list,json=gameCodeList,proto3" json:"game_code_list,omitempty"` // game_code
}

func (x *GetDepotTokenReq) Reset() {
	*x = GetDepotTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDepotTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepotTokenReq) ProtoMessage() {}

func (x *GetDepotTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepotTokenReq.ProtoReflect.Descriptor instead.
func (*GetDepotTokenReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{45}
}

func (x *GetDepotTokenReq) GetGameCodeList() []string {
	if x != nil {
		return x.GameCodeList
	}
	return nil
}

type DepotGoogleDrive struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsOauth2Grant bool `protobuf:"varint,1,opt,name=is_oauth2_grant,json=isOauth2Grant,proto3" json:"is_oauth2_grant,omitempty"` // 是否是oauth2授权， 默认为service account
}

func (x *DepotGoogleDrive) Reset() {
	*x = DepotGoogleDrive{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepotGoogleDrive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepotGoogleDrive) ProtoMessage() {}

func (x *DepotGoogleDrive) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepotGoogleDrive.ProtoReflect.Descriptor instead.
func (*DepotGoogleDrive) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{46}
}

func (x *DepotGoogleDrive) GetIsOauth2Grant() bool {
	if x != nil {
		return x.IsOauth2Grant
	}
	return false
}

type DepotExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoogleDrive *DepotGoogleDrive `protobuf:"bytes,1,opt,name=google_drive,json=googleDrive,proto3" json:"google_drive,omitempty"`
}

func (x *DepotExtra) Reset() {
	*x = DepotExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepotExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepotExtra) ProtoMessage() {}

func (x *DepotExtra) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepotExtra.ProtoReflect.Descriptor instead.
func (*DepotExtra) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{47}
}

func (x *DepotExtra) GetGoogleDrive() *DepotGoogleDrive {
	if x != nil {
		return x.GoogleDrive
	}
	return nil
}

type DepotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepotId              string      `protobuf:"bytes,1,opt,name=depot_id,json=depotId,proto3" json:"depot_id,omitempty"`                                          // depot id
	DepotName            string      `protobuf:"bytes,2,opt,name=depot_name,json=depotName,proto3" json:"depot_name,omitempty"`                                    // depot名称
	PublicToken          string      `protobuf:"bytes,3,opt,name=public_token,json=publicToken,proto3" json:"public_token,omitempty"`                              // token信息
	GameCode             string      `protobuf:"bytes,4,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                       // game_code
	GameName             string      `protobuf:"bytes,5,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`                                       // game_code名称
	ArthubCode           string      `protobuf:"bytes,6,opt,name=arthub_code,json=arthubCode,proto3" json:"arthub_code,omitempty"`                                 // arthub_code名称
	Type                 int64       `protobuf:"varint,7,opt,name=type,proto3" json:"type,omitempty"`                                                              // 默认 1表示arthub，2表示google drive
	GoogleServiceAccount string      `protobuf:"bytes,8,opt,name=google_service_account,json=googleServiceAccount,proto3" json:"google_service_account,omitempty"` // google drive账户鉴权信息 (service account 或者 oauth2)
	CloudDriveStatus     string      `protobuf:"bytes,9,opt,name=cloud_drive_status,json=cloudDriveStatus,proto3" json:"cloud_drive_status,omitempty"`             // 网盘状态，为空或pending-待接入， initializing - 初始化中， active-正常使用中， unauthorized - 授权失效
	Extra                *DepotExtra `protobuf:"bytes,10,opt,name=extra,proto3" json:"extra,omitempty"`
}

func (x *DepotInfo) Reset() {
	*x = DepotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepotInfo) ProtoMessage() {}

func (x *DepotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepotInfo.ProtoReflect.Descriptor instead.
func (*DepotInfo) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{48}
}

func (x *DepotInfo) GetDepotId() string {
	if x != nil {
		return x.DepotId
	}
	return ""
}

func (x *DepotInfo) GetDepotName() string {
	if x != nil {
		return x.DepotName
	}
	return ""
}

func (x *DepotInfo) GetPublicToken() string {
	if x != nil {
		return x.PublicToken
	}
	return ""
}

func (x *DepotInfo) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *DepotInfo) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

func (x *DepotInfo) GetArthubCode() string {
	if x != nil {
		return x.ArthubCode
	}
	return ""
}

func (x *DepotInfo) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *DepotInfo) GetGoogleServiceAccount() string {
	if x != nil {
		return x.GoogleServiceAccount
	}
	return ""
}

func (x *DepotInfo) GetCloudDriveStatus() string {
	if x != nil {
		return x.CloudDriveStatus
	}
	return ""
}

func (x *DepotInfo) GetExtra() *DepotExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

type GetDepotTokenRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Items  []*DepotInfo `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`   // depot信息列表
}

func (x *GetDepotTokenRsp) Reset() {
	*x = GetDepotTokenRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDepotTokenRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepotTokenRsp) ProtoMessage() {}

func (x *GetDepotTokenRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepotTokenRsp.ProtoReflect.Descriptor instead.
func (*GetDepotTokenRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{49}
}

func (x *GetDepotTokenRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetDepotTokenRsp) GetItems() []*DepotInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type GenerateArthubTempDownloadUrlReqItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId            string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                // 素材id列表
	DownloadName       string `protobuf:"bytes,2,opt,name=download_name,json=downloadName,proto3" json:"download_name,omitempty"` // 下载文件重命名，默认为存储文件名
	ContentDisposition string `protobuf:"bytes,3,opt,name=content_disposition,json=contentDisposition,proto3" json:"content_disposition,omitempty"`
}

func (x *GenerateArthubTempDownloadUrlReqItem) Reset() {
	*x = GenerateArthubTempDownloadUrlReqItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateArthubTempDownloadUrlReqItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateArthubTempDownloadUrlReqItem) ProtoMessage() {}

func (x *GenerateArthubTempDownloadUrlReqItem) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateArthubTempDownloadUrlReqItem.ProtoReflect.Descriptor instead.
func (*GenerateArthubTempDownloadUrlReqItem) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{50}
}

func (x *GenerateArthubTempDownloadUrlReqItem) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *GenerateArthubTempDownloadUrlReqItem) GetDownloadName() string {
	if x != nil {
		return x.DownloadName
	}
	return ""
}

func (x *GenerateArthubTempDownloadUrlReqItem) GetContentDisposition() string {
	if x != nil {
		return x.ContentDisposition
	}
	return ""
}

// 获取arthub素材临时下载url, POST, /api/v1/material_display/generate_arthub_temp_download_url
type GenerateArthubTempDownloadUrlReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*GenerateArthubTempDownloadUrlReqItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`                          // 素材id列表
	DepotCode string                                  `protobuf:"bytes,2,opt,name=depot_code,json=depotCode,proto3" json:"depot_code,omitempty"` // 对应指定game_code
}

func (x *GenerateArthubTempDownloadUrlReq) Reset() {
	*x = GenerateArthubTempDownloadUrlReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateArthubTempDownloadUrlReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateArthubTempDownloadUrlReq) ProtoMessage() {}

func (x *GenerateArthubTempDownloadUrlReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateArthubTempDownloadUrlReq.ProtoReflect.Descriptor instead.
func (*GenerateArthubTempDownloadUrlReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{51}
}

func (x *GenerateArthubTempDownloadUrlReq) GetItems() []*GenerateArthubTempDownloadUrlReqItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *GenerateArthubTempDownloadUrlReq) GetDepotCode() string {
	if x != nil {
		return x.DepotCode
	}
	return ""
}

type GenerateArthubTempDownloadUrlSucceedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId   string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`       // 文件在S3上的路径
	SignedUrl string `protobuf:"bytes,2,opt,name=signed_url,json=signedUrl,proto3" json:"signed_url,omitempty"` // 签名后的url
	Expire    uint64 `protobuf:"varint,3,opt,name=expire,proto3" json:"expire,omitempty"`                       // signed_url超时时间
}

func (x *GenerateArthubTempDownloadUrlSucceedItem) Reset() {
	*x = GenerateArthubTempDownloadUrlSucceedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateArthubTempDownloadUrlSucceedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateArthubTempDownloadUrlSucceedItem) ProtoMessage() {}

func (x *GenerateArthubTempDownloadUrlSucceedItem) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateArthubTempDownloadUrlSucceedItem.ProtoReflect.Descriptor instead.
func (*GenerateArthubTempDownloadUrlSucceedItem) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{52}
}

func (x *GenerateArthubTempDownloadUrlSucceedItem) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *GenerateArthubTempDownloadUrlSucceedItem) GetSignedUrl() string {
	if x != nil {
		return x.SignedUrl
	}
	return ""
}

func (x *GenerateArthubTempDownloadUrlSucceedItem) GetExpire() uint64 {
	if x != nil {
		return x.Expire
	}
	return 0
}

type GenerateArthubTempDownloadUrlFailedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // 文件在S3上的路径
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                // 失败原因
}

func (x *GenerateArthubTempDownloadUrlFailedItem) Reset() {
	*x = GenerateArthubTempDownloadUrlFailedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateArthubTempDownloadUrlFailedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateArthubTempDownloadUrlFailedItem) ProtoMessage() {}

func (x *GenerateArthubTempDownloadUrlFailedItem) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateArthubTempDownloadUrlFailedItem.ProtoReflect.Descriptor instead.
func (*GenerateArthubTempDownloadUrlFailedItem) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{53}
}

func (x *GenerateArthubTempDownloadUrlFailedItem) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *GenerateArthubTempDownloadUrlFailedItem) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GenerateArthubTempDownloadUrlRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result       *aix.Result                                 `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                 // 返回结果
	SucceedItems []*GenerateArthubTempDownloadUrlSucceedItem `protobuf:"bytes,2,rep,name=succeed_items,json=succeedItems,proto3" json:"succeed_items,omitempty"` // 成功生成的arthub临时下载url
	FailedItems  []*GenerateArthubTempDownloadUrlFailedItem  `protobuf:"bytes,3,rep,name=failed_items,json=failedItems,proto3" json:"failed_items,omitempty"`    // 生成arthub临时下载url失败的素材列表信息
}

func (x *GenerateArthubTempDownloadUrlRsp) Reset() {
	*x = GenerateArthubTempDownloadUrlRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateArthubTempDownloadUrlRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateArthubTempDownloadUrlRsp) ProtoMessage() {}

func (x *GenerateArthubTempDownloadUrlRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateArthubTempDownloadUrlRsp.ProtoReflect.Descriptor instead.
func (*GenerateArthubTempDownloadUrlRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{54}
}

func (x *GenerateArthubTempDownloadUrlRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GenerateArthubTempDownloadUrlRsp) GetSucceedItems() []*GenerateArthubTempDownloadUrlSucceedItem {
	if x != nil {
		return x.SucceedItems
	}
	return nil
}

func (x *GenerateArthubTempDownloadUrlRsp) GetFailedItems() []*GenerateArthubTempDownloadUrlFailedItem {
	if x != nil {
		return x.FailedItems
	}
	return nil
}

// 获取arthub gamecode信息, POST, /api/v1/material_display/list_depot
type ListDepotReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset uint64 `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"` // 偏移量
	Limit  uint64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`   // 每次请求最大数量, 默认返回10 条数据
}

func (x *ListDepotReq) Reset() {
	*x = ListDepotReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDepotReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepotReq) ProtoMessage() {}

func (x *ListDepotReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepotReq.ProtoReflect.Descriptor instead.
func (*ListDepotReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{55}
}

func (x *ListDepotReq) GetOffset() uint64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ListDepotReq) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListDepotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepotId   string `protobuf:"bytes,1,opt,name=depot_id,json=depotId,proto3" json:"depot_id,omitempty"`       // depot id
	DepotName string `protobuf:"bytes,2,opt,name=depot_name,json=depotName,proto3" json:"depot_name,omitempty"` // depot名称
	GameCode  string `protobuf:"bytes,3,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`    // game_code
	GameName  string `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`    // game_code名称
}

func (x *ListDepotInfo) Reset() {
	*x = ListDepotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDepotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepotInfo) ProtoMessage() {}

func (x *ListDepotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepotInfo.ProtoReflect.Descriptor instead.
func (*ListDepotInfo) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{56}
}

func (x *ListDepotInfo) GetDepotId() string {
	if x != nil {
		return x.DepotId
	}
	return ""
}

func (x *ListDepotInfo) GetDepotName() string {
	if x != nil {
		return x.DepotName
	}
	return ""
}

func (x *ListDepotInfo) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *ListDepotInfo) GetGameName() string {
	if x != nil {
		return x.GameName
	}
	return ""
}

type ListDepotRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result      `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Items  []*ListDepotInfo `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`   // depot信息列表
	Total  uint64           `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`  // gamecode总数
}

func (x *ListDepotRsp) Reset() {
	*x = ListDepotRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDepotRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDepotRsp) ProtoMessage() {}

func (x *ListDepotRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDepotRsp.ProtoReflect.Descriptor instead.
func (*ListDepotRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{57}
}

func (x *ListDepotRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ListDepotRsp) GetItems() []*ListDepotInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListDepotRsp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 上传素材上报, POST, /api/v1/material_display/upload_to_arthub
type UploadToArthubReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetIdList []string                 `protobuf:"bytes,1,rep,name=asset_id_list,json=assetIdList,proto3" json:"asset_id_list,omitempty"` // 上传素材id
	GameCode    string                   `protobuf:"bytes,2,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`            // 素材game_code，即素材来源
	IsMeta      bool                     `protobuf:"varint,3,opt,name=is_meta,json=isMeta,proto3" json:"is_meta,omitempty"`                 // 是否提供素材详情信息，false: 无需提供素材详细信息；true: 提供素材详情信息
	Metas       []*UploadToArthubReqMeta `protobuf:"bytes,4,rep,name=metas,proto3" json:"metas,omitempty"`                                  // 素材详情信息
	AixUploader string                   `protobuf:"bytes,5,opt,name=aix_uploader,json=aixUploader,proto3" json:"aix_uploader,omitempty"`   // aix平台的上传人
}

func (x *UploadToArthubReq) Reset() {
	*x = UploadToArthubReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadToArthubReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadToArthubReq) ProtoMessage() {}

func (x *UploadToArthubReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadToArthubReq.ProtoReflect.Descriptor instead.
func (*UploadToArthubReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{58}
}

func (x *UploadToArthubReq) GetAssetIdList() []string {
	if x != nil {
		return x.AssetIdList
	}
	return nil
}

func (x *UploadToArthubReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *UploadToArthubReq) GetIsMeta() bool {
	if x != nil {
		return x.IsMeta
	}
	return false
}

func (x *UploadToArthubReq) GetMetas() []*UploadToArthubReqMeta {
	if x != nil {
		return x.Metas
	}
	return nil
}

func (x *UploadToArthubReq) GetAixUploader() string {
	if x != nil {
		return x.AixUploader
	}
	return ""
}

type UploadToArthubReqMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId  string `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // 上传素材id
	Width    uint64 `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`                   // 宽
	High     uint64 `protobuf:"varint,3,opt,name=high,proto3" json:"high,omitempty"`                     // 高
	Duration string `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`              // 时长（视频类）
}

func (x *UploadToArthubReqMeta) Reset() {
	*x = UploadToArthubReqMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadToArthubReqMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadToArthubReqMeta) ProtoMessage() {}

func (x *UploadToArthubReqMeta) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadToArthubReqMeta.ProtoReflect.Descriptor instead.
func (*UploadToArthubReqMeta) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{59}
}

func (x *UploadToArthubReqMeta) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *UploadToArthubReqMeta) GetWidth() uint64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *UploadToArthubReqMeta) GetHigh() uint64 {
	if x != nil {
		return x.High
	}
	return 0
}

func (x *UploadToArthubReqMeta) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

type UploadToArthubRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                            // 返回结果
	SucceedAssetIds []string    `protobuf:"bytes,2,rep,name=succeed_asset_ids,json=succeedAssetIds,proto3" json:"succeed_asset_ids,omitempty"` // 成功上报asset信息的素材id
	FailedAssetIds  []string    `protobuf:"bytes,3,rep,name=failed_asset_ids,json=failedAssetIds,proto3" json:"failed_asset_ids,omitempty"`    // 成功上报asset信息的素材id
}

func (x *UploadToArthubRsp) Reset() {
	*x = UploadToArthubRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadToArthubRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadToArthubRsp) ProtoMessage() {}

func (x *UploadToArthubRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadToArthubRsp.ProtoReflect.Descriptor instead.
func (*UploadToArthubRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{60}
}

func (x *UploadToArthubRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *UploadToArthubRsp) GetSucceedAssetIds() []string {
	if x != nil {
		return x.SucceedAssetIds
	}
	return nil
}

func (x *UploadToArthubRsp) GetFailedAssetIds() []string {
	if x != nil {
		return x.FailedAssetIds
	}
	return nil
}

// 获取用户上传的素材列表请求, POST, /api/v1/material_display/media_material_list
type MediaMaterialListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offset          uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`                                            // 起始偏移
	Count           uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                              // 拉取数量
	FormatType      uint32   `protobuf:"varint,3,opt,name=format_type,json=formatType,proto3" json:"format_type,omitempty"`                  // 是否类型，0-全部，1-视频，2-图片
	Media           uint32   `protobuf:"varint,4,opt,name=media,proto3" json:"media,omitempty"`                                              // 1-google 2-facebook
	GameCode        string   `protobuf:"bytes,5,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                         // game_code
	AccountId       string   `protobuf:"bytes,6,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                      // 上传账号
	CampaignSubType uint32   `protobuf:"varint,7,opt,name=campaign_sub_type,json=campaignSubType,proto3" json:"campaign_sub_type,omitempty"` // campaign sub_type: 具体值查看枚举: google_advertise.proto CampaignSubType
	Keyword         string   `protobuf:"bytes,8,opt,name=keyword,proto3" json:"keyword,omitempty"`                                           // 素材名称模糊搜索关键字
	AssetNames      []string `protobuf:"bytes,9,rep,name=asset_names,json=assetNames,proto3" json:"asset_names,omitempty"`                   // 素材名称批量精确搜索，若指定asset_names，则不分页
	AssetRatios     []string `protobuf:"bytes,10,rep,name=asset_ratios,json=assetRatios,proto3" json:"asset_ratios,omitempty"`               // 需要筛选的素材比例, 不填默认不进行筛选
}

func (x *MediaMaterialListReq) Reset() {
	*x = MediaMaterialListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialListReq) ProtoMessage() {}

func (x *MediaMaterialListReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialListReq.ProtoReflect.Descriptor instead.
func (*MediaMaterialListReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{61}
}

func (x *MediaMaterialListReq) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *MediaMaterialListReq) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *MediaMaterialListReq) GetFormatType() uint32 {
	if x != nil {
		return x.FormatType
	}
	return 0
}

func (x *MediaMaterialListReq) GetMedia() uint32 {
	if x != nil {
		return x.Media
	}
	return 0
}

func (x *MediaMaterialListReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *MediaMaterialListReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MediaMaterialListReq) GetCampaignSubType() uint32 {
	if x != nil {
		return x.CampaignSubType
	}
	return 0
}

func (x *MediaMaterialListReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *MediaMaterialListReq) GetAssetNames() []string {
	if x != nil {
		return x.AssetNames
	}
	return nil
}

func (x *MediaMaterialListReq) GetAssetRatios() []string {
	if x != nil {
		return x.AssetRatios
	}
	return nil
}

// 用户上传的素材元数据
type MediaMaterialMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId      string                   `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                  // 素材索引
	Name         string                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                       // 素材名称
	Status       uint32                   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`                                  // 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
	UplineDate   string                   `protobuf:"bytes,4,opt,name=upline_date,json=uplineDate,proto3" json:"upline_date,omitempty"`         // 上线日期
	OfflineDate  string                   `protobuf:"bytes,5,opt,name=offline_date,json=offlineDate,proto3" json:"offline_date,omitempty"`      // 下线日期
	OnlineDays   uint32                   `protobuf:"varint,6,opt,name=online_days,json=onlineDays,proto3" json:"online_days,omitempty"`        // 在线天数
	Format       string                   `protobuf:"bytes,7,opt,name=format,proto3" json:"format,omitempty"`                                   // 素材格式
	Duration     string                   `protobuf:"bytes,8,opt,name=duration,proto3" json:"duration,omitempty"`                               // 素材时长
	PreviewUrl   string                   `protobuf:"bytes,9,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`         // 素材预览url
	CreateDate   string                   `protobuf:"bytes,10,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`        // 创建时间
	OriginUrl    string                   `protobuf:"bytes,11,opt,name=origin_url,json=originUrl,proto3" json:"origin_url,omitempty"`           // arthub的originUrl
	Detail       *MediaMaterialMetaDetail `protobuf:"bytes,12,opt,name=detail,proto3" json:"detail,omitempty"`                                  // 素材详情
	ResourceName string                   `protobuf:"bytes,13,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"`  // resource_name
	CoverHash    string                   `protobuf:"bytes,14,opt,name=cover_hash,json=coverHash,proto3" json:"cover_hash,omitempty"`           // 封面hash
	AssetStatus  int32                    `protobuf:"varint,15,opt,name=asset_status,json=assetStatus,proto3" json:"asset_status,omitempty"`    // 素材在仓库状态, 1-正常, 2-回收站, 3-删除
	OnlineStatus int32                    `protobuf:"varint,16,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"` // 素材曝光状态
	OnlineDate   string                   `protobuf:"bytes,17,opt,name=online_date,json=onlineDate,proto3" json:"online_date,omitempty"`        // 素材第一次曝光时间
	AssetRatio   string                   `protobuf:"bytes,18,opt,name=asset_ratio,json=assetRatio,proto3" json:"asset_ratio,omitempty"`        // 素材宽高比, 精确到小数点后2位, 如1.00
}

func (x *MediaMaterialMeta) Reset() {
	*x = MediaMaterialMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialMeta) ProtoMessage() {}

func (x *MediaMaterialMeta) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialMeta.ProtoReflect.Descriptor instead.
func (*MediaMaterialMeta) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{62}
}

func (x *MediaMaterialMeta) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *MediaMaterialMeta) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MediaMaterialMeta) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MediaMaterialMeta) GetUplineDate() string {
	if x != nil {
		return x.UplineDate
	}
	return ""
}

func (x *MediaMaterialMeta) GetOfflineDate() string {
	if x != nil {
		return x.OfflineDate
	}
	return ""
}

func (x *MediaMaterialMeta) GetOnlineDays() uint32 {
	if x != nil {
		return x.OnlineDays
	}
	return 0
}

func (x *MediaMaterialMeta) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *MediaMaterialMeta) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

func (x *MediaMaterialMeta) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *MediaMaterialMeta) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *MediaMaterialMeta) GetOriginUrl() string {
	if x != nil {
		return x.OriginUrl
	}
	return ""
}

func (x *MediaMaterialMeta) GetDetail() *MediaMaterialMetaDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *MediaMaterialMeta) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *MediaMaterialMeta) GetCoverHash() string {
	if x != nil {
		return x.CoverHash
	}
	return ""
}

func (x *MediaMaterialMeta) GetAssetStatus() int32 {
	if x != nil {
		return x.AssetStatus
	}
	return 0
}

func (x *MediaMaterialMeta) GetOnlineStatus() int32 {
	if x != nil {
		return x.OnlineStatus
	}
	return 0
}

func (x *MediaMaterialMeta) GetOnlineDate() string {
	if x != nil {
		return x.OnlineDate
	}
	return ""
}

func (x *MediaMaterialMeta) GetAssetRatio() string {
	if x != nil {
		return x.AssetRatio
	}
	return ""
}

// 素材详情
type MediaMaterialMetaDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size              uint64 `protobuf:"varint,1,opt,name=size,proto3" json:"size,omitempty"`                                                     // 素材大小
	UpdateDate        string `protobuf:"bytes,2,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`                        // 更新日期
	Creator           string `protobuf:"bytes,3,opt,name=creator,proto3" json:"creator,omitempty"`                                                // 素材创建者
	Updater           string `protobuf:"bytes,4,opt,name=updater,proto3" json:"updater,omitempty"`                                                // 素材更新者
	ManualFirstLabel  string `protobuf:"bytes,5,opt,name=manual_first_label,json=manualFirstLabel,proto3" json:"manual_first_label,omitempty"`    // 人工一级标签
	ManualSecondLabel string `protobuf:"bytes,6,opt,name=manual_second_label,json=manualSecondLabel,proto3" json:"manual_second_label,omitempty"` // 人工二级标签
	Width             uint32 `protobuf:"varint,7,opt,name=width,proto3" json:"width,omitempty"`                                                   // 宽
	High              uint32 `protobuf:"varint,8,opt,name=high,proto3" json:"high,omitempty"`                                                     // 高
	FrameRate         string `protobuf:"bytes,9,opt,name=frame_rate,json=frameRate,proto3" json:"frame_rate,omitempty"`                           // 帧率（视频类）
	AspectRatio       string `protobuf:"bytes,10,opt,name=aspect_ratio,json=aspectRatio,proto3" json:"aspect_ratio,omitempty"`                    // 比例（视频类）
	BitRate           string `protobuf:"bytes,11,opt,name=bit_rate,json=bitRate,proto3" json:"bit_rate,omitempty"`                                // 视频比特率
	CompressionFormat string `protobuf:"bytes,12,opt,name=compression_format,json=compressionFormat,proto3" json:"compression_format,omitempty"`  // 视频压缩格式
	RobotFirstLabel   string `protobuf:"bytes,13,opt,name=robot_first_label,json=robotFirstLabel,proto3" json:"robot_first_label,omitempty"`      // 第1层机器标签
	RobotSecondLabel  string `protobuf:"bytes,14,opt,name=robot_second_label,json=robotSecondLabel,proto3" json:"robot_second_label,omitempty"`   // 第2层机器标签
	Cover             string `protobuf:"bytes,15,opt,name=cover,proto3" json:"cover,omitempty"`                                                   // 封面
}

func (x *MediaMaterialMetaDetail) Reset() {
	*x = MediaMaterialMetaDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialMetaDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialMetaDetail) ProtoMessage() {}

func (x *MediaMaterialMetaDetail) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialMetaDetail.ProtoReflect.Descriptor instead.
func (*MediaMaterialMetaDetail) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{63}
}

func (x *MediaMaterialMetaDetail) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *MediaMaterialMetaDetail) GetUpdateDate() string {
	if x != nil {
		return x.UpdateDate
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetManualFirstLabel() string {
	if x != nil {
		return x.ManualFirstLabel
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetManualSecondLabel() string {
	if x != nil {
		return x.ManualSecondLabel
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *MediaMaterialMetaDetail) GetHigh() uint32 {
	if x != nil {
		return x.High
	}
	return 0
}

func (x *MediaMaterialMetaDetail) GetFrameRate() string {
	if x != nil {
		return x.FrameRate
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetAspectRatio() string {
	if x != nil {
		return x.AspectRatio
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetBitRate() string {
	if x != nil {
		return x.BitRate
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetCompressionFormat() string {
	if x != nil {
		return x.CompressionFormat
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetRobotFirstLabel() string {
	if x != nil {
		return x.RobotFirstLabel
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetRobotSecondLabel() string {
	if x != nil {
		return x.RobotSecondLabel
	}
	return ""
}

func (x *MediaMaterialMetaDetail) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

type MediaMaterialListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result          `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MediaMaterialMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 用户素材列表
	Total     uint32               `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 素材总数
}

func (x *MediaMaterialListRsp) Reset() {
	*x = MediaMaterialListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialListRsp) ProtoMessage() {}

func (x *MediaMaterialListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialListRsp.ProtoReflect.Descriptor instead.
func (*MediaMaterialListRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{64}
}

func (x *MediaMaterialListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *MediaMaterialListRsp) GetMaterials() []*MediaMaterialMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

func (x *MediaMaterialListRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取其他信息, POST, /api/v1/material_display/get_ext_info
type GetExtInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetExtInfoReq) Reset() {
	*x = GetExtInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExtInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExtInfoReq) ProtoMessage() {}

func (x *GetExtInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExtInfoReq.ProtoReflect.Descriptor instead.
func (*GetExtInfoReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{65}
}

type GetExtInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                        // 返回结果
	LabelList []string    `protobuf:"bytes,2,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"` // 一级标签下拉列表
}

func (x *GetExtInfoRsp) Reset() {
	*x = GetExtInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExtInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExtInfoRsp) ProtoMessage() {}

func (x *GetExtInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExtInfoRsp.ProtoReflect.Descriptor instead.
func (*GetExtInfoRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{66}
}

func (x *GetExtInfoRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetExtInfoRsp) GetLabelList() []string {
	if x != nil {
		return x.LabelList
	}
	return nil
}

// 通过目录获取媒体列表请求, POST, /api/v1/material_display/media_material_by_directory_id
type MediaMaterialByDirectoryIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid                uint64 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`                                                            // 保留字段
	Uuid               string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`                                                           // 前端生成随机ID
	Offset             uint32 `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`                                                      // 起始偏移
	Count              uint32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                                                        // 拉取数量
	IsFilterStatus     uint32 `protobuf:"varint,5,opt,name=is_filter_status,json=isFilterStatus,proto3" json:"is_filter_status,omitempty"`              // 是否按照状态过滤，0-否，1-是
	Status             uint32 `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`                                                      // 过滤状态，is_filter_status=1生效 0-未上传；1-正在上传；2-上传成功；3-上传失败
	DirectoryId        string `protobuf:"bytes,7,opt,name=directory_id,json=directoryId,proto3" json:"directory_id,omitempty"`                          // 目录ID
	UploadState        uint32 `protobuf:"varint,8,opt,name=upload_state,json=uploadState,proto3" json:"upload_state,omitempty"`                         // (已废弃) 是否上传至广告库，0-否，1-是
	WithDetail         uint32 `protobuf:"varint,9,opt,name=with_detail,json=withDetail,proto3" json:"with_detail,omitempty"`                            // 是否需要拉取详情 0-不需要 1-需要
	OnlineStatus       uint32 `protobuf:"varint,10,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"`                     // 在线状态筛选
	FilterOnlineStatus bool   `protobuf:"varint,11,opt,name=filter_online_status,json=filterOnlineStatus,proto3" json:"filter_online_status,omitempty"` // 是否过滤在线状态
}

func (x *MediaMaterialByDirectoryIdReq) Reset() {
	*x = MediaMaterialByDirectoryIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialByDirectoryIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialByDirectoryIdReq) ProtoMessage() {}

func (x *MediaMaterialByDirectoryIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialByDirectoryIdReq.ProtoReflect.Descriptor instead.
func (*MediaMaterialByDirectoryIdReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{67}
}

func (x *MediaMaterialByDirectoryIdReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdReq) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetIsFilterStatus() uint32 {
	if x != nil {
		return x.IsFilterStatus
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetDirectoryId() string {
	if x != nil {
		return x.DirectoryId
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdReq) GetUploadState() uint32 {
	if x != nil {
		return x.UploadState
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetWithDetail() uint32 {
	if x != nil {
		return x.WithDetail
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetOnlineStatus() uint32 {
	if x != nil {
		return x.OnlineStatus
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdReq) GetFilterOnlineStatus() bool {
	if x != nil {
		return x.FilterOnlineStatus
	}
	return false
}

// 素材元数据
type MediaMaterialByDirectoryIdMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetId      string       `protobuf:"bytes,1,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                  // 素材索引
	Name         string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                       // 素材名称
	Status       uint32       `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`                                  // 状态 0-未上传；1-正在上传；2-上传成功；3-上传失败
	UplineDate   string       `protobuf:"bytes,4,opt,name=upline_date,json=uplineDate,proto3" json:"upline_date,omitempty"`         // 上线日期
	OfflineDate  string       `protobuf:"bytes,5,opt,name=offline_date,json=offlineDate,proto3" json:"offline_date,omitempty"`      // 下线日期
	OnlineDays   uint32       `protobuf:"varint,6,opt,name=online_days,json=onlineDays,proto3" json:"online_days,omitempty"`        // 在线天数
	Formate      string       `protobuf:"bytes,7,opt,name=formate,proto3" json:"formate,omitempty"`                                 // 素材格式
	Duration     string       `protobuf:"bytes,8,opt,name=duration,proto3" json:"duration,omitempty"`                               // 素材时长
	PreviewUrl   string       `protobuf:"bytes,9,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`         // 素材预览url
	CreateDate   string       `protobuf:"bytes,10,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"`        // 创建时间
	MaterialExt  *MaterialExt `protobuf:"bytes,11,opt,name=material_ext,json=materialExt,proto3" json:"material_ext,omitempty"`     // 素材详情
	AssetStatus  int32        `protobuf:"varint,12,opt,name=asset_status,json=assetStatus,proto3" json:"asset_status,omitempty"`    // 素材在仓库状态, 1-正常, 2-回收站, 3-删除
	OnlineStatus int32        `protobuf:"varint,13,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"` // 素材曝光状态
	OnlineDate   string       `protobuf:"bytes,14,opt,name=online_date,json=onlineDate,proto3" json:"online_date,omitempty"`        // 素材第一次曝光时间
}

func (x *MediaMaterialByDirectoryIdMeta) Reset() {
	*x = MediaMaterialByDirectoryIdMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialByDirectoryIdMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialByDirectoryIdMeta) ProtoMessage() {}

func (x *MediaMaterialByDirectoryIdMeta) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialByDirectoryIdMeta.ProtoReflect.Descriptor instead.
func (*MediaMaterialByDirectoryIdMeta) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{68}
}

func (x *MediaMaterialByDirectoryIdMeta) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdMeta) GetUplineDate() string {
	if x != nil {
		return x.UplineDate
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetOfflineDate() string {
	if x != nil {
		return x.OfflineDate
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetOnlineDays() uint32 {
	if x != nil {
		return x.OnlineDays
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdMeta) GetFormate() string {
	if x != nil {
		return x.Formate
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *MediaMaterialByDirectoryIdMeta) GetMaterialExt() *MaterialExt {
	if x != nil {
		return x.MaterialExt
	}
	return nil
}

func (x *MediaMaterialByDirectoryIdMeta) GetAssetStatus() int32 {
	if x != nil {
		return x.AssetStatus
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdMeta) GetOnlineStatus() int32 {
	if x != nil {
		return x.OnlineStatus
	}
	return 0
}

func (x *MediaMaterialByDirectoryIdMeta) GetOnlineDate() string {
	if x != nil {
		return x.OnlineDate
	}
	return ""
}

type MediaMaterialByDirectoryIdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result                       `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MediaMaterialByDirectoryIdMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 素材列表
	Total     uint32                            `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 素材总数
}

func (x *MediaMaterialByDirectoryIdRsp) Reset() {
	*x = MediaMaterialByDirectoryIdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialByDirectoryIdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialByDirectoryIdRsp) ProtoMessage() {}

func (x *MediaMaterialByDirectoryIdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialByDirectoryIdRsp.ProtoReflect.Descriptor instead.
func (*MediaMaterialByDirectoryIdRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{69}
}

func (x *MediaMaterialByDirectoryIdRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *MediaMaterialByDirectoryIdRsp) GetMaterials() []*MediaMaterialByDirectoryIdMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

func (x *MediaMaterialByDirectoryIdRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 根据ID(resource_name)列表查询素材名称列表, POST, /api/v1/material_display/media_material_name_list
type MediaMaterialNameListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FormatType      uint32   `protobuf:"varint,1,opt,name=format_type,json=formatType,proto3" json:"format_type,omitempty"`                  // 是否类型，0-全部，1-视频，2-图片
	Media           uint32   `protobuf:"varint,2,opt,name=media,proto3" json:"media,omitempty"`                                              // 1-google 2-facebook
	GameCode        string   `protobuf:"bytes,3,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                         // game_code
	AccountId       string   `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`                      // 上传账号
	CampaignSubType uint32   `protobuf:"varint,5,opt,name=campaign_sub_type,json=campaignSubType,proto3" json:"campaign_sub_type,omitempty"` // campaign sub_type: 具体值查看枚举: google_advertise.proto CampaignSubType
	ResourceNames   []string `protobuf:"bytes,6,rep,name=resource_names,json=resourceNames,proto3" json:"resource_names,omitempty"`          // 素材ID(resource_name)列表
}

func (x *MediaMaterialNameListReq) Reset() {
	*x = MediaMaterialNameListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialNameListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialNameListReq) ProtoMessage() {}

func (x *MediaMaterialNameListReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialNameListReq.ProtoReflect.Descriptor instead.
func (*MediaMaterialNameListReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{70}
}

func (x *MediaMaterialNameListReq) GetFormatType() uint32 {
	if x != nil {
		return x.FormatType
	}
	return 0
}

func (x *MediaMaterialNameListReq) GetMedia() uint32 {
	if x != nil {
		return x.Media
	}
	return 0
}

func (x *MediaMaterialNameListReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *MediaMaterialNameListReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *MediaMaterialNameListReq) GetCampaignSubType() uint32 {
	if x != nil {
		return x.CampaignSubType
	}
	return 0
}

func (x *MediaMaterialNameListReq) GetResourceNames() []string {
	if x != nil {
		return x.ResourceNames
	}
	return nil
}

type MaterialAssetNameInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceName string                   `protobuf:"bytes,1,opt,name=resource_name,json=resourceName,proto3" json:"resource_name,omitempty"` // 素材ID(resource_name)
	AssetId      string                   `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                // 素材唯一id
	AssetName    string                   `protobuf:"bytes,3,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`          // 素材名称
	Format       string                   `protobuf:"bytes,4,opt,name=format,proto3" json:"format,omitempty"`                                 // 素材格式
	Duration     string                   `protobuf:"bytes,5,opt,name=duration,proto3" json:"duration,omitempty"`                             // 素材时长
	PreviewUrl   string                   `protobuf:"bytes,6,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`       // 素材预览url
	Detail       *MediaMaterialMetaDetail `protobuf:"bytes,7,opt,name=detail,proto3" json:"detail,omitempty"`                                 // 素材详情
	CoverHash    string                   `protobuf:"bytes,8,opt,name=cover_hash,json=coverHash,proto3" json:"cover_hash,omitempty"`          // 素材封面hash, tiktok中即为封面image resource name
	AssetRatio   string                   `protobuf:"bytes,18,opt,name=asset_ratio,json=assetRatio,proto3" json:"asset_ratio,omitempty"`      // 素材宽高比, 精确到小数点后2位, 如1.00
}

func (x *MaterialAssetNameInfo) Reset() {
	*x = MaterialAssetNameInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaterialAssetNameInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaterialAssetNameInfo) ProtoMessage() {}

func (x *MaterialAssetNameInfo) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaterialAssetNameInfo.ProtoReflect.Descriptor instead.
func (*MaterialAssetNameInfo) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{71}
}

func (x *MaterialAssetNameInfo) GetResourceName() string {
	if x != nil {
		return x.ResourceName
	}
	return ""
}

func (x *MaterialAssetNameInfo) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *MaterialAssetNameInfo) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *MaterialAssetNameInfo) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

func (x *MaterialAssetNameInfo) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

func (x *MaterialAssetNameInfo) GetPreviewUrl() string {
	if x != nil {
		return x.PreviewUrl
	}
	return ""
}

func (x *MaterialAssetNameInfo) GetDetail() *MediaMaterialMetaDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *MaterialAssetNameInfo) GetCoverHash() string {
	if x != nil {
		return x.CoverHash
	}
	return ""
}

func (x *MaterialAssetNameInfo) GetAssetRatio() string {
	if x != nil {
		return x.AssetRatio
	}
	return ""
}

type MediaMaterialNameListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result         *aix.Result              `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                                         // 返回结果
	AssetNameInfos []*MaterialAssetNameInfo `protobuf:"bytes,2,rep,name=asset_name_infos,json=assetNameInfos,proto3" json:"asset_name_infos,omitempty"` // 素材信息列表
}

func (x *MediaMaterialNameListRsp) Reset() {
	*x = MediaMaterialNameListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediaMaterialNameListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaMaterialNameListRsp) ProtoMessage() {}

func (x *MediaMaterialNameListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaMaterialNameListRsp.ProtoReflect.Descriptor instead.
func (*MediaMaterialNameListRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{72}
}

func (x *MediaMaterialNameListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *MediaMaterialNameListRsp) GetAssetNameInfos() []*MaterialAssetNameInfo {
	if x != nil {
		return x.AssetNameInfos
	}
	return nil
}

// 上传渠道任务相关 ----------
type UploadChannelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Channel          uint32   `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"`                                            // 参看common constant.MediaXXXX 枚举
	MediaPath        string   `protobuf:"bytes,2,opt,name=media_path,json=mediaPath,proto3" json:"media_path,omitempty"`                        // 渠道目录名称
	MediaDirectoryId string   `protobuf:"bytes,3,opt,name=media_directory_id,json=mediaDirectoryId,proto3" json:"media_directory_id,omitempty"` // [后台用，前端不传]渠道目录id
	Accounts         string   `protobuf:"bytes,4,opt,name=accounts,proto3" json:"accounts,omitempty"`                                           // 上传账号, 多个账号用逗号分隔
	Language         string   `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`                                           // 指定素材语言, 目前unity/applovin渠道有使用
	CampaignId       string   `protobuf:"bytes,6,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`                     // 指定上传到某个campaign, 目前applovin有使用
	CreativeSetName  string   `protobuf:"bytes,7,opt,name=creative_set_name,json=creativeSetName,proto3" json:"creative_set_name,omitempty"`    // creative set name, 目前applovin有使用
	CreativeSetId    string   `protobuf:"bytes,8,opt,name=creative_set_id,json=creativeSetId,proto3" json:"creative_set_id,omitempty"`          // [后台用，前端不传]creative set id
	Countries        []string `protobuf:"bytes,9,rep,name=countries,proto3" json:"countries,omitempty"`                                         // 指定国家列表, 目前applovin有使用
}

func (x *UploadChannelInfo) Reset() {
	*x = UploadChannelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadChannelInfo) ProtoMessage() {}

func (x *UploadChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadChannelInfo.ProtoReflect.Descriptor instead.
func (*UploadChannelInfo) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{73}
}

func (x *UploadChannelInfo) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *UploadChannelInfo) GetMediaPath() string {
	if x != nil {
		return x.MediaPath
	}
	return ""
}

func (x *UploadChannelInfo) GetMediaDirectoryId() string {
	if x != nil {
		return x.MediaDirectoryId
	}
	return ""
}

func (x *UploadChannelInfo) GetAccounts() string {
	if x != nil {
		return x.Accounts
	}
	return ""
}

func (x *UploadChannelInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UploadChannelInfo) GetCampaignId() string {
	if x != nil {
		return x.CampaignId
	}
	return ""
}

func (x *UploadChannelInfo) GetCreativeSetName() string {
	if x != nil {
		return x.CreativeSetName
	}
	return ""
}

func (x *UploadChannelInfo) GetCreativeSetId() string {
	if x != nil {
		return x.CreativeSetId
	}
	return ""
}

func (x *UploadChannelInfo) GetCountries() []string {
	if x != nil {
		return x.Countries
	}
	return nil
}

// 增加上传任务, POST, /api/v1/material_display/add_upload_to_channel_task
type AddUploadToChannelTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode                string               `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Country                 string               `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	ToChannels              []*UploadChannelInfo `protobuf:"bytes,3,rep,name=to_channels,json=toChannels,proto3" json:"to_channels,omitempty"`                                                // 上传渠道配置信息
	AssetIds                []string             `protobuf:"bytes,4,rep,name=asset_ids,json=assetIds,proto3" json:"asset_ids,omitempty"`                                                      // 素材ID列表
	Creator                 string               `protobuf:"bytes,5,opt,name=creator,proto3" json:"creator,omitempty"`                                                                        // 创建人
	Accounts                string               `protobuf:"bytes,6,opt,name=accounts,proto3" json:"accounts,omitempty"`                                                                      // 指定上传账号，用逗号给开 - deprecated
	NotifyDays              uint32               `protobuf:"varint,7,opt,name=notify_days,json=notifyDays,proto3" json:"notify_days,omitempty"`                                               // 提醒天数，为0不提醒
	OfflineDate             string               `protobuf:"bytes,8,opt,name=offline_date,json=offlineDate,proto3" json:"offline_date,omitempty"`                                             // 下线日期，为空不指定
	Language                string               `protobuf:"bytes,9,opt,name=language,proto3" json:"language,omitempty"`                                                                      // 指定素材语言, 目前unity渠道有使用 - deprecated
	AutomaticSyncTaskRuleId int64                `protobuf:"varint,10,opt,name=automatic_sync_task_rule_id,json=automaticSyncTaskRuleId,proto3" json:"automatic_sync_task_rule_id,omitempty"` // 自动上传任务规则id(后台内部使用)
}

func (x *AddUploadToChannelTaskReq) Reset() {
	*x = AddUploadToChannelTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUploadToChannelTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUploadToChannelTaskReq) ProtoMessage() {}

func (x *AddUploadToChannelTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUploadToChannelTaskReq.ProtoReflect.Descriptor instead.
func (*AddUploadToChannelTaskReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{74}
}

func (x *AddUploadToChannelTaskReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *AddUploadToChannelTaskReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *AddUploadToChannelTaskReq) GetToChannels() []*UploadChannelInfo {
	if x != nil {
		return x.ToChannels
	}
	return nil
}

func (x *AddUploadToChannelTaskReq) GetAssetIds() []string {
	if x != nil {
		return x.AssetIds
	}
	return nil
}

func (x *AddUploadToChannelTaskReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *AddUploadToChannelTaskReq) GetAccounts() string {
	if x != nil {
		return x.Accounts
	}
	return ""
}

func (x *AddUploadToChannelTaskReq) GetNotifyDays() uint32 {
	if x != nil {
		return x.NotifyDays
	}
	return 0
}

func (x *AddUploadToChannelTaskReq) GetOfflineDate() string {
	if x != nil {
		return x.OfflineDate
	}
	return ""
}

func (x *AddUploadToChannelTaskReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *AddUploadToChannelTaskReq) GetAutomaticSyncTaskRuleId() int64 {
	if x != nil {
		return x.AutomaticSyncTaskRuleId
	}
	return 0
}

type AddUploadToChannelTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *AddUploadToChannelTaskRsp) Reset() {
	*x = AddUploadToChannelTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUploadToChannelTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUploadToChannelTaskRsp) ProtoMessage() {}

func (x *AddUploadToChannelTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUploadToChannelTaskRsp.ProtoReflect.Descriptor instead.
func (*AddUploadToChannelTaskRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{75}
}

func (x *AddUploadToChannelTaskRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 取消正在上传的任务, POST, /api/v1/material_display/cancel_upload_to_channel_task
type CancelUploadToChannelTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	TaskIds  []string `protobuf:"bytes,2,rep,name=task_ids,json=taskIds,proto3" json:"task_ids,omitempty"` // 任务ID列表
}

func (x *CancelUploadToChannelTaskReq) Reset() {
	*x = CancelUploadToChannelTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelUploadToChannelTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelUploadToChannelTaskReq) ProtoMessage() {}

func (x *CancelUploadToChannelTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelUploadToChannelTaskReq.ProtoReflect.Descriptor instead.
func (*CancelUploadToChannelTaskReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{76}
}

func (x *CancelUploadToChannelTaskReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CancelUploadToChannelTaskReq) GetTaskIds() []string {
	if x != nil {
		return x.TaskIds
	}
	return nil
}

type CancelUploadToChannelTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *CancelUploadToChannelTaskRsp) Reset() {
	*x = CancelUploadToChannelTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelUploadToChannelTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelUploadToChannelTaskRsp) ProtoMessage() {}

func (x *CancelUploadToChannelTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelUploadToChannelTaskRsp.ProtoReflect.Descriptor instead.
func (*CancelUploadToChannelTaskRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{77}
}

func (x *CancelUploadToChannelTaskRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 重启上传失败的任务, POST, /api/v1/material_display/resume_upload_to_channel_task
type ResumeUploadToChannelTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string   `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	TaskIds  []string `protobuf:"bytes,2,rep,name=task_ids,json=taskIds,proto3" json:"task_ids,omitempty"` // 任务ID列表
}

func (x *ResumeUploadToChannelTaskReq) Reset() {
	*x = ResumeUploadToChannelTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeUploadToChannelTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeUploadToChannelTaskReq) ProtoMessage() {}

func (x *ResumeUploadToChannelTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeUploadToChannelTaskReq.ProtoReflect.Descriptor instead.
func (*ResumeUploadToChannelTaskReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{78}
}

func (x *ResumeUploadToChannelTaskReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *ResumeUploadToChannelTaskReq) GetTaskIds() []string {
	if x != nil {
		return x.TaskIds
	}
	return nil
}

type ResumeUploadToChannelTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *ResumeUploadToChannelTaskRsp) Reset() {
	*x = ResumeUploadToChannelTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeUploadToChannelTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeUploadToChannelTaskRsp) ProtoMessage() {}

func (x *ResumeUploadToChannelTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeUploadToChannelTaskRsp.ProtoReflect.Descriptor instead.
func (*ResumeUploadToChannelTaskRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{79}
}

func (x *ResumeUploadToChannelTaskRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 获取提示关键词, POST, /api/v1/material_display/get_keyword_list
type GetKeywordListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prefix string `protobuf:"bytes,1,opt,name=prefix,proto3" json:"prefix,omitempty"` // 前缀
	Limit  uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`  // 限制数量
}

func (x *GetKeywordListReq) Reset() {
	*x = GetKeywordListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKeywordListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKeywordListReq) ProtoMessage() {}

func (x *GetKeywordListReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKeywordListReq.ProtoReflect.Descriptor instead.
func (*GetKeywordListReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{80}
}

func (x *GetKeywordListReq) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *GetKeywordListReq) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetKeywordListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result      *aix.Result   `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`           // 返回结果
	Suggestions []*Suggestion `protobuf:"bytes,2,rep,name=Suggestions,proto3" json:"Suggestions,omitempty"` // 关键词列表
}

func (x *GetKeywordListRsp) Reset() {
	*x = GetKeywordListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetKeywordListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKeywordListRsp) ProtoMessage() {}

func (x *GetKeywordListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKeywordListRsp.ProtoReflect.Descriptor instead.
func (*GetKeywordListRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{81}
}

func (x *GetKeywordListRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetKeywordListRsp) GetSuggestions() []*Suggestion {
	if x != nil {
		return x.Suggestions
	}
	return nil
}

type Suggestion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword   string `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Frequency uint32 `protobuf:"varint,2,opt,name=frequency,proto3" json:"frequency,omitempty"`
}

func (x *Suggestion) Reset() {
	*x = Suggestion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Suggestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Suggestion) ProtoMessage() {}

func (x *Suggestion) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Suggestion.ProtoReflect.Descriptor instead.
func (*Suggestion) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{82}
}

func (x *Suggestion) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *Suggestion) GetFrequency() uint32 {
	if x != nil {
		return x.Frequency
	}
	return 0
}

type LabelOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LabelName string `protobuf:"bytes,1,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	Options   string `protobuf:"bytes,2,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *LabelOption) Reset() {
	*x = LabelOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelOption) ProtoMessage() {}

func (x *LabelOption) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelOption.ProtoReflect.Descriptor instead.
func (*LabelOption) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{83}
}

func (x *LabelOption) GetLabelName() string {
	if x != nil {
		return x.LabelName
	}
	return ""
}

func (x *LabelOption) GetOptions() string {
	if x != nil {
		return x.Options
	}
	return ""
}

// 标签规则
type LabelRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 自增id
	GameCode         string         `protobuf:"bytes,2,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Rule             string         `protobuf:"bytes,3,opt,name=rule,proto3" json:"rule,omitempty"`       // 规则名 如：RG3030
	Type             int32          `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`      // 规则类型，目前只有 2:前缀
	Labels           []*AssetLabel  `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`   // 配置的标签
	Creater          string         `protobuf:"bytes,6,opt,name=creater,proto3" json:"creater,omitempty"` // 创建者
	Updater          string         `protobuf:"bytes,7,opt,name=updater,proto3" json:"updater,omitempty"` // 修改人
	CreateTime       string         `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime       string         `protobuf:"bytes,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	LabelOptions     []*LabelOption `protobuf:"bytes,10,rep,name=label_options,json=labelOptions,proto3" json:"label_options,omitempty"`
	ImpressionStatus string         `protobuf:"bytes,11,opt,name=impression_status,json=impressionStatus,proto3" json:"impression_status,omitempty"` // 曝光状态
	ImpressionDate   string         `protobuf:"bytes,12,opt,name=impression_date,json=impressionDate,proto3" json:"impression_date,omitempty"`       // 曝光日期
	OfflineDate      string         `protobuf:"bytes,13,opt,name=offline_date,json=offlineDate,proto3" json:"offline_date,omitempty"`                // 下线日期
	LinkAsset        *MaterialMeta  `protobuf:"bytes,14,opt,name=link_asset,json=linkAsset,proto3" json:"link_asset,omitempty"`                      // 关联的素材信息-如果有
}

func (x *LabelRule) Reset() {
	*x = LabelRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelRule) ProtoMessage() {}

func (x *LabelRule) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelRule.ProtoReflect.Descriptor instead.
func (*LabelRule) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{84}
}

func (x *LabelRule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LabelRule) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *LabelRule) GetRule() string {
	if x != nil {
		return x.Rule
	}
	return ""
}

func (x *LabelRule) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *LabelRule) GetLabels() []*AssetLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *LabelRule) GetCreater() string {
	if x != nil {
		return x.Creater
	}
	return ""
}

func (x *LabelRule) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *LabelRule) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *LabelRule) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *LabelRule) GetLabelOptions() []*LabelOption {
	if x != nil {
		return x.LabelOptions
	}
	return nil
}

func (x *LabelRule) GetImpressionStatus() string {
	if x != nil {
		return x.ImpressionStatus
	}
	return ""
}

func (x *LabelRule) GetImpressionDate() string {
	if x != nil {
		return x.ImpressionDate
	}
	return ""
}

func (x *LabelRule) GetOfflineDate() string {
	if x != nil {
		return x.OfflineDate
	}
	return ""
}

func (x *LabelRule) GetLinkAsset() *MaterialMeta {
	if x != nil {
		return x.LinkAsset
	}
	return nil
}

// 拉取标签规则, POST, /api/v1/material_display/get_label_rules
type GetLabelRulesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode            string        `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Names               []string      `protobuf:"bytes,2,rep,name=names,proto3" json:"names,omitempty"` // 名字匹配，或
	Labels              []*AssetLabel `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	LabelSearchType     int32         `protobuf:"varint,4,opt,name=label_search_type,json=labelSearchType,proto3" json:"label_search_type,omitempty"`            // 标签搜索方式, 0-默认(取并集), 1-取并集; 2-取交集
	ImpressionStatus    string        `protobuf:"bytes,5,opt,name=impression_status,json=impressionStatus,proto3" json:"impression_status,omitempty"`            // 非空时 过滤Published/Unpublished
	StartImpressionDate string        `protobuf:"bytes,6,opt,name=start_impression_date,json=startImpressionDate,proto3" json:"start_impression_date,omitempty"` // 非空时 >=曝光日期20240101
	EndImpressionDate   string        `protobuf:"bytes,7,opt,name=end_impression_date,json=endImpressionDate,proto3" json:"end_impression_date,omitempty"`       // 非空时 <=曝光日期20240103
	Page                int32         `protobuf:"varint,8,opt,name=page,proto3" json:"page,omitempty"`                                                           // 分页数，从0开始
	PageSize            int32         `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`                                   // 每页条数，最多100条
	StartOfflineDate    string        `protobuf:"bytes,10,opt,name=start_offline_date,json=startOfflineDate,proto3" json:"start_offline_date,omitempty"`         // 非空时 >=下线日期20240101
	EndOfflineDate      string        `protobuf:"bytes,11,opt,name=end_offline_date,json=endOfflineDate,proto3" json:"end_offline_date,omitempty"`               // 非空时 <=下线日期20240103
}

func (x *GetLabelRulesReq) Reset() {
	*x = GetLabelRulesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLabelRulesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLabelRulesReq) ProtoMessage() {}

func (x *GetLabelRulesReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLabelRulesReq.ProtoReflect.Descriptor instead.
func (*GetLabelRulesReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{85}
}

func (x *GetLabelRulesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetLabelRulesReq) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *GetLabelRulesReq) GetLabels() []*AssetLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *GetLabelRulesReq) GetLabelSearchType() int32 {
	if x != nil {
		return x.LabelSearchType
	}
	return 0
}

func (x *GetLabelRulesReq) GetImpressionStatus() string {
	if x != nil {
		return x.ImpressionStatus
	}
	return ""
}

func (x *GetLabelRulesReq) GetStartImpressionDate() string {
	if x != nil {
		return x.StartImpressionDate
	}
	return ""
}

func (x *GetLabelRulesReq) GetEndImpressionDate() string {
	if x != nil {
		return x.EndImpressionDate
	}
	return ""
}

func (x *GetLabelRulesReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetLabelRulesReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetLabelRulesReq) GetStartOfflineDate() string {
	if x != nil {
		return x.StartOfflineDate
	}
	return ""
}

func (x *GetLabelRulesReq) GetEndOfflineDate() string {
	if x != nil {
		return x.EndOfflineDate
	}
	return ""
}

type GetLabelRulesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result  `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Rules  []*LabelRule `protobuf:"bytes,2,rep,name=rules,proto3" json:"rules,omitempty"`   // 标签规则列表
	Total  uint32       `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`  // 标签规则总数
}

func (x *GetLabelRulesRsp) Reset() {
	*x = GetLabelRulesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLabelRulesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLabelRulesRsp) ProtoMessage() {}

func (x *GetLabelRulesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLabelRulesRsp.ProtoReflect.Descriptor instead.
func (*GetLabelRulesRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{86}
}

func (x *GetLabelRulesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetLabelRulesRsp) GetRules() []*LabelRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *GetLabelRulesRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 导出标签规则, POST, /api/v1/material_display/download_label_rules
type DownloadLabelRulesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode            string        `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Names               []string      `protobuf:"bytes,2,rep,name=names,proto3" json:"names,omitempty"` // 名字匹配，或
	Labels              []*AssetLabel `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	LabelSearchType     int32         `protobuf:"varint,4,opt,name=label_search_type,json=labelSearchType,proto3" json:"label_search_type,omitempty"`            // 标签搜索方式, 0-默认(取并集), 1-取并集; 2-取交集
	ImpressionStatus    string        `protobuf:"bytes,5,opt,name=impression_status,json=impressionStatus,proto3" json:"impression_status,omitempty"`            // 非空时 过滤Published/Unpublished
	StartImpressionDate string        `protobuf:"bytes,6,opt,name=start_impression_date,json=startImpressionDate,proto3" json:"start_impression_date,omitempty"` // 非空时 >=曝光日期20240101
	EndImpressionDate   string        `protobuf:"bytes,7,opt,name=end_impression_date,json=endImpressionDate,proto3" json:"end_impression_date,omitempty"`       // 非空时 <=曝光日期20240103
	StartOfflineDate    string        `protobuf:"bytes,8,opt,name=start_offline_date,json=startOfflineDate,proto3" json:"start_offline_date,omitempty"`          // 非空时 >=下线日期20240101
	EndOfflineDate      string        `protobuf:"bytes,9,opt,name=end_offline_date,json=endOfflineDate,proto3" json:"end_offline_date,omitempty"`                // 非空时 <=下线日期20240103
}

func (x *DownloadLabelRulesReq) Reset() {
	*x = DownloadLabelRulesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadLabelRulesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadLabelRulesReq) ProtoMessage() {}

func (x *DownloadLabelRulesReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadLabelRulesReq.ProtoReflect.Descriptor instead.
func (*DownloadLabelRulesReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{87}
}

func (x *DownloadLabelRulesReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *DownloadLabelRulesReq) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *DownloadLabelRulesReq) GetLabels() []*AssetLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *DownloadLabelRulesReq) GetLabelSearchType() int32 {
	if x != nil {
		return x.LabelSearchType
	}
	return 0
}

func (x *DownloadLabelRulesReq) GetImpressionStatus() string {
	if x != nil {
		return x.ImpressionStatus
	}
	return ""
}

func (x *DownloadLabelRulesReq) GetStartImpressionDate() string {
	if x != nil {
		return x.StartImpressionDate
	}
	return ""
}

func (x *DownloadLabelRulesReq) GetEndImpressionDate() string {
	if x != nil {
		return x.EndImpressionDate
	}
	return ""
}

func (x *DownloadLabelRulesReq) GetStartOfflineDate() string {
	if x != nil {
		return x.StartOfflineDate
	}
	return ""
}

func (x *DownloadLabelRulesReq) GetEndOfflineDate() string {
	if x != nil {
		return x.EndOfflineDate
	}
	return ""
}

type DownloadLabelRulesRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result  *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                  // 返回结果
	CosFile string      `protobuf:"bytes,2,opt,name=cos_file,json=cosFile,proto3" json:"cos_file,omitempty"` // 生成的下载文件cos地址
}

func (x *DownloadLabelRulesRsp) Reset() {
	*x = DownloadLabelRulesRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadLabelRulesRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadLabelRulesRsp) ProtoMessage() {}

func (x *DownloadLabelRulesRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadLabelRulesRsp.ProtoReflect.Descriptor instead.
func (*DownloadLabelRulesRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{88}
}

func (x *DownloadLabelRulesRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *DownloadLabelRulesRsp) GetCosFile() string {
	if x != nil {
		return x.CosFile
	}
	return ""
}

// 增加/更新标签规则, POST, /api/v1/material_display/add_label_rule
type AddLabelRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rule *LabelRule `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *AddLabelRuleReq) Reset() {
	*x = AddLabelRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLabelRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLabelRuleReq) ProtoMessage() {}

func (x *AddLabelRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLabelRuleReq.ProtoReflect.Descriptor instead.
func (*AddLabelRuleReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{89}
}

func (x *AddLabelRuleReq) GetRule() *LabelRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

type AddLabelRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *AddLabelRuleRsp) Reset() {
	*x = AddLabelRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLabelRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLabelRuleRsp) ProtoMessage() {}

func (x *AddLabelRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLabelRuleRsp.ProtoReflect.Descriptor instead.
func (*AddLabelRuleRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{90}
}

func (x *AddLabelRuleRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 更新asset层级标签, POST, /api/v1/material_display/update_asset_level_labels
type UpdateAssetLevelLabelsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode  string        `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	AssetName string        `protobuf:"bytes,2,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	Labels    []*AssetLabel `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"` // 配置的标签, 如果为空则清空
}

func (x *UpdateAssetLevelLabelsReq) Reset() {
	*x = UpdateAssetLevelLabelsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAssetLevelLabelsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetLevelLabelsReq) ProtoMessage() {}

func (x *UpdateAssetLevelLabelsReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetLevelLabelsReq.ProtoReflect.Descriptor instead.
func (*UpdateAssetLevelLabelsReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{91}
}

func (x *UpdateAssetLevelLabelsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *UpdateAssetLevelLabelsReq) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *UpdateAssetLevelLabelsReq) GetLabels() []*AssetLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

type UpdateAssetLevelLabelsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *UpdateAssetLevelLabelsRsp) Reset() {
	*x = UpdateAssetLevelLabelsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAssetLevelLabelsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssetLevelLabelsRsp) ProtoMessage() {}

func (x *UpdateAssetLevelLabelsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssetLevelLabelsRsp.ProtoReflect.Descriptor instead.
func (*UpdateAssetLevelLabelsRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{92}
}

func (x *UpdateAssetLevelLabelsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 修改标签规则机器内容标签, POST, /api/v1/material_display/modify_rule_content_labels
type ModifyRuleContentLabelsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string        `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Rule     string        `protobuf:"bytes,2,opt,name=rule,proto3" json:"rule,omitempty"`     // 标签规则，如： V-123
	Add      []*AssetLabel `protobuf:"bytes,3,rep,name=add,proto3" json:"add,omitempty"`       // 增加的内容标签列表
	Remove   []*AssetLabel `protobuf:"bytes,4,rep,name=remove,proto3" json:"remove,omitempty"` // 删除内容标签列表
}

func (x *ModifyRuleContentLabelsReq) Reset() {
	*x = ModifyRuleContentLabelsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyRuleContentLabelsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyRuleContentLabelsReq) ProtoMessage() {}

func (x *ModifyRuleContentLabelsReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyRuleContentLabelsReq.ProtoReflect.Descriptor instead.
func (*ModifyRuleContentLabelsReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{93}
}

func (x *ModifyRuleContentLabelsReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *ModifyRuleContentLabelsReq) GetRule() string {
	if x != nil {
		return x.Rule
	}
	return ""
}

func (x *ModifyRuleContentLabelsReq) GetAdd() []*AssetLabel {
	if x != nil {
		return x.Add
	}
	return nil
}

func (x *ModifyRuleContentLabelsReq) GetRemove() []*AssetLabel {
	if x != nil {
		return x.Remove
	}
	return nil
}

type ModifyRuleContentLabelsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *ModifyRuleContentLabelsRsp) Reset() {
	*x = ModifyRuleContentLabelsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyRuleContentLabelsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyRuleContentLabelsRsp) ProtoMessage() {}

func (x *ModifyRuleContentLabelsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyRuleContentLabelsRsp.ProtoReflect.Descriptor instead.
func (*ModifyRuleContentLabelsRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{94}
}

func (x *ModifyRuleContentLabelsRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 修改标签规则二级标签, POST, /api/v1/material_display/modify_label_rule_second_label
type ModifyLabelRuleSecondLabelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode       string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	SerialId       string `protobuf:"bytes,2,opt,name=serial_id,json=serialId,proto3" json:"serial_id,omitempty"`                     // 标签规则serial id
	FirstLabel     string `protobuf:"bytes,3,opt,name=first_label,json=firstLabel,proto3" json:"first_label,omitempty"`               // 一级标签
	SecondLabel    string `protobuf:"bytes,4,opt,name=second_label,json=secondLabel,proto3" json:"second_label,omitempty"`            // 二级标签
	NewSecondLabel string `protobuf:"bytes,5,opt,name=new_second_label,json=newSecondLabel,proto3" json:"new_second_label,omitempty"` // 新的二级标签, 当is_delete=false时必填
	IsDelete       bool   `protobuf:"varint,6,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`                    // 是否删除二级标签
}

func (x *ModifyLabelRuleSecondLabelReq) Reset() {
	*x = ModifyLabelRuleSecondLabelReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyLabelRuleSecondLabelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyLabelRuleSecondLabelReq) ProtoMessage() {}

func (x *ModifyLabelRuleSecondLabelReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyLabelRuleSecondLabelReq.ProtoReflect.Descriptor instead.
func (*ModifyLabelRuleSecondLabelReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{95}
}

func (x *ModifyLabelRuleSecondLabelReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *ModifyLabelRuleSecondLabelReq) GetSerialId() string {
	if x != nil {
		return x.SerialId
	}
	return ""
}

func (x *ModifyLabelRuleSecondLabelReq) GetFirstLabel() string {
	if x != nil {
		return x.FirstLabel
	}
	return ""
}

func (x *ModifyLabelRuleSecondLabelReq) GetSecondLabel() string {
	if x != nil {
		return x.SecondLabel
	}
	return ""
}

func (x *ModifyLabelRuleSecondLabelReq) GetNewSecondLabel() string {
	if x != nil {
		return x.NewSecondLabel
	}
	return ""
}

func (x *ModifyLabelRuleSecondLabelReq) GetIsDelete() bool {
	if x != nil {
		return x.IsDelete
	}
	return false
}

type ModifyLabelRuleSecondLabelRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *ModifyLabelRuleSecondLabelRsp) Reset() {
	*x = ModifyLabelRuleSecondLabelRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyLabelRuleSecondLabelRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyLabelRuleSecondLabelRsp) ProtoMessage() {}

func (x *ModifyLabelRuleSecondLabelRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyLabelRuleSecondLabelRsp.ProtoReflect.Descriptor instead.
func (*ModifyLabelRuleSecondLabelRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{96}
}

func (x *ModifyLabelRuleSecondLabelRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 删除标签规则, POST, /api/v1/material_display/delete_label_rule
type DeleteLabelRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rule *LabelRule `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
}

func (x *DeleteLabelRuleReq) Reset() {
	*x = DeleteLabelRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLabelRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLabelRuleReq) ProtoMessage() {}

func (x *DeleteLabelRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLabelRuleReq.ProtoReflect.Descriptor instead.
func (*DeleteLabelRuleReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{97}
}

func (x *DeleteLabelRuleReq) GetRule() *LabelRule {
	if x != nil {
		return x.Rule
	}
	return nil
}

type DeleteLabelRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *DeleteLabelRuleRsp) Reset() {
	*x = DeleteLabelRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLabelRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLabelRuleRsp) ProtoMessage() {}

func (x *DeleteLabelRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLabelRuleRsp.ProtoReflect.Descriptor instead.
func (*DeleteLabelRuleRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{98}
}

func (x *DeleteLabelRuleRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 拉取标签规则下的素材, POST, /api/v1/material_display/get_assets_by_label_rule
type GetAssetsByLabelRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	RuleId   int64  `protobuf:"varint,2,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`       // 标签规则id
	Page     int32  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`                         // 分页数，从0开始
	PageSize int32  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页条数，最多100条
}

func (x *GetAssetsByLabelRuleReq) Reset() {
	*x = GetAssetsByLabelRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetsByLabelRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetsByLabelRuleReq) ProtoMessage() {}

func (x *GetAssetsByLabelRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetsByLabelRuleReq.ProtoReflect.Descriptor instead.
func (*GetAssetsByLabelRuleReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{99}
}

func (x *GetAssetsByLabelRuleReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetAssetsByLabelRuleReq) GetRuleId() int64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

func (x *GetAssetsByLabelRuleReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAssetsByLabelRuleReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetAssetsByLabelRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 返回结果
	Materials []*MaterialMeta `protobuf:"bytes,2,rep,name=materials,proto3" json:"materials,omitempty"` // 素材列表
	Total     uint32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`        // 素材总数
}

func (x *GetAssetsByLabelRuleRsp) Reset() {
	*x = GetAssetsByLabelRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetsByLabelRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetsByLabelRuleRsp) ProtoMessage() {}

func (x *GetAssetsByLabelRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetsByLabelRuleRsp.ProtoReflect.Descriptor instead.
func (*GetAssetsByLabelRuleRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{100}
}

func (x *GetAssetsByLabelRuleRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAssetsByLabelRuleRsp) GetMaterials() []*MaterialMeta {
	if x != nil {
		return x.Materials
	}
	return nil
}

func (x *GetAssetsByLabelRuleRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 渠道素材结构
type ChannelAsset struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                      // 自增ID
	ChannelType      int32         `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`                 // 渠道类型, 1-google; 2-facebook
	ChannelAccountId string        `protobuf:"bytes,3,opt,name=channel_account_id,json=channelAccountId,proto3" json:"channel_account_id,omitempty"` // 渠道账号(account_id)
	ChannelAssetId   string        `protobuf:"bytes,4,opt,name=channel_asset_id,json=channelAssetId,proto3" json:"channel_asset_id,omitempty"`       // 渠道端ID(asset_id)
	AssetName        string        `protobuf:"bytes,5,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`                        // 素材名称
	YoutubeId        string        `protobuf:"bytes,6,opt,name=youtube_id,json=youtubeId,proto3" json:"youtube_id,omitempty"`                        // youtube_id, google渠道使用
	CreateBy         string        `protobuf:"bytes,7,opt,name=create_by,json=createBy,proto3" json:"create_by,omitempty"`                           // 创建人
	CreateTime       string        `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                     // 创建时间
	UpdateBy         string        `protobuf:"bytes,9,opt,name=update_by,json=updateBy,proto3" json:"update_by,omitempty"`                           // 更新人
	UpdateTime       string        `protobuf:"bytes,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                    // 更新时间
	Sdate            string        `protobuf:"bytes,11,opt,name=sdate,proto3" json:"sdate,omitempty"`                                                // 从realtime表同步过来的日期
	Labels           []*AssetLabel `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels,omitempty"`                                              // 素材自己的标签
	ImpressionStatus string        `protobuf:"bytes,13,opt,name=impression_status,json=impressionStatus,proto3" json:"impression_status,omitempty"`  // 曝光状态
	ImpressionDate   string        `protobuf:"bytes,14,opt,name=impression_date,json=impressionDate,proto3" json:"impression_date,omitempty"`        // 曝光日期
}

func (x *ChannelAsset) Reset() {
	*x = ChannelAsset{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAsset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAsset) ProtoMessage() {}

func (x *ChannelAsset) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAsset.ProtoReflect.Descriptor instead.
func (*ChannelAsset) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{101}
}

func (x *ChannelAsset) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChannelAsset) GetChannelType() int32 {
	if x != nil {
		return x.ChannelType
	}
	return 0
}

func (x *ChannelAsset) GetChannelAccountId() string {
	if x != nil {
		return x.ChannelAccountId
	}
	return ""
}

func (x *ChannelAsset) GetChannelAssetId() string {
	if x != nil {
		return x.ChannelAssetId
	}
	return ""
}

func (x *ChannelAsset) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *ChannelAsset) GetYoutubeId() string {
	if x != nil {
		return x.YoutubeId
	}
	return ""
}

func (x *ChannelAsset) GetCreateBy() string {
	if x != nil {
		return x.CreateBy
	}
	return ""
}

func (x *ChannelAsset) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ChannelAsset) GetUpdateBy() string {
	if x != nil {
		return x.UpdateBy
	}
	return ""
}

func (x *ChannelAsset) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ChannelAsset) GetSdate() string {
	if x != nil {
		return x.Sdate
	}
	return ""
}

func (x *ChannelAsset) GetLabels() []*AssetLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ChannelAsset) GetImpressionStatus() string {
	if x != nil {
		return x.ImpressionStatus
	}
	return ""
}

func (x *ChannelAsset) GetImpressionDate() string {
	if x != nil {
		return x.ImpressionDate
	}
	return ""
}

// 拉取标签规则下的渠道素材, POST, /api/v1/material_display/get_channel_assets_by_label_rule
type GetChannelAssetsByLabelRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	RuleId   int64  `protobuf:"varint,2,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`       // 标签规则id
	Page     int32  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`                         // 分页数，从0开始
	PageSize int32  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页条数，最多100条
}

func (x *GetChannelAssetsByLabelRuleReq) Reset() {
	*x = GetChannelAssetsByLabelRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAssetsByLabelRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAssetsByLabelRuleReq) ProtoMessage() {}

func (x *GetChannelAssetsByLabelRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAssetsByLabelRuleReq.ProtoReflect.Descriptor instead.
func (*GetChannelAssetsByLabelRuleReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{102}
}

func (x *GetChannelAssetsByLabelRuleReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetChannelAssetsByLabelRuleReq) GetRuleId() int64 {
	if x != nil {
		return x.RuleId
	}
	return 0
}

func (x *GetChannelAssetsByLabelRuleReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetChannelAssetsByLabelRuleReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetChannelAssetsByLabelRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result     `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Assets []*ChannelAsset `protobuf:"bytes,2,rep,name=assets,proto3" json:"assets,omitempty"` // 素材列表
	Total  uint32          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`  // 素材总数
}

func (x *GetChannelAssetsByLabelRuleRsp) Reset() {
	*x = GetChannelAssetsByLabelRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChannelAssetsByLabelRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelAssetsByLabelRuleRsp) ProtoMessage() {}

func (x *GetChannelAssetsByLabelRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelAssetsByLabelRuleRsp.ProtoReflect.Descriptor instead.
func (*GetChannelAssetsByLabelRuleRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{103}
}

func (x *GetChannelAssetsByLabelRuleRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetChannelAssetsByLabelRuleRsp) GetAssets() []*ChannelAsset {
	if x != nil {
		return x.Assets
	}
	return nil
}

func (x *GetChannelAssetsByLabelRuleRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 提交标签规则批量打标任务 POST, /api/v1/material_display/add_label_rule_task
type AddLabelRuleTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	CosFile  string `protobuf:"bytes,2,opt,name=cos_file,json=cosFile,proto3" json:"cos_file,omitempty"`
}

func (x *AddLabelRuleTaskReq) Reset() {
	*x = AddLabelRuleTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLabelRuleTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLabelRuleTaskReq) ProtoMessage() {}

func (x *AddLabelRuleTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLabelRuleTaskReq.ProtoReflect.Descriptor instead.
func (*AddLabelRuleTaskReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{104}
}

func (x *AddLabelRuleTaskReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *AddLabelRuleTaskReq) GetCosFile() string {
	if x != nil {
		return x.CosFile
	}
	return ""
}

type AddLabelRuleTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *AddLabelRuleTaskRsp) Reset() {
	*x = AddLabelRuleTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLabelRuleTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLabelRuleTaskRsp) ProtoMessage() {}

func (x *AddLabelRuleTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLabelRuleTaskRsp.ProtoReflect.Descriptor instead.
func (*AddLabelRuleTaskRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{105}
}

func (x *AddLabelRuleTaskRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// V2提交标签规则批量打标任务(支持serial和asset层级) POST, /api/v1/material_display/add_label_rule_task_v2
type AddLabelRuleTaskV2Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	CosFile  string `protobuf:"bytes,2,opt,name=cos_file,json=cosFile,proto3" json:"cos_file,omitempty"`
}

func (x *AddLabelRuleTaskV2Req) Reset() {
	*x = AddLabelRuleTaskV2Req{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLabelRuleTaskV2Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLabelRuleTaskV2Req) ProtoMessage() {}

func (x *AddLabelRuleTaskV2Req) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLabelRuleTaskV2Req.ProtoReflect.Descriptor instead.
func (*AddLabelRuleTaskV2Req) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{106}
}

func (x *AddLabelRuleTaskV2Req) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *AddLabelRuleTaskV2Req) GetCosFile() string {
	if x != nil {
		return x.CosFile
	}
	return ""
}

type AddLabelRuleTaskV2Rsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *AddLabelRuleTaskV2Rsp) Reset() {
	*x = AddLabelRuleTaskV2Rsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLabelRuleTaskV2Rsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLabelRuleTaskV2Rsp) ProtoMessage() {}

func (x *AddLabelRuleTaskV2Rsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLabelRuleTaskV2Rsp.ProtoReflect.Descriptor instead.
func (*AddLabelRuleTaskV2Rsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{107}
}

func (x *AddLabelRuleTaskV2Rsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type SyncTaskDir struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                               // 目录id
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                           // 目录名称
	FullPathName  string `protobuf:"bytes,3,opt,name=full_path_name,json=fullPathName,proto3" json:"full_path_name,omitempty"`     // 目录全路径名称
	IncludeSubDir bool   `protobuf:"varint,4,opt,name=include_sub_dir,json=includeSubDir,proto3" json:"include_sub_dir,omitempty"` // 是否包括子目录
}

func (x *SyncTaskDir) Reset() {
	*x = SyncTaskDir{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncTaskDir) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncTaskDir) ProtoMessage() {}

func (x *SyncTaskDir) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncTaskDir.ProtoReflect.Descriptor instead.
func (*SyncTaskDir) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{108}
}

func (x *SyncTaskDir) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SyncTaskDir) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SyncTaskDir) GetFullPathName() string {
	if x != nil {
		return x.FullPathName
	}
	return ""
}

func (x *SyncTaskDir) GetIncludeSubDir() bool {
	if x != nil {
		return x.IncludeSubDir
	}
	return false
}

type SyncTaskMedia struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Channel  int32  `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"`  // 渠道
	Accounts string `protobuf:"bytes,2,opt,name=accounts,proto3" json:"accounts,omitempty"` // 指定上传账号，用逗号分割多个账号
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"` // 语言, 目前untiy需要指定
}

func (x *SyncTaskMedia) Reset() {
	*x = SyncTaskMedia{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncTaskMedia) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncTaskMedia) ProtoMessage() {}

func (x *SyncTaskMedia) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncTaskMedia.ProtoReflect.Descriptor instead.
func (*SyncTaskMedia) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{109}
}

func (x *SyncTaskMedia) GetChannel() int32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *SyncTaskMedia) GetAccounts() string {
	if x != nil {
		return x.Accounts
	}
	return ""
}

func (x *SyncTaskMedia) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type AutomaticSyncTaskRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                     // 自增ID，不为0时表示更新
	GameCode             string           `protobuf:"bytes,2,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                                          // 游戏code
	Name                 string           `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                  // 规则名称
	Status               int32            `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`                                                             // 规则状态, 0-禁用, 1-启用
	AssetType            int32            `protobuf:"varint,5,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`                                      // 指定素材类型, 0-不限， 1-视频， 2-图片, 3-html
	Dirs                 []*SyncTaskDir   `protobuf:"bytes,6,rep,name=dirs,proto3" json:"dirs,omitempty"`                                                                  // 目录列表
	Medias               []*SyncTaskMedia `protobuf:"bytes,7,rep,name=medias,proto3" json:"medias,omitempty"`                                                              // 上传渠道列表
	EndDate              string           `protobuf:"bytes,8,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`                                             // 结束日期, 格式: 20250101, 为空表示永久
	CreateUser           string           `protobuf:"bytes,9,opt,name=create_user,json=createUser,proto3" json:"create_user,omitempty"`                                    // 创建人
	CreateTime           string           `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                                   // 创建时间
	UpdateUser           string           `protobuf:"bytes,11,opt,name=update_user,json=updateUser,proto3" json:"update_user,omitempty"`                                   // 更新人
	UpdateTime           string           `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                                   // 更新时间
	StatusChangeTime     string           `protobuf:"bytes,13,opt,name=status_change_time,json=statusChangeTime,proto3" json:"status_change_time,omitempty"`               // 状态变更时间
	StartCloudUploadTime string           `protobuf:"bytes,14,opt,name=start_cloud_upload_time,json=startCloudUploadTime,proto3" json:"start_cloud_upload_time,omitempty"` // 格式: 2024-01-01 01:02:03  表示自动化规则 处理那些 > start_cloud_upload_time 的素材
}

func (x *AutomaticSyncTaskRule) Reset() {
	*x = AutomaticSyncTaskRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutomaticSyncTaskRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutomaticSyncTaskRule) ProtoMessage() {}

func (x *AutomaticSyncTaskRule) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutomaticSyncTaskRule.ProtoReflect.Descriptor instead.
func (*AutomaticSyncTaskRule) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{110}
}

func (x *AutomaticSyncTaskRule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AutomaticSyncTaskRule) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AutomaticSyncTaskRule) GetAssetType() int32 {
	if x != nil {
		return x.AssetType
	}
	return 0
}

func (x *AutomaticSyncTaskRule) GetDirs() []*SyncTaskDir {
	if x != nil {
		return x.Dirs
	}
	return nil
}

func (x *AutomaticSyncTaskRule) GetMedias() []*SyncTaskMedia {
	if x != nil {
		return x.Medias
	}
	return nil
}

func (x *AutomaticSyncTaskRule) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetStatusChangeTime() string {
	if x != nil {
		return x.StatusChangeTime
	}
	return ""
}

func (x *AutomaticSyncTaskRule) GetStartCloudUploadTime() string {
	if x != nil {
		return x.StartCloudUploadTime
	}
	return ""
}

// 增加/更新自动化上传任务规则 POST, /api/v1/material_display/add_automatic_sync_task_rule
type AddAutomaticSyncTaskRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskRule *AutomaticSyncTaskRule `protobuf:"bytes,1,opt,name=task_rule,json=taskRule,proto3" json:"task_rule,omitempty"`
}

func (x *AddAutomaticSyncTaskRuleReq) Reset() {
	*x = AddAutomaticSyncTaskRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAutomaticSyncTaskRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAutomaticSyncTaskRuleReq) ProtoMessage() {}

func (x *AddAutomaticSyncTaskRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAutomaticSyncTaskRuleReq.ProtoReflect.Descriptor instead.
func (*AddAutomaticSyncTaskRuleReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{111}
}

func (x *AddAutomaticSyncTaskRuleReq) GetTaskRule() *AutomaticSyncTaskRule {
	if x != nil {
		return x.TaskRule
	}
	return nil
}

type AddAutomaticSyncTaskRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
	Id     int64       `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`        // 规则id
}

func (x *AddAutomaticSyncTaskRuleRsp) Reset() {
	*x = AddAutomaticSyncTaskRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[112]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAutomaticSyncTaskRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAutomaticSyncTaskRuleRsp) ProtoMessage() {}

func (x *AddAutomaticSyncTaskRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[112]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAutomaticSyncTaskRuleRsp.ProtoReflect.Descriptor instead.
func (*AddAutomaticSyncTaskRuleRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{112}
}

func (x *AddAutomaticSyncTaskRuleRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *AddAutomaticSyncTaskRuleRsp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 检查自动化上传任务规则名称是否重复 POST, /api/v1/material_display/check_automatic_sync_task_rule_name
type CheckAutomaticSyncTaskRuleNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"` // 游戏code
	Id       int64  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                            // 规则id
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                         // 规则名称
}

func (x *CheckAutomaticSyncTaskRuleNameReq) Reset() {
	*x = CheckAutomaticSyncTaskRuleNameReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[113]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAutomaticSyncTaskRuleNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAutomaticSyncTaskRuleNameReq) ProtoMessage() {}

func (x *CheckAutomaticSyncTaskRuleNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[113]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAutomaticSyncTaskRuleNameReq.ProtoReflect.Descriptor instead.
func (*CheckAutomaticSyncTaskRuleNameReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{113}
}

func (x *CheckAutomaticSyncTaskRuleNameReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CheckAutomaticSyncTaskRuleNameReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CheckAutomaticSyncTaskRuleNameReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckAutomaticSyncTaskRuleNameRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`        // 返回结果
	Duplicate bool        `protobuf:"varint,2,opt,name=duplicate,proto3" json:"duplicate,omitempty"` // 是否重复
}

func (x *CheckAutomaticSyncTaskRuleNameRsp) Reset() {
	*x = CheckAutomaticSyncTaskRuleNameRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAutomaticSyncTaskRuleNameRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAutomaticSyncTaskRuleNameRsp) ProtoMessage() {}

func (x *CheckAutomaticSyncTaskRuleNameRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAutomaticSyncTaskRuleNameRsp.ProtoReflect.Descriptor instead.
func (*CheckAutomaticSyncTaskRuleNameRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{114}
}

func (x *CheckAutomaticSyncTaskRuleNameRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *CheckAutomaticSyncTaskRuleNameRsp) GetDuplicate() bool {
	if x != nil {
		return x.Duplicate
	}
	return false
}

// 拉取自动化上传任务规则 POST, /api/v1/material_display/get_automatic_sync_task_rule
type GetAutomaticSyncTaskRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Id       int64  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                             // 任务规则id，不为0只取这一个
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                          // 非空时名字搜索
	Page     int32  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`                         // 分页数，从0开始
	PageSize int32  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页条数，最多100条
}

func (x *GetAutomaticSyncTaskRuleReq) Reset() {
	*x = GetAutomaticSyncTaskRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[115]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutomaticSyncTaskRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutomaticSyncTaskRuleReq) ProtoMessage() {}

func (x *GetAutomaticSyncTaskRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[115]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutomaticSyncTaskRuleReq.ProtoReflect.Descriptor instead.
func (*GetAutomaticSyncTaskRuleReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{115}
}

func (x *GetAutomaticSyncTaskRuleReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *GetAutomaticSyncTaskRuleReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAutomaticSyncTaskRuleReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAutomaticSyncTaskRuleReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAutomaticSyncTaskRuleReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetAutomaticSyncTaskRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result    *aix.Result              `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`                        // 返回结果
	TaskRules []*AutomaticSyncTaskRule `protobuf:"bytes,2,rep,name=task_rules,json=taskRules,proto3" json:"task_rules,omitempty"` // 规则列表
	Total     uint32                   `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`                         // 总数
}

func (x *GetAutomaticSyncTaskRuleRsp) Reset() {
	*x = GetAutomaticSyncTaskRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[116]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAutomaticSyncTaskRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutomaticSyncTaskRuleRsp) ProtoMessage() {}

func (x *GetAutomaticSyncTaskRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[116]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutomaticSyncTaskRuleRsp.ProtoReflect.Descriptor instead.
func (*GetAutomaticSyncTaskRuleRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{116}
}

func (x *GetAutomaticSyncTaskRuleRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *GetAutomaticSyncTaskRuleRsp) GetTaskRules() []*AutomaticSyncTaskRule {
	if x != nil {
		return x.TaskRules
	}
	return nil
}

func (x *GetAutomaticSyncTaskRuleRsp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 更新自动化上传任务规则状态 POST, /api/v1/material_display/change_automatic_sync_task_rule_status
type ChangeAutomaticSyncTaskRuleStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Id       int64  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`         // 规则id
	Status   int32  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"` // 规则状态, 0-禁用, 1-启用
}

func (x *ChangeAutomaticSyncTaskRuleStatusReq) Reset() {
	*x = ChangeAutomaticSyncTaskRuleStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeAutomaticSyncTaskRuleStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeAutomaticSyncTaskRuleStatusReq) ProtoMessage() {}

func (x *ChangeAutomaticSyncTaskRuleStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeAutomaticSyncTaskRuleStatusReq.ProtoReflect.Descriptor instead.
func (*ChangeAutomaticSyncTaskRuleStatusReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{117}
}

func (x *ChangeAutomaticSyncTaskRuleStatusReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *ChangeAutomaticSyncTaskRuleStatusReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeAutomaticSyncTaskRuleStatusReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type ChangeAutomaticSyncTaskRuleStatusRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *ChangeAutomaticSyncTaskRuleStatusRsp) Reset() {
	*x = ChangeAutomaticSyncTaskRuleStatusRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeAutomaticSyncTaskRuleStatusRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeAutomaticSyncTaskRuleStatusRsp) ProtoMessage() {}

func (x *ChangeAutomaticSyncTaskRuleStatusRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeAutomaticSyncTaskRuleStatusRsp.ProtoReflect.Descriptor instead.
func (*ChangeAutomaticSyncTaskRuleStatusRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{118}
}

func (x *ChangeAutomaticSyncTaskRuleStatusRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

// 删除自动化上传任务规则 POST, /api/v1/material_display/delete_automatic_sync_task_rule
type DeleteAutomaticSyncTaskRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode string `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`
	Id       int64  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"` // 规则id
}

func (x *DeleteAutomaticSyncTaskRuleReq) Reset() {
	*x = DeleteAutomaticSyncTaskRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[119]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAutomaticSyncTaskRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAutomaticSyncTaskRuleReq) ProtoMessage() {}

func (x *DeleteAutomaticSyncTaskRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[119]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAutomaticSyncTaskRuleReq.ProtoReflect.Descriptor instead.
func (*DeleteAutomaticSyncTaskRuleReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{119}
}

func (x *DeleteAutomaticSyncTaskRuleReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *DeleteAutomaticSyncTaskRuleReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteAutomaticSyncTaskRuleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *DeleteAutomaticSyncTaskRuleRsp) Reset() {
	*x = DeleteAutomaticSyncTaskRuleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[120]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAutomaticSyncTaskRuleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAutomaticSyncTaskRuleRsp) ProtoMessage() {}

func (x *DeleteAutomaticSyncTaskRuleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[120]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAutomaticSyncTaskRuleRsp.ProtoReflect.Descriptor instead.
func (*DeleteAutomaticSyncTaskRuleRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{120}
}

func (x *DeleteAutomaticSyncTaskRuleRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

type ArthubAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArthubCode  string `protobuf:"bytes,1,opt,name=arthub_code,json=arthubCode,proto3" json:"arthub_code,omitempty"`
	PublicToken string `protobuf:"bytes,2,opt,name=public_token,json=publicToken,proto3" json:"public_token,omitempty"`
}

func (x *ArthubAuth) Reset() {
	*x = ArthubAuth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[121]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArthubAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArthubAuth) ProtoMessage() {}

func (x *ArthubAuth) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[121]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArthubAuth.ProtoReflect.Descriptor instead.
func (*ArthubAuth) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{121}
}

func (x *ArthubAuth) GetArthubCode() string {
	if x != nil {
		return x.ArthubCode
	}
	return ""
}

func (x *ArthubAuth) GetPublicToken() string {
	if x != nil {
		return x.PublicToken
	}
	return ""
}

type Oauth2Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId     string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret string `protobuf:"bytes,2,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	RedirectUri  string `protobuf:"bytes,3,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`
	AuthUri      string `protobuf:"bytes,4,opt,name=auth_uri,json=authUri,proto3" json:"auth_uri,omitempty"`
	TokenUri     string `protobuf:"bytes,5,opt,name=token_uri,json=tokenUri,proto3" json:"token_uri,omitempty"`
}

func (x *Oauth2Config) Reset() {
	*x = Oauth2Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[122]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Oauth2Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oauth2Config) ProtoMessage() {}

func (x *Oauth2Config) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[122]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oauth2Config.ProtoReflect.Descriptor instead.
func (*Oauth2Config) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{122}
}

func (x *Oauth2Config) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Oauth2Config) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *Oauth2Config) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *Oauth2Config) GetAuthUri() string {
	if x != nil {
		return x.AuthUri
	}
	return ""
}

func (x *Oauth2Config) GetTokenUri() string {
	if x != nil {
		return x.TokenUri
	}
	return ""
}

type Oauth2Token struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken  string `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken string `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	TokenType    string `protobuf:"bytes,3,opt,name=token_type,json=tokenType,proto3" json:"token_type,omitempty"`
	Expiry       string `protobuf:"bytes,4,opt,name=expiry,proto3" json:"expiry,omitempty"`
}

func (x *Oauth2Token) Reset() {
	*x = Oauth2Token{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[123]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Oauth2Token) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Oauth2Token) ProtoMessage() {}

func (x *Oauth2Token) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[123]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Oauth2Token.ProtoReflect.Descriptor instead.
func (*Oauth2Token) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{123}
}

func (x *Oauth2Token) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *Oauth2Token) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *Oauth2Token) GetTokenType() string {
	if x != nil {
		return x.TokenType
	}
	return ""
}

func (x *Oauth2Token) GetExpiry() string {
	if x != nil {
		return x.Expiry
	}
	return ""
}

type GoogleDriveAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config *Oauth2Config `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	Token  *Oauth2Token  `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *GoogleDriveAuth) Reset() {
	*x = GoogleDriveAuth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[124]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoogleDriveAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleDriveAuth) ProtoMessage() {}

func (x *GoogleDriveAuth) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[124]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleDriveAuth.ProtoReflect.Descriptor instead.
func (*GoogleDriveAuth) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{124}
}

func (x *GoogleDriveAuth) GetConfig() *Oauth2Config {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GoogleDriveAuth) GetToken() *Oauth2Token {
	if x != nil {
		return x.Token
	}
	return nil
}

// 网盘接入授权 POST /api/v1/material_display/cloud_drive_grant
type CloudDriveGrantReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GameCode        string           `protobuf:"bytes,1,opt,name=game_code,json=gameCode,proto3" json:"game_code,omitempty"`                        // 游戏code
	Type            int32            `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                                               // 网盘类型， 参看utils.GAME_DEPOT_TYPE_XXX
	RootId          string           `protobuf:"bytes,3,opt,name=root_id,json=rootId,proto3" json:"root_id,omitempty"`                              // 网盘根目录id
	ArthubAuth      *ArthubAuth      `protobuf:"bytes,4,opt,name=arthub_auth,json=arthubAuth,proto3" json:"arthub_auth,omitempty"`                  // arthub授权
	GoogleDriveAuth *GoogleDriveAuth `protobuf:"bytes,5,opt,name=google_drive_auth,json=googleDriveAuth,proto3" json:"google_drive_auth,omitempty"` // google drive授权
}

func (x *CloudDriveGrantReq) Reset() {
	*x = CloudDriveGrantReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[125]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudDriveGrantReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudDriveGrantReq) ProtoMessage() {}

func (x *CloudDriveGrantReq) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[125]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudDriveGrantReq.ProtoReflect.Descriptor instead.
func (*CloudDriveGrantReq) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{125}
}

func (x *CloudDriveGrantReq) GetGameCode() string {
	if x != nil {
		return x.GameCode
	}
	return ""
}

func (x *CloudDriveGrantReq) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CloudDriveGrantReq) GetRootId() string {
	if x != nil {
		return x.RootId
	}
	return ""
}

func (x *CloudDriveGrantReq) GetArthubAuth() *ArthubAuth {
	if x != nil {
		return x.ArthubAuth
	}
	return nil
}

func (x *CloudDriveGrantReq) GetGoogleDriveAuth() *GoogleDriveAuth {
	if x != nil {
		return x.GoogleDriveAuth
	}
	return nil
}

type CloudDriveGrantRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result *aix.Result `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"` // 返回结果
}

func (x *CloudDriveGrantRsp) Reset() {
	*x = CloudDriveGrantRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_material_display_material_display_proto_msgTypes[126]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudDriveGrantRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudDriveGrantRsp) ProtoMessage() {}

func (x *CloudDriveGrantRsp) ProtoReflect() protoreflect.Message {
	mi := &file_material_display_material_display_proto_msgTypes[126]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudDriveGrantRsp.ProtoReflect.Descriptor instead.
func (*CloudDriveGrantRsp) Descriptor() ([]byte, []int) {
	return file_material_display_material_display_proto_rawDescGZIP(), []int{126}
}

func (x *CloudDriveGrantRsp) GetResult() *aix.Result {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_material_display_material_display_proto protoreflect.FileDescriptor

var file_material_display_material_display_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x1a, 0x1c, 0x61, 0x69, 0x78,
	0x2f, 0x61, 0x69, 0x78, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe5, 0x02, 0x0a, 0x0f, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x69, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x69, 0x74,
	0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x77, 0x69, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x30, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xfb, 0x0c, 0x0a, 0x0c, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x52, 0x0b, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74,
	0x68, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6c,
	0x6c, 0x50, 0x61, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x69, 0x78,
	0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x4f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x61,
	0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x66, 0x61, 0x63, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x30,
	0x0a, 0x14, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x66, 0x61,
	0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x32, 0x0a, 0x15, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x66, 0x66,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x66, 0x61, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11,
	0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x6c, 0x61, 0x79,
	0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x21, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x31, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x31, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x32, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x32, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x33, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x33, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x75, 0x64, 0x69, 0x74, 0x49, 0x64, 0x18, 0x26, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x75, 0x64, 0x69, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x27, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x28, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x79,
	0x6e, 0x63, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6e, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x6e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x79,
	0x6e, 0x63, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x2d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x52, 0x0d, 0x73, 0x79, 0x6e, 0x63, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x8a, 0x01, 0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x55, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x22, 0xe4, 0x01, 0x0a, 0x09, 0x55, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x05, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x10, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x2c,
	0x0a, 0x12, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0x4a, 0x0a, 0x0a,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61,
	0x6b, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x74, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x6b,
	0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74,
	0x61, 0x6b, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd9, 0x01, 0x0a, 0x05, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x69, 0x67, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x68, 0x69, 0x67, 0x68, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x69,
	0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x22, 0xff, 0x01, 0x0a, 0x0b, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x45, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x39, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x55, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x52,
	0x09, 0x75, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x12, 0x2d, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x3c, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x52,
	0x05, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x22, 0x7b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x40, 0x0a, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x65, 0x78,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x45, 0x78, 0x74, 0x22, 0x60, 0x0a, 0x14, 0x42, 0x74, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x42, 0x74, 0x47, 0x65, 0x74, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x49, 0x0a, 0x11, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x65, 0x78, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x52, 0x0f, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x39,
	0x0a, 0x1a, 0x42, 0x74, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x22, 0x7f, 0x0a, 0x1a, 0x42, 0x74, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x22, 0x6f, 0x0a, 0x0a, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0x3e, 0x0a, 0x10, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x25, 0x0a, 0x09, 0x53,
	0x79, 0x6e, 0x63, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x22, 0xc2, 0x07, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x65, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x13, 0x0a, 0x05, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x04, 0x69, 0x73, 0x41, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x69, 0x78, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x11, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x13, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x13,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x73, 0x79, 0x6e, 0x63, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x18, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x79, 0x6e, 0x63,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x65, 0x6e, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x5a, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x40, 0x0a, 0x1d, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x73, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x1d,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x73, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xa2, 0x01, 0x0a, 0x10, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x64, 0x69, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x44, 0x69, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x67, 0x65, 0x74, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x22, 0x82, 0x04, 0x0a, 0x09,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x65, 0x61, 0x66, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x65, 0x61, 0x66,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x14, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x66,
	0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74,
	0x68, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x30, 0x0a, 0x14, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x7e, 0x0a, 0x10, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x69, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x04, 0x64, 0x69, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0x59, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x12,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52,
	0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x69, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x04, 0x64, 0x69, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x3c,
	0x0a, 0x0f, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x69, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x69, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x65, 0x0a, 0x0f,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x47, 0x65, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x03,
	0x64, 0x69, 0x72, 0x22, 0x7f, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x72, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x72, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x22, 0x68, 0x0a, 0x12, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x2d, 0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x03, 0x64, 0x69, 0x72, 0x22, 0x4b,
	0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x6f, 0x76, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xe4, 0x02, 0x0a, 0x0c,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x79, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68,
	0x49, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d,
	0x6f, 0x76, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x45, 0x0a, 0x0e, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49,
	0x64, 0x73, 0x22, 0xc3, 0x01, 0x0a, 0x0f, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53,
	0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x75,
	0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0x63, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x4d,
	0x0a, 0x11, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x55, 0x0a,
	0x06, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x72,
	0x65, 0x73, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x4d, 0x73, 0x67, 0x22, 0x75, 0x0a, 0x12, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x3a, 0x0a, 0x0c, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x52,
	0x0a, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc5, 0x01, 0x0a, 0x0b,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x6f, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x50,
	0x61, 0x74, 0x68, 0x22, 0x75, 0x0a, 0x12, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x12, 0x28, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x50, 0x61, 0x74, 0x68, 0x22, 0x39, 0x0a, 0x12, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x62, 0x0a, 0x15, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xd4, 0x03, 0x0a, 0x0e, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x2c, 0x0a, 0x12, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28,
	0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x65, 0x61, 0x66, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c,
	0x65, 0x61, 0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x14, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24,
	0x0a, 0x0e, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x75, 0x6c, 0x6c,
	0x50, 0x61, 0x74, 0x68, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x88, 0x01, 0x0a, 0x15, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x34, 0x0a, 0x04, 0x64, 0x69, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x04, 0x64, 0x69, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x38, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x24, 0x0a, 0x0e, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x3a, 0x0a, 0x10, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f,
	0x6f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x5f, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x47, 0x72, 0x61, 0x6e,
	0x74, 0x22, 0x53, 0x0a, 0x0a, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12,
	0x45, 0x0a, 0x0c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x52, 0x0b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x22, 0xef, 0x02, 0x0a, 0x09, 0x44, 0x65, 0x70, 0x6f, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x72, 0x74, 0x68, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x72, 0x74, 0x68, 0x75, 0x62, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x34, 0x0a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x14, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x44, 0x72, 0x69, 0x76, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x6a, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x44,
	0x65, 0x70, 0x6f, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x31, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x41, 0x72, 0x74, 0x68, 0x75, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a,
	0x13, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8f,
	0x01, 0x0a, 0x20, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x68, 0x75,
	0x62, 0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c,
	0x52, 0x65, 0x71, 0x12, 0x4c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x72,
	0x74, 0x68, 0x75, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x55, 0x72, 0x6c, 0x52, 0x65, 0x71, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x7c, 0x0a, 0x28, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x68,
	0x75, 0x62, 0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x22, 0x5e,
	0x0a, 0x27, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x68, 0x75, 0x62,
	0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x86,
	0x02, 0x0a, 0x20, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x68, 0x75,
	0x62, 0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f, 0x0a, 0x0d, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x68, 0x75,
	0x62, 0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x5c, 0x0a, 0x0c, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x41, 0x72, 0x74, 0x68, 0x75,
	0x62, 0x54, 0x65, 0x6d, 0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c,
	0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x3c, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x65, 0x70, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x83, 0x01, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65,
	0x70, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x6f, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x70, 0x6f, 0x74,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x70, 0x6f, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x0c,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xcf,
	0x01, 0x0a, 0x11, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x41, 0x72, 0x74, 0x68, 0x75,
	0x62, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3d,
	0x0a, 0x05, 0x6d, 0x65, 0x74, 0x61, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x41, 0x72, 0x74, 0x68, 0x75, 0x62, 0x52,
	0x65, 0x71, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x05, 0x6d, 0x65, 0x74, 0x61, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x69, 0x78, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x69, 0x78, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x78, 0x0a, 0x15, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x41, 0x72, 0x74, 0x68,
	0x75, 0x62, 0x52, 0x65, 0x71, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x69,
	0x67, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x68, 0x69, 0x67, 0x68, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8e, 0x01, 0x0a, 0x11, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x41, 0x72, 0x74, 0x68, 0x75, 0x62, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64,
	0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x61, 0x69,
	0x6c, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x22, 0xc1, 0x02, 0x0a, 0x14,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61,
	0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x22,
	0xe5, 0x04, 0x0a, 0x11, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x79,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x41, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x48, 0x61, 0x73, 0x68, 0x12, 0x21, 0x0a,
	0x0c, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x86, 0x04, 0x0a, 0x17, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x69, 0x67, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x68, 0x69, 0x67, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x52,
	0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x73, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x74, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x69, 0x74, 0x52, 0x61, 0x74,
	0x65, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63,
	0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x12, 0x2a, 0x0a, 0x11, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x12,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x22, 0x94, 0x01, 0x0a, 0x14, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x41,
	0x0a, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x0f, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x53, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xf3, 0x02,
	0x0a, 0x1d, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x42,
	0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x69,
	0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x69, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x77, 0x69, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x0a, 0x0d,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x30, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x12, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xef, 0x03, 0x0a, 0x1e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x42, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x49, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x75, 0x70, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61,
	0x79, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x6d, 0x61,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x52,
	0x0b, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x45, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x1d, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x42, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x49, 0x64, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4e, 0x0a, 0x09,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x42, 0x79, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0xe0, 0x01, 0x0a, 0x18, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xce, 0x02, 0x0a, 0x15, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x55, 0x72, 0x6c, 0x12, 0x41, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x4d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22, 0x92, 0x01, 0x0a, 0x18, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0xc5, 0x02, 0x0a, 0x11,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49,
	0x64, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x53, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x22, 0x89, 0x03, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x54, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x44, 0x0a, 0x0b, 0x74, 0x6f, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x74, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x64, 0x61, 0x79, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x44, 0x61,
	0x79, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x3c, 0x0a, 0x1b, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x5f, 0x73,
	0x79, 0x6e, 0x63, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x17, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x63, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x22,
	0x40, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x56, 0x0a, 0x1c, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x54, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x73, 0x22, 0x43, 0x0a, 0x1c, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x56,
	0x0a, 0x1c, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x73, 0x22, 0x43, 0x0a, 0x1c, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x41, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x78,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x0b, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x2e, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x53, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x44, 0x0a, 0x0a, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x22, 0x46,
	0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x88, 0x04, 0x0a, 0x09, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0d, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d,
	0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6c, 0x69, 0x6e, 0x6b, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x22, 0xc1, 0x03, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x69,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x61, 0x72, 0x74, 0x49, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13,
	0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x6e, 0x64, 0x49, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x65,
	0x6e, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x31, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x95, 0x03, 0x0a, 0x15, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6d, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x61, 0x72, 0x74, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6e, 0x64, 0x5f,
	0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x6e, 0x64, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x64, 0x5f, 0x6f, 0x66,
	0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x65, 0x6e, 0x64, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x22, 0x57, 0x0a, 0x15, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x63, 0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x42, 0x0a, 0x0f, 0x41, 0x64, 0x64,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x04,
	0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x22, 0x36, 0x0a,
	0x0f, 0x41, 0x64, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x34, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x22, 0x40, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52,
	0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xb3, 0x01, 0x0a, 0x1a, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x03, 0x61, 0x64, 0x64, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x52, 0x03, 0x61, 0x64, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x22, 0x41, 0x0a,
	0x1a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69,
	0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0xe4, 0x01, 0x0a, 0x1d, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x12, 0x28, 0x0a, 0x10, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x65, 0x77, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0x44, 0x0a, 0x1d, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x45, 0x0a,
	0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x04,
	0x72, 0x75, 0x6c, 0x65, 0x22, 0x39, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x80, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x42, 0x79, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x22, 0x92, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xf5, 0x03, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x79, 0x6f, 0x75, 0x74, 0x75, 0x62, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6d, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x22,
	0x87, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x1e, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x42, 0x79,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61,
	0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x36, 0x0a, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x52, 0x06, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x4d, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x3a,
	0x0a, 0x13, 0x41, 0x64, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4f, 0x0a, 0x15, 0x41, 0x64,
	0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x56, 0x32,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x3c, 0x0a, 0x15, 0x41,
	0x64, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x56,
	0x32, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x7f, 0x0a, 0x0b, 0x53, 0x79, 0x6e,
	0x63, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x69, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e,
	0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x73, 0x75,
	0x62, 0x5f, 0x64, 0x69, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x53, 0x75, 0x62, 0x44, 0x69, 0x72, 0x22, 0x61, 0x0a, 0x0d, 0x53, 0x79,
	0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0xff, 0x03,
	0x0a, 0x15, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x31, 0x0a, 0x04, 0x64, 0x69, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x69, 0x72, 0x52, 0x04, 0x64, 0x69,
	0x72, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x52, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x63, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53,
	0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x44,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79,
	0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x52, 0x75, 0x6c, 0x65, 0x22, 0x52, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x64, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x66,
	0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63,
	0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x75, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x22, 0x8f, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xa0, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a,
	0x0a, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79,
	0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x6b, 0x0a, 0x24, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79,
	0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4b, 0x0a, 0x24, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x4d, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x45, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x63, 0x53, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x50, 0x0a, 0x0a, 0x41,
	0x72, 0x74, 0x68, 0x75, 0x62, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x72, 0x74,
	0x68, 0x75, 0x62, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x61, 0x72, 0x74, 0x68, 0x75, 0x62, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xab, 0x01,
	0x0a, 0x0c, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x69,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x55, 0x72, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x75, 0x72, 0x69, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x55, 0x72, 0x69, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x55, 0x72, 0x69, 0x22, 0x8c, 0x01, 0x0a, 0x0b,
	0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x22, 0x7e, 0x0a, 0x0f, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x36, 0x0a,
	0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x4f, 0x61, 0x75, 0x74, 0x68, 0x32, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xec, 0x01, 0x0a, 0x12, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x44, 0x72, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0b, 0x61,
	0x72, 0x74, 0x68, 0x75, 0x62, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x2e, 0x41, 0x72, 0x74, 0x68, 0x75, 0x62, 0x41, 0x75, 0x74, 0x68, 0x52, 0x0a,
	0x61, 0x72, 0x74, 0x68, 0x75, 0x62, 0x41, 0x75, 0x74, 0x68, 0x12, 0x4d, 0x0a, 0x11, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x0f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x41, 0x75, 0x74, 0x68, 0x22, 0x39, 0x0a, 0x12, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x44, 0x72, 0x69, 0x76, 0x65, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x23, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x61, 0x69, 0x78, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2a, 0x50, 0x0a, 0x0e, 0x4d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x4f, 0x54, 0x5f, 0x55, 0x50,
	0x4c, 0x4f, 0x41, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x45,
	0x44, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x42, 0x3f, 0x5a, 0x3d, 0x65, 0x2e, 0x63, 0x6f, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x74, 0x6c, 0x67, 0x61, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x70, 0x74, 0x63, 0x2f, 0x61, 0x69, 0x78, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_material_display_material_display_proto_rawDescOnce sync.Once
	file_material_display_material_display_proto_rawDescData = file_material_display_material_display_proto_rawDesc
)

func file_material_display_material_display_proto_rawDescGZIP() []byte {
	file_material_display_material_display_proto_rawDescOnce.Do(func() {
		file_material_display_material_display_proto_rawDescData = protoimpl.X.CompressGZIP(file_material_display_material_display_proto_rawDescData)
	})
	return file_material_display_material_display_proto_rawDescData
}

var file_material_display_material_display_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_material_display_material_display_proto_msgTypes = make([]protoimpl.MessageInfo, 127)
var file_material_display_material_display_proto_goTypes = []interface{}{
	(MaterialStatus)(0),                              // 0: material_display.MaterialStatus
	(*MaterialListReq)(nil),                          // 1: material_display.MaterialListReq
	(*MaterialMeta)(nil),                             // 2: material_display.MaterialMeta
	(*MaterialListRsp)(nil),                          // 3: material_display.MaterialListRsp
	(*GetMaterialInfoReq)(nil),                       // 4: material_display.GetMaterialInfoReq
	(*Universal)(nil),                                // 5: material_display.Universal
	(*Label)(nil),                                    // 6: material_display.Label
	(*Statistics)(nil),                               // 7: material_display.Statistics
	(*Video)(nil),                                    // 8: material_display.Video
	(*MaterialExt)(nil),                              // 9: material_display.MaterialExt
	(*GetMaterialInfoRsp)(nil),                       // 10: material_display.GetMaterialInfoRsp
	(*BtGetMaterialInfoReq)(nil),                     // 11: material_display.BtGetMaterialInfoReq
	(*BtGetMaterialInfoRsp)(nil),                     // 12: material_display.BtGetMaterialInfoRsp
	(*BtGetMaterialInfoDetailReq)(nil),               // 13: material_display.BtGetMaterialInfoDetailReq
	(*BtGetMaterialInfoDetailRsp)(nil),               // 14: material_display.BtGetMaterialInfoDetailRsp
	(*AssetLabel)(nil),                               // 15: material_display.AssetLabel
	(*AssetFieldFilter)(nil),                         // 16: material_display.AssetFieldFilter
	(*SyncMedia)(nil),                                // 17: material_display.SyncMedia
	(*SearchMaterialsReq)(nil),                       // 18: material_display.SearchMaterialsReq
	(*SearchMaterialsRsp)(nil),                       // 19: material_display.SearchMaterialsRsp
	(*SearchMaterialsByNameReq)(nil),                 // 20: material_display.SearchMaterialsByNameReq
	(*SearchMaterialsByNameRsp)(nil),                 // 21: material_display.SearchMaterialsByNameRsp
	(*BatchSearchMaterialsByNameReq)(nil),            // 22: material_display.BatchSearchMaterialsByNameReq
	(*BatchSearchMaterialsByNameRsp)(nil),            // 23: material_display.BatchSearchMaterialsByNameRsp
	(*DirectoryListReq)(nil),                         // 24: material_display.DirectoryListReq
	(*Directory)(nil),                                // 25: material_display.Directory
	(*DirectoryListRsp)(nil),                         // 26: material_display.DirectoryListRsp
	(*DirectorySearchReq)(nil),                       // 27: material_display.DirectorySearchReq
	(*DirectorySearchRsp)(nil),                       // 28: material_display.DirectorySearchRsp
	(*DirectoryGetReq)(nil),                          // 29: material_display.DirectoryGetReq
	(*DirectoryGetRsp)(nil),                          // 30: material_display.DirectoryGetRsp
	(*DirectoryCreateReq)(nil),                       // 31: material_display.DirectoryCreateReq
	(*DirectoryCreateRsp)(nil),                       // 32: material_display.DirectoryCreateRsp
	(*MaterialMoveReq)(nil),                          // 33: material_display.MaterialMoveReq
	(*MaterialInfo)(nil),                             // 34: material_display.MaterialInfo
	(*MaterialMoveRsp)(nil),                          // 35: material_display.MaterialMoveRsp
	(*MaterialSetInfo)(nil),                          // 36: material_display.MaterialSetInfo
	(*SetMaterialInfoReq)(nil),                       // 37: material_display.SetMaterialInfoReq
	(*SetRes)(nil),                                   // 38: material_display.SetRes
	(*SetMaterialInfoRsp)(nil),                       // 39: material_display.SetMaterialInfoRsp
	(*UploadAsset)(nil),                              // 40: material_display.UploadAsset
	(*UploadToChannelReq)(nil),                       // 41: material_display.UploadToChannelReq
	(*UploadToChannelRsp)(nil),                       // 42: material_display.UploadToChannelRsp
	(*MediaDirectoryListReq)(nil),                    // 43: material_display.MediaDirectoryListReq
	(*MediaDirectory)(nil),                           // 44: material_display.MediaDirectory
	(*MediaDirectoryListRsp)(nil),                    // 45: material_display.MediaDirectoryListRsp
	(*GetDepotTokenReq)(nil),                         // 46: material_display.GetDepotTokenReq
	(*DepotGoogleDrive)(nil),                         // 47: material_display.DepotGoogleDrive
	(*DepotExtra)(nil),                               // 48: material_display.DepotExtra
	(*DepotInfo)(nil),                                // 49: material_display.DepotInfo
	(*GetDepotTokenRsp)(nil),                         // 50: material_display.GetDepotTokenRsp
	(*GenerateArthubTempDownloadUrlReqItem)(nil),     // 51: material_display.GenerateArthubTempDownloadUrlReqItem
	(*GenerateArthubTempDownloadUrlReq)(nil),         // 52: material_display.GenerateArthubTempDownloadUrlReq
	(*GenerateArthubTempDownloadUrlSucceedItem)(nil), // 53: material_display.GenerateArthubTempDownloadUrlSucceedItem
	(*GenerateArthubTempDownloadUrlFailedItem)(nil),  // 54: material_display.GenerateArthubTempDownloadUrlFailedItem
	(*GenerateArthubTempDownloadUrlRsp)(nil),         // 55: material_display.GenerateArthubTempDownloadUrlRsp
	(*ListDepotReq)(nil),                             // 56: material_display.ListDepotReq
	(*ListDepotInfo)(nil),                            // 57: material_display.ListDepotInfo
	(*ListDepotRsp)(nil),                             // 58: material_display.ListDepotRsp
	(*UploadToArthubReq)(nil),                        // 59: material_display.UploadToArthubReq
	(*UploadToArthubReqMeta)(nil),                    // 60: material_display.UploadToArthubReqMeta
	(*UploadToArthubRsp)(nil),                        // 61: material_display.UploadToArthubRsp
	(*MediaMaterialListReq)(nil),                     // 62: material_display.MediaMaterialListReq
	(*MediaMaterialMeta)(nil),                        // 63: material_display.MediaMaterialMeta
	(*MediaMaterialMetaDetail)(nil),                  // 64: material_display.MediaMaterialMetaDetail
	(*MediaMaterialListRsp)(nil),                     // 65: material_display.MediaMaterialListRsp
	(*GetExtInfoReq)(nil),                            // 66: material_display.GetExtInfoReq
	(*GetExtInfoRsp)(nil),                            // 67: material_display.GetExtInfoRsp
	(*MediaMaterialByDirectoryIdReq)(nil),            // 68: material_display.MediaMaterialByDirectoryIdReq
	(*MediaMaterialByDirectoryIdMeta)(nil),           // 69: material_display.MediaMaterialByDirectoryIdMeta
	(*MediaMaterialByDirectoryIdRsp)(nil),            // 70: material_display.MediaMaterialByDirectoryIdRsp
	(*MediaMaterialNameListReq)(nil),                 // 71: material_display.MediaMaterialNameListReq
	(*MaterialAssetNameInfo)(nil),                    // 72: material_display.MaterialAssetNameInfo
	(*MediaMaterialNameListRsp)(nil),                 // 73: material_display.MediaMaterialNameListRsp
	(*UploadChannelInfo)(nil),                        // 74: material_display.UploadChannelInfo
	(*AddUploadToChannelTaskReq)(nil),                // 75: material_display.AddUploadToChannelTaskReq
	(*AddUploadToChannelTaskRsp)(nil),                // 76: material_display.AddUploadToChannelTaskRsp
	(*CancelUploadToChannelTaskReq)(nil),             // 77: material_display.CancelUploadToChannelTaskReq
	(*CancelUploadToChannelTaskRsp)(nil),             // 78: material_display.CancelUploadToChannelTaskRsp
	(*ResumeUploadToChannelTaskReq)(nil),             // 79: material_display.ResumeUploadToChannelTaskReq
	(*ResumeUploadToChannelTaskRsp)(nil),             // 80: material_display.ResumeUploadToChannelTaskRsp
	(*GetKeywordListReq)(nil),                        // 81: material_display.GetKeywordListReq
	(*GetKeywordListRsp)(nil),                        // 82: material_display.GetKeywordListRsp
	(*Suggestion)(nil),                               // 83: material_display.Suggestion
	(*LabelOption)(nil),                              // 84: material_display.LabelOption
	(*LabelRule)(nil),                                // 85: material_display.LabelRule
	(*GetLabelRulesReq)(nil),                         // 86: material_display.GetLabelRulesReq
	(*GetLabelRulesRsp)(nil),                         // 87: material_display.GetLabelRulesRsp
	(*DownloadLabelRulesReq)(nil),                    // 88: material_display.DownloadLabelRulesReq
	(*DownloadLabelRulesRsp)(nil),                    // 89: material_display.DownloadLabelRulesRsp
	(*AddLabelRuleReq)(nil),                          // 90: material_display.AddLabelRuleReq
	(*AddLabelRuleRsp)(nil),                          // 91: material_display.AddLabelRuleRsp
	(*UpdateAssetLevelLabelsReq)(nil),                // 92: material_display.UpdateAssetLevelLabelsReq
	(*UpdateAssetLevelLabelsRsp)(nil),                // 93: material_display.UpdateAssetLevelLabelsRsp
	(*ModifyRuleContentLabelsReq)(nil),               // 94: material_display.ModifyRuleContentLabelsReq
	(*ModifyRuleContentLabelsRsp)(nil),               // 95: material_display.ModifyRuleContentLabelsRsp
	(*ModifyLabelRuleSecondLabelReq)(nil),            // 96: material_display.ModifyLabelRuleSecondLabelReq
	(*ModifyLabelRuleSecondLabelRsp)(nil),            // 97: material_display.ModifyLabelRuleSecondLabelRsp
	(*DeleteLabelRuleReq)(nil),                       // 98: material_display.DeleteLabelRuleReq
	(*DeleteLabelRuleRsp)(nil),                       // 99: material_display.DeleteLabelRuleRsp
	(*GetAssetsByLabelRuleReq)(nil),                  // 100: material_display.GetAssetsByLabelRuleReq
	(*GetAssetsByLabelRuleRsp)(nil),                  // 101: material_display.GetAssetsByLabelRuleRsp
	(*ChannelAsset)(nil),                             // 102: material_display.ChannelAsset
	(*GetChannelAssetsByLabelRuleReq)(nil),           // 103: material_display.GetChannelAssetsByLabelRuleReq
	(*GetChannelAssetsByLabelRuleRsp)(nil),           // 104: material_display.GetChannelAssetsByLabelRuleRsp
	(*AddLabelRuleTaskReq)(nil),                      // 105: material_display.AddLabelRuleTaskReq
	(*AddLabelRuleTaskRsp)(nil),                      // 106: material_display.AddLabelRuleTaskRsp
	(*AddLabelRuleTaskV2Req)(nil),                    // 107: material_display.AddLabelRuleTaskV2Req
	(*AddLabelRuleTaskV2Rsp)(nil),                    // 108: material_display.AddLabelRuleTaskV2Rsp
	(*SyncTaskDir)(nil),                              // 109: material_display.SyncTaskDir
	(*SyncTaskMedia)(nil),                            // 110: material_display.SyncTaskMedia
	(*AutomaticSyncTaskRule)(nil),                    // 111: material_display.AutomaticSyncTaskRule
	(*AddAutomaticSyncTaskRuleReq)(nil),              // 112: material_display.AddAutomaticSyncTaskRuleReq
	(*AddAutomaticSyncTaskRuleRsp)(nil),              // 113: material_display.AddAutomaticSyncTaskRuleRsp
	(*CheckAutomaticSyncTaskRuleNameReq)(nil),        // 114: material_display.CheckAutomaticSyncTaskRuleNameReq
	(*CheckAutomaticSyncTaskRuleNameRsp)(nil),        // 115: material_display.CheckAutomaticSyncTaskRuleNameRsp
	(*GetAutomaticSyncTaskRuleReq)(nil),              // 116: material_display.GetAutomaticSyncTaskRuleReq
	(*GetAutomaticSyncTaskRuleRsp)(nil),              // 117: material_display.GetAutomaticSyncTaskRuleRsp
	(*ChangeAutomaticSyncTaskRuleStatusReq)(nil),     // 118: material_display.changeAutomaticSyncTaskRuleStatusReq
	(*ChangeAutomaticSyncTaskRuleStatusRsp)(nil),     // 119: material_display.changeAutomaticSyncTaskRuleStatusRsp
	(*DeleteAutomaticSyncTaskRuleReq)(nil),           // 120: material_display.DeleteAutomaticSyncTaskRuleReq
	(*DeleteAutomaticSyncTaskRuleRsp)(nil),           // 121: material_display.DeleteAutomaticSyncTaskRuleRsp
	(*ArthubAuth)(nil),                               // 122: material_display.ArthubAuth
	(*Oauth2Config)(nil),                             // 123: material_display.Oauth2Config
	(*Oauth2Token)(nil),                              // 124: material_display.Oauth2Token
	(*GoogleDriveAuth)(nil),                          // 125: material_display.GoogleDriveAuth
	(*CloudDriveGrantReq)(nil),                       // 126: material_display.CloudDriveGrantReq
	(*CloudDriveGrantRsp)(nil),                       // 127: material_display.CloudDriveGrantRsp
	(*aix.Result)(nil),                               // 128: aix.Result
}
var file_material_display_material_display_proto_depIdxs = []int32{
	9,   // 0: material_display.MaterialMeta.material_ext:type_name -> material_display.MaterialExt
	15,  // 1: material_display.MaterialMeta.labels:type_name -> material_display.AssetLabel
	17,  // 2: material_display.MaterialMeta.sync_media_list:type_name -> material_display.SyncMedia
	128, // 3: material_display.MaterialListRsp.result:type_name -> aix.Result
	2,   // 4: material_display.MaterialListRsp.materials:type_name -> material_display.MaterialMeta
	5,   // 5: material_display.MaterialExt.universal:type_name -> material_display.Universal
	6,   // 6: material_display.MaterialExt.label:type_name -> material_display.Label
	7,   // 7: material_display.MaterialExt.statistics:type_name -> material_display.Statistics
	8,   // 8: material_display.MaterialExt.video:type_name -> material_display.Video
	128, // 9: material_display.GetMaterialInfoRsp.result:type_name -> aix.Result
	9,   // 10: material_display.GetMaterialInfoRsp.material_ext:type_name -> material_display.MaterialExt
	128, // 11: material_display.BtGetMaterialInfoRsp.result:type_name -> aix.Result
	9,   // 12: material_display.BtGetMaterialInfoRsp.material_ext_list:type_name -> material_display.MaterialExt
	128, // 13: material_display.BtGetMaterialInfoDetailRsp.result:type_name -> aix.Result
	2,   // 14: material_display.BtGetMaterialInfoDetailRsp.materials:type_name -> material_display.MaterialMeta
	15,  // 15: material_display.SearchMaterialsReq.labels:type_name -> material_display.AssetLabel
	16,  // 16: material_display.SearchMaterialsReq.asset_field_filters:type_name -> material_display.AssetFieldFilter
	128, // 17: material_display.SearchMaterialsRsp.result:type_name -> aix.Result
	2,   // 18: material_display.SearchMaterialsRsp.materials:type_name -> material_display.MaterialMeta
	128, // 19: material_display.SearchMaterialsByNameRsp.result:type_name -> aix.Result
	2,   // 20: material_display.SearchMaterialsByNameRsp.materials:type_name -> material_display.MaterialMeta
	128, // 21: material_display.BatchSearchMaterialsByNameRsp.result:type_name -> aix.Result
	2,   // 22: material_display.BatchSearchMaterialsByNameRsp.materials:type_name -> material_display.MaterialMeta
	128, // 23: material_display.DirectoryListRsp.result:type_name -> aix.Result
	25,  // 24: material_display.DirectoryListRsp.dirs:type_name -> material_display.Directory
	128, // 25: material_display.DirectorySearchRsp.result:type_name -> aix.Result
	25,  // 26: material_display.DirectorySearchRsp.dirs:type_name -> material_display.Directory
	128, // 27: material_display.DirectoryGetRsp.result:type_name -> aix.Result
	25,  // 28: material_display.DirectoryGetRsp.dir:type_name -> material_display.Directory
	128, // 29: material_display.DirectoryCreateRsp.result:type_name -> aix.Result
	25,  // 30: material_display.DirectoryCreateRsp.dir:type_name -> material_display.Directory
	128, // 31: material_display.MaterialMoveRsp.result:type_name -> aix.Result
	34,  // 32: material_display.MaterialMoveRsp.material_items:type_name -> material_display.MaterialInfo
	6,   // 33: material_display.MaterialSetInfo.label:type_name -> material_display.Label
	36,  // 34: material_display.SetMaterialInfoReq.material_set_list:type_name -> material_display.MaterialSetInfo
	128, // 35: material_display.SetMaterialInfoRsp.result:type_name -> aix.Result
	38,  // 36: material_display.SetMaterialInfoRsp.set_res_list:type_name -> material_display.SetRes
	40,  // 37: material_display.UploadToChannelReq.assets:type_name -> material_display.UploadAsset
	128, // 38: material_display.UploadToChannelRsp.result:type_name -> aix.Result
	128, // 39: material_display.MediaDirectoryListRsp.result:type_name -> aix.Result
	44,  // 40: material_display.MediaDirectoryListRsp.dirs:type_name -> material_display.MediaDirectory
	47,  // 41: material_display.DepotExtra.google_drive:type_name -> material_display.DepotGoogleDrive
	48,  // 42: material_display.DepotInfo.extra:type_name -> material_display.DepotExtra
	128, // 43: material_display.GetDepotTokenRsp.result:type_name -> aix.Result
	49,  // 44: material_display.GetDepotTokenRsp.items:type_name -> material_display.DepotInfo
	51,  // 45: material_display.GenerateArthubTempDownloadUrlReq.items:type_name -> material_display.GenerateArthubTempDownloadUrlReqItem
	128, // 46: material_display.GenerateArthubTempDownloadUrlRsp.result:type_name -> aix.Result
	53,  // 47: material_display.GenerateArthubTempDownloadUrlRsp.succeed_items:type_name -> material_display.GenerateArthubTempDownloadUrlSucceedItem
	54,  // 48: material_display.GenerateArthubTempDownloadUrlRsp.failed_items:type_name -> material_display.GenerateArthubTempDownloadUrlFailedItem
	128, // 49: material_display.ListDepotRsp.result:type_name -> aix.Result
	57,  // 50: material_display.ListDepotRsp.items:type_name -> material_display.ListDepotInfo
	60,  // 51: material_display.UploadToArthubReq.metas:type_name -> material_display.UploadToArthubReqMeta
	128, // 52: material_display.UploadToArthubRsp.result:type_name -> aix.Result
	64,  // 53: material_display.MediaMaterialMeta.detail:type_name -> material_display.MediaMaterialMetaDetail
	128, // 54: material_display.MediaMaterialListRsp.result:type_name -> aix.Result
	63,  // 55: material_display.MediaMaterialListRsp.materials:type_name -> material_display.MediaMaterialMeta
	128, // 56: material_display.GetExtInfoRsp.result:type_name -> aix.Result
	9,   // 57: material_display.MediaMaterialByDirectoryIdMeta.material_ext:type_name -> material_display.MaterialExt
	128, // 58: material_display.MediaMaterialByDirectoryIdRsp.result:type_name -> aix.Result
	69,  // 59: material_display.MediaMaterialByDirectoryIdRsp.materials:type_name -> material_display.MediaMaterialByDirectoryIdMeta
	64,  // 60: material_display.MaterialAssetNameInfo.detail:type_name -> material_display.MediaMaterialMetaDetail
	128, // 61: material_display.MediaMaterialNameListRsp.result:type_name -> aix.Result
	72,  // 62: material_display.MediaMaterialNameListRsp.asset_name_infos:type_name -> material_display.MaterialAssetNameInfo
	74,  // 63: material_display.AddUploadToChannelTaskReq.to_channels:type_name -> material_display.UploadChannelInfo
	128, // 64: material_display.AddUploadToChannelTaskRsp.result:type_name -> aix.Result
	128, // 65: material_display.CancelUploadToChannelTaskRsp.result:type_name -> aix.Result
	128, // 66: material_display.ResumeUploadToChannelTaskRsp.result:type_name -> aix.Result
	128, // 67: material_display.GetKeywordListRsp.result:type_name -> aix.Result
	83,  // 68: material_display.GetKeywordListRsp.Suggestions:type_name -> material_display.Suggestion
	15,  // 69: material_display.LabelRule.labels:type_name -> material_display.AssetLabel
	84,  // 70: material_display.LabelRule.label_options:type_name -> material_display.LabelOption
	2,   // 71: material_display.LabelRule.link_asset:type_name -> material_display.MaterialMeta
	15,  // 72: material_display.GetLabelRulesReq.labels:type_name -> material_display.AssetLabel
	128, // 73: material_display.GetLabelRulesRsp.result:type_name -> aix.Result
	85,  // 74: material_display.GetLabelRulesRsp.rules:type_name -> material_display.LabelRule
	15,  // 75: material_display.DownloadLabelRulesReq.labels:type_name -> material_display.AssetLabel
	128, // 76: material_display.DownloadLabelRulesRsp.result:type_name -> aix.Result
	85,  // 77: material_display.AddLabelRuleReq.rule:type_name -> material_display.LabelRule
	128, // 78: material_display.AddLabelRuleRsp.result:type_name -> aix.Result
	15,  // 79: material_display.UpdateAssetLevelLabelsReq.labels:type_name -> material_display.AssetLabel
	128, // 80: material_display.UpdateAssetLevelLabelsRsp.result:type_name -> aix.Result
	15,  // 81: material_display.ModifyRuleContentLabelsReq.add:type_name -> material_display.AssetLabel
	15,  // 82: material_display.ModifyRuleContentLabelsReq.remove:type_name -> material_display.AssetLabel
	128, // 83: material_display.ModifyRuleContentLabelsRsp.result:type_name -> aix.Result
	128, // 84: material_display.ModifyLabelRuleSecondLabelRsp.result:type_name -> aix.Result
	85,  // 85: material_display.DeleteLabelRuleReq.rule:type_name -> material_display.LabelRule
	128, // 86: material_display.DeleteLabelRuleRsp.result:type_name -> aix.Result
	128, // 87: material_display.GetAssetsByLabelRuleRsp.result:type_name -> aix.Result
	2,   // 88: material_display.GetAssetsByLabelRuleRsp.materials:type_name -> material_display.MaterialMeta
	15,  // 89: material_display.ChannelAsset.labels:type_name -> material_display.AssetLabel
	128, // 90: material_display.GetChannelAssetsByLabelRuleRsp.result:type_name -> aix.Result
	102, // 91: material_display.GetChannelAssetsByLabelRuleRsp.assets:type_name -> material_display.ChannelAsset
	128, // 92: material_display.AddLabelRuleTaskRsp.result:type_name -> aix.Result
	128, // 93: material_display.AddLabelRuleTaskV2Rsp.result:type_name -> aix.Result
	109, // 94: material_display.AutomaticSyncTaskRule.dirs:type_name -> material_display.SyncTaskDir
	110, // 95: material_display.AutomaticSyncTaskRule.medias:type_name -> material_display.SyncTaskMedia
	111, // 96: material_display.AddAutomaticSyncTaskRuleReq.task_rule:type_name -> material_display.AutomaticSyncTaskRule
	128, // 97: material_display.AddAutomaticSyncTaskRuleRsp.result:type_name -> aix.Result
	128, // 98: material_display.CheckAutomaticSyncTaskRuleNameRsp.result:type_name -> aix.Result
	128, // 99: material_display.GetAutomaticSyncTaskRuleRsp.result:type_name -> aix.Result
	111, // 100: material_display.GetAutomaticSyncTaskRuleRsp.task_rules:type_name -> material_display.AutomaticSyncTaskRule
	128, // 101: material_display.changeAutomaticSyncTaskRuleStatusRsp.result:type_name -> aix.Result
	128, // 102: material_display.DeleteAutomaticSyncTaskRuleRsp.result:type_name -> aix.Result
	123, // 103: material_display.GoogleDriveAuth.config:type_name -> material_display.Oauth2Config
	124, // 104: material_display.GoogleDriveAuth.token:type_name -> material_display.Oauth2Token
	122, // 105: material_display.CloudDriveGrantReq.arthub_auth:type_name -> material_display.ArthubAuth
	125, // 106: material_display.CloudDriveGrantReq.google_drive_auth:type_name -> material_display.GoogleDriveAuth
	128, // 107: material_display.CloudDriveGrantRsp.result:type_name -> aix.Result
	108, // [108:108] is the sub-list for method output_type
	108, // [108:108] is the sub-list for method input_type
	108, // [108:108] is the sub-list for extension type_name
	108, // [108:108] is the sub-list for extension extendee
	0,   // [0:108] is the sub-list for field type_name
}

func init() { file_material_display_material_display_proto_init() }
func file_material_display_material_display_proto_init() {
	if File_material_display_material_display_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_material_display_material_display_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMaterialInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Universal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Statistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Video); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialExt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMaterialInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BtGetMaterialInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BtGetMaterialInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BtGetMaterialInfoDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BtGetMaterialInfoDetailRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetFieldFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncMedia); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchMaterialsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchMaterialsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchMaterialsByNameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchMaterialsByNameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchSearchMaterialsByNameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchSearchMaterialsByNameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectoryListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Directory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectoryListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectorySearchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectorySearchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectoryGetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectoryGetRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectoryCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DirectoryCreateRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialMoveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialMoveRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialSetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMaterialInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMaterialInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadToChannelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadToChannelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaDirectoryListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaDirectory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaDirectoryListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDepotTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepotGoogleDrive); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepotExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDepotTokenRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateArthubTempDownloadUrlReqItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateArthubTempDownloadUrlReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateArthubTempDownloadUrlSucceedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateArthubTempDownloadUrlFailedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateArthubTempDownloadUrlRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDepotReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDepotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDepotRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadToArthubReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadToArthubReqMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadToArthubRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialMetaDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExtInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExtInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialByDirectoryIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialByDirectoryIdMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialByDirectoryIdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialNameListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaterialAssetNameInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediaMaterialNameListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadChannelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUploadToChannelTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUploadToChannelTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelUploadToChannelTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelUploadToChannelTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeUploadToChannelTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeUploadToChannelTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKeywordListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetKeywordListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Suggestion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLabelRulesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLabelRulesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadLabelRulesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadLabelRulesRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLabelRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLabelRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAssetLevelLabelsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAssetLevelLabelsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyRuleContentLabelsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyRuleContentLabelsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyLabelRuleSecondLabelReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyLabelRuleSecondLabelRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLabelRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLabelRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetsByLabelRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetsByLabelRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAsset); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAssetsByLabelRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChannelAssetsByLabelRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLabelRuleTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLabelRuleTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLabelRuleTaskV2Req); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLabelRuleTaskV2Rsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncTaskDir); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncTaskMedia); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutomaticSyncTaskRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAutomaticSyncTaskRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[112].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAutomaticSyncTaskRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[113].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAutomaticSyncTaskRuleNameReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[114].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAutomaticSyncTaskRuleNameRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[115].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutomaticSyncTaskRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[116].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAutomaticSyncTaskRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[117].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeAutomaticSyncTaskRuleStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[118].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeAutomaticSyncTaskRuleStatusRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[119].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAutomaticSyncTaskRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[120].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAutomaticSyncTaskRuleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[121].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArthubAuth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[122].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Oauth2Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[123].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Oauth2Token); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[124].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoogleDriveAuth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[125].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudDriveGrantReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_material_display_material_display_proto_msgTypes[126].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudDriveGrantRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_material_display_material_display_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   127,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_material_display_material_display_proto_goTypes,
		DependencyIndexes: file_material_display_material_display_proto_depIdxs,
		EnumInfos:         file_material_display_material_display_proto_enumTypes,
		MessageInfos:      file_material_display_material_display_proto_msgTypes,
	}.Build()
	File_material_display_material_display_proto = out.File
	file_material_display_material_display_proto_rawDesc = nil
	file_material_display_material_display_proto_goTypes = nil
	file_material_display_material_display_proto_depIdxs = nil
}
