sync_game_code_list: ['gas','aov_nsa']
# clickhouse db配置
bi_db_name: ieg_ads_bi_test

# pg_pivot_table schema.table
pg_pivot_table: pivot_server.creative_analysis_pivot
# ad_hoc_server打标服务
ad_hoc_server_on: 0
ad_hoc_server: 10.190.12.248:8080
push_game_code_list:
  - "test"
  - "drive_test"
  - "hok_prod"
notify_game_code_list: []
qiwei_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=112ea781-804c-479e-ba9a-643515b19088"
aix_url: "https://test.aix.intlgame.com/creative/hub?__game="
notify_unused_material_cron: "*/5 * * * *"

creative_analysis_pivot_new: ieg_ads_bi_test.creative_analysis_pivot_new
creative_insights_target: http://101.33.51.32:8080

clickhouse_env: ieg_ads_bi_test

# 是否聚合统计标签规则的曝光日期 (标签规则只需要计算最终的曝光日期即可)
# 因为是在定时任务同步全量渠道素材的时候触发的
# 可能会回溯历史日期渠道素材，这里可以关闭，避免不必要的计算，以提高效率
# 待正常每天同步的时候，再打开
agg_label_rule_impression_date: true

# 是否禁用clickhouse， 默认false
# 海外gcp环境没有clickhouse，需要禁用
disabel_clickhouse: false

# 哪些游戏bigquery的pivot表需要加game_code后缀
creative_daily_pivot_new_suffix_games:
  - "mc_demo"

# 解析serial的时候需要移除的后缀
serial_remove_suffix:
  - "_001"
  - "_002"
  - "_003"
  - "_004"
  - "_005"
  - "_006"
  - "_007"
  - "_008"
  - "_009"
  - " – Copy"
  - "– Copy"
  - " - Copy"
  - "- Copy"
  - "-copy"

# 哪些游戏解析素材名称到标签
parse_name_to_label_templates:
  - game_code: "aov_nsa"
    min_split_parts: 11 # 分割后至少多少段
    parts: 
      - index: 5 # 分割后的第几段，从1开始
        first_label: "素材角色" # 对应的一级标签名称
        separator: "&" # 该二级标签的分割符，为空表示不分割
        to_title_case: true # 二级标签是否转成title case
      - index: 6 # 分割后的第几段，从1开始
        first_label: "Duration" # 对应的一级标签名称
        duration_map: true # 是否解析成duration映射
      - index: 10 # 分割后的第几段，从1开始
        first_label: "供应商" # 对应的一级标签名称
      - index: 11 # 分割后的第几段，从1开始
        first_label: "对接人员" # 对应的一级标签名称
  - game_code: "nikke_test"
    parts_separator: "-" # 分割符, 为空则采用serial name分割方式
    min_split_parts: 9 # 分割后至少多少段
    parts: 
      - index: 4 # 分割后的第几段，从1开始
        first_label: "制作方式" # 对应的一级标签名称
        match_reg: "^[0-9]{3,}$"  # 正则匹配，为空表示不用匹配
      - index: 5 # 分割后的第几段，从1开始
        first_label: "素材主题" # 对应的一级标签名称
        separator: "+" # 该二级标签的分割符，为空表示不分割
        match_items_num: 2 # 分割后需要匹配的段数，0表示不用匹配
        want_items_index: # 匹配后需要取哪些数据，下标从1开始, 为空则全部取
          - 2