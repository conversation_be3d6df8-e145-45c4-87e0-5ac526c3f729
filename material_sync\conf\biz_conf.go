package conf

import (
	"os"
	"regexp"

	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"

	jsoniter "github.com/json-iterator/go"
	"gopkg.in/yaml.v2"
)

// BizConfStruct biz_conf_xx.yaml 定义的结构
type BizConfStruct struct {
	SyncGameCodeList           []string                    `yaml:"sync_game_code_list"`
	BiDbName                   string                      `yaml:"bi_db_name"`
	PGPivotTable               string                      `yaml:"pg_pivot_table"`
	AdHocServer                string                      `yaml:"ad_hoc_server"`
	AdHocServerOn              uint32                      `yaml:"ad_hoc_server_on"`
	PushGameCodeList           []string                    `yaml:"push_game_code_list"`
	CreativeAnalysisPivotNew   string                      `yaml:"creative_analysis_pivot_new"`
	QiweiURL                   string                      `yaml:"qiwei_url"`                     // 企业微信机器人地址
	NotifyGameCodeList         []string                    `yaml:"notify_game_code_list"`         // 通知gameCode
	AixUrl                     string                      `yaml:"aix_url"`                       // 素材库页面地址
	NotifyUnusedMaterialCron   string                      `yaml:"notify_unused_material_cron"`   // 通知未使用素材的cron表达式
	CreativeInsightsTarget     string                      `yaml:"creative_insights_target"`      // 素材视频关联算法端服务器
	MediaContentMapGames       []string                    `yaml:"media_content_map_games"`       // 启用内容匹配算法的游戏列表
	ClickhouseEnv              string                      `yaml:"clickhouse_env"`                // clickhouse环境
	SyncImpressionDateHistorys []SyncImpressionDateHistory `yaml:"sync_impression_date_historys"` // 同步历史上线数据配置

	// 20231220
	Cos Cos `yaml:"cos"` // cos配置

	// 20240424
	AggLabelRuleImpressionDate bool `yaml:"agg_label_rule_impression_date"` // 是否聚合统计标签规则的曝光日期

	// 20240801
	DisabelClickhouse bool `yaml:"disabel_clickhouse"` // 是否禁用clickhouse

	// 20241118
	ParseNameToLabelTemplates []*ParseNameToLabelTemplate `yaml:"parse_name_to_label_templates"` // 哪些游戏解析素材名称到标签

	CreativeDailyPivotNewSuffixGames []string `yaml:"creative_daily_pivot_new_suffix_games"` // 哪些游戏bigquery的pivot表需要加game_code后缀

	SerialRemoveSuffix []string `yaml:"serial_remove_suffix"` //  解析serial的时候需要移除的后缀
}

// ParseNameToLabelTemplate 解析素材名称到标签的模板
type ParseNameToLabelTemplate struct {
	GameCode       string `yaml:"game_code"`
	PartsSeparator string `yaml:"parts_separator"` // 分割符, 为空则采用serial name分割方式
	MinSplitParts  int    `yaml:"min_split_parts"` // 分割后至少多少个部分
	Parts          []*struct {
		Index          int    `yaml:"index"`            // 分割后的第几个，从1开始
		FirstLabel     string `yaml:"first_label"`      // 对应的一级标签名称
		Separator      string `yaml:"separator"`        // 该二级标签的分割符，为空表示不分割
		MatchItemsNum  int    `yaml:"match_items_num"`  // 分割后需要匹配的段数，0表示不用匹配
		WantItemsIndex []int  `yaml:"want_items_index"` // 匹配后需要取哪些数据，下标从1开始, 为空则全部取
		ToTitleCase    bool   `yaml:"to_title_case"`    // 二级标签是否转成title case
		DurationMap    bool   `yaml:"duration_map"`     // 是否解析成duration映射

		MatchReg        string         `yaml:"match_reg"` // 正则匹配，为空表示不用匹配
		CompileMatchReg *regexp.Regexp `yaml:"-"`         // match_reg编译后的正则
	} `yaml:"parts"`
}

// SyncImpressionDateHistory ...
type SyncImpressionDateHistory struct {
	GameCode  string `yaml:"game_code"`
	StartTime string `yaml:"start_time"`
	EndTime   string `yaml:"end_time"`
	IfExecute bool   `yaml:"if_execute"`
}

// Cos ...
type Cos struct {
	BucketURL string `yaml:"bucket_url"`
	SecretID  string `yaml:"secret_id"`
	SecretKey string `yaml:"secret_key"`
}

// bizConf 业务配置
var bizConf BizConfStruct

// LoadBizConf 读取业务配置
func LoadBizConf() {
	confPath := "conf/biz_conf.yaml"
	content, err := os.ReadFile(confPath)
	if err != nil {
		log.Fatalf("fail to read '%v': %v", confPath, err)
	}

	err = yaml.Unmarshal(content, &bizConf)
	if err != nil {
		log.Fatalf("error: %v", err)
	}

	bizConfContent, err := jsoniter.Marshal(bizConf)
	if err != nil {
		log.Fatalf("marshal biz conf failed: %s", err.Error())
	}

	log.Debugf("load biz conf: %v", string(bizConfContent))

	// 校验解析素材名称到标签
	for _, t := range bizConf.ParseNameToLabelTemplates {
		if t.GameCode == "" {
			log.Fatalf("ParseNameToLabelTemplate.GameCode can't be empty")
		}
		for _, p := range t.Parts {
			if p.Index <= 0 { // 从1开始
				log.Fatalf("ParseNameToLabelTemplate.Parts.Index can't be less than 1")
			}
			if p.Index > t.MinSplitParts {
				log.Fatalf("ParseNameToLabelTemplate.Parts.Index can't be greater than %v", t.MinSplitParts)
			}
			if p.FirstLabel == "" {
				log.Fatalf("ParseNameToLabelTemplate.Parts.FirstLabel can't be empty")
			}
			if p.MatchItemsNum < 0 {
				log.Fatalf("ParseNameToLabelTemplate.Parts.MatchItemsNum can't be less than 0")
			}
			for _, index := range p.WantItemsIndex {
				if index <= 0 { // 从1开始
					log.Fatalf("ParseNameToLabelTemplate.Parts.WantItemsIndex can't be less than 1")
				}
			}
			if p.MatchReg != "" {
				p.CompileMatchReg, err = regexp.Compile(p.MatchReg)
				if err != nil {
					log.Fatalf("ParseNameToLabelTemplate.Parts.MatchReg compile failed: %v", err)
				}
			}
		}
	}
}

// GetBizConf ...
func GetBizConf() *BizConfStruct {
	return &bizConf
}

// GetGameParseNameToLabelTemplate 获取游戏解析素材名称到标签模板， 如果没有则返回nil
func GetGameParseNameToLabelTemplate(gameCode string) *ParseNameToLabelTemplate {
	for _, t := range bizConf.ParseNameToLabelTemplates {
		if t.GameCode == gameCode {
			return t
		}
	}

	return nil
}
