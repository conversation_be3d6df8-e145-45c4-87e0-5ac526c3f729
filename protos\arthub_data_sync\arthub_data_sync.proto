syntax = "proto3";

package arthub_data_sync;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/arthub_data_sync";

import "aix/aix_common_message.proto";

// 内部接口 触发定时任务, POST, /api/v1/arthub_data_sync/cron_trigger
message CronTriggerReq {
    string cron_name = 1;  // 定时任务名字
}

message CronTriggerRsp {
    aix.Result result = 1;  // 返回结果
}

// 内部测试接口, POST, /api/v1/arthub_data_sync/say_hi
message SayHiReq {
    string arthub_code  = 1;  // arthub code
    string token        = 2;  // token
    repeated uint64 ids = 3;  // 素材列表
}

message SayHiRsp {
    aix.Result result = 1;  // 返回结果
}

// 同步素材status, POST, /api/v1/arthub_data_sync/sync_status
message SyncStatusReq {
}

message SyncStatusRsp {
    aix.Result result = 1;  // 返回结果
}

// 同步素材status, POST, /api/v1/arthub_data_sync/sync_material
message SyncMaterialReq {
    string game_code = 1;  // game_code
    string node_id   = 2;  // 节点ID
    int32 node_type  = 3;  // 节点类型, 1-arthub dir
}

message SyncMaterialRsp {
    aix.Result result  = 1;  // 返回结果
    int32 expeted_time = 2;  // 预期时间(秒)
}