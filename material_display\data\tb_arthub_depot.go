package data

import (
	"context"
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// GetDeportMaxPriority 获取网盘当前最大优先级
func GetDeportMaxPriority(ctx context.Context, depotType int) (int, error) {
	var rows []*model.ArthubDepot

	query := postgresql.GetDBWithContext(ctx).Model(&model.ArthubDepot{})
	query.Where("type = ?", depotType).Order("priority desc").Limit(1)
	err := query.Select(&rows)
	if err != nil {
		return 0, err
	}
	if len(rows) > 0 {
		return rows[0].Priority, nil
	}

	return 0, nil
}

// UpsertDepotByFields 插入网盘信息 或者更新网盘指定字段信息
func UpsertDepotByFields(ctx context.Context, depot *model.ArthubDepot, fields []string) error {
	if len(fields) == 0 {
		return fmt.Errorf("fields is empty")
	}
	db := postgresql.GetDBWithContext(ctx)

	query := db.Model(depot)
	query.OnConflict("(game_code) DO UPDATE")
	for _, field := range fields {
		query.Set(fmt.Sprintf("%s=EXCLUDED.%s", field, field))
	}

	_, err := query.Insert()
	return err
}
