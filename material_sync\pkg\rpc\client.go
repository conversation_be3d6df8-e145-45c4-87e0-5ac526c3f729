package rpc

import (
	"context"
	"e.coding.intlgame.com/ptc/aix-backend/common/constant"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/preprocess"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
)

var client *resty.Client

func init() {
	client = resty.New()
	client.SetLogger(&restyLogger{})
	client.JSONUnmarshal = clientJSONUnmarshal
	client.SetDebug(true)
	// 设置请求超时
	client.SetTimeout(time.Minute)
}

// NewRequest 新生成一个request请求
func NewRequest(ctx context.Context) *resty.Request {
	return client.R().SetHeader("request_id", ctx.Value(constant.HeaderTraceID).(string))
}

func clientJSONUnmarshal(data []byte, v interface{}) error {
	return preprocess.Unmarshal(data, v)
}

type restyLogger struct {
}

// type Logger interface {
// 	Debugf(format string, v ...interface{})
// 	Errorf(format string, v ...interface{})
// 	Warnf(format string, v ...interface{})
// }

func (l *restyLogger) Debugf(format string, v ...interface{}) {
	log.Debugf(format, v)
}

func (l *restyLogger) Errorf(format string, v ...interface{}) {
	log.Errorf(format, v)
}

func (l *restyLogger) Warnf(format string, v ...interface{}) {
	log.Warnf(format, v)
}

// Test ...
func Test(ctx *gin.Context) {
	rsp, err := client.R().SetHeader("request_id", ctx.GetString(constant.HeaderTraceID)).Get("http://127.0.0.1?a=b")
	fmt.Println(err, string(rsp.Body()))
}
