syntax = "proto3";

package test;
option go_package="e.coding.intlgame.com/ptc/aix-backend/protos/test";

import "aix/aix_common_message.proto";
import "google/protobuf/struct.proto";

message TestReq {
  string label = 1;
  uint32 type = 2;
  repeated uint64 reps = 3;
}

message TestRsq {
  aix.Result result = 1;
  string label = 2;
  uint32 type = 3;
  repeated uint64 reps = 4;
  google.protobuf.Struct activity_info = 5;
}