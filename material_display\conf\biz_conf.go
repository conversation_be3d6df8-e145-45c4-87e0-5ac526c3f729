package conf

import (
	"io/ioutil"
	"log"

	pkglog "e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"

	jsoniter "github.com/json-iterator/go"
	"gopkg.in/yaml.v2"
)

type GameMap struct {
	GameCode    string `yaml:"game_code"` // 游戏代号
	DstGameCode string `yaml:"dst_game_code"`
}

type LabelConf struct {
	GameCode  string   `yaml:"game_code"`  // 游戏代号
	LabelList []string `yaml:"label_list"` // 标签列表
}

// Cos ...
type Cos struct {
	BucketURL string `yaml:"bucket_url"`
	SecretID  string `yaml:"secret_id"`
	SecretKey string `yaml:"secret_key"`
}

// DropboxTokenOpenAPI dropbox网盘token openapi配置
type DropboxTokenOpenAPI struct {
	// token openapi 相关配置
	TokenHost string `yaml:"token_host"`
	AppID     string `yaml:"app_id"`
	SigKey    string `yaml:"sig_key"`
}

// CloudDrive ...
type CloudDrive struct {
	CreateTablesBaseGame string              `yaml:"create_tables_base_game"` // 以哪个游戏为基准创建表
	DropboxTokenOpenAPI  DropboxTokenOpenAPI `yaml:"dropbox_token_openapi"`
}

// BizConfStruct biz_conf_xx.yaml 定义的结构
type BizConfStruct struct {
	GameCodeMap []GameMap   `yaml:"game_code_map"`
	LabelList   []LabelConf `yaml:"label_list"`

	// GoogleImgRatio Image sizes for image ads
	GoogleImgRatio map[string][]string `yaml:"google_img_ratio"`

	MaterialSync string `yaml:"material_sync"` // material_sync 服务地址

	ApplovinAdvertiseHost string `yaml:"applovin_advertise_host"`

	// 20231225
	Cos Cos `yaml:"cos"` // cos配置, 已经切换到common/pkg/setting包 server_conf.yaml处理

	// 20231226
	GetFirstLabelOptionURL string `yaml:"get_first_label_option_url"` // 获取一级标签配置url

	NormalizeUTCTime bool `yaml:"normalize_utc_time"` // 是否统一时间到utc

	SerialRemoveSuffix []string `yaml:"serial_remove_suffix"` //  解析serial的时候需要移除的后缀

	CloudDrive CloudDrive `yaml:"cloud_drive"` // 网盘相关配置
}

// bizConf 业务配置
var bizConf BizConfStruct

// LoadBizConf 读取业务配置
func LoadBizConf() {
	confPath := "conf/biz_conf.yaml"
	content, err := ioutil.ReadFile(confPath)
	if err != nil {
		log.Fatalf("fail to read '%v': %v", confPath, err)
	}

	err = yaml.Unmarshal(content, &bizConf)
	if err != nil {
		log.Fatalf("error: %v", err)
	}

	bizConfContent, err := jsoniter.Marshal(bizConf)
	if err != nil {
		log.Fatalf("marshal biz conf failed: %s", err.Error())
	}
	pkglog.Debugf("load biz conf: %v", string(bizConfContent))
}

// GetBizConf ...
func GetBizConf() *BizConfStruct {
	return &bizConf
}
