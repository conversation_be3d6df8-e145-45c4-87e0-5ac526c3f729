package data

import (
	"context"
	"fmt"

	model "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
)

// GetCreativeDirectoryByID 根据id查creative_directory
func GetCreativeDirectoryByID(ctx context.Context,
	gameCode string, directoryID string) (*model.CreativeDirectory, error) {
	table := model.GetCreativeDirectoryTableName(gameCode)

	d := &model.CreativeDirectory{}
	db := postgresql.GetDBWithContext(ctx)
	err := db.Model(d).Table(table).Where("id = ?", directoryID).First()
	if err != nil {
		return nil, err
	}

	return d, nil
}

// SearchCreativeDirectoryFilter ...
type SearchCreativeDirectoryFilter struct {
	PathPrefix string // 路径前缀匹配
	Name       string // 名字匹配
	Offset     int
	Limit      int
}

// SearchCreativeDirectory 根据条件查creative_directory
func SearchCreativeDirectory(ctx context.Context,
	gameCode string, filter *SearchCreativeDirectoryFilter) ([]*model.CreativeDirectory, int, error) {
	var rows []*model.CreativeDirectory
	db := postgresql.GetDBWithContext(ctx)
	query := db.Model(&rows).Table(model.GetCreativeDirectoryTableName(gameCode))

	pathPrefix := fmt.Sprintf("%s%%", filter.PathPrefix)
	query.Where("full_path_id LIKE ?", pathPrefix)
	likeName := fmt.Sprintf("%%%s%%", filter.Name)
	query.Where("name ILIKE ?", likeName)

	query.Order("name", "id")
	query.Offset(filter.Offset)
	query.Limit(filter.Limit)

	count, err := query.SelectAndCount()

	return rows, count, err
}
