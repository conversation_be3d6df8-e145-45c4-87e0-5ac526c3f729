syntax = "proto3";

package dropbox_sync;
option go_package = "e.coding.intlgame.com/ptc/aix-backend/protos/dropbox_sync";

import "aix/aix_common_message.proto";

message SayHiReq {
    string msg       = 3;
}

message SayHiRsp {
    aix.Result result = 1;
    string     msg    = 2;
}


// 内部接口 触发定时任务, POST, /api/v1/dropbox_sync/cron_trigger
message CronTriggerReq {
    string cron_name = 1;  // 定时任务名字
    string game_code = 2;
    string depot_id  = 3;
}

message CronTriggerRsp {
    aix.Result result = 1;  // 返回结果
}