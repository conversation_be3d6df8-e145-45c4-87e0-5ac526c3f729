package service

import (
	"context"
	"fmt"

	"e.coding.intlgame.com/ptc/aix-backend/common/metrics/ginmetrics"
	pgmodel "e.coding.intlgame.com/ptc/aix-backend/common/model/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/common/pkg/log"
	pgdb "e.coding.intlgame.com/ptc/aix-backend/common/pkg/postgresql"
	"e.coding.intlgame.com/ptc/aix-backend/material_sync/metrics"

	"time"
)

// SyncAssetMapTask 设置aix_asset_id到channel_asset_id的映射
func SyncAssetMapTask(ctx context.Context) {
	syncAssetMapLoop()
}

// syncAssetMapLoop 设置aix_asset_id到channel_asset_id映射循环
func syncAssetMapLoop() {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "syncAssetMapLoop start")

	st := time.Now()

	game_codes, err := getGameCodes(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "getGameCodes failed: %s", err)
		return
	}

	for _, game_code := range game_codes {
		SyncAssetMap(game_code)
		clearInvalidAssetMap(game_code)
	}

	cost := time.Since(st)
	log.InfoContextf(ctx, "syncAssetMapLoop end, cost: %v", cost)
}

// SyncAssetMap 同步某个游戏的素材映射
func SyncAssetMap(game_code string) {
	ctx := log.NewSessionIDContext()
	log.DebugContextf(ctx, "syncAssetMapGameCode %s start", game_code)

	st := time.Now()

	channel_assets, err := latestChannelAssets(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "latestChannelAssets failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get channel assets number: %d", len(channel_assets))

	var resource_name_asset_maps []pgmodel.CreativeRecommendAssetMap
	channel_assets, resource_name_asset_maps, err = genAssetMapByResourceName(ctx, game_code, channel_assets)
	if err != nil {
		log.ErrorContextf(ctx, "genAssetMapGameCodeByResourceName failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get resource name maps number: %d, remain unmapped channel assets number: %d", len(resource_name_asset_maps), len(channel_assets))

	var content_asset_maps []pgmodel.CreativeRecommendAssetMap
	channel_assets, content_asset_maps, err = genAssetMapByContent(ctx, game_code, channel_assets)
	if err != nil {
		log.ErrorContextf(ctx, "genAssetMapByAssetContent failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get content maps number: %d, remain unmapped channel assets number: %d", len(content_asset_maps), len(channel_assets))

	var name_asset_maps []pgmodel.CreativeRecommendAssetMap
	channel_assets, name_asset_maps, err = genAssetMapByName(ctx, game_code, channel_assets)
	if err != nil {
		log.ErrorContextf(ctx, "genAssetMapByName failed: %s", err)
		return
	}

	log.DebugContextf(ctx, "get name maps number: %d, remain unmapped channel assets number: %d", len(name_asset_maps), len(channel_assets))

	var asset_maps []pgmodel.CreativeRecommendAssetMap
	asset_maps = append(asset_maps, resource_name_asset_maps...)
	asset_maps = append(asset_maps, content_asset_maps...)
	asset_maps = append(asset_maps, name_asset_maps...)

	author := "material_sync.sync_asset_map"
	err = upsertAssetMaps(ctx, game_code, author, asset_maps)
	if err != nil {
		log.ErrorContextf(ctx, "upsertAssetMaps failed: %s", err)
		return
	}

	cost := time.Since(st)
	log.InfoContextf(ctx, "syncAssetMapGameCode %s end: %v", game_code, cost)

	reportAssetMap(ctx, game_code, asset_maps, channel_assets)
}

// reportAssetMap 素材关联上报
func reportAssetMap(ctx context.Context, game_code string, asset_maps []pgmodel.CreativeRecommendAssetMap, unmapped_channel_assets []channelAsset) {
	report_data := make(map[string](map[string]int))
	report_data["Google"] = make(map[string]int)
	report_data["Facebook"] = make(map[string]int)

	channel_type2name := make(map[int32]string)
	channel_type2name[1] = "Google"
	channel_type2name[2] = "Facebook"

	map_source2name := make(map[int32]string)
	map_source2name[1] = "ResourceNameMapped"
	map_source2name[2] = "NameMapped"
	map_source2name[3] = "ContentMapped"

	for _, m := range asset_maps {
		count_type_map := report_data[channel_type2name[m.ChannelType]]
		count_type_map[map_source2name[m.MapSource]]++
		count_type_map["Total"]++
	}

	for _, a := range unmapped_channel_assets {
		count_type_map := report_data[channel_type2name[a.ChannelType]]
		count_type_map["Unmapped"]++
		count_type_map["Total"]++
	}

	log.DebugContextf(ctx, "%s get report data: %+v", game_code, report_data)

	metric := ginmetrics.GetMonitor().GetMetric(metrics.AssetMap)
	for channel, map_source2count := range report_data {
		for map_source, count := range map_source2count {
			metric.SetGaugeValueWithLabels(map[string]string{"game_code": game_code, "channel": channel, "count_type": map_source}, float64(count))
		}
	}
}

// upsertAssetMaps 更新插入的素材映射关系
func upsertAssetMaps(ctx context.Context, game_code, author string, records []pgmodel.CreativeRecommendAssetMap) error {
	if len(records) == 0 {
		return nil
	}

	time_now := time.Now().Format("2006-01-02 15:04:05")
	new_records := make([]pgmodel.CreativeRecommendAssetMap, 0, len(records))
	for _, record := range records {
		record.CreateBy = author
		record.UpdateBy = author
		record.CreateTime = time_now
		record.UpdateTime = time_now

		new_records = append(new_records, record)
	}

	pg_query := pgdb.GetDBWithContext(ctx).Model(&new_records).Table(pgmodel.GetCreativeRecommendAssetMapTableName(game_code))
	pg_query.OnConflict("(channel_type, channel_account_id, channel_asset_id) do update")
	pg_query.Set("aix_asset_id=excluded.aix_asset_id")
	pg_query.Set("map_source=excluded.map_source")
	pg_query.Set("update_by=excluded.update_by")
	pg_query.Set("update_time=excluded.update_time")

	_, err := pg_query.Insert()
	if err != nil {
		return fmt.Errorf("insert asset maps failed: %s", err)
	}

	return nil
}

// genAssetMapByResourceName 通过resource_name进行映射
// 返回参数说明: 1 没有映射上的channel assets; 2 生成的映射关系; 3 error信息
func genAssetMapByResourceName(ctx context.Context, game_code string, channel_assets []channelAsset) ([]channelAsset, []pgmodel.CreativeRecommendAssetMap, error) {
	uploads, err := getUploadsForAssetMap(ctx, game_code)
	if err != nil {
		log.ErrorContextf(ctx, "getUploadsForAssetMap failed: %s", err)
	}

	resource_name2asset_id := make(map[string]string)
	for _, upload := range uploads {
		resource_name2asset_id[upload.ResourceName] = upload.AssetId
	}

	var unmapped_assets []channelAsset
	var asset_maps []pgmodel.CreativeRecommendAssetMap
	for _, channel_asset := range channel_assets {
		var resource_name string
		switch channel_asset.ChannelType {
		case 1: // google
			resource_name = getGoogleAdResourceName(channel_asset.AccountId, channel_asset.AssetId)
		case 2: // facebook facebook的的channel_asset_id就是upload表中的asset_id
			resource_name = channel_asset.AssetId
		default:
			log.ErrorContextf(ctx, "get unknown channel type: %+v", channel_asset)
			continue
		}

		aix_asset_id, ok := resource_name2asset_id[resource_name]
		if !ok {
			unmapped_assets = append(unmapped_assets, channel_asset)
			continue
		}

		var asset_map pgmodel.CreativeRecommendAssetMap
		asset_map.AixAssetId = aix_asset_id
		asset_map.ChannelAccountID = channel_asset.AccountId
		asset_map.ChannelAssetId = channel_asset.AssetId
		asset_map.ChannelType = channel_asset.ChannelType
		asset_map.MapSource = 1 // 通过resource name进行映射

		asset_maps = append(asset_maps, asset_map)
	}

	return unmapped_assets, asset_maps, nil
}

// genAssetMapByContent 通过素材内容进行映射
// 返回参数说明: 1 没有映射上的channel assets; 2 生成的映射关系; 3 error信息
func genAssetMapByContent(ctx context.Context, game_code string, channel_assets []channelAsset) ([]channelAsset, []pgmodel.CreativeRecommendAssetMap, error) {
	youtube_id2aix_asset_id, err := getYoutubeID2AixAssetId(ctx, game_code)
	if err != nil {
		return nil, nil, fmt.Errorf("getYoutubeID2AixAssetId failed: %s", err)
	}

	var unmapped_assets []channelAsset
	var asset_maps []pgmodel.CreativeRecommendAssetMap
	for _, channel_asset := range channel_assets {
		aix_asset_id, ok := youtube_id2aix_asset_id[channel_asset.YoutubeId]
		if !ok || len(aix_asset_id) == 0 {
			unmapped_assets = append(unmapped_assets, channel_asset)
			continue
		}

		var asset_map pgmodel.CreativeRecommendAssetMap
		asset_map.AixAssetId = aix_asset_id
		asset_map.ChannelAccountID = channel_asset.AccountId
		asset_map.ChannelAssetId = channel_asset.AssetId
		asset_map.ChannelType = channel_asset.ChannelType
		asset_map.MapSource = 3 // 通过素材内容进行映射

		asset_maps = append(asset_maps, asset_map)
	}

	return unmapped_assets, asset_maps, nil
}

// genAssetMapByName 通过素材名称进行映射
// 返回参数说明: 1 没有映射上的channel assets; 2 生成的映射关系; 3 error信息
func genAssetMapByName(ctx context.Context, game_code string, channel_assets []channelAsset) ([]channelAsset, []pgmodel.CreativeRecommendAssetMap, error) {
	overviews, err := getOverviewsForAssetMap(ctx, game_code)
	if err != nil {
		return nil, nil, fmt.Errorf("getOverviewsForAssetMap failed: %s", err)
	}

	asset_name2overview := make(map[string]pgmodel.CreativeOverview)
	for _, overview := range overviews {
		exist_overview, ok := asset_name2overview[overview.AssetName]
		if ok && exist_overview.UpdatedDate > overview.UpdatedDate { // 如果存在名称冲突则选最新的一个
			continue
		}

		asset_name2overview[overview.AssetName] = overview
	}

	getOverview := func(name string) (*pgmodel.CreativeOverview, bool) {
		if overview, ok := asset_name2overview[name]; ok {
			return &overview, true
		}

		kebab_name := getKebabName(name)
		if overview, ok := asset_name2overview[kebab_name]; ok {
			return &overview, true
		}

		pure_name := getPureName(name)
		if overview, ok := asset_name2overview[pure_name]; ok {
			return &overview, true
		}

		kebab_pure_name := getKebabName(pure_name)
		if overview, ok := asset_name2overview[kebab_pure_name]; ok {
			return &overview, true
		}

		return nil, false
	}

	var unmapped_assets []channelAsset
	var asset_maps []pgmodel.CreativeRecommendAssetMap
	for _, channel_asset := range channel_assets {
		overview, ok := getOverview(channel_asset.AssetName)
		if !ok {
			unmapped_assets = append(unmapped_assets, channel_asset)
			continue
		}

		var asset_map pgmodel.CreativeRecommendAssetMap
		asset_map.AixAssetId = overview.AssetID
		asset_map.ChannelAccountID = channel_asset.AccountId
		asset_map.ChannelAssetId = channel_asset.AssetId
		asset_map.ChannelType = channel_asset.ChannelType
		asset_map.MapSource = 2 // 通过素材名称进行映射

		asset_maps = append(asset_maps, asset_map)
	}

	return unmapped_assets, asset_maps, nil
}

// getYoutubeID2AixAssetId 获取内容映射关系
func getYoutubeID2AixAssetId(ctx context.Context, game_code string) (map[string]string, error) {
	var records []pgmodel.CreativeRecommendMediaContentMap
	pg_query := pgdb.GetDBWithContext(ctx).Model(&records).Table(pgmodel.GetCreativeRecommendMediaContentMapTableName(game_code))
	pg_query.Column("aix_asset_id", "media_id")
	pg_query.Where("storage_type=5") // 5表示youtube

	err := pg_query.Select()
	if err != nil {
		return nil, fmt.Errorf("select media content maps failed: %s", err)
	}

	youtube_id2aix_asset_id := make(map[string]string)
	for _, m := range records {
		youtube_id2aix_asset_id[m.MediaID] = m.AixAssetId
	}

	return youtube_id2aix_asset_id, nil
}

// getUploadsForAssetMap 获取用于数据挖掘的上传素材列表
func getUploadsForAssetMap(ctx context.Context, game_code string) ([]pgmodel.CreativeMediaUpload, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.CreativeMediaUpload{})
	pg_query.Column("asset_id", "account_id", "resource_name")
	pg_query.Where("game_code=?", game_code)

	var uploads []pgmodel.CreativeMediaUpload
	err := pg_query.Select(&uploads)
	if err != nil {
		return nil, fmt.Errorf("select creative uploads failed: %s", err)
	}

	return uploads, nil
}

// getOverviewsForAssetMap 获取可用的aix素材
func getOverviewsForAssetMap(ctx context.Context, game_code string) ([]pgmodel.CreativeOverview, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.CreativeOverview{})
	table_name := pgmodel.GetCreativeOverviewTableName(game_code)
	pg_query.Table(table_name)
	pg_query.Column("asset_id")
	pg_query.Column("asset_name")
	pg_query.Column("updated_date")
	pg_query.Where("length(asset_name)>0")
	pg_query.Where("asset_status=1")

	var overviews []pgmodel.CreativeOverview
	err := pg_query.Select(&overviews)
	if err != nil {
		return nil, fmt.Errorf("select creative overviews failed: %s", err)
	}
	log.DebugContextf(ctx, "get overviews number: %d", len(overviews))

	return overviews, nil
}

// latestChannelAssets 获取最近的渠道端素材
func latestChannelAssets(ctx context.Context, game_code string) ([]channelAsset, error) {
	pg_query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.FacebookRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(game_code))
	today := time.Now().AddDate(0, 0, -1).Format("********") // 延时一天避免时差
	pg_query.Where("dtstatdate=?", today)
	select_columns := []string{"asset_name", "account_id", "asset_id"}
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)

	var fb_records []pgmodel.FacebookRealtimeAssetInfo
	err := pg_query.Select(&fb_records)
	if err != nil {
		log.WarningContextf(ctx, "get facebook online number failed: %s", err)
		fb_records = nil
	}
	log.DebugContextf(ctx, "get facebook channel assets number: %d", len(fb_records))

	pg_query = pgdb.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	pg_query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(game_code))
	pg_query.Where("dtstatdate=?", today)
	select_columns = []string{"asset_name", "account_id", "asset_id", "youtube_id"}
	pg_query.Column(select_columns...)
	pg_query.Group(select_columns...)

	var gg_records []pgmodel.GoogleRealtimeAssetInfo
	err = pg_query.Select(&gg_records)
	if err != nil {
		log.WarningContextf(ctx, "get google online numbers failed: %s", err)
	}
	log.DebugContextf(ctx, "get google channel assets number: %d", len(gg_records))

	var channel_assets []channelAsset
	for _, record := range gg_records {
		if len(record.AssetId) == 0 {
			continue
		}

		ad_asset := channelAsset{}
		ad_asset.AssetName = record.AssetName
		ad_asset.AccountId = record.AccountId
		ad_asset.AssetId = record.AssetId
		ad_asset.ChannelType = 1 // google
		ad_asset.YoutubeId = record.YoutubeId

		channel_assets = append(channel_assets, ad_asset)
	}

	for _, record := range fb_records {
		if len(record.AssetId) == 0 {
			continue
		}

		ad_asset := channelAsset{}
		ad_asset.AssetName = record.AssetName
		ad_asset.AccountId = record.AccountId
		ad_asset.AssetId = record.AssetId
		ad_asset.ChannelType = 2 // facebook

		channel_assets = append(channel_assets, ad_asset)
	}

	return channel_assets, nil
}

// channelAssetsByDate 获取某个日期的渠道端素材
func channelAssetsByDate(ctx context.Context, gameCode string, date string) ([]*channelAsset, error) {
	query := pgdb.GetDBWithContext(ctx).Model(&pgmodel.FacebookRealtimeAssetInfo{})
	query.Table(pgmodel.GetFacebookRealtimeAssetInfoTableName(gameCode))
	query.Where("dtstatdate=?", date)
	selectColumns := []string{"asset_name", "account_id", "asset_id"}
	query.Column(selectColumns...)
	query.Group(selectColumns...)

	var fbRecords []*pgmodel.FacebookRealtimeAssetInfo
	err := query.Select(&fbRecords)
	if err != nil {
		log.WarningContextf(ctx, "get facebook online number failed: %s", err)
		fbRecords = nil
	}
	log.DebugContextf(ctx, "get facebook channel assets number: %d", len(fbRecords))

	query = pgdb.GetDBWithContext(ctx).Model(&pgmodel.GoogleRealtimeAssetInfo{})
	query.Table(pgmodel.GetGoogleRealtimeAssetInfoTableName(gameCode))
	query.Where("dtstatdate=?", date)
	selectColumns = []string{"asset_name", "account_id", "asset_id", "youtube_id"}
	query.Column(selectColumns...)
	query.Group(selectColumns...)

	var ggRecords []*pgmodel.GoogleRealtimeAssetInfo
	err = query.Select(&ggRecords)
	if err != nil {
		log.WarningContextf(ctx, "get google online numbers failed: %s", err)
	}
	log.DebugContextf(ctx, "get google channel assets number: %d", len(ggRecords))

	var channelAssets []*channelAsset
	for _, record := range ggRecords {
		if len(record.AssetId) == 0 {
			continue
		}

		a := &channelAsset{}
		a.AssetName = record.AssetName
		a.AccountId = record.AccountId
		a.AssetId = record.AssetId
		a.ChannelType = 1 // google
		a.YoutubeId = record.YoutubeId

		channelAssets = append(channelAssets, a)
	}

	for _, record := range fbRecords {
		if len(record.AssetId) == 0 {
			continue
		}

		a := &channelAsset{}
		a.AssetName = record.AssetName
		a.AccountId = record.AccountId
		a.AssetId = record.AssetId
		a.ChannelType = 2 // facebook

		channelAssets = append(channelAssets, a)
	}

	return channelAssets, nil
}
